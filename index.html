<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dune 2 Enhanced</title>
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <div id="gameArea">
        <!-- Top Perimeter: Resource Bar -->
        <div id="topPerimeter" class="perimeter-section">
            <div id="resourcePanel" class="ui-panel">
                <div class="resource-group primary">
                    <div class="resource-item spice">
                        <span class="resource-icon">🏺</span>
                        <span class="resource-amount" id="spiceAmount">0</span>
                        <span class="resource-rate" id="spiceRate">+0/min</span>
                    </div>
                    <div class="resource-item water">
                        <span class="resource-icon">💧</span>
                        <span class="resource-amount" id="waterAmount">0</span>
                        <span class="resource-rate" id="waterRate">+0/min</span>
                    </div>
                </div>
                <div class="resource-group secondary">
                    <div class="resource-item power">
                        <span class="resource-icon">⚡</span>
                        <span class="resource-amount" id="powerAmount">0</span>
                        <span class="resource-rate">/<span id="maxPower">0</span></span>
                    </div>
                    <div class="resource-item technology">
                        <span class="resource-icon">🔬</span>
                        <span class="resource-amount" id="techAmount">0</span>
                        <span class="resource-rate" id="techRate">+0/min</span>
                    </div>
                </div>
                <div class="resource-storage">
                    <div class="storage-indicator">
                        <span class="storage-label">Units</span>
                        <div class="storage-bar">
                            <div class="storage-fill" id="unitStorageFill" style="width: 0%"></div>
                        </div>
                        <span class="storage-text"><span id="unitCount">0</span>/<span id="unitMax">50</span></span>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls in Top Bar -->
            <div id="gameControls" class="ui-panel-compact">
                <button class="button-base" onclick="game.togglePause()">⏸️ Pause</button>
                <button class="button-base" onclick="game.openTechTree()">🔬 Research</button>
            </div>
        </div>
        
        <!-- Left Perimeter: Unit Management Panel -->
        <div id="leftPerimeter" class="perimeter-section collapsible">
            <div class="perimeter-header">
                <h4>Unit Management</h4>
                <button id="leftPanelToggle" class="panel-toggle">◀</button>
            </div>
            <div id="unitInfoPanel" class="ui-panel">
                        <div class="unit-header">
                            <div class="unit-portrait">
                                <img id="unitSprite" src="" alt="Unit" class="unit-image">
                                <div class="unit-level" id="unitLevel" style="display: none;">Lvl 1</div>
                                <div class="unit-experience" id="unitExperience" style="display: none;">
                                    <div class="xp-bar">
                                        <div class="xp-fill" id="unitXpFill" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="unit-identity">
                                <h3 class="unit-name" id="infoPanelName"></h3>
                                <p class="unit-type" id="unitType"></p>
                                <div class="unit-status" id="unitStatusBadges">
                                    <!-- Status badges will be added dynamically -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="unit-stats">
                            <div class="stat-group combat">
                                <div class="stat-item full-width">
                                    <span class="stat-icon">❤️</span>
                                    <span class="stat-label">Health</span>
                                    <div class="stat-value">
                                        <span class="current" id="infoPanelHealthCurrent">0</span>/<span class="max" id="infoPanelHealthMax">0</span>
                                    </div>
                                    <div class="health-bar">
                                        <div class="health-fill" id="unitHealthFill" style="width: 100%"></div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">⚔️</span>
                                    <span class="stat-label">Attack</span>
                                    <span class="stat-value" id="infoPanelAttack">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">🛡️</span>
                                    <span class="stat-label">Armor</span>
                                    <span class="stat-value" id="infoPanelDefense">0</span>
                                </div>
                            </div>
                            
                            <div class="stat-group mobility">
                                <div class="stat-item">
                                    <span class="stat-icon">💨</span>
                                    <span class="stat-label">Speed</span>
                                    <span class="stat-value" id="infoPanelSpeed">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">👁️</span>
                                    <span class="stat-label">Range</span>
                                    <span class="stat-value" id="infoPanelRange">0</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="unit-abilities" id="unitAbilities" style="display: none;">
                            <h4>Abilities</h4>
                            <div class="ability-list" id="abilityList">
                                <!-- Abilities will be added dynamically -->
                            </div>
                        </div>
                        
                        <div class="unit-commands" id="unitCommands">
                            <!-- Command buttons will be added dynamically -->
                        </div>

                        <!-- Legacy elements for compatibility -->
                        <div id="unitVisual" style="display: none;">
                            <img id="unitSpriteOld" src="" alt="Unit Sprite">
                        </div>
                        <div id="unitStats" style="display: none;">
                            <p>Health: <span id="infoPanelHealth"></span></p>
                            <p>Attack: <span id="infoPanelAttackOld"></span></p>
                            <p>Defense: <span id="infoPanelDefenseOld"></span></p>
                            <p>Speed: <span id="infoPanelSpeedOld"></span></p>
                        </div>
                        
                        <div id="productionQueue" style="display: none;">
                            <h4>Production Queue</h4>
                            <div id="queueItems">
                                <!-- Queue items will be added dynamically -->
                            </div>
                        </div>
            </div>
        </div>
        
        <!-- Right Perimeter: Strategic Overview -->
        <div id="rightPerimeter" class="perimeter-section collapsible">
            <div class="perimeter-header">
                <h4>Strategic Overview</h4>
                <button id="rightPanelToggle" class="panel-toggle">▶</button>
            </div>
            
            <!-- Enhanced Minimap -->
            <div id="minimapContainer" class="ui-panel">
                <div class="minimap-header">
                    <h4>Strategic Map</h4>
                    <div class="minimap-controls">
                        <button class="minimap-zoom" data-zoom="1x">1x</button>
                        <button class="minimap-zoom active" data-zoom="2x">2x</button>
                        <button class="minimap-zoom" data-zoom="4x">4x</button>
                    </div>
                </div>
                
                <div class="minimap-wrapper">
                    <canvas id="minimapCanvas" width="250" height="250"></canvas>
                    <div class="minimap-overlays">
                        <div class="viewport-indicator" id="viewportIndicator"></div>
                        <div class="strategic-markers" id="strategicMarkers">
                            <!-- Strategic markers will be added dynamically -->
                        </div>
                    </div>
                </div>
                
                <div class="minimap-legend">
                    <div class="legend-item">
                        <div class="legend-color terrain-desert"></div>
                        <span>Desert</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color terrain-rock"></div>
                        <span>Rock</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color terrain-spice"></div>
                        <span>Spice</span>
                    </div>
                </div>
                
                <div class="minimap-options">
                    <label class="option-toggle">
                        <input type="checkbox" checked id="showUnitsToggle">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Show Units</span>
                    </label>
                    <label class="option-toggle">
                        <input type="checkbox" checked id="showResourcesToggle">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Show Resources</span>
                    </label>
                    <label class="option-toggle">
                        <input type="checkbox" id="showTerrainToggle">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Terrain Info</span>
                    </label>
                </div>
            </div>
            
            <!-- Notifications in Right Panel -->
            <div id="notificationContainer"></div>
        </div>
        
        <!-- Central Area: Game Map -->
        <div id="centralArea" class="perimeter-section">
            <canvas id="gameCanvas" width="3000" height="3000"></canvas>
            <div id="selectionBox"></div>
        </div>
        
        <!-- Bottom Perimeter: Construction/Production Panel -->
        <div id="bottomPerimeter" class="perimeter-section">
            <div id="buildPanel" class="ui-panel">
                        <div id="buildCategories" class="tab-buttons">
                            <!-- Build category buttons will be dynamically loaded here by BuildPanel.js -->
                        </div>
                        <div id="buildingGrid" class="button-grid">
                            <!-- Build options will be dynamically loaded here by BuildPanel.js -->
                        </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Technology Tree Modal (hidden by default) -->
    <div id="techTreeModal" class="screen-overlay" style="display: none;">
        <div class="tech-tree-panel ui-panel" style="position: relative; z-index: 101;">
            <div class="tech-header">
                <h2>Technology Tree</h2>
                <div class="tech-points">
                    <span class="tech-icon">🔬</span>
                    <span class="points-available" id="techPoints">0</span>
                    <span class="points-label">Research Points</span>
                </div>
                <button class="button-base" onclick="game.closeTechTree()" style="margin-left: auto;">Close</button>
            </div>
            
            <div class="tech-categories">
                <button class="tech-category active" data-category="military">Military</button>
                <button class="tech-category" data-category="industrial">Industrial</button>
                <button class="tech-category" data-category="defensive">Defensive</button>
                <button class="tech-category" data-category="logistics">Logistics</button>
            </div>
            
            <div class="tech-tree-container">
                <div class="tech-tree" id="techTree" data-category="military">
                    <!-- Technology nodes will be added dynamically -->
                </div>
            </div>
            
            <div class="tech-details" id="techDetails">
                <div class="selected-tech" style="display: none;">
                    <h3 id="selectedTechName">Select a Technology</h3>
                    <p class="tech-description" id="selectedTechDescription">Click on a technology to view details.</p>
                    <div class="tech-effects" id="selectedTechEffects">
                        <!-- Effects will be added dynamically -->
                    </div>
                    <button class="research-button" id="researchButton" style="display: none;">Research</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Screen Overlays -->
    <div id="pauseOverlay" class="screen-overlay">
        <h1>Game Paused</h1>
        <button class="button-base" onclick="game.togglePause()">Resume</button>
        <button class="button-base button-secondary" onclick="game.openTechTree()" style="margin-top: 10px;">Technology Tree</button>
    </div>

    <div id="victoryScreen" class="screen-overlay">
        <h1>Victory!</h1>
        <div id="victoryStats"></div>
        <button class="button-base" onclick="game.restart()">Play Again</button>
    </div>

    <div id="defeatScreen" class="screen-overlay">
        <h1>Defeat!</h1>
        <div id="defeatStats"></div>
        <button class="button-base" onclick="game.restart()">Try Again</button>
    </div>

    <!-- Terrain Info Tooltip -->
    <div id="terrainTooltip" class="ui-panel" style="position: absolute; display: none; z-index: 1000; pointer-events: none; padding: 8px; font-size: 0.8em;">
        <div id="terrainTooltipContent">
            <!-- Terrain information will be added dynamically -->
        </div>
    </div>

    <script type="module" src="/js/main.js"></script>
</body>
</html>
