/* Enhanced Dune 2 UI Design System */
@import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;600;700&display=swap');

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */
:root {
  /* Primary Dune Orange - Spice/Desert theme */
  --dune-primary: #D2691E;
  --dune-primary-light: #F4A460;
  --dune-primary-dark: #A0522D;

  /* Secondary Blue - Technology/House Atreides theme */
  --dune-secondary: #4F8FBF;
  --dune-secondary-light: #79A8BF;
  --dune-secondary-dark: #2E5A7A;

  /* Accent Gold - Precious resources/Imperial theme */
  --dune-accent: #FFD700;
  --dune-accent-light: #FFED4E;
  --dune-accent-dark: #B8860B;

  /* Functional Colors */
  --color-success: #4CAF50;
  --color-success-light: #81C784;
  --color-success-dark: #388E3C;
  --color-warning: #FF9800;
  --color-warning-light: #FFB74D;
  --color-warning-dark: #F57C00;
  --color-error: #F44336;
  --color-error-light: #E57373;
  --color-error-dark: #D32F2F;
  --color-info: #2196F3;
  --color-info-light: #64B5F6;
  --color-info-dark: #1976D2;

  /* Background System */
  --bg-primary: #121418;
  --bg-secondary: #1E232D;
  --bg-tertiary: #2A3240;
  --bg-panel: rgba(30, 35, 45, 0.92);
  --bg-panel-hover: rgba(30, 35, 45, 0.98);
  --bg-panel-active: rgba(47, 54, 64, 0.95);
  --bg-overlay: rgba(10, 12, 15, 0.85);
  --bg-tooltip: rgba(0, 0, 0, 0.9);

  /* Text Color System */
  --text-primary: #FFFFFF;
  --text-secondary: #C0C0D0;
  --text-tertiary: #90A0B0;
  --text-disabled: #707880;
  --text-accent: #6FA8DC;
  --text-inverse: #121418;
  --text-warning: #FFB74D;
  --text-error: #FF6B6B;

  /* Resource-Specific Colors */
  --resource-spice: #D2691E;
  --resource-spice-bg: rgba(210, 105, 30, 0.1);
  --resource-water: #4FC3F7;
  --resource-water-bg: rgba(79, 195, 247, 0.1);
  --resource-tech: #9C27B0;
  --resource-tech-bg: rgba(156, 39, 176, 0.1);
  --resource-minerals: #FF9800;
  --resource-minerals-bg: rgba(255, 152, 0, 0.1);
  --resource-power: #FFEB3B;
  --resource-power-bg: rgba(255, 235, 59, 0.1);

  /* Faction Colors */
  --faction-player: #4F8FBF;
  --faction-enemy: #D32F2F;
  --faction-neutral: #757575;
  --faction-ally: #4CAF50;
  --faction-trade: #FF9800;

  /* Typography */
  --font-family-primary: 'Exo 2', 'Segoe UI', sans-serif;
  --font-family-mono: 'Fira Code', 'Consolas', monospace;
  
  --font-size-display: 2.5rem;
  --font-size-h1: 2rem;
  --font-size-h2: 1.5rem;
  --font-size-h3: 1.25rem;
  --font-size-h4: 1.125rem;
  --font-size-body: 1rem;
  --font-size-body-small: 0.875rem;
  --font-size-ui: 0.875rem;
  --font-size-ui-small: 0.75rem;
  --font-size-mono: 0.875rem;
  
  --line-height-display: 1.2;
  --line-height-heading: 1.3;
  --line-height-body: 1.5;
  --line-height-ui: 1.4;
  --line-height-mono: 1.4;
  
  --font-weight-display: 300;
  --font-weight-heading: 500;
  --font-weight-body: 400;
  --font-weight-ui: 500;
  --font-weight-mono: 400;

  /* Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Component Spacing */
  --panel-padding: var(--space-md);
  --panel-margin: var(--space-md);
  --panel-gap: var(--space-sm);
  --button-padding-x: var(--space-md);
  --button-padding-y: var(--space-sm);
  --button-gap: var(--space-sm);
  --grid-gap: var(--space-sm);
  --grid-margin: var(--space-md);

  /* Transitions */
  --transition-fast: 0.1s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
  
  /* Phase 4 Animation Variables */
  --bounce-easing: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --smooth-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --elastic-easing: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Responsive Breakpoints */
  --breakpoint-mobile: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-large: 1440px;
  --breakpoint-xl: 1920px;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  overflow: hidden;
  font-family: var(--font-family-primary);
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  font-weight: var(--font-weight-body);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
}

#gameArea {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: var(--bg-primary);
  overflow: hidden;
  display: grid;
  grid-template-areas:
    "top-bar top-bar top-bar"
    "left-panel central-area right-panel"
    "bottom-bar bottom-bar bottom-bar";
  grid-template-rows: 60px 1fr 180px;
  grid-template-columns: 320px 1fr 320px;
  gap: 0;
}

/* ===== PERIMETER LAYOUT SYSTEM ===== */
.perimeter-section {
  position: relative;
  background-color: transparent;
  overflow: hidden;
}

#topPerimeter {
  grid-area: top-bar;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--bg-panel);
  border-bottom: 1px solid rgba(79, 143, 191, 0.3);
  z-index: 20;
}

#leftPerimeter {
  grid-area: left-panel;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-panel);
  border-right: 1px solid rgba(79, 143, 191, 0.3);
  transition: all var(--transition-normal);
  z-index: 15;
}

#rightPerimeter {
  grid-area: right-panel;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-panel);
  border-left: 1px solid rgba(79, 143, 191, 0.3);
  transition: all var(--transition-normal);
  z-index: 15;
}

#centralArea {
  grid-area: central-area;
  position: relative;
  background-color: var(--bg-primary);
  overflow: hidden;
  z-index: 5;
}

#bottomPerimeter {
  grid-area: bottom-bar;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--bg-panel);
  border-top: 1px solid rgba(79, 143, 191, 0.3);
  z-index: 15;
}

/* ===== COLLAPSIBLE PANEL SYSTEM ===== */
.collapsible {
  transition: all var(--transition-normal) var(--smooth-easing);
}

.collapsible.collapsed {
  width: 60px !important;
}

#leftPerimeter.collapsed {
  grid-template-columns: 60px 1fr 320px;
}

#rightPerimeter.collapsed {
  grid-template-columns: 320px 1fr 60px;
}

.perimeter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid rgba(79, 143, 191, 0.2);
  min-height: 40px;
}

.perimeter-header h4 {
  margin: 0;
  color: var(--text-accent);
  font-size: 0.9em;
  font-weight: 600;
}

.panel-toggle {
  background: transparent;
  border: 1px solid var(--dune-secondary-dark);
  color: var(--text-secondary);
  padding: var(--space-xs);
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
  transition: all var(--transition-fast);
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-toggle:hover {
  background: var(--dune-secondary);
  color: var(--text-primary);
}

.collapsed .perimeter-header h4 {
  display: none;
}

.collapsed .panel-toggle {
  transform: rotate(180deg);
}

/* ===== GAME CANVAS POSITIONING ===== */
#gameCanvas {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: contain;
}

/* ===== ENHANCED RESOURCE PANEL ===== */
#resourcePanel {
  display: flex;
  gap: var(--space-lg);
  align-items: center;
  padding: 0;
  background: transparent;
  border-radius: 0;
  backdrop-filter: none;
  margin: 0;
  pointer-events: auto;
  flex: 1;
}

#gameControls {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

#gameControls .button-base {
  padding: var(--space-xs) var(--space-sm);
  min-height: 32px;
  font-size: 0.8em;
}

.resource-group {
  display: flex;
  gap: var(--space-md);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  min-width: 120px;
}

.resource-icon {
  font-size: 1.2em;
  width: 20px;
  text-align: center;
}

.resource-amount {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 60px;
  text-align: right;
}

.resource-rate {
  font-size: 0.85em;
  color: var(--text-tertiary);
  min-width: 50px;
}

.resource-rate.positive { color: var(--color-success); }
.resource-rate.negative { color: var(--color-warning); }

.storage-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  min-width: 200px;
}

.storage-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-secondary);
  border-radius: 3px;
  overflow: hidden;
}

.storage-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--dune-secondary), var(--dune-secondary-light));
  transition: width 0.3s ease;
}

.storage-fill.warning { background: linear-gradient(90deg, var(--color-warning), var(--color-warning-light)); }
.storage-fill.critical { background: linear-gradient(90deg, var(--color-error), var(--color-error-light)); }

/* ===== PANEL SYSTEM ===== */
.ui-panel {
  pointer-events: auto;
  padding: var(--panel-padding);
  margin: var(--space-sm);
  border-radius: 6px;
  background-color: rgba(30, 35, 45, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(79, 143, 191, 0.3);
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  flex: 1;
  overflow-y: auto;
}

/* Perimeter panels have different styling */
#leftPerimeter .ui-panel,
#rightPerimeter .ui-panel,
#bottomPerimeter .ui-panel {
  background-color: transparent;
  border: none;
  box-shadow: none;
  backdrop-filter: none;
  margin: 0;
}

.ui-panel:hover {
  background-color: var(--bg-panel-hover);
  border-color: rgba(79, 143, 191, 0.5);
}

.ui-panel-compact {
  padding: var(--space-sm);
  margin: var(--space-sm);
}

.ui-panel-highlight {
  border-color: var(--dune-accent);
  box-shadow: 
    0 2px 10px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 215, 0, 0.1);
}

.ui-panel-warning {
  border-left: 4px solid var(--color-warning);
  background-color: rgba(255, 152, 0, 0.05);
}

.ui-panel-error {
  border-left: 4px solid var(--color-error);
  background-color: rgba(244, 67, 54, 0.05);
}

/* ===== REMOVED OLD LAYOUT STYLES ===== */
/* Old bottom UI container styles removed for new perimeter layout */

/* ===== ENHANCED UNIT INFO PANEL ===== */
#unitInfoPanel {
  width: 100%;
  min-height: 280px;
  display: none;
  flex-direction: column;
  gap: var(--space-md);
  padding: var(--space-md);
  background-color: rgba(30, 35, 45, 0.95);
  border-radius: 6px;
  border: 1px solid rgba(79, 143, 191, 0.3);
}

.unit-header {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(79, 143, 191, 0.2);
}

.unit-portrait {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 2px solid var(--dune-secondary);
}

.unit-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.unit-level {
  position: absolute;
  top: 4px;
  right: 4px;
  background: var(--dune-accent);
  color: var(--text-inverse);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75em;
  font-weight: 600;
}

.unit-experience {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.5);
}

.xp-bar {
  height: 100%;
  background: var(--bg-secondary);
}

.xp-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--dune-accent-dark), var(--dune-accent));
  transition: width 0.5s ease;
}

.unit-identity {
  flex: 1;
}

.unit-name {
  margin: 0 0 var(--space-xs) 0;
  color: var(--text-accent);
  font-size: 1.1em;
  font-weight: 600;
}

.unit-type {
  margin: 0 0 var(--space-sm) 0;
  color: var(--text-tertiary);
  font-size: 0.9em;
}

.unit-status {
  display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 500;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.status-badge.veteran {
  background: var(--dune-accent);
  color: var(--text-inverse);
}

.status-badge.formation {
  background: var(--dune-secondary);
  color: var(--text-primary);
}

.unit-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

.stat-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-sm);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-sm);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.stat-item.full-width {
  grid-column: 1 / -1;
}

.stat-icon {
  font-size: 1.1em;
}

.stat-label {
  font-size: 0.85em;
  color: var(--text-tertiary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value .current {
  color: var(--text-primary);
}

.stat-value .max {
  color: var(--text-tertiary);
}

.health-bar {
  height: 4px;
  background: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin-top: var(--space-xs);
}

.health-fill {
  height: 100%;
  background: var(--color-success);
  transition: width 0.3s ease, background-color 0.3s ease;
}

.health-fill.damaged { background: var(--color-warning); }
.health-fill.critical { background: var(--color-error); }

.unit-abilities {
  margin-bottom: var(--space-md);
}

.unit-abilities h4 {
  margin: 0 0 var(--space-sm) 0;
  color: var(--text-accent);
  font-size: 0.9em;
  font-weight: 600;
}

.ability-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.ability-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background: var(--bg-tertiary);
  border: 1px solid var(--dune-secondary-dark);
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.15s ease;
}

.ability-button:hover {
  background: var(--dune-secondary);
  color: var(--text-primary);
}

.ability-button.cooldown {
  opacity: 0.6;
  cursor: not-allowed;
}

.ability-button.cooldown:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.ability-icon {
  width: 20px;
  text-align: center;
}

.ability-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.ability-cooldown {
  font-size: 0.85em;
  color: var(--text-tertiary);
}

/* ===== LEGACY SUPPORT FOR EXISTING ELEMENTS ===== */
#unitVisual {
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(79, 143, 191, 0.4);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
}

#unitVisual img {
  max-width: 90%;
  max-height: 90%;
}

#unitStats {
  font-size: 0.95em;
  line-height: 1.6;
}

#unitStats p {
  margin: 3px 0;
  display: flex;
  justify-content: space-between;
}

#unitStats p span {
  color: var(--text-primary);
  font-weight: 500;
}

#unitCommands {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  gap: 6px;
  margin-top: 8px;
}

.unit-commands {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-sm);
}

.command-button {
  padding: var(--space-sm);
  background: var(--bg-tertiary);
  border: 1px solid var(--dune-secondary-dark);
  border-radius: 4px;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.command-button:hover {
  background: var(--dune-secondary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.command-button:active {
  transform: translateY(0);
}

/* ===== BUTTON SYSTEM ===== */
.button-base {
  padding: var(--button-padding-y) var(--button-padding-x);
  min-height: 44px;
  border-radius: 4px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-ui);
  font-weight: 500;
  text-align: center;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--dune-secondary-dark);
  cursor: pointer;
  transition: all 0.15s ease;
  user-select: none;
}

.button-base:hover {
  background-color: var(--dune-secondary);
  color: var(--text-primary);
  border-color: var(--dune-secondary-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(79, 143, 191, 0.2);
}

.button-base:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(79, 143, 191, 0.3);
}

.button-base:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-disabled);
  border-color: var(--bg-tertiary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button-primary {
  background-color: var(--dune-primary);
  color: var(--text-primary);
  border-color: var(--dune-primary-dark);
}

.button-primary:hover {
  background-color: var(--dune-primary-light);
  border-color: var(--dune-primary);
}

.button-secondary {
  background-color: transparent;
  color: var(--text-accent);
  border-color: var(--dune-secondary);
}

.button-secondary:hover {
  background-color: var(--dune-secondary);
  color: var(--text-primary);
}

.button-danger {
  background-color: var(--color-error);
  color: var(--text-primary);
  border-color: var(--color-error-dark);
}

.button-danger:hover {
  background-color: var(--color-error-light);
}

.unit-button, .build-button, .upgrade-button, .tab-button {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--dune-secondary-dark);
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-family: var(--font-family-primary);
  font-size: 0.9em;
  font-weight: 500;
  text-align: center;
  transition: all 0.15s ease;
}

.unit-button:hover, .build-button:hover, .upgrade-button:hover, .tab-button:not(.active):hover {
  background-color: var(--dune-secondary);
  border-color: var(--dune-secondary-light);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.unit-button:active, .build-button:active, .upgrade-button:active, .tab-button:active {
  background-color: var(--dune-secondary-dark);
  transform: translateY(0px);
}

.unit-button:disabled, .build-button:disabled, .upgrade-button:disabled {
  background-color: var(--bg-secondary);
  border-color: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
  transform: translateY(0px);
  box-shadow: none;
}

.button-cost {
  font-size: 0.85em;
  color: var(--color-warning-light);
  margin-left: 5px;
  display: block;
  font-weight: 400;
}

/* ===== BUILD PANEL ===== */
#buildPanel {
  width: 100%;
  max-width: none;
  padding: var(--space-md);
  background-color: rgba(30, 35, 45, 0.95);
  border-radius: 6px;
  border: 1px solid rgba(79, 143, 191, 0.3);
}

.tab-buttons {
  display: flex;
  margin-bottom: var(--space-md);
  background-color: rgba(0,0,0,0.2);
  border-radius: 4px;
  padding: 4px;
}

.tab-button {
  flex-grow: 1;
  padding: 8px 5px;
  font-size: 0.85em;
  background-color: transparent;
  border-color: transparent;
  color: var(--text-tertiary);
}

.tab-button.active {
  background-color: var(--dune-secondary);
  color: var(--text-primary);
  border-color: var(--dune-secondary);
  box-shadow: 0 0 8px rgba(79, 143, 191, 0.3);
  font-weight: 700;
  transform: translateY(0px);
}

.tab-button.active:hover {
  background-color: var(--dune-secondary-light);
}

#buildContent {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.build-button {
  padding: 10px 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

/* ===== PRODUCTION QUEUE ===== */
#productionQueue {
  margin-top: var(--space-md);
  border-top: 1px solid rgba(79, 143, 191, 0.2);
  padding-top: var(--space-md);
}

.queue-item {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 6px 8px;
  margin-bottom: 4px;
  border-radius: 3px;
  font-size: 0.9em;
  position: relative;
  overflow: hidden;
  border-left: 3px solid var(--dune-secondary);
}

.queue-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(79, 143, 191, 0.25);
  z-index: -1;
}

/* ===== PROGRESS INDICATORS ===== */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--dune-secondary-dark), 
    var(--dune-secondary), 
    var(--dune-secondary-light));
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.health-bar-fill.high { background-color: var(--color-success); }
.health-bar-fill.medium { background-color: var(--color-warning); }
.health-bar-fill.low { background-color: var(--color-error); }

/* ===== ENHANCED MINIMAP ===== */
#minimapContainer {
  width: 100%;
  padding: var(--space-md);
  background-color: rgba(30, 35, 45, 0.95);
  border-radius: 6px;
  border: 1px solid rgba(79, 143, 191, 0.3);
  margin-bottom: var(--space-md);
}

.minimap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.minimap-header h4 {
  margin: 0;
  color: var(--text-accent);
  font-size: 0.9em;
  font-weight: 600;
}

.minimap-controls {
  display: flex;
  gap: var(--space-xs);
}

.minimap-zoom {
  padding: var(--space-xs) var(--space-sm);
  background: var(--bg-tertiary);
  border: 1px solid var(--dune-secondary-dark);
  border-radius: 3px;
  color: var(--text-secondary);
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.15s ease;
}

.minimap-zoom.active {
  background: var(--dune-secondary);
  color: var(--text-primary);
}

.minimap-wrapper {
  position: relative;
  width: 250px;
  height: 250px;
  margin-bottom: var(--space-md);
  border: 2px solid var(--dune-secondary-dark);
  border-radius: 6px;
  overflow: hidden;
}

#minimapCanvas {
  width: 100%;
  height: 100%;
  background-color: #1a1c22;
  border-radius: 3px;
}

.minimap-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.viewport-indicator {
  position: absolute;
  border: 2px solid var(--text-primary);
  border-radius: 2px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
  pointer-events: auto;
  cursor: move;
}

.strategic-markers {
  position: absolute;
  width: 100%;
  height: 100%;
}

.marker {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%);
}

.marker.resource {
  background: var(--resource-spice);
  box-shadow: 0 0 6px var(--resource-spice);
}

.marker.enemy {
  background: var(--faction-enemy);
  box-shadow: 0 0 6px var(--faction-enemy);
}

.marker.ally {
  background: var(--faction-ally);
  box-shadow: 0 0 6px var(--faction-ally);
}

.minimap-legend {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  font-size: 0.8em;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.terrain-desert { background: #8B4513; }
.terrain-rock { background: #696969; }
.terrain-spice { background: var(--resource-spice); }

.minimap-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.option-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
  font-size: 0.85em;
}

.option-toggle input {
  display: none;
}

.toggle-slider {
  width: 32px;
  height: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  position: relative;
  transition: background 0.2s ease;
}

.toggle-slider::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background: var(--text-tertiary);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.option-toggle input:checked + .toggle-slider {
  background: var(--dune-secondary);
}

.option-toggle input:checked + .toggle-slider::after {
  transform: translateX(16px);
  background: var(--text-primary);
}

.toggle-label {
  color: var(--text-secondary);
}

/* ===== NOTIFICATIONS ===== */
#notificationContainer {
  position: static;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
  padding: var(--space-sm);
}

.notification {
  background-color: var(--bg-panel);
  border: 1px solid var(--dune-secondary);
  border-left-width: 4px;
  padding: var(--space-md) var(--space-lg);
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.9em;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: slideInAndFadeOut 3s forwards;
}

.notification.error { border-left-color: var(--color-error); }
.notification.success { border-left-color: var(--color-success); }
.notification.info { border-left-color: var(--dune-secondary); }

@keyframes slideInAndFadeOut {
  0% { opacity: 0; transform: translateX(20px); }
  15% { opacity: 1; transform: translateX(0); }
  85% { opacity: 1; transform: translateX(0); }
  100% { opacity: 0; transform: translateX(20px); }
}

/* ===== SELECTION BOX ===== */
#selectionBox {
  position: absolute;
  border: 1.5px solid var(--text-accent);
  background-color: rgba(79, 143, 191, 0.15);
  display: none;
  pointer-events: none;
  border-radius: 2px;
}

/* ===== SCREEN OVERLAYS ===== */
.screen-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  color: var(--text-accent);
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 2.8em;
  font-weight: var(--font-weight-display);
  z-index: 100;
  text-shadow: 0 0 15px rgba(79, 143, 191, 0.3);
}

.screen-overlay h1 {
  font-size: 1em;
  margin-bottom: 20px;
  font-weight: var(--font-weight-heading);
}

.screen-overlay button {
  padding: 12px 25px;
  font-size: 0.5em;
  margin-top: 25px;
  min-width: 150px;
}

.screen-overlay button:hover {
  box-shadow: 0 0 10px rgba(79, 143, 191, 0.4);
}

/* ===== HOVER EFFECTS ===== */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(79, 143, 191, 0.3);
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-pulse {
  animation: pulse 2s infinite;
}

.loading-spin {
  animation: spin 1s linear infinite;
}

/* ===== ACCESSIBILITY ===== */
.focus-visible {
  outline: 2px solid var(--dune-accent);
  outline-offset: 2px;
}

.focus-visible:not(.focus-visible) {
  outline: none;
}

@media (prefers-contrast: high) {
  :root {
    --text-primary: #FFFFFF;
    --text-secondary: #FFFFFF;
    --bg-panel: #000000;
    --dune-secondary: #0066CC;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  #gameArea {
    grid-template-columns: 280px 1fr 280px;
  }
  
  .perimeter-header h4 {
    font-size: 0.8em;
  }
}

@media (max-width: 768px) {
  :root {
    --font-size-display: 2rem;
    --font-size-h1: 1.75rem;
    --font-size-h2: 1.375rem;
    --font-size-h3: 1.125rem;
  }
  
  #gameArea {
    grid-template-areas:
      "top-bar"
      "central-area"
      "bottom-bar";
    grid-template-rows: 60px 1fr auto;
    grid-template-columns: 1fr;
  }
  
  #leftPerimeter,
  #rightPerimeter {
    display: none;
  }
  
  #bottomPerimeter {
    grid-area: bottom-bar;
    flex-direction: column;
    height: auto;
    max-height: 40vh;
    overflow-y: auto;
  }
  
  #buildPanel {
    margin-bottom: var(--space-md);
  }
}

@media (max-width: 480px) {
  :root {
    --font-size-display: 1.75rem;
    --font-size-h1: 1.5rem;
    --font-size-h2: 1.25rem;
    --font-size-h3: 1rem;
  }
  
  .resource-item {
    min-width: 100px;
  }
  
  #resourcePanel {
    flex-wrap: wrap;
    gap: var(--space-sm);
  }
  
  #gameArea {
    grid-template-rows: auto 1fr auto;
  }
  
  #topPerimeter {
    padding: var(--space-xs) var(--space-sm);
  }
  
  #gameControls {
    display: none;
  }
}

/* ===== PHASE 4 POLISH & INTEGRATION STYLES ===== */

/* Enhanced Animations */
@keyframes slideInFade {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes feedbackPulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes buttonHover {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

@keyframes healthPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes resourceFlow {
  0% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(-20px); opacity: 0; }
}

@keyframes constructionSpark {
  0% { opacity: 1; transform: scale(0.5) rotate(0deg); }
  50% { opacity: 0.8; transform: scale(1) rotate(180deg); }
  100% { opacity: 0; transform: scale(1.5) rotate(360deg); }
}

@keyframes unitSpawn {
  0% {
    opacity: 0;
    transform: scale(0.1) rotate(0deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

@keyframes explosionRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced Button Animations */
.build-button, .command-button, .upgrade-button {
  transition: all var(--transition-fast) var(--smooth-easing);
}

.build-button:hover, .command-button:hover, .upgrade-button:hover {
  animation: buttonHover 0.15s var(--smooth-easing) forwards;
  box-shadow: 0 4px 12px rgba(79, 143, 191, 0.3);
}

.build-button:active, .command-button:active, .upgrade-button:active {
  animation: none;
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Enhanced Health Bar Animations */
.health-fill {
  transition: width 0.3s var(--smooth-easing), background-color 0.2s ease;
}

.health-fill.damaged {
  animation: healthPulse 1s ease-in-out infinite;
}

.health-fill.critical {
  animation: healthPulse 0.5s ease-in-out infinite;
}

/* Resource Collection Animation */
.resource-collection-text {
  position: absolute;
  font-weight: bold;
  font-size: 14px;
  color: var(--dune-accent);
  pointer-events: none;
  z-index: 9999;
  animation: resourceFlow 1s var(--smooth-easing) forwards;
}

/* Construction Effects */
.construction-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--dune-accent);
  border-radius: 50%;
  pointer-events: none;
  animation: constructionSpark 0.8s ease-out forwards;
}

/* Unit Spawn Effects */
.unit-spawn-effect {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 2px solid var(--color-success);
  border-radius: 50%;
  pointer-events: none;
  animation: unitSpawn 0.5s var(--bounce-easing) forwards;
}

/* Explosion Effects */
.explosion-effect {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-error);
  border-radius: 50%;
  pointer-events: none;
  animation: explosionRipple 0.6s ease-out forwards;
}

/* Enhanced Notification Animations */
.notification {
  animation: slideInFade 0.3s var(--smooth-easing) forwards;
}

.notification.fade-out {
  animation: slideInFade 0.3s var(--smooth-easing) reverse forwards;
}

/* Performance Indicator */
.performance-indicator {
  position: fixed;
  top: 10px;
  left: 10px;
  background: var(--bg-panel);
  padding: var(--space-sm);
  border-radius: 4px;
  font-family: var(--font-family-mono);
  font-size: 0.8em;
  color: var(--text-tertiary);
  z-index: 9999;
  border: 1px solid var(--dune-secondary-dark);
}

.performance-indicator.warning {
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.performance-indicator.critical {
  border-color: var(--color-error);
  color: var(--color-error);
  animation: healthPulse 0.5s ease-in-out infinite;
}

/* Debug Panel */
#debugPanel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: var(--bg-panel);
  color: var(--text-primary);
  padding: var(--space-md);
  border-radius: 6px;
  font-family: var(--font-family-mono);
  font-size: 0.8em;
  z-index: 10000;
  border: 1px solid var(--dune-secondary);
  backdrop-filter: blur(10px);
  min-width: 200px;
}

#debugPanel h3 {
  margin: 0 0 var(--space-sm) 0;
  color: var(--dune-accent);
  font-size: 1em;
}

#debugPanel button {
  margin: var(--space-xs) var(--space-xs) 0 0;
  padding: var(--space-xs) var(--space-sm);
  font-size: 0.75em;
  min-height: auto;
}

/* Enhanced Tooltips */
.tooltip {
  position: absolute;
  background: var(--bg-tooltip);
  color: var(--text-primary);
  padding: var(--space-sm) var(--space-md);
  border-radius: 4px;
  font-size: 0.85em;
  max-width: 250px;
  z-index: 9999;
  pointer-events: none;
  border: 1px solid var(--dune-secondary-dark);
  backdrop-filter: blur(8px);
  animation: slideInFade 0.2s var(--smooth-easing) forwards;
}

.tooltip::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid var(--bg-tooltip);
}

/* Enhanced Progress Bars */
.progress-bar-enhanced {
  position: relative;
  overflow: hidden;
}

.progress-bar-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  animation: shimmer 2s infinite;
}

/* Smooth Panel Transitions */
.ui-panel {
  transition: all var(--transition-normal) var(--smooth-easing);
}

.ui-panel.slide-in {
  animation: slideInFade 0.3s var(--smooth-easing) forwards;
}

.ui-panel.slide-out {
  animation: slideInFade 0.3s var(--smooth-easing) reverse forwards;
}

/* Enhanced Selection Effects */
.entity-selected {
  filter: drop-shadow(0 0 8px var(--dune-accent));
  animation: healthPulse 2s ease-in-out infinite;
}

.entity-highlighted {
  filter: drop-shadow(0 0 6px var(--text-primary));
}

/* Smooth Camera Transitions */
.camera-transition {
  transition: transform 0.5s var(--smooth-easing);
}

/* Enhanced Minimap */
.minimap-wrapper {
  transition: all var(--transition-normal);
}

.minimap-wrapper:hover {
  border-color: var(--dune-secondary);
  box-shadow: 0 0 15px rgba(79, 143, 191, 0.2);
}

/* Save/Load UI */
.save-slot {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--dune-secondary-dark);
  border-radius: 4px;
  margin-bottom: var(--space-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.save-slot:hover {
  background: var(--bg-panel-hover);
  border-color: var(--dune-secondary);
  transform: translateX(4px);
}

.save-slot-preview {
  width: 60px;
  height: 40px;
  background: var(--bg-secondary);
  border-radius: 3px;
  border: 1px solid var(--dune-secondary-dark);
}

.save-slot-info {
  flex: 1;
}

.save-slot-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.save-slot-meta {
  font-size: 0.8em;
  color: var(--text-tertiary);
}

.save-slot-actions {
  display: flex;
  gap: var(--space-xs);
}

/* Help System */
.help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.help-content {
  background: var(--bg-panel);
  border: 1px solid var(--dune-secondary);
  border-radius: 8px;
  padding: var(--space-2xl);
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideInFade 0.3s var(--bounce-easing) forwards;
}

.help-content h2 {
  color: var(--dune-accent);
  margin-bottom: var(--space-lg);
}

.help-section {
  margin-bottom: var(--space-lg);
}

.help-section h3 {
  color: var(--text-accent);
  margin-bottom: var(--space-md);
}

.help-shortcut {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm);
  background: var(--bg-tertiary);
  border-radius: 3px;
  margin-bottom: var(--space-xs);
}

.help-key {
  background: var(--bg-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: 3px;
  font-family: var(--font-family-mono);
  font-weight: 600;
  color: var(--dune-accent);
}

/* Context Menu */
.context-menu {
  position: absolute;
  background: var(--bg-panel);
  border: 1px solid var(--dune-secondary);
  border-radius: 4px;
  padding: var(--space-xs);
  z-index: 9999;
  min-width: 150px;
  animation: slideInFade 0.2s var(--smooth-easing) forwards;
}

.context-menu-item {
  padding: var(--space-sm) var(--space-md);
  cursor: pointer;
  border-radius: 3px;
  transition: background var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.context-menu-item:hover {
  background: var(--dune-secondary);
  color: var(--text-primary);
}

.context-menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.context-menu-item.disabled:hover {
  background: transparent;
  color: var(--text-secondary);
}

/* Enhanced Fog of War */
.fog-overlay {
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.fog-overlay.explored {
  opacity: 0.4;
}

.fog-overlay.visible {
  opacity: 0;
}

/* Particle System Enhancements */
.particle-container {
  position: absolute;
  pointer-events: none;
  z-index: 5;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

.particle.spark {
  background: var(--dune-accent);
  box-shadow: 0 0 6px var(--dune-accent);
}

.particle.smoke {
  background: rgba(128, 128, 128, 0.6);
  border-radius: 30%;
}

.particle.explosion {
  background: var(--color-error);
  box-shadow: 0 0 10px var(--color-error);
}

/* Enhanced Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--dune-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Enhanced Error States */
.error-message {
  background: var(--color-error);
  color: var(--text-primary);
  padding: var(--space-md);
  border-radius: 4px;
  margin: var(--space-md);
  animation: feedbackPulse 0.5s ease-out;
}

/* Performance Optimization Classes */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-transform {
  transition: transform var(--transition-normal) var(--smooth-easing);
}

.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
