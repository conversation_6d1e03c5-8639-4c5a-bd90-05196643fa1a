export class SaveLoadManager {
    constructor(game) {
        this.game = game;
        this.maxSaveSlots = 10;
        this.savePrefix = 'dune2_save_';
        this.metadataKey = 'dune2_save_metadata';
    }
    
    saveGame(slotNumber = 0, description = '') {
        try {
            const gameState = this.serializeGameState();
            const saveData = {
                gameState: gameState,
                metadata: {
                    slotNumber: slotNumber,
                    description: description,
                    timestamp: Date.now(),
                    gameTime: this.game.gameTime,
                    difficulty: this.game.balanceManager?.difficulty || 'normal',
                    playerStats: this.game.teams.player.stats,
                    version: '1.0.0'
                }
            };
            
            // Compress the save data
            const compressedData = this.compressData(saveData);
            
            // Save to localStorage
            const saveKey = this.savePrefix + slotNumber;
            localStorage.setItem(saveKey, compressedData);
            
            // Update metadata
            this.updateSaveMetadata(saveData.metadata);
            
            this.game.ui.showNotification(`Game saved to slot ${slotNumber}`, 'success');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            this.game.ui.showNotification('Failed to save game', 'error');
            return false;
        }
    }
    
    loadGame(slotNumber = 0) {
        try {
            const saveKey = this.savePrefix + slotNumber;
            const compressedData = localStorage.getItem(saveKey);
            
            if (!compressedData) {
                this.game.ui.showNotification(`No save found in slot ${slotNumber}`, 'warning');
                return false;
            }
            
            // Decompress the save data
            const saveData = this.decompressData(compressedData);
            
            if (!saveData || !saveData.gameState) {
                this.game.ui.showNotification('Invalid save data', 'error');
                return false;
            }
            
            // Restore game state
            this.deserializeGameState(saveData.gameState);
            
            this.game.ui.showNotification(`Game loaded from slot ${slotNumber}`, 'success');
            return true;
        } catch (error) {
            console.error('Failed to load game:', error);
            this.game.ui.showNotification('Failed to load game', 'error');
            return false;
        }
    }
    
    serializeGameState() {
        return {
            // Core game state
            gameTime: this.game.gameTime,
            viewX: this.game.viewX,
            viewY: this.game.viewY,
            zoom: this.game.zoom,
            paused: this.game.paused,
            
            // Resources
            resources: this.game.resources,
            
            // Teams
            teams: this.serializeTeams(),
            
            // Entities
            buildings: this.serializeBuildings(),
            units: this.serializeUnits(),
            spiceFields: this.serializeSpiceFields(),
            terrain: this.game.terrain,
            
            // Systems state
            resourceManager: this.game.resourceManager.exportState(),
            technologySystem: this.game.technologySystem.exportState(),
            diplomacySystem: this.game.diplomacySystem?.exportState(),
            combatSystem: this.game.combatSystem.exportState(),
            aiController: this.game.aiController.exportState(),
            
            // UI state
            selectedUnits: this.game.selectedUnits.map(unit => unit.id),
            selectedBuilding: this.game.selectedBuilding?.id,
            controlGroups: this.serializeControlGroups(),
            productionQueues: this.serializeProductionQueues(),
            
            // Fog of war
            fogOfWarData: this.game.fogOfWarData,
            
            // Game settings
            difficulty: this.game.balanceManager?.difficulty,
            
            // Victory conditions
            victorySystem: this.game.victorySystem?.exportState()
        };
    }
    
    deserializeGameState(gameState) {
        // Restore core game state
        this.game.gameTime = gameState.gameTime || 0;
        this.game.viewX = gameState.viewX || 0;
        this.game.viewY = gameState.viewY || 0;
        this.game.zoom = gameState.zoom || 1;
        this.game.paused = gameState.paused || false;
        
        // Clear existing entities
        this.game.buildings = [];
        this.game.units = [];
        this.game.projectiles = [];
        this.game.selectedUnits = [];
        this.game.selectedBuilding = null;
        
        // Clear team arrays
        Object.values(this.game.teams).forEach(team => {
            team.buildings = [];
            team.units = [];
        });
        
        // Restore resources
        if (gameState.resources) {
            this.game.resources = { ...this.game.resources, ...gameState.resources };
        }
        
        // Restore teams
        if (gameState.teams) {
            this.deserializeTeams(gameState.teams);
        }
        
        // Restore terrain and spice fields
        if (gameState.terrain) {
            this.game.terrain = gameState.terrain;
        }
        if (gameState.spiceFields) {
            this.deserializeSpiceFields(gameState.spiceFields);
        }
        
        // Restore buildings
        if (gameState.buildings) {
            this.deserializeBuildings(gameState.buildings);
        }
        
        // Restore units
        if (gameState.units) {
            this.deserializeUnits(gameState.units);
        }
        
        // Restore systems state
        if (gameState.resourceManager) {
            this.game.resourceManager.importState(gameState.resourceManager);
        }
        if (gameState.technologySystem) {
            this.game.technologySystem.importState(gameState.technologySystem);
        }
        if (gameState.diplomacySystem && this.game.diplomacySystem) {
            this.game.diplomacySystem.importState(gameState.diplomacySystem);
        }
        if (gameState.combatSystem) {
            this.game.combatSystem.importState(gameState.combatSystem);
        }
        if (gameState.aiController) {
            this.game.aiController.importState(gameState.aiController);
        }
        if (gameState.victorySystem && this.game.victorySystem) {
            this.game.victorySystem.importState(gameState.victorySystem);
        }
        
        // Restore UI state
        if (gameState.controlGroups) {
            this.deserializeControlGroups(gameState.controlGroups);
        }
        if (gameState.productionQueues) {
            this.deserializeProductionQueues(gameState.productionQueues);
        }
        
        // Restore fog of war
        if (gameState.fogOfWarData) {
            this.game.fogOfWarData = gameState.fogOfWarData;
        }
        
        // Restore game settings
        if (gameState.difficulty && this.game.balanceManager) {
            this.game.balanceManager.setDifficulty(gameState.difficulty);
        }
        
        // Update game state
        this.game.updatePower();
        this.game.pathfinder.updateGridObstacles();
        this.game.updateFogOfWar();
        this.game.ui.updateResourceDisplay();
        this.game.ui.updateBuildPanel();
        this.game.ui.updateUnitInfoPanel();
        this.game.updateViewport();
    }
    
    serializeTeams() {
        const teams = {};
        Object.keys(this.game.teams).forEach(teamId => {
            const team = this.game.teams[teamId];
            teams[teamId] = {
                color: team.color,
                upgrades: team.upgrades,
                stats: team.stats,
                spice: team.spice || 0
            };
        });
        return teams;
    }
    
    deserializeTeams(teamsData) {
        Object.keys(teamsData).forEach(teamId => {
            if (this.game.teams[teamId]) {
                const teamData = teamsData[teamId];
                this.game.teams[teamId].color = teamData.color;
                this.game.teams[teamId].upgrades = teamData.upgrades || {};
                this.game.teams[teamId].stats = teamData.stats || {};
                this.game.teams[teamId].spice = teamData.spice || 0;
            }
        });
    }
    
    serializeBuildings() {
        return this.game.buildings.map(building => ({
            id: building.id,
            type: building.type,
            x: building.x,
            y: building.y,
            team: building.team,
            health: building.health,
            maxHealth: building.maxHealth,
            constructionProgress: building.constructionProgress,
            rallyPoint: building.rallyPoint
        }));
    }
    
    deserializeBuildings(buildingsData) {
        buildingsData.forEach(buildingData => {
            const building = this.game.addBuilding({
                type: buildingData.type,
                x: buildingData.x,
                y: buildingData.y,
                team: buildingData.team,
                instant: true
            });
            
            if (building) {
                building.id = buildingData.id;
                building.health = buildingData.health;
                building.maxHealth = buildingData.maxHealth;
                building.constructionProgress = buildingData.constructionProgress;
                building.rallyPoint = buildingData.rallyPoint;
            }
        });
    }
    
    serializeUnits() {
        return this.game.units.map(unit => ({
            id: unit.id,
            type: unit.type,
            x: unit.x,
            y: unit.y,
            team: unit.team,
            health: unit.health,
            maxHealth: unit.maxHealth,
            state: unit.state,
            targetX: unit.targetX,
            targetY: unit.targetY,
            rotation: unit.rotation,
            combatXP: unit.combatXP || 0,
            level: unit.level || 1
        }));
    }
    
    deserializeUnits(unitsData) {
        unitsData.forEach(unitData => {
            const unit = this.game.createUnit(
                unitData.type,
                unitData.x,
                unitData.y,
                unitData.team
            );
            
            if (unit) {
                unit.id = unitData.id;
                unit.health = unitData.health;
                unit.maxHealth = unitData.maxHealth;
                unit.state = unitData.state;
                unit.targetX = unitData.targetX;
                unit.targetY = unitData.targetY;
                unit.rotation = unitData.rotation;
                unit.combatXP = unitData.combatXP || 0;
                unit.level = unitData.level || 1;
            }
        });
    }
    
    serializeSpiceFields() {
        return this.game.spiceFields.map(field => ({
            x: field.x,
            y: field.y,
            radius: field.radius,
            amount: field.amount,
            maxAmount: field.maxAmount
        }));
    }
    
    deserializeSpiceFields(spiceFieldsData) {
        this.game.spiceFields = spiceFieldsData.map(fieldData => ({
            x: fieldData.x,
            y: fieldData.y,
            radius: fieldData.radius,
            amount: fieldData.amount,
            maxAmount: fieldData.maxAmount
        }));
    }
    
    serializeControlGroups() {
        const groups = {};
        Object.keys(this.game.controlGroups).forEach(groupNum => {
            groups[groupNum] = this.game.controlGroups[groupNum].map(unit => unit.id);
        });
        return groups;
    }
    
    deserializeControlGroups(groupsData) {
        this.game.controlGroups = {};
        Object.keys(groupsData).forEach(groupNum => {
            const unitIds = groupsData[groupNum];
            this.game.controlGroups[groupNum] = unitIds
                .map(id => this.game.units.find(unit => unit.id === id))
                .filter(unit => unit); // Remove null/undefined units
        });
    }
    
    serializeProductionQueues() {
        const queues = {};
        this.game.productionQueues.forEach((queue, buildingId) => {
            queues[buildingId] = queue;
        });
        return queues;
    }
    
    deserializeProductionQueues(queuesData) {
        this.game.productionQueues.clear();
        Object.keys(queuesData).forEach(buildingId => {
            this.game.productionQueues.set(buildingId, queuesData[buildingId]);
        });
    }
    
    compressData(data) {
        // Simple JSON stringification for now
        // In a real implementation, you might use LZ compression
        return JSON.stringify(data);
    }
    
    decompressData(compressedData) {
        try {
            return JSON.parse(compressedData);
        } catch (error) {
            console.error('Failed to decompress save data:', error);
            return null;
        }
    }
    
    updateSaveMetadata(metadata) {
        try {
            const existingMetadata = JSON.parse(localStorage.getItem(this.metadataKey) || '[]');
            
            // Remove existing metadata for this slot
            const filteredMetadata = existingMetadata.filter(meta => meta.slotNumber !== metadata.slotNumber);
            
            // Add new metadata
            filteredMetadata.push(metadata);
            
            // Sort by slot number
            filteredMetadata.sort((a, b) => a.slotNumber - b.slotNumber);
            
            localStorage.setItem(this.metadataKey, JSON.stringify(filteredMetadata));
        } catch (error) {
            console.error('Failed to update save metadata:', error);
        }
    }
    
    getSaveMetadata() {
        try {
            return JSON.parse(localStorage.getItem(this.metadataKey) || '[]');
        } catch (error) {
            console.error('Failed to get save metadata:', error);
            return [];
        }
    }
    
    deleteSave(slotNumber) {
        try {
            const saveKey = this.savePrefix + slotNumber;
            localStorage.removeItem(saveKey);
            
            // Update metadata
            const metadata = this.getSaveMetadata();
            const filteredMetadata = metadata.filter(meta => meta.slotNumber !== slotNumber);
            localStorage.setItem(this.metadataKey, JSON.stringify(filteredMetadata));
            
            this.game.ui.showNotification(`Save slot ${slotNumber} deleted`, 'info');
            return true;
        } catch (error) {
            console.error('Failed to delete save:', error);
            this.game.ui.showNotification('Failed to delete save', 'error');
            return false;
        }
    }
    
    exportSave(slotNumber) {
        try {
            const saveKey = this.savePrefix + slotNumber;
            const saveData = localStorage.getItem(saveKey);
            
            if (!saveData) {
                this.game.ui.showNotification(`No save found in slot ${slotNumber}`, 'warning');
                return null;
            }
            
            // Create downloadable file
            const blob = new Blob([saveData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `dune2_save_slot_${slotNumber}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.game.ui.showNotification('Save exported successfully', 'success');
            return true;
        } catch (error) {
            console.error('Failed to export save:', error);
            this.game.ui.showNotification('Failed to export save', 'error');
            return false;
        }
    }
    
    importSave(file, slotNumber) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const saveData = e.target.result;
                    const saveKey = this.savePrefix + slotNumber;
                    
                    // Validate the save data
                    const parsed = JSON.parse(saveData);
                    if (!parsed.gameState || !parsed.metadata) {
                        throw new Error('Invalid save file format');
                    }
                    
                    localStorage.setItem(saveKey, saveData);
                    this.updateSaveMetadata(parsed.metadata);
                    
                    this.game.ui.showNotification(`Save imported to slot ${slotNumber}`, 'success');
                    resolve(true);
                } catch (error) {
                    console.error('Failed to import save:', error);
                    this.game.ui.showNotification('Failed to import save', 'error');
                    reject(error);
                }
            };
            
            reader.onerror = () => {
                this.game.ui.showNotification('Failed to read save file', 'error');
                reject(new Error('Failed to read file'));
            };
            
            reader.readAsText(file);
        });
    }
}