import { Game } from './core/Game.js';

document.addEventListener('DOMContentLoaded', () => {
    try {
        const game = new Game();
        window.game = game; // Make accessible for HTML onclicks and debugging
        if (game.gameLoop && typeof game.gameLoop.setGameSpeed === 'function') {
            game.gameLoop.setGameSpeed(0.25); // Slow down game by 4x
        }
    } catch (error) {
        // Display error to user
        document.body.innerHTML = `
            <div style="padding: 20px; background: #ff4444; color: white; font-family: monospace;">
                <h2>Game Initialization Error</h2>
                <p><strong>Error:</strong> ${error.message}</p>
                <p><strong>Check browser console for details</strong></p>
            </div>
        `;
    }
});
