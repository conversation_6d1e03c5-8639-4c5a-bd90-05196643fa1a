export class GameBalanceManager {
    constructor(game) {
        this.game = game;
        this.difficulty = 'normal';
        this.balanceSettings = {
            unitCostMultipliers: {},
            buildingCostMultipliers: {},
            difficultyModifiers: {
                easy: {
                    playerDamageBonus: 1.2,
                    enemyDamageReduction: 0.8,
                    resourceMultiplier: 1.3,
                    buildSpeedBonus: 1.2
                },
                normal: {
                    playerDamageBonus: 1.0,
                    enemyDamageReduction: 1.0,
                    resourceMultiplier: 1.0,
                    buildSpeedBonus: 1.0
                },
                hard: {
                    playerDamageBonus: 0.8,
                    enemyDamageReduction: 1.2,
                    resourceMultiplier: 0.8,
                    buildSpeedBonus: 0.8
                },
                nightmare: {
                    playerDamageBonus: 0.6,
                    enemyDamageReduction: 1.5,
                    resourceMultiplier: 0.6,
                    buildSpeedBonus: 0.6
                }
            }
        };
        
        this.performanceMetrics = {
            averageGameLength: 0,
            playerWinRate: 0,
            unitUsageStats: {},
            buildingUsageStats: {}
        };
    }
    
    setDifficulty(difficulty) {
        this.difficulty = difficulty;
        const modifiers = this.balanceSettings.difficultyModifiers[difficulty];
        
        if (modifiers) {
            // Apply difficulty modifiers to AI
            this.game.aiController.applyDifficultyModifiers(difficulty);
            
            // Apply modifiers to combat system
            this.game.combatSystem.applyDifficultyModifiers(difficulty);
            
            // Apply modifiers to resource system
            this.game.resourceManager.setDifficultyMultiplier(modifiers.resourceMultiplier);
            
            // Apply modifiers to technology system
            this.game.technologySystem.setBuildSpeedMultiplier(modifiers.buildSpeedBonus);
            
            this.game.ui.showNotification(`Difficulty set to ${difficulty}`, 'info');
        }
    }
    
    adjustUnitCost(unitType, multiplier) {
        this.balanceSettings.unitCostMultipliers[unitType] = multiplier;
        
        // Update unit data
        const unitData = this.game.unitTypes[unitType];
        if (unitData) {
            unitData.cost = Math.floor(unitData.baseCost * multiplier);
        }
    }
    
    adjustBuildingCost(buildingType, multiplier) {
        this.balanceSettings.buildingCostMultipliers[buildingType] = multiplier;
        
        // Update building data
        const buildingData = this.game.buildingTypes[buildingType];
        if (buildingData) {
            buildingData.cost = Math.floor(buildingData.baseCost * multiplier);
        }
    }
    
    validateBalance() {
        const report = {
            difficulty: this.difficulty,
            issues: [],
            recommendations: [],
            metrics: this.performanceMetrics
        };
        
        // Check unit cost balance
        Object.keys(this.game.unitTypes).forEach(unitType => {
            const unit = this.game.unitTypes[unitType];
            const costEfficiency = unit.damage / unit.cost;
            
            if (costEfficiency > 0.5) {
                report.issues.push(`${unitType} may be overpowered (cost efficiency: ${costEfficiency.toFixed(2)})`);
                report.recommendations.push(`Consider increasing ${unitType} cost or reducing damage`);
            } else if (costEfficiency < 0.1) {
                report.issues.push(`${unitType} may be underpowered (cost efficiency: ${costEfficiency.toFixed(2)})`);
                report.recommendations.push(`Consider decreasing ${unitType} cost or increasing damage`);
            }
        });
        
        // Check AI performance
        const aiPerformance = this.game.aiController.getPerformanceRating();
        if (aiPerformance.powerRatio < 0.5) {
            report.issues.push('AI is significantly weaker than player');
            report.recommendations.push('Consider increasing AI difficulty or resource bonuses');
        } else if (aiPerformance.powerRatio > 2.0) {
            report.issues.push('AI is significantly stronger than player');
            report.recommendations.push('Consider decreasing AI difficulty or resource bonuses');
        }
        
        // Check combat effectiveness
        const combatRating = this.game.combatSystem.getCombatEffectivenessRating();
        if (combatRating < 0.8 || combatRating > 1.2) {
            report.issues.push(`Combat effectiveness imbalanced: ${combatRating.toFixed(2)}`);
            report.recommendations.push('Review damage type effectiveness matrix');
        }
        
        return report;
    }
    
    updatePerformanceMetrics(gameData) {
        // Update metrics based on completed game
        if (gameData.gameLength) {
            this.performanceMetrics.averageGameLength = 
                (this.performanceMetrics.averageGameLength + gameData.gameLength) / 2;
        }
        
        if (gameData.playerWon !== undefined) {
            this.performanceMetrics.playerWinRate = 
                (this.performanceMetrics.playerWinRate + (gameData.playerWon ? 1 : 0)) / 2;
        }
        
        // Update unit usage statistics
        if (gameData.unitStats) {
            Object.keys(gameData.unitStats).forEach(unitType => {
                if (!this.performanceMetrics.unitUsageStats[unitType]) {
                    this.performanceMetrics.unitUsageStats[unitType] = 0;
                }
                this.performanceMetrics.unitUsageStats[unitType] += gameData.unitStats[unitType];
            });
        }
    }
    
    getBalanceRecommendations() {
        const recommendations = [];
        
        // Analyze unit usage
        const totalUnitUsage = Object.values(this.performanceMetrics.unitUsageStats)
            .reduce((sum, usage) => sum + usage, 0);
        
        Object.keys(this.performanceMetrics.unitUsageStats).forEach(unitType => {
            const usage = this.performanceMetrics.unitUsageStats[unitType];
            const usagePercentage = usage / totalUnitUsage;
            
            if (usagePercentage < 0.05) {
                recommendations.push({
                    type: 'unit_buff',
                    target: unitType,
                    reason: 'Low usage rate',
                    suggestion: 'Consider buffing or reducing cost'
                });
            } else if (usagePercentage > 0.4) {
                recommendations.push({
                    type: 'unit_nerf',
                    target: unitType,
                    reason: 'Overused',
                    suggestion: 'Consider nerfing or increasing cost'
                });
            }
        });
        
        // Analyze win rate
        if (this.performanceMetrics.playerWinRate < 0.3) {
            recommendations.push({
                type: 'difficulty_reduction',
                reason: 'Low player win rate',
                suggestion: 'Consider reducing AI difficulty or buffing player units'
            });
        } else if (this.performanceMetrics.playerWinRate > 0.8) {
            recommendations.push({
                type: 'difficulty_increase',
                reason: 'High player win rate',
                suggestion: 'Consider increasing AI difficulty or nerfing player units'
            });
        }
        
        return recommendations;
    }
    
    applyAutoBalance() {
        const recommendations = this.getBalanceRecommendations();
        let changesApplied = 0;
        
        recommendations.forEach(rec => {
            switch (rec.type) {
                case 'unit_buff':
                    this.adjustUnitCost(rec.target, 0.9); // 10% cost reduction
                    changesApplied++;
                    break;
                case 'unit_nerf':
                    this.adjustUnitCost(rec.target, 1.1); // 10% cost increase
                    changesApplied++;
                    break;
                case 'difficulty_reduction':
                    if (this.difficulty === 'hard') this.setDifficulty('normal');
                    else if (this.difficulty === 'normal') this.setDifficulty('easy');
                    changesApplied++;
                    break;
                case 'difficulty_increase':
                    if (this.difficulty === 'easy') this.setDifficulty('normal');
                    else if (this.difficulty === 'normal') this.setDifficulty('hard');
                    changesApplied++;
                    break;
            }
        });
        
        if (changesApplied > 0) {
            this.game.ui.showNotification(
                `Auto-balance applied ${changesApplied} changes`, 
                'info'
            );
        }
        
        return changesApplied;
    }
    
    exportBalanceData() {
        return {
            difficulty: this.difficulty,
            balanceSettings: this.balanceSettings,
            performanceMetrics: this.performanceMetrics,
            timestamp: Date.now()
        };
    }
    
    importBalanceData(data) {
        if (data.difficulty) this.difficulty = data.difficulty;
        if (data.balanceSettings) this.balanceSettings = { ...this.balanceSettings, ...data.balanceSettings };
        if (data.performanceMetrics) this.performanceMetrics = { ...this.performanceMetrics, ...data.performanceMetrics };
    }
}