export class Entity {
    constructor(game, config, entityData) {
        this.game = game;
        this.id = config.id || `e_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        this.x = config.x;
        this.y = config.y;
        this.team = config.team;
        this.type = config.type;
        this.lastAttacker = null; // Initialize lastAttacker

        this.health = entityData.health;
        this.maxHealth = entityData.health;
        
        if (this.team === 'enemy') { // Apply "easier mode" nerfs for enemies
            this.health = Math.floor(this.health * (this.isBuilding ? 0.6 : 0.5));
            this.maxHealth = this.health;
            if (entityData.damage) this.damage = Math.floor(entityData.damage * (this.isBuilding ? 0.6 : 0.5));
            if (entityData.fireRate) this.fireRate = (entityData.fireRate || 1) * (this.isBuilding ? 0.8 : 0.7);
        } else if (this.team === 'player' && !this.isBuilding) { // Player unit upgrades
            const upgrades = this.game.teams.player.upgrades;
            this.maxHealth += (upgrades.armor || 0) * 10;
            this.health = this.maxHealth;
            if (this.damage) this.damage += (upgrades.damage || 0) * 5;
            if (this.speed) this.speed *= (1 + (upgrades.speed || 0) * 0.1);
            if (this.type === 'harvester' && this.maxCarry) this.maxCarry += (upgrades.harvesting || 0) * 50;
        }


        // No longer using SVG elements directly on Entity
    }

    get isBuilding() {
        return this.game.buildingTypes[this.type] !== undefined;
    }
    
    get isUnit() {
        return this.game.unitTypes[this.type] !== undefined;
    }

    updateHealthBar(ctx) {
        const healthPercent = this.health / this.maxHealth;
        if (healthPercent < 1 && this.health > 0) {
            const barWidth = (this.width || this.size * 2); // Use width/size from entity data
            const barHeight = 4;
            const barX = this.x - barWidth / 2;
            const barY = this.y - (this.height || this.size * 2) / 2 - 10; // Position above entity

            // Background
            ctx.fillStyle = '#333';
            ctx.fillRect(barX, barY, barWidth, barHeight);

            // Health
            ctx.fillStyle = healthPercent > 0.6 ? '#0f0' : healthPercent > 0.3 ? '#ff0' : '#f00';
            ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
        }
    }

    destroy() {
        // No SVG element to remove. Cleanup from game arrays is handled in Game.js
    }

    // To be implemented by subclasses
    update(deltaTime) {}
    draw(ctx) {
        // This method will be implemented by subclasses (Unit, Building, Projectile)
        // It will contain the Canvas drawing logic for the entity.
    }
}
