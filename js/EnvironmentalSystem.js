export class EnvironmentalSystem {
    constructor(game) {
        this.game = game;
        
        // Time and weather state
        this.gameTime = 0; // Time in game minutes
        this.timeScale = 0.1; // 1 real second = 0.1 game minutes (much slower)
        this.dayLength = 600; // 600 game minutes = 1 day (10 real minutes per day)
        
        // Weather system
        this.currentWeather = 'clear';
        this.weatherDuration = 0;
        this.weatherTransition = 0;
        this.nextWeatherChange = this.getRandomWeatherDuration();
        
        // Weather types and their effects
        this.weatherTypes = {
            clear: {
                name: 'Clear Weather',
                visibilityModifier: 1.0,
                movementModifier: 1.0,
                damageModifier: 1.0,
                color: 'rgba(255, 255, 200, 0.1)',
                description: 'Perfect conditions for combat'
            },
            sandstorm: {
                name: 'Sandstorm',
                visibilityModifier: 0.6,
                movementModifier: 0.8,
                damageModifier: 0.9,
                color: 'rgba(194, 154, 108, 0.4)',
                description: 'Reduced visibility and movement speed',
                particles: true
            },
            spice_storm: {
                name: 'Spice Storm',
                visibilityModifier: 0.4,
                movementModifier: 0.7,
                damageModifier: 0.8,
                color: 'rgba(255, 140, 0, 0.5)',
                description: 'Severe weather with electrical interference',
                particles: true,
                electrical: true
            },
            heat_wave: {
                name: 'Heat Wave',
                visibilityModifier: 0.9,
                movementModifier: 0.9,
                damageModifier: 1.1,
                color: 'rgba(255, 100, 100, 0.2)',
                description: 'Extreme heat affects unit performance'
            }
        };
        
        // Day/night cycle effects with smooth transition ranges
        this.dayNightEffects = {
            dawn: { visibility: 0.8, damage: 0.95, name: 'Dawn', baseAlpha: 0.1 },
            day: { visibility: 1.0, damage: 1.0, name: 'Day', baseAlpha: 0.0 },
            dusk: { visibility: 0.7, damage: 0.9, name: 'Dusk', baseAlpha: 0.2 },
            night: { visibility: 0.5, damage: 0.85, name: 'Night', baseAlpha: 0.4 }
        };
        
        // Smooth transition parameters
        this.transitionDuration = 0.1; // 10% of day length for transitions
        
        // Environmental hazards
        this.hazards = [];
        this.hazardTypes = {
            spice_geyser: {
                name: 'Spice Geyser',
                duration: 30, // seconds
                radius: 80,
                damage: 5,
                interval: 2, // damage every 2 seconds
                color: 'rgba(255, 165, 0, 0.6)'
            },
            sinkhole: {
                name: 'Sinkhole',
                duration: 60,
                radius: 50,
                damage: 10,
                interval: 1,
                color: 'rgba(139, 69, 19, 0.7)',
                movement_trap: true
            },
            electrical_storm: {
                name: 'Electrical Storm',
                duration: 45,
                radius: 120,
                damage: 8,
                interval: 3,
                color: 'rgba(100, 100, 255, 0.5)',
                disables_electronics: true
            }
        };
        
        this.lastHazardSpawn = 0;
        this.hazardSpawnInterval = 120; // Spawn hazard every 2 minutes
        
        // Particle systems for weather effects
        this.particles = [];
        this.maxParticles = 100;
    }
    
    // Update environmental systems
    update(deltaTime) {
        this.gameTime += deltaTime * this.timeScale;
        this.weatherDuration += deltaTime;
        
        // Update weather
        this.updateWeather(deltaTime);
        
        // Update hazards
        this.updateHazards(deltaTime);
        
        // Update particles
        this.updateParticles(deltaTime);
        
        // Spawn new hazards occasionally
        if (this.gameTime - this.lastHazardSpawn > this.hazardSpawnInterval) {
            this.spawnRandomHazard();
            this.lastHazardSpawn = this.gameTime;
        }
    }
    
    // Weather system
    updateWeather(deltaTime) {
        if (this.weatherDuration >= this.nextWeatherChange) {
            this.changeWeather();
        }
        
        // Update weather particles
        const currentWeatherData = this.weatherTypes[this.currentWeather];
        if (currentWeatherData.particles) {
            this.spawnWeatherParticles();
        }
    }
    
    changeWeather() {
        const weatherOptions = Object.keys(this.weatherTypes);
        let newWeather;
        
        // Weighted random selection (clear weather more common)
        const rand = Math.random();
        if (rand < 0.5) {
            newWeather = 'clear';
        } else if (rand < 0.75) {
            newWeather = 'sandstorm';
        } else if (rand < 0.9) {
            newWeather = 'heat_wave';
        } else {
            newWeather = 'spice_storm';
        }
        
        if (newWeather !== this.currentWeather) {
            this.currentWeather = newWeather;
            this.weatherDuration = 0;
            this.nextWeatherChange = this.getRandomWeatherDuration();
            
            const weatherData = this.weatherTypes[newWeather];
            this.game.ui.showNotification(
                `Weather changed: ${weatherData.name}`, 
                'info'
            );
        }
    }
    
    getRandomWeatherDuration() {
        return 60 + Math.random() * 120; // 1-3 minutes
    }
    
    // Get current time of day with smooth transitions
    getTimeOfDay() {
        const dayProgress = (this.gameTime % this.dayLength) / this.dayLength;
        
        // Define time periods with smoother boundaries
        if (dayProgress < 0.2) return 'night';
        if (dayProgress < 0.35) return 'dawn';
        if (dayProgress < 0.65) return 'day';
        if (dayProgress < 0.8) return 'dusk';
        return 'night';
    }
    
    // Get smooth day/night lighting values with interpolation
    getDayNightLighting() {
        const dayProgress = (this.gameTime % this.dayLength) / this.dayLength;
        
        // Define key lighting points throughout the day
        const lightingCurve = [
            { progress: 0.0, alpha: 0.4, visibility: 0.5, damage: 0.85 }, // Midnight
            { progress: 0.2, alpha: 0.4, visibility: 0.5, damage: 0.85 }, // Late night
            { progress: 0.25, alpha: 0.3, visibility: 0.6, damage: 0.9 }, // Pre-dawn
            { progress: 0.35, alpha: 0.1, visibility: 0.8, damage: 0.95 }, // Dawn
            { progress: 0.5, alpha: 0.0, visibility: 1.0, damage: 1.0 }, // Noon
            { progress: 0.65, alpha: 0.0, visibility: 1.0, damage: 1.0 }, // Afternoon
            { progress: 0.75, alpha: 0.15, visibility: 0.8, damage: 0.95 }, // Dusk
            { progress: 0.8, alpha: 0.25, visibility: 0.7, damage: 0.9 }, // Early evening
            { progress: 0.9, alpha: 0.35, visibility: 0.6, damage: 0.87 }, // Evening
            { progress: 1.0, alpha: 0.4, visibility: 0.5, damage: 0.85 }  // Midnight
        ];
        
        // Find the two points to interpolate between
        let point1 = lightingCurve[0];
        let point2 = lightingCurve[1];
        
        for (let i = 0; i < lightingCurve.length - 1; i++) {
            if (dayProgress >= lightingCurve[i].progress && dayProgress <= lightingCurve[i + 1].progress) {
                point1 = lightingCurve[i];
                point2 = lightingCurve[i + 1];
                break;
            }
        }
        
        // Calculate interpolation factor
        const range = point2.progress - point1.progress;
        const localProgress = range > 0 ? (dayProgress - point1.progress) / range : 0;
        
        // Apply smooth easing function for more natural transitions
        const easedProgress = this.smoothStep(localProgress);
        
        // Interpolate values
        return {
            alpha: this.lerp(point1.alpha, point2.alpha, easedProgress),
            visibility: this.lerp(point1.visibility, point2.visibility, easedProgress),
            damage: this.lerp(point1.damage, point2.damage, easedProgress)
        };
    }
    
    // Linear interpolation
    lerp(a, b, t) {
        return a + (b - a) * t;
    }
    
    // Smooth step function for natural easing
    smoothStep(t) {
        return t * t * (3 - 2 * t);
    }
    
    // Get combined environmental modifiers with smooth day/night transitions
    getEnvironmentalModifiers() {
        const weather = this.weatherTypes[this.currentWeather];
        const lighting = this.getDayNightLighting();
        
        return {
            visibility: weather.visibilityModifier * lighting.visibility,
            movement: weather.movementModifier,
            damage: weather.damageModifier * lighting.damage,
            weather: this.currentWeather,
            timeOfDay: this.getTimeOfDay(),
            lightingAlpha: lighting.alpha
        };
    }
    
    // Environmental hazards
    updateHazards(deltaTime) {
        for (let i = this.hazards.length - 1; i >= 0; i--) {
            const hazard = this.hazards[i];
            hazard.duration -= deltaTime;
            hazard.timeSinceLastDamage += deltaTime;
            
            // Apply hazard effects
            if (hazard.timeSinceLastDamage >= hazard.interval) {
                this.applyHazardEffects(hazard);
                hazard.timeSinceLastDamage = 0;
            }
            
            // Remove expired hazards
            if (hazard.duration <= 0) {
                this.hazards.splice(i, 1);
            }
        }
    }
    
    spawnRandomHazard() {
        const hazardTypeNames = Object.keys(this.hazardTypes);
        const hazardType = hazardTypeNames[Math.floor(Math.random() * hazardTypeNames.length)];
        
        // Find a valid spawn location
        let attempts = 0;
        let x, y;
        
        do {
            x = Math.random() * this.game.width;
            y = Math.random() * this.game.height;
            attempts++;
        } while (attempts < 20 && this.isNearPlayerBase(x, y, 300));
        
        if (attempts < 20) {
            this.spawnHazard(hazardType, x, y);
        }
    }
    
    spawnHazard(type, x, y) {
        const hazardData = this.hazardTypes[type];
        
        const hazard = {
            type: type,
            x: x,
            y: y,
            radius: hazardData.radius,
            damage: hazardData.damage,
            interval: hazardData.interval,
            duration: hazardData.duration,
            timeSinceLastDamage: 0,
            color: hazardData.color,
            ...hazardData
        };
        
        this.hazards.push(hazard);
        
        this.game.ui.showNotification(
            `${hazardData.name} detected at coordinates (${Math.floor(x)}, ${Math.floor(y)})!`,
            'warning'
        );
    }
    
    applyHazardEffects(hazard) {
        // Find units in hazard area
        [...this.game.units, ...this.game.neutralEnemies.neutralUnits].forEach(unit => {
            const distSq = (unit.x - hazard.x) ** 2 + (unit.y - hazard.y) ** 2;
            if (distSq < hazard.radius ** 2) {
                // Apply damage
                unit.health -= hazard.damage;
                unit.lastAttacker = null; // Environmental damage
                
                // Special effects
                if (hazard.movement_trap) {
                    // Slow down units in sinkhole
                    unit.speed *= 0.5;
                    setTimeout(() => {
                        if (unit.health > 0) {
                            unit.speed /= 0.5; // Restore speed
                        }
                    }, 1000);
                }
                
                if (hazard.disables_electronics && unit.category === 'vehicle') {
                    // Disable vehicle temporarily
                    unit.disabled = true;
                    setTimeout(() => {
                        if (unit.health > 0) {
                            unit.disabled = false;
                        }
                    }, 5000);
                }
                
                // Visual effect
                this.game.createHitEffect(unit.x, unit.y);
            }
        });
        
        // Create visual effect at hazard center
        this.game.visualEffects.createEnvironmentalEffect(hazard.x, hazard.y, hazard.type);
    }
    
    isNearPlayerBase(x, y, radius) {
        return this.game.teams.player.buildings.some(building => {
            if (building.type === 'base') {
                const distSq = (building.x - x) ** 2 + (building.y - y) ** 2;
                return distSq < radius ** 2;
            }
            return false;
        });
    }
    
    // Particle system for weather effects
    spawnWeatherParticles() {
        if (this.particles.length >= this.maxParticles) return;
        
        const viewX = this.game.viewX;
        const viewY = this.game.viewY;
        const viewWidth = this.game.gameCanvas.width / this.game.zoom;
        const viewHeight = this.game.gameCanvas.height / this.game.zoom;
        
        for (let i = 0; i < 3; i++) {
            if (this.particles.length >= this.maxParticles) break;
            
            const particle = {
                x: viewX + Math.random() * viewWidth,
                y: viewY - 50,
                vx: (Math.random() - 0.5) * 20,
                vy: 50 + Math.random() * 30,
                life: 3 + Math.random() * 2,
                maxLife: 5,
                size: 1 + Math.random() * 3,
                type: this.currentWeather
            };
            
            this.particles.push(particle);
        }
    }
    
    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime;
            
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }
    
    // Rendering
    draw(ctx) {
        // Draw environmental overlay
        this.drawEnvironmentalOverlay(ctx);
        
        // Draw hazards
        this.drawHazards(ctx);
        
        // Draw weather particles
        this.drawWeatherParticles(ctx);
    }
    
    drawEnvironmentalOverlay(ctx) {
        const weather = this.weatherTypes[this.currentWeather];
        const lighting = this.getDayNightLighting();
        
        // Weather overlay
        if (weather.color) {
            ctx.fillStyle = weather.color;
            ctx.fillRect(0, 0, this.game.width, this.game.height);
        }
        
        // Smooth day/night overlay with gradual color transitions
        if (lighting.alpha > 0) {
            // Create a more natural night color that varies based on time
            const dayProgress = (this.gameTime % this.dayLength) / this.dayLength;
            
            // Vary the night color slightly based on time for more realism
            let nightR = 0;
            let nightG = 0;
            let nightB = 50;
            
            // Add subtle color variations during different night phases
            if (dayProgress > 0.8 || dayProgress < 0.2) {
                // Deep night - darker blue
                nightB = 40;
            } else if (dayProgress >= 0.2 && dayProgress < 0.35) {
                // Dawn - warmer tones
                nightR = 20;
                nightG = 10;
                nightB = 60;
            } else if (dayProgress >= 0.65 && dayProgress < 0.8) {
                // Dusk - warmer orange tones
                nightR = 30;
                nightG = 15;
                nightB = 45;
            }
            
            ctx.fillStyle = `rgba(${nightR}, ${nightG}, ${nightB}, ${lighting.alpha})`;
            ctx.fillRect(0, 0, this.game.width, this.game.height);
        }
    }
    
    drawHazards(ctx) {
        this.hazards.forEach(hazard => {
            // Pulsing effect
            const pulse = 0.8 + 0.2 * Math.sin(this.gameTime * 3);
            
            ctx.save();
            ctx.globalAlpha = 0.6 * pulse;
            ctx.fillStyle = hazard.color;
            ctx.beginPath();
            ctx.arc(hazard.x, hazard.y, hazard.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Warning border
            ctx.globalAlpha = 1;
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.setLineDash([]);
            
            ctx.restore();
        });
    }
    
    drawWeatherParticles(ctx) {
        this.particles.forEach(particle => {
            const alpha = particle.life / particle.maxLife;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            if (particle.type === 'sandstorm') {
                ctx.fillStyle = '#C19A6B';
            } else if (particle.type === 'spice_storm') {
                ctx.fillStyle = '#FF8C00';
            }
            
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }
    
    // Get environmental info for UI
    getEnvironmentalInfo() {
        const modifiers = this.getEnvironmentalModifiers();
        const weather = this.weatherTypes[this.currentWeather];
        
        return {
            weather: weather.name,
            timeOfDay: this.dayNightEffects[modifiers.timeOfDay].name,
            modifiers: modifiers,
            hazards: this.hazards.length,
            gameTime: this.gameTime
        };
    }
}