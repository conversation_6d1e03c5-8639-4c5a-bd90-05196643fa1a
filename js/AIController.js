import { GameData } from './GameData.js';

export class AIController {
    constructor(game, teamId = 'enemy') {
        this.game = game;
        this.teamId = teamId;
        this.team = game.gameState.teams[teamId];
        this.team.spice = 500; // Initial AI spice
        this.buildOrder = ['power_plant', 'barracks', 'refinery', 'factory', 'gun_turret']; // Simplified
        this.currentBuildIndex = 0;
        
        // Enhanced AI properties
        this.aggressionLevel = 1.0;
        this.personality = 'balanced'; // balanced, aggressive, defensive, economic, technological
        this.researchPriorities = ['military', 'economic', 'defensive'];
        this.diplomaticStance = 'neutral';
        
        // Strategic AI state
        this.strategicGoals = new Set(['expand', 'research', 'military']);
        this.currentStrategy = 'early_game';
        this.lastStrategyUpdate = 0;
        
        // Technology research queue
        this.researchQueue = [];
        this.currentResearch = null;
        this.researchCooldown = 0;
        
        // Diplomatic memory
        this.diplomaticMemory = {
            lastPlayerAction: null,
            relationTrend: 0,
            trustLevel: 0.5
        };
        
        // Resource management
        this.resourceTargets = {
            spice: 2000,
            water: 500,
            techPoints: 200
        };
        
        // Unit composition preferences
        this.unitComposition = {
            infantry: 0.4,
            vehicles: 0.4,
            support: 0.2
        };
        
        // Advanced AI integration flag
        this.isAdvancedAI = false;
        this.advancedAIController = null;
        
        this.initializeAIPersonality();
    }
    
    initializeAIPersonality() {
        // Set personality-based preferences
        switch (this.personality) {
            case 'aggressive':
                this.aggressionLevel = 1.5;
                this.researchPriorities = ['military', 'advanced', 'defensive'];
                this.unitComposition = { infantry: 0.3, vehicles: 0.6, support: 0.1 };
                this.strategicGoals = new Set(['military', 'expand', 'dominate']);
                break;
            case 'defensive':
                this.aggressionLevel = 0.7;
                this.researchPriorities = ['defensive', 'military', 'economic'];
                this.unitComposition = { infantry: 0.5, vehicles: 0.3, support: 0.2 };
                this.strategicGoals = new Set(['defend', 'research', 'fortify']);
                break;
            case 'economic':
                this.aggressionLevel = 0.8;
                this.researchPriorities = ['economic', 'advanced', 'military'];
                this.resourceTargets = { spice: 5000, water: 1000, techPoints: 500 };
                this.strategicGoals = new Set(['expand', 'economy', 'research']);
                break;
            case 'technological':
                this.aggressionLevel = 0.9;
                this.researchPriorities = ['advanced', 'strategic', 'military'];
                this.resourceTargets = { spice: 3000, water: 800, techPoints: 1000 };
                this.strategicGoals = new Set(['research', 'technology', 'advance']);
                break;
        }
    }

    // Advanced AI integration methods
    upgradeToAdvancedAI(advancedController) {
        this.isAdvancedAI = true;
        this.advancedAIController = advancedController;
        
        // Transfer basic AI state to advanced AI
        if (advancedController) {
            advancedController.personalityTraits = this.convertPersonalityToTraits();
            advancedController.team = this.team;
            advancedController.teamId = this.teamId;
            advancedController.game = this.game;
        }
        
    }
    
    convertPersonalityToTraits() {
        // Convert basic personality to advanced AI traits
        const baseTraits = {
            aggression: 0.5,
            cunning: 0.5,
            opportunism: 0.5,
            adaptability: 0.5,
            patience: 0.5
        };
        
        switch (this.personality) {
            case 'aggressive':
                baseTraits.aggression = 0.8;
                baseTraits.opportunism = 0.7;
                baseTraits.patience = 0.3;
                break;
            case 'defensive':
                baseTraits.aggression = 0.3;
                baseTraits.patience = 0.8;
                baseTraits.cunning = 0.6;
                break;
            case 'economic':
                baseTraits.opportunism = 0.8;
                baseTraits.patience = 0.7;
                baseTraits.adaptability = 0.6;
                break;
            case 'technological':
                baseTraits.cunning = 0.8;
                baseTraits.adaptability = 0.7;
                baseTraits.patience = 0.6;
                break;
            default: // balanced
                // Keep default values
                break;
        }
        
        return baseTraits;
    }

    update(deltaTime) {
        console.log(`🎮 AI DEBUG: Basic AIController.update called for team ${this.teamId}`, {
            isAdvancedAI: this.isAdvancedAI,
            hasAdvancedController: !!this.advancedAIController,
            deltaTime: deltaTime,
            teamSpice: this.team.spice,
            teamUnits: this.team.units.length,
            teamBuildings: this.team.buildings.length
        });
        
        // If advanced AI is active, delegate to it
        if (this.isAdvancedAI && this.advancedAIController) {
            console.log(`🎮 AI DEBUG: Delegating to advanced AI controller for team ${this.teamId}`);
            return this.advancedAIController.update(deltaTime);
        }
        
        // Otherwise use basic AI logic
        console.log(`🎮 AI DEBUG: Using basic AI logic for team ${this.teamId}`);
        this.team.spice += 2 * deltaTime; // Slower passive income

        // Update strategic state
        this.updateStrategy(deltaTime);
        
        // Make decisions less frequently but more intelligently
        if (Math.random() < 0.02 * deltaTime) { // Decision check rate
            this.decideBuild();
        }
        if (Math.random() < 0.05 * deltaTime) {
            this.decideUnitProduction();
        }
        if (Math.random() < 0.01 * deltaTime) {
            this.decideResearch();
        }
        if (Math.random() < 0.008 * deltaTime) {
            this.decideDiplomacy();
        }
        
        this.controlUnits(deltaTime);
        this.manageResources(deltaTime);
    }
    
    updateStrategy(deltaTime) {
        const now = this.game.gameTime;
        
        // Update strategy every 2 minutes
        if (now - this.lastStrategyUpdate >= 120) {
            this.lastStrategyUpdate = now;
            this.evaluateStrategicSituation();
        }
        
        // Update research cooldown
        if (this.researchCooldown > 0) {
            this.researchCooldown -= deltaTime;
        }
    }
    
    evaluateStrategicSituation() {
        const gameTime = this.game.gameTime;
        const playerPower = this.calculatePlayerPower();
        const aiPower = this.calculateAIPower();
        
        // Determine current game phase
        if (gameTime < 300) { // First 5 minutes
            this.currentStrategy = 'early_game';
        } else if (gameTime < 900) { // 5-15 minutes
            this.currentStrategy = 'mid_game';
        } else {
            this.currentStrategy = 'late_game';
        }
        
        // Adjust strategy based on relative power
        const powerRatio = aiPower / Math.max(playerPower, 0.1);
        
        if (powerRatio < 0.7) {
            // AI is behind, focus on economy and defense
            this.strategicGoals = new Set(['economy', 'defend', 'research']);
            this.aggressionLevel *= 0.8;
        } else if (powerRatio > 1.3) {
            // AI is ahead, be more aggressive
            this.strategicGoals = new Set(['military', 'expand', 'dominate']);
            this.aggressionLevel *= 1.2;
        } else {
            // Balanced situation, maintain current strategy
            this.initializeAIPersonality(); // Reset to base personality
        }
    }
    
    calculatePlayerPower() {
        const playerUnits = this.game.gameState.teams.player.units.length;
        const playerBuildings = this.game.gameState.teams.player.buildings.length;
        const playerResources = this.game.resourceManager.getResource('spice');
        const playerTech = this.game.technologySystem.researchedTechnologies.size;
        
        return (playerUnits * 10) + (playerBuildings * 50) + (playerResources / 100) + (playerTech * 100);
    }
    
    calculateAIPower() {
        const aiUnits = this.team.units.length;
        const aiBuildings = this.team.buildings.length;
        const aiResources = this.team.spice;
        
        return (aiUnits * 10) + (aiBuildings * 50) + (aiResources / 100);
    }
    
    decideResearch() {
        if (!this.game.technologySystem || this.researchCooldown > 0) return;
        
        // Check if AI has research lab
        const researchLab = this.team.buildings.find(b =>
            b.type === 'research_lab' && b.constructionProgress >= 100
        );
        
        if (!researchLab) {
            // Try to build research lab if we have the resources
            if (this.team.spice >= 800) {
                this.tryBuildBuilding('research_lab');
            }
            return;
        }
        
        // Get available technologies
        const availableTech = this.getAvailableTechnologies();
        if (availableTech.length === 0) return;
        
        // Select technology based on AI priorities
        const selectedTech = this.selectTechnologyToResearch(availableTech);
        
        if (selectedTech && this.canAffordTechnology(selectedTech)) {
            this.startResearch(selectedTech);
            this.researchCooldown = 30; // 30 second cooldown between research decisions
        }
    }
    
    getAvailableTechnologies() {
        const allTech = Object.keys(this.game.technologySystem.technologies);
        return allTech.filter(techId => {
            const tech = this.game.technologySystem.technologies[techId];
            
            // Check if already researched
            if (this.game.technologySystem.researchedTechnologies.has(techId)) return false;
            
            // Check prerequisites (simplified for AI)
            if (tech.prerequisites && tech.prerequisites.length > 0) {
                return tech.prerequisites.every(prereq =>
                    this.game.technologySystem.researchedTechnologies.has(prereq)
                );
            }
            
            return true;
        });
    }
    
    selectTechnologyToResearch(availableTech) {
        // Score technologies based on AI priorities
        const scoredTech = availableTech.map(techId => {
            const tech = this.game.technologySystem.technologies[techId];
            let score = 0;
            
            // Base score by tier (prefer lower tier first)
            score += (5 - tech.tier) * 20;
            
            // Category preference scoring
            const categoryIndex = this.researchPriorities.indexOf(tech.category);
            if (categoryIndex !== -1) {
                score += (3 - categoryIndex) * 30;
            }
            
            // Strategic goal alignment
            if (this.strategicGoals.has('military') && tech.category === 'military') score += 40;
            if (this.strategicGoals.has('economy') && tech.category === 'economic') score += 40;
            if (this.strategicGoals.has('research') && tech.category === 'advanced') score += 40;
            if (this.strategicGoals.has('defend') && tech.category === 'defensive') score += 40;
            
            // Affordability factor
            const cost = this.calculateTechCost(tech);
            if (cost <= this.team.spice * 0.5) score += 20; // Can easily afford
            else if (cost <= this.team.spice) score += 10; // Can afford
            else score -= 30; // Too expensive
            
            return { techId, score, tech };
        });
        
        // Sort by score and return the best option
        scoredTech.sort((a, b) => b.score - a.score);
        return scoredTech.length > 0 ? scoredTech[0].techId : null;
    }
    
    calculateTechCost(tech) {
        // Simplified cost calculation for AI
        let totalCost = 0;
        if (tech.cost.spice) totalCost += tech.cost.spice;
        if (tech.cost.water) totalCost += tech.cost.water * 2; // Water is more valuable
        if (tech.cost.techPoints) totalCost += tech.cost.techPoints * 5; // Tech points are very valuable
        
        return totalCost;
    }
    
    canAffordTechnology(techId) {
        const tech = this.game.technologySystem.technologies[techId];
        if (!tech) return false;
        
        // Simplified affordability check for AI
        const spiceCost = tech.cost.spice || 0;
        return this.team.spice >= spiceCost;
    }
    
    startResearch(techId) {
        // Simulate AI starting research
        const tech = this.game.technologySystem.technologies[techId];
        if (!tech) return;
        
        // Pay the spice cost (simplified)
        const spiceCost = tech.cost.spice || 0;
        this.team.spice -= spiceCost;
        
        // Add to AI research queue or start immediately
        this.currentResearch = {
            techId,
            progress: 0,
            totalTime: tech.researchTime,
            startTime: this.game.gameTime
        };
        
    }
    
    decideDiplomacy() {
        if (!this.game.diplomacySystem) return;
        
        // AI diplomatic decision making
        const diplomaticSummary = this.game.diplomacySystem.getDiplomaticSummary();
        
        // Simple AI diplomacy logic
        Object.keys(diplomaticSummary).forEach(factionId => {
            const faction = diplomaticSummary[factionId];
            
            // Skip if this is the AI's own faction
            if (factionId === this.teamId) return;
            
            // Decide on diplomatic actions based on personality and situation
            this.evaluateDiplomaticAction(factionId, faction);
        });
    }
    
    evaluateDiplomaticAction(factionId, factionData) {
        const playerPower = this.calculatePlayerPower();
        const aiPower = this.calculateAIPower();
        const powerRatio = aiPower / Math.max(playerPower, 0.1);
        
        // AI is weak, try to make alliances
        if (powerRatio < 0.8 && factionData.state === 'NEUTRAL') {
            if (Math.random() < 0.3 && factionData.canProposeAlliance) {
                // AI would propose alliance (this would need integration with diplomacy system)
            }
        }
        
        // AI is strong and aggressive, consider declaring war
        if (this.personality === 'aggressive' && powerRatio > 1.2) {
            if (factionData.state === 'UNFRIENDLY' && Math.random() < 0.2) {
                // AI considers war
            }
        }
        
        // Economic AI tries to establish trade
        if (this.personality === 'economic' && factionData.state === 'FRIENDLY') {
            if (Math.random() < 0.4 && factionData.canProposeTrade) {
                // AI considers trade
            }
        }
    }
    
    manageResources(deltaTime) {
        // AI resource management
        const currentSpice = this.team.spice;
        
        // Adjust resource targets based on strategy
        if (this.strategicGoals.has('economy')) {
            this.resourceTargets.spice = Math.max(this.resourceTargets.spice, 5000);
        }
        
        if (this.strategicGoals.has('military')) {
            // Spend more aggressively on military
            if (currentSpice > this.resourceTargets.spice * 0.8) {
                this.prioritizeMilitaryProduction();
            }
        }
        
        // Emergency resource management
        if (currentSpice < 500) {
            // Focus on economy
            this.strategicGoals.add('economy');
            this.strategicGoals.delete('military');
        }
    }
    
    prioritizeMilitaryProduction() {
        // Increase military unit production when AI has excess resources
        const militaryUnits = this.team.units.filter(u => u.damage > 0).length;
        const targetMilitaryUnits = Math.floor(this.team.units.length * 0.7);
        
        if (militaryUnits < targetMilitaryUnits) {
            // Try to produce more military units
            this.forceUnitProduction('military');
        }
    }
    
    forceUnitProduction(category) {
        const unitTypes = category === 'military' ? ['soldier', 'tank'] : ['harvester'];
        const selectedType = unitTypes[Math.floor(Math.random() * unitTypes.length)];
        
        const unitData = GameData.getUnitTypes()[selectedType];
        if (this.team.spice >= unitData.cost) {
            // Find appropriate production building
            let productionBuilding = null;
            if (selectedType === 'harvester' || selectedType === 'tank') {
                productionBuilding = this.team.buildings.find(b =>
                    b.type === 'factory' && b.constructionProgress >= 100 && !b.ai_isProducing
                );
            } else {
                productionBuilding = this.team.buildings.find(b =>
                    b.type === 'barracks' && b.constructionProgress >= 100 && !b.ai_isProducing
                );
            }
            
            if (productionBuilding) {
                productionBuilding.ai_isProducing = true;
                this.team.spice -= unitData.cost;
                
                setTimeout(() => {
                    this.game.createUnit(selectedType,
                        productionBuilding.x + 60,
                        productionBuilding.y + 60,
                        this.teamId
                    );
                    productionBuilding.ai_isProducing = false;
                }, unitData.buildTime * 1000 * 0.8); // AI builds 20% faster
            }
        }
    }
    
    tryBuildBuilding(buildingType) {
        const buildingData = GameData.getBuildingTypes()[buildingType];
        if (!buildingData || this.team.spice < buildingData.cost) return false;
        
        const bases = this.team.buildings.filter(b => b.type === 'base');
        if (bases.length === 0) return false;
        
        return this._addBuildingSafe(bases[0], buildingType);
    }

    decideBuild() {
        const bases = this.team.buildings.filter(b => b.type === 'base');
        if (bases.length === 0) return; // No base, can't build

        // Try to build something from a simplified build order
        let buildingToConstruct = null;
        let requiredCost = 0;

        // Simple logic: if few power plants, build one. If few barracks, build one.
        const powerPlants = this.team.buildings.filter(b => b.type === 'power_plant').length;
        const barracks = this.team.buildings.filter(b => b.type === 'barracks').length;
        const factories = this.team.buildings.filter(b => b.type === 'factory').length;

        if (powerPlants < bases.length * 1) { // One power plant per base seems reasonable for easy AI
            buildingToConstruct = 'power_plant';
        } else if (barracks < bases.length * 1 && powerPlants > 0) {
            buildingToConstruct = 'barracks';
        } else if (factories < 1 && powerPlants > 0 && barracks > 0) { // Only one factory for easy AI
            buildingToConstruct = 'factory';
        } else if (Math.random() < 0.3) { // Occasionally build a turret if other needs met
             if (this.team.buildings.filter(b => b.type === 'gun_turret').length < 2) { // Max 2 turrets
                buildingToConstruct = 'gun_turret';
             }
        }
        
        if (buildingToConstruct) {
            const typeInfo = GameData.getBuildingTypes()[buildingToConstruct];
            requiredCost = typeInfo.cost * 1.5; // AI pays a bit more effectively
             if (this.team.spice >= requiredCost && this._canPlaceBuilding(bases[0], typeInfo)) {
                 this._addBuildingSafe(bases[0], buildingToConstruct);
             }
        }
    }
    
    _canPlaceBuilding(nearBuilding, buildingType) { // Simplified check
        // In a real scenario, this would involve pathfinder or other placement logic
        return true; 
    }

    _addBuildingSafe(nearBuilding, typeToBuild) {
        const typeInfo = GameData.getBuildingTypes()[typeToBuild];
        const baseOffset = 150; // Distance from base center
        const angleIncrement = Math.PI * 2 / 8; // 8 directions around the base

        for (let i = 0; i < 8; i++) { // Try 8 positions around the base
            const angle = i * angleIncrement;
            const x = nearBuilding.x + Math.cos(angle) * baseOffset;
            const y = nearBuilding.y + Math.sin(angle) * baseOffset;

            if (this.game.isValidBuildingPlacement(x, y, typeInfo)) {
                this.game.addBuilding({ type: typeToBuild, x, y, team: this.teamId });
                this.team.spice -= typeInfo.cost;
                return true;
            }
        }
        // Fallback to random if structured placement fails
        for (let attempts = 0; attempts < 5; attempts++) {
            const angle = Math.random() * Math.PI * 2;
            const dist = 100 + Math.random() * 200;
            const x = nearBuilding.x + Math.cos(angle) * dist;
            const y = nearBuilding.y + Math.sin(angle) * dist;
            if (this.game.isValidBuildingPlacement(x, y, typeInfo)) {
                this.game.addBuilding({ type: typeToBuild, x, y, team: this.teamId });
                this.team.spice -= typeInfo.cost;
                return true;
            }
        }
        return false;
    }

    decideUnitProduction() {
        const unitCap = 20; // Increased AI unit cap for more aggressive AI
        if (this.team.units.length >= unitCap) return;

        const playerCombatUnits = this.game.gameState.teams.player.units.filter(u => u.damage > 0).length;
        const aiCombatUnits = this.team.units.filter(u => u.damage > 0).length;
        const harvesters = this.team.units.filter(u => u.type === 'harvester').length;

        let unitTypeToBuild = null;

        // Prioritize harvesters if spice income is low or few harvesters
        if (harvesters < 2 && this.team.spice < 1000) {
            unitTypeToBuild = 'harvester';
        } else if (aiCombatUnits < playerCombatUnits * 1.2 || aiCombatUnits < 5) { // If player has more combat units or AI has few
            unitTypeToBuild = (Math.random() < 0.7) ? 'tank' : 'soldier'; // Prioritize tanks
        } else {
            unitTypeToBuild = 'soldier'; // Default
        }

        const unitData = GameData.getUnitTypes()[unitTypeToBuild];
        const cost = unitData.cost;

        if (this.team.spice >= cost) {
            let productionBuilding = null;
            if (unitTypeToBuild === 'harvester' || unitTypeToBuild === 'tank') {
                productionBuilding = this.team.buildings.find(b => b.type === 'factory' && b.constructionProgress >= 100 && !b.ai_isProducing);
            } else { // Soldier
                productionBuilding = this.team.buildings.find(b => b.type === 'barracks' && b.constructionProgress >= 100 && !b.ai_isProducing);
            }

            if (productionBuilding) {
                productionBuilding.ai_isProducing = true;
                this.team.spice -= cost;
                setTimeout(() => {
                    // Spawn unit near building, possibly with a rally point if implemented
                    this.game.createUnit(unitTypeToBuild, productionBuilding.x + 60, productionBuilding.y + 60, this.teamId);
                    productionBuilding.ai_isProducing = false;
                }, unitData.buildTime * 1000); // AI builds at normal speed now
            }
        }
    }

    controlUnits(deltaTime) {
        const retreatThreshold = 0.3; // Retreat if health is below 30%

        this.team.units.forEach(unit => {
            // Harvester logic
            if (unit.type === 'harvester' && unit.state === 'idle') {
                unit.state = 'harvesting'; // Let unit's own logic handle harvesting
                return;
            }

            // Retreat logic for combat units
            if (unit.damage > 0 && unit.health / unit.maxHealth < retreatThreshold && unit.state !== 'retreating') {
                const friendlyBase = this.findNearestFriendlyBuilding(unit, 'base');
                if (friendlyBase) {
                    unit.targetX = friendlyBase.x;
                    unit.targetY = friendlyBase.y;
                    unit.state = 'retreating';
                    unit.aiTarget = null; // Clear attack target
                    unit.path = this.game.pathfinder.findPath(unit.x, unit.y, unit.targetX, unit.targetY);
                    unit.pathIndex = 0;
                    return; // Unit is retreating, skip other actions
                }
            }

            // If retreating, continue moving to base
            if (unit.state === 'retreating') {
                const friendlyBase = this.findNearestFriendlyBuilding(unit, 'base');
                if (friendlyBase) {
                    const distToBaseSq = (friendlyBase.x - unit.x)**2 + (friendlyBase.y - unit.y)**2;
                    const baseHealingRadiusSq = (friendlyBase.healingRadius || 100)**2; // Use healingRadius for "safe" zone
                    if (distToBaseSq < baseHealingRadiusSq * 0.5) { // If close enough to base, go idle and heal
                        unit.state = 'idle';
                        unit.path = [];
                    } else {
                        this.moveUnit(unit, deltaTime); // Continue moving to base
                    }
                } else { // Base destroyed while retreating
                    unit.state = 'idle';
                }
                return; // Unit is retreating, skip other actions
            }

            // Normal combat/movement logic
            if (unit.state === 'idle' || unit.state === 'moving') {
                const enemyTarget = this.findNearestEnemyEntity(unit, 400); // Find any enemy, not just player
                if (enemyTarget) {
                    unit.aiTarget = enemyTarget;
                    unit.state = 'attacking';
                    // Start combat if in range
                    if (this.game.combatSystem && this.game.combatSystem.canAttack(unit, enemyTarget)) {
                        this.game.combatSystem.startCombat(unit, enemyTarget);
                    }
                } else {
                    // Patrol behavior - move towards enemy territory
                    if (Math.random() < 0.005 * deltaTime * 60) {
                        const patrolTarget = this.selectPatrolTarget();
                        if (patrolTarget) {
                            unit.targetX = patrolTarget.x + (Math.random() - 0.5) * 300;
                            unit.targetY = patrolTarget.y + (Math.random() - 0.5) * 300;
                            unit.state = 'moving';
                            unit.path = this.game.pathfinder.findPath(unit.x, unit.y, unit.targetX, unit.targetY);
                            unit.pathIndex = 0;
                        }
                    }
                }
            }
        });

        // Group attack coordination
        const combatUnits = this.team.units.filter(u => u.damage > 0 && u.state !== 'attacking' && u.state !== 'retreating');
        if (combatUnits.length >= 3 && Math.random() < 0.002 * deltaTime * 60) {
            const targetBase = this.selectPrimaryEnemyBase();
            if (targetBase) {
                combatUnits.forEach(unit => {
                    unit.targetX = targetBase.x + (Math.random() - 0.5) * 200;
                    unit.targetY = targetBase.y + (Math.random() - 0.5) * 200;
                    unit.state = 'moving';
                    unit.aiTarget = targetBase;
                    unit.path = this.game.pathfinder.findPath(unit.x, unit.y, unit.targetX, unit.targetY);
                    unit.pathIndex = 0;
                });
            }
        }
    }
    
    // Phase 4 Integration Methods
    updateBalance(settings) {
        // Update AI difficulty and behavior based on balance settings
        if (settings.aggressionLevel !== undefined) {
            this.aggressionLevel = settings.aggressionLevel;
        }
        
        if (settings.resourceMultiplier !== undefined) {
            // Adjust AI resource generation
            this.resourceMultiplier = settings.resourceMultiplier;
        }
        
        if (settings.unitCap !== undefined) {
            this.unitCap = settings.unitCap;
        }
        
        if (settings.buildSpeed !== undefined) {
            this.buildSpeedMultiplier = settings.buildSpeed;
        }
    }
    
    // Export state for save/load
    exportState() {
        return {
            teamId: this.teamId,
            aggressionLevel: this.aggressionLevel,
            personality: this.personality,
            researchPriorities: this.researchPriorities,
            diplomaticStance: this.diplomaticStance,
            strategicGoals: Array.from(this.strategicGoals),
            currentStrategy: this.currentStrategy,
            lastStrategyUpdate: this.lastStrategyUpdate,
            researchQueue: this.researchQueue,
            currentResearch: this.currentResearch,
            researchCooldown: this.researchCooldown,
            diplomaticMemory: this.diplomaticMemory,
            resourceTargets: this.resourceTargets,
            unitComposition: this.unitComposition,
            resourceMultiplier: this.resourceMultiplier || 1,
            unitCap: this.unitCap || 20,
            buildSpeedMultiplier: this.buildSpeedMultiplier || 1
        };
    }
    
    // Import state for save/load
    importState(state) {
        this.teamId = state.teamId;
        this.aggressionLevel = state.aggressionLevel;
        this.personality = state.personality;
        this.researchPriorities = state.researchPriorities;
        this.diplomaticStance = state.diplomaticStance;
        this.strategicGoals = new Set(state.strategicGoals);
        this.currentStrategy = state.currentStrategy;
        this.lastStrategyUpdate = state.lastStrategyUpdate;
        this.researchQueue = state.researchQueue || [];
        this.currentResearch = state.currentResearch;
        this.researchCooldown = state.researchCooldown || 0;
        this.diplomaticMemory = state.diplomaticMemory;
        this.resourceTargets = state.resourceTargets;
        this.unitComposition = state.unitComposition;
        this.resourceMultiplier = state.resourceMultiplier || 1;
        this.unitCap = state.unitCap || 20;
        this.buildSpeedMultiplier = state.buildSpeedMultiplier || 1;
    }
    
    // Get AI performance rating for balance manager
    getPerformanceRating() {
        const aiPower = this.calculateAIPower();
        const playerPower = this.calculatePlayerPower();
        
        return {
            powerRatio: aiPower / Math.max(playerPower, 0.1),
            unitCount: this.team.units.length,
            buildingCount: this.team.buildings.length,
            resourceLevel: this.team.spice,
            aggressionLevel: this.aggressionLevel
        };
    }
    
    // Apply difficulty modifiers
    applyDifficultyModifiers(difficulty) {
        const modifiers = {
            easy: {
                aggressionLevel: 0.6,
                resourceMultiplier: 0.8,
                unitCap: 15,
                buildSpeed: 1.2
            },
            normal: {
                aggressionLevel: 1.0,
                resourceMultiplier: 1.0,
                unitCap: 20,
                buildSpeed: 1.0
            },
            hard: {
                aggressionLevel: 1.4,
                resourceMultiplier: 1.3,
                unitCap: 30,
                buildSpeed: 0.8
            },
            nightmare: {
                aggressionLevel: 1.8,
                resourceMultiplier: 1.6,
                unitCap: 40,
                buildSpeed: 0.6
            }
        };
        
        const modifier = modifiers[difficulty] || modifiers.normal;
        this.updateBalance(modifier);
    }
    
    // Enemy detection and targeting methods
    findNearestEnemyEntity(unit, maxRange = 400) {
        let nearestEnemy = null;
        let minDistance = maxRange;
        
        // Check all teams except our own
        Object.keys(this.game.gameState.teams).forEach(teamId => {
            if (teamId === this.teamId) return; // Skip our own team
            
            const enemyTeam = this.game.gameState.teams[teamId];
            
            // Check enemy units
            enemyTeam.units.forEach(enemyUnit => {
                if (enemyUnit.health <= 0) return;
                
                const distance = Math.sqrt((unit.x - enemyUnit.x) ** 2 + (unit.y - enemyUnit.y) ** 2);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemyUnit;
                }
            });
            
            // Check enemy buildings
            enemyTeam.buildings.forEach(enemyBuilding => {
                if (enemyBuilding.health <= 0 || enemyBuilding.constructionProgress < 100) return;
                
                const distance = Math.sqrt((unit.x - enemyBuilding.x) ** 2 + (unit.y - enemyBuilding.y) ** 2);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemyBuilding;
                }
            });
        });
        
        return nearestEnemy;
    }
    
    findNearestFriendlyBuilding(unit, buildingType = null) {
        let nearestBuilding = null;
        let minDistance = Infinity;
        
        this.team.buildings.forEach(building => {
            if (building.health <= 0 || building.constructionProgress < 100) return;
            if (buildingType && building.type !== buildingType) return;
            
            const distance = Math.sqrt((unit.x - building.x) ** 2 + (unit.y - building.y) ** 2);
            if (distance < minDistance) {
                minDistance = distance;
                nearestBuilding = building;
            }
        });
        
        return nearestBuilding;
    }
    
    selectPatrolTarget() {
        // Find enemy bases to patrol towards
        const enemyBases = [];
        
        Object.keys(this.game.gameState.teams).forEach(teamId => {
            if (teamId === this.teamId) return;
            
            const enemyTeam = this.game.gameState.teams[teamId];
            enemyTeam.buildings.forEach(building => {
                if (building.type === 'base' && building.health > 0 && building.constructionProgress >= 100) {
                    enemyBases.push(building);
                }
            });
        });
        
        // Return random enemy base or null
        return enemyBases.length > 0 ? enemyBases[Math.floor(Math.random() * enemyBases.length)] : null;
    }
    
    selectPrimaryEnemyBase() {
        // Prioritize player base, then other enemy bases
        const playerTeam = this.game.gameState.teams.player;
        if (playerTeam) {
            const playerBase = playerTeam.buildings.find(b => b.type === 'base' && b.health > 0);
            if (playerBase) return playerBase;
        }
        
        // Find any enemy base
        return this.selectPatrolTarget();
    }
    
    moveUnit(unit, deltaTime) {
        // Simple movement implementation
        if (!unit.path || unit.path.length === 0) return;
        
        const target = unit.path[unit.pathIndex || 0];
        if (!target) return;
        
        const dx = target.x - unit.x;
        const dy = target.y - unit.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 5) {
            unit.pathIndex = (unit.pathIndex || 0) + 1;
            if (unit.pathIndex >= unit.path.length) {
                unit.path = [];
                unit.pathIndex = 0;
                unit.state = 'idle';
            }
        } else {
            const speed = unit.speed || 2;
            const moveDistance = speed * deltaTime * 60;
            unit.x += (dx / distance) * Math.min(moveDistance, distance);
            unit.y += (dy / distance) * Math.min(moveDistance, distance);
        }
    }
}
