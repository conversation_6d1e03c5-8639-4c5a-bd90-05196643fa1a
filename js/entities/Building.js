import { BaseEntity } from './BaseEntity.js';
import { GameData } from '../GameData.js';
import { createBuildingImage } from '../GameSVGs.js';

export class Building extends BaseEntity {
    constructor(x, y, type, team = 'neutral') {
        super(x, y, team);
        
        this.type = type;
        this.name = type;
        
        // Building identification
        this.isBuilding = true;
        this.isUnit = false; // Ensure buildings are not treated as units
        
        // Get building data from GameData
        const buildingData = GameData.getBuildingTypes()[type];
        if (buildingData) {
            this.applyBuildingData(buildingData);
        }
        
        // Construction properties
        this.constructionProgress = 0;
        this.maxConstructionTime = 30000; // 30 seconds default
        this.isUnderConstruction = true;
        this.constructionStartTime = Date.now();
        
        // Production properties
        this.produces = [];
        this.productionQueue = [];
        this.currentProduction = null;
        this.productionProgress = 0;
        this.productionTime = 0;
        
        // Power and resources
        this.powerGeneration = 0;
        this.powerConsumption = 0;
        this.spiceStorage = 0;
        this.maxSpiceStorage = 0;
        
        // Defense properties
        this.armor = 0;
        this.shieldCapacity = 0;
        this.currentShield = 0;
        this.shieldRegenRate = 0;
        
        // Special properties
        this.isRefinery = false;
        this.isFactory = false;
        this.isDefensive = false;
        this.isCore = false;
        this.canRepair = false;
        
        // Visual properties
        this.sprite = null;
        this.animationFrame = 0;
        this.constructionEffect = 0;
        
        // Initialize SVG sprite with type mapping
        console.log(`[DEBUG] Building: Creating building ${type} with SVG sprite`);
        if (this.type) {
            // Map GameData building types to GameSVGs keys
            const svgTypeMap = {
                'construction_yard': 'command_center',
                'gun_turret': 'turret',
                'high_tech_factory': 'factory', // Use regular factory SVG for now
                'palace': 'command_center' // Use command center SVG for now
            };
            
            const svgType = svgTypeMap[this.type] || this.type;
            console.log(`[DEBUG] Building: Mapping ${this.type} to SVG type ${svgType}`);
            
            this.sprite = createBuildingImage(svgType,
                () => {
                    console.log(`[DEBUG] Building: SVG sprite loaded successfully for ${this.type} (${svgType})`);
                },
                () => {
                    console.log(`[DEBUG] Building: SVG sprite failed to load for ${this.type} (${svgType})`);
                }
            );
        }
        
        // Rally point for production buildings
        this.rallyPoint = { x: x + 50, y: y + 50 };
        
        // Initialize based on type
        this.initializeBuildingType();
    }

    applyBuildingData(buildingData) {
        this.name = buildingData.name || this.type;
        this.health = buildingData.health || 200;
        this.maxHealth = buildingData.health || 200;
        this.width = buildingData.width || 40;
        this.height = buildingData.height || 40;
        this.color = buildingData.color || '#666666';
        this.maxConstructionTime = (buildingData.buildTime || 30) * 1000;
        this.armor = buildingData.armor || 0;
        
        // Production
        this.produces = buildingData.produces || [];
        
        // Power
        this.powerGeneration = buildingData.power > 0 ? buildingData.power : 0;
        this.powerConsumption = buildingData.power < 0 ? -buildingData.power : 0;
        
        // Storage
        this.maxSpiceStorage = buildingData.storage || 0;
        
        // Special properties
        this.isRefinery = buildingData.category === 'economic' && this.type.includes('refinery');
        this.isFactory = buildingData.category === 'production';
        this.isDefensive = buildingData.category === 'defense';
        this.isCore = buildingData.category === 'core';
        this.canRepair = buildingData.canRepair || false;
        
        // Shields
        this.shieldCapacity = buildingData.shields || 0;
        this.currentShield = this.shieldCapacity;
        this.shieldRegenRate = buildingData.shieldRegen || 0;
    }

    initializeBuildingType() {
        // Set team-specific colors
        switch (this.team) {
            case 'player':
                this.color = '#0088CC';
                break;
            case 'enemy':
                this.color = '#CC2200';
                break;
            case 'neutral':
                this.color = '#888888';
                break;
        }
        
        // Start construction
        if (this.isUnderConstruction) {
            this.health = 1; // Start with minimal health during construction
        }
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        // Update construction
        if (this.isUnderConstruction) {
            this.updateConstruction(deltaTime);
        } else {
            // Update production
            this.updateProduction(deltaTime);
            
            // Update shields
            this.updateShields(deltaTime);
            
            // Update special functions
            this.updateSpecialFunctions(deltaTime);
        }
        
        // Update visual effects
        this.updateVisualEffects(deltaTime);
    }

    updateConstruction(deltaTime) {
        const elapsedTime = Date.now() - this.constructionStartTime;
        this.constructionProgress = Math.min(100, (elapsedTime / this.maxConstructionTime) * 100);
        
        // Update health based on construction progress
        this.health = Math.max(1, (this.constructionProgress / 100) * this.maxHealth);
        
        if (this.constructionProgress >= 100) {
            this.completeConstruction();
        }
        
        this.constructionEffect += deltaTime * 5;
    }

    completeConstruction() {
        this.isUnderConstruction = false;
        this.constructionProgress = 100;
        this.health = this.maxHealth;
        this.currentShield = this.shieldCapacity;
        
        this.emit('constructionComplete');
    }

    updateProduction(deltaTime) {
        if (!this.currentProduction) {
            // Start next item in queue
            if (this.productionQueue.length > 0) {
                this.startNextProduction();
            }
            return;
        }
        
        this.productionProgress += (deltaTime * 1000);
        const progressPercent = (this.productionProgress / this.productionTime) * 100;
        
        if (progressPercent >= 100) {
            this.completeProduction();
        }
    }

    startNextProduction() {
        if (this.productionQueue.length === 0) return;
        
        const nextItem = this.productionQueue.shift();
        this.currentProduction = nextItem;
        this.productionProgress = 0;
        
        const unitData = GameData.getUnitTypes()[nextItem.type];
        this.productionTime = (unitData?.buildTime || 10) * 1000;
        
        this.emit('productionStarted', { item: nextItem });
    }

    completeProduction() {
        if (!this.currentProduction) return;
        
        const completedItem = this.currentProduction;
        this.currentProduction = null;
        this.productionProgress = 0;
        this.productionTime = 0;
        
        this.emit('productionComplete', { 
            item: completedItem,
            rallyPoint: this.rallyPoint
        });
    }

    updateShields(deltaTime) {
        if (this.currentShield < this.shieldCapacity && this.shieldRegenRate > 0) {
            this.currentShield = Math.min(
                this.shieldCapacity,
                this.currentShield + this.shieldRegenRate * deltaTime
            );
        }
    }

    updateSpecialFunctions(deltaTime) {
        // Refinery functions
        if (this.isRefinery) {
            this.updateRefineryFunctions(deltaTime);
        }
        
        // Repair functions
        if (this.canRepair) {
            this.updateRepairFunctions(deltaTime);
        }
    }

    updateRefineryFunctions(deltaTime) {
        // Process spice if any is stored
        if (this.spiceStorage > 0) {
            const processRate = 10 * deltaTime; // 10 spice per second
            const processed = Math.min(this.spiceStorage, processRate);
            this.spiceStorage -= processed;
            
            this.emit('spiceProcessed', { amount: processed });
        }
    }

    updateRepairFunctions(deltaTime) {
        // Auto-repair nearby friendly units/buildings
        // This would need integration with the game's unit/building systems
    }

    updateVisualEffects(deltaTime) {
        this.animationFrame += deltaTime * 2;
        if (this.animationFrame >= Math.PI * 2) {
            this.animationFrame = 0;
        }
    }

    // Production methods
    addToProductionQueue(unitType, priority = false) {
        const unitData = GameData.getUnitTypes()[unitType];
        if (!unitData) return false;
        
        // Check if this building can produce this unit
        if (!this.produces.includes(unitType)) return false;
        
        const productionItem = {
            type: unitType,
            cost: unitData.cost,
            buildTime: unitData.buildTime || 10,
            addedAt: Date.now()
        };
        
        if (priority) {
            this.productionQueue.unshift(productionItem);
        } else {
            this.productionQueue.push(productionItem);
        }
        
        this.emit('itemAddedToQueue', { item: productionItem });
        return true;
    }

    cancelProduction(index = -1) {
        if (index === -1) {
            // Cancel current production
            if (this.currentProduction) {
                const cancelled = this.currentProduction;
                this.currentProduction = null;
                this.productionProgress = 0;
                this.productionTime = 0;
                this.emit('productionCancelled', { item: cancelled });
                return cancelled;
            }
        } else if (index >= 0 && index < this.productionQueue.length) {
            // Cancel queued item
            const cancelled = this.productionQueue.splice(index, 1)[0];
            this.emit('queueItemCancelled', { item: cancelled, index });
            return cancelled;
        }
        return null;
    }

    clearProductionQueue() {
        const cleared = [...this.productionQueue];
        this.productionQueue = [];
        this.emit('queueCleared', { items: cleared });
        return cleared;
    }

    // Resource methods
    addSpice(amount) {
        if (!this.isRefinery) return 0;
        
        const canStore = this.maxSpiceStorage - this.spiceStorage;
        const actualAmount = Math.min(amount, canStore);
        this.spiceStorage += actualAmount;
        
        this.emit('spiceAdded', { amount: actualAmount, total: this.spiceStorage });
        return actualAmount;
    }

    // Power methods
    getPowerOutput() {
        if (this.isUnderConstruction) return 0;
        return this.powerGeneration;
    }

    getPowerConsumption() {
        if (this.isUnderConstruction) return 0;
        return this.powerConsumption;
    }

    // Combat methods
    takeDamage(amount, source = null) {
        let actualDamage = amount;
        
        // Apply armor reduction
        if (this.armor > 0) {
            actualDamage = Math.max(1, amount - this.armor);
        }
        
        // Shields absorb damage first
        if (this.currentShield > 0) {
            const shieldDamage = Math.min(actualDamage, this.currentShield);
            this.currentShield -= shieldDamage;
            actualDamage -= shieldDamage;
        }
        
        // Apply remaining damage to health
        if (actualDamage > 0) {
            const healthDamage = super.takeDamage(actualDamage, source);
            return shieldDamage + healthDamage;
        }
        
        this.emit('damage', { amount: amount, actualDamage: actualDamage, source });
        return amount;
    }

    // Rally point methods
    setRallyPoint(x, y) {
        this.rallyPoint = { x, y };
        this.emit('rallyPointSet', { x, y });
    }

    // Drawing
    drawEntity(ctx) {
        console.log(`[DEBUG] Building: Drawing building ${this.type}, sprite loaded: ${this.sprite && this.sprite.complete}`);
        
        if (this.isUnderConstruction) {
            this.drawConstruction(ctx);
        } else {
            this.drawCompleted(ctx);
        }
        
        // Draw shields
        if (this.currentShield > 0) {
            this.drawShields(ctx);
        }
        
        // Draw production indicator
        if (this.currentProduction) {
            this.drawProductionIndicator(ctx);
        }
    }

    drawConstruction(ctx) {
        // Draw construction scaffolding
        ctx.strokeStyle = '#FFFF00';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.strokeRect(-this.width / 2, -this.height / 2, this.width, this.height);
        ctx.setLineDash([]);
        
        // Draw partial building
        const constructedHeight = (this.constructionProgress / 100) * this.height;
        ctx.fillStyle = this.color;
        ctx.fillRect(
            -this.width / 2, 
            this.height / 2 - constructedHeight, 
            this.width, 
            constructedHeight
        );
        
        // Draw construction progress
        const sparkleOffset = Math.sin(this.constructionEffect) * 5;
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(-this.width / 2 + sparkleOffset, -this.height / 2, 2, 2);
        ctx.fillRect(this.width / 2 - sparkleOffset, -this.height / 2, 2, 2);
    }

    drawCompleted(ctx) {
        // Try to draw SVG sprite first, fallback to basic rendering
        if (this.sprite && this.sprite.complete && this.sprite.naturalWidth !== 0) {
            console.log(`[DEBUG] Building: Drawing SVG sprite for ${this.type}`);
            ctx.drawImage(this.sprite, -this.width / 2, -this.height / 2, this.width, this.height);
        } else {
            console.log(`[DEBUG] Building: Drawing fallback rectangle for ${this.type}`);
            // Fallback to rectangle rendering
            ctx.fillStyle = this.color;
            ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
            
            // Draw building details based on type
            if (this.isFactory) {
                // Draw factory details
                ctx.fillStyle = '#444444';
                ctx.fillRect(-this.width / 4, -this.height / 4, this.width / 2, this.height / 2);
            } else if (this.isDefensive) {
                // Draw defensive details
                ctx.strokeStyle = '#FF0000';
                ctx.lineWidth = 3;
                ctx.strokeRect(-this.width / 2, -this.height / 2, this.width, this.height);
            } else if (this.isRefinery) {
                // Draw refinery details
                ctx.fillStyle = '#FFA500';
                ctx.fillRect(-5, -this.height / 2, 10, this.height);
            }
            
            // Draw power indicators
            if (this.powerGeneration > 0) {
                ctx.fillStyle = '#00FF00';
                ctx.fillRect(this.width / 2 - 4, -this.height / 2, 4, 8);
            }
        }
    }

    drawShields(ctx) {
        const shieldPercent = this.currentShield / this.shieldCapacity;
        const radius = Math.max(this.width, this.height) / 2 + 8;
        
        ctx.strokeStyle = `rgba(0, 255, 255, ${shieldPercent * 0.7})`;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, Math.PI * 2);
        ctx.stroke();
    }

    drawProductionIndicator(ctx) {
        const progressPercent = (this.productionProgress / this.productionTime) * 100;
        const barWidth = this.width;
        const barHeight = 4;
        const barY = this.height / 2 + 8;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
        
        // Progress
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(-barWidth / 2, barY, (barWidth * progressPercent) / 100, barHeight);
        
        // Border
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.strokeRect(-barWidth / 2, barY, barWidth, barHeight);
    }

    // Serialization
    toJSON() {
        return {
            ...super.toJSON(),
            type: this.type,
            constructionProgress: this.constructionProgress,
            isUnderConstruction: this.isUnderConstruction,
            productionQueue: this.productionQueue,
            currentProduction: this.currentProduction,
            productionProgress: this.productionProgress,
            spiceStorage: this.spiceStorage,
            currentShield: this.currentShield,
            rallyPoint: this.rallyPoint
        };
    }
}