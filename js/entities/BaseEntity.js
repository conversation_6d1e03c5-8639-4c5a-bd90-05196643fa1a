export class BaseEntity {
    constructor(x, y, team = 'neutral') {
        this.id = BaseEntity.generateId();
        this.x = x;
        this.y = y;
        this.team = team;
        this.health = 100;
        this.maxHealth = 100;
        this.selected = false;
        this.visible = true;
        this.active = true;
        
        // Visual properties
        this.width = 20;
        this.height = 20;
        this.color = '#888888';
        this.angle = 0;
        
        // Physics properties
        this.velocity = { x: 0, y: 0 };
        this.acceleration = { x: 0, y: 0 };
        this.friction = 0.9;
        
        // Collision properties
        this.collisionRadius = Math.max(this.width, this.height) / 2;
        this.solid = true;
        
        // Status effects
        this.statusEffects = new Map();
        
        // Events
        this.eventListeners = new Map();
        
        // Creation timestamp
        this.createdAt = Date.now();
        this.lastUpdate = Date.now();
    }

    static generateId() {
        return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    update(deltaTime) {
        this.lastUpdate = Date.now();
        
        // Update physics
        this.updatePhysics(deltaTime);
        
        // Update status effects
        this.updateStatusEffects(deltaTime);
        
        // Clamp health
        this.health = Math.max(0, Math.min(this.health, this.maxHealth));
    }

    updatePhysics(deltaTime) {
        // Apply acceleration to velocity
        this.velocity.x += this.acceleration.x * deltaTime;
        this.velocity.y += this.acceleration.y * deltaTime;
        
        // Apply friction
        this.velocity.x *= this.friction;
        this.velocity.y *= this.friction;
        
        // Apply velocity to position
        this.x += this.velocity.x * deltaTime * 60;
        this.y += this.velocity.y * deltaTime * 60;
        
        // Reset acceleration
        this.acceleration.x = 0;
        this.acceleration.y = 0;
    }

    updateStatusEffects(deltaTime) {
        for (const [effectName, effect] of this.statusEffects) {
            effect.duration -= deltaTime * 1000;
            
            if (effect.duration <= 0) {
                this.removeStatusEffect(effectName);
            } else if (effect.onUpdate) {
                effect.onUpdate(this, deltaTime);
            }
        }
    }

    draw(ctx, camera = { x: 0, y: 0 }) {
        if (!this.visible) return;
        
        // The camera object is just for compatibility
        // The actual viewport transformation is already applied in RenderingSystem
        // So we just need to translate to the entity's world position
        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle);
        
        // Draw entity
        this.drawEntity(ctx);
        
        // Draw health bar if damaged and alive
        if (this.health < this.maxHealth && this.isAlive()) {
            this.drawHealthBar(ctx);
        }
        
        // Draw selection indicator (but only if not handled by SelectionManager and alive)
        if (this.selected && !this.isUnit && this.isAlive()) {
            this.drawSelectionIndicator(ctx);
        }
        
        ctx.restore();
    }

    drawEntity(ctx) {
        // Default entity drawing - override in subclasses
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
    }

    drawHealthBar(ctx) {
        const barWidth = this.width;
        const barHeight = 4;
        const barY = -this.height / 2 - 8;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
        
        // Health
        const healthPercent = this.health / this.maxHealth;
        const healthColor = healthPercent > 0.6 ? '#00FF00' : 
                           healthPercent > 0.3 ? '#FFFF00' : '#FF0000';
        
        ctx.fillStyle = healthColor;
        ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.strokeRect(-barWidth / 2, barY, barWidth, barHeight);
    }

    drawSelectionIndicator(ctx) {
        const radius = Math.max(this.width, this.height) / 2 + 5;
        
        ctx.strokeStyle = '#00FFFF';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, Math.PI * 2);
        ctx.stroke();
        ctx.setLineDash([]);
    }

    // Damage and healing
    takeDamage(amount, source = null) {
        const actualDamage = Math.min(amount, this.health);
        this.health -= actualDamage;
        
        this.emit('damage', { amount: actualDamage, source });
        
        if (this.health <= 0) {
            this.handleDeath(source);
        }
        
        return actualDamage;
    }

    /**
     * Handle entity death - emit proper death events and cleanup
     */
    handleDeath(source = null) {
        if (!this.isAlive()) return; // Already dead
        
        // Emit death event with position for visual effects
        this.emit('death', {
            entity: this,
            x: this.x,
            y: this.y,
            source
        });
        
        // Emit global death event for game systems
        if (window.game && window.game.eventBus) {
            window.game.eventBus.emit('combat:entity_death', {
                entity: this,
                x: this.x,
                y: this.y,
                source
            });
        }
        
        this.destroy();
    }

    heal(amount) {
        const actualHealing = Math.min(amount, this.maxHealth - this.health);
        this.health += actualHealing;
        
        this.emit('heal', { amount: actualHealing });
        
        return actualHealing;
    }

    // Status effects
    addStatusEffect(name, effect) {
        this.statusEffects.set(name, {
            duration: effect.duration || 5000,
            onApply: effect.onApply,
            onUpdate: effect.onUpdate,
            onRemove: effect.onRemove,
            ...effect
        });
        
        if (effect.onApply) {
            effect.onApply(this);
        }
        
        this.emit('statusEffectAdded', { name, effect });
    }

    removeStatusEffect(name) {
        const effect = this.statusEffects.get(name);
        if (effect) {
            if (effect.onRemove) {
                effect.onRemove(this);
            }
            this.statusEffects.delete(name);
            this.emit('statusEffectRemoved', { name, effect });
        }
    }

    hasStatusEffect(name) {
        return this.statusEffects.has(name);
    }

    // Collision detection
    isCollidingWith(other) {
        const dx = this.x - other.x;
        const dy = this.y - other.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (this.collisionRadius + other.collisionRadius);
    }

    getDistanceTo(other) {
        const dx = this.x - other.x;
        const dy = this.y - other.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    getAngleTo(other) {
        return Math.atan2(other.y - this.y, other.x - this.x);
    }

    // Movement
    moveTo(x, y) {
        this.x = x;
        this.y = y;
        this.emit('moved', { x, y });
    }

    moveBy(dx, dy) {
        this.x += dx;
        this.y += dy;
        this.emit('moved', { x: this.x, y: this.y });
    }

    applyForce(fx, fy) {
        this.acceleration.x += fx;
        this.acceleration.y += fy;
    }

    // Event system
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data = {}) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }

    // Lifecycle
    destroy() {
        this.active = false;
        this.visible = false; // Ensure it's not drawn
        this.emit('destroyed');
        
        // Clear all event listeners
        this.eventListeners.clear();
        
        // Clear all status effects
        this.statusEffects.clear();
    }

    // Utility methods
    isAlive() {
        return this.health > 0 && this.active;
    }

    isEnemy(other) {
        return this.team !== other.team && other.team !== 'neutral' && this.team !== 'neutral';
    }

    isAlly(other) {
        return this.team === other.team && this.team !== 'neutral';
    }

    getBounds() {
        return {
            left: this.x - this.width / 2,
            right: this.x + this.width / 2,
            top: this.y - this.height / 2,
            bottom: this.y + this.height / 2
        };
    }

    getCenter() {
        return { x: this.x, y: this.y };
    }

    // Serialization
    toJSON() {
        return {
            id: this.id,
            type: this.constructor.name,
            x: this.x,
            y: this.y,
            team: this.team,
            health: this.health,
            maxHealth: this.maxHealth,
            angle: this.angle,
            createdAt: this.createdAt
        };
    }
}