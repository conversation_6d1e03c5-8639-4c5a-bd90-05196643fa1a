import { createUnitImage, getUnitSVG } from '../GameSVGs.js'; // Import SVG utilities

import { BaseEntity } from './BaseEntity.js';
import { GameData } from '../GameData.js';

export class Unit extends BaseEntity {
    constructor(x, y, type, team = 'neutral') {
        super(x, y, team);
        
        this.isUnit = true; // Mark as unit to prevent double selection indicators
        this.type = type;
        this.name = type;

        
        this.sprite = null; // Initialize sprite property
        if (this.type) {
            this.sprite = createUnitImage(this.type,
                () => {
                    // Optional: force a redraw or set a flag if needed, though drawImage handles it
                },
                () => {
                }
            );
        }
        
        // Get unit data from GameData
        const unitData = GameData.getUnitTypes()[type];
        if (unitData) {
            this.applyUnitData(unitData);
        }
        
        // Movement and pathfinding
        this.speed = 2;
        this.targetX = x;
        this.targetY = y;
        this.path = [];
        this.pathIndex = 0;
        this.isMoving = false;
        this.moveCommand = null;
        
        // Combat properties
        this.damage = 10;
        this.range = 50;
        this.attackCooldown = 0;
        this.maxAttackCooldown = 1000;
        this.target = null;
        this.lastAttackTime = 0;
        
        // Unit-specific properties
        this.experience = 0;
        this.level = 1;
        this.veterancy = 0;
        this.morale = 100;
        
        // Special abilities
        this.abilities = [];
        this.canHeal = false;
        this.canRepair = false;
        this.stealth = false;
        this.deployed = false;
        this.deployable = false;
        
        // Combat bonuses
        this.deployedDamageBonus = 1.0;
        this.deployedRangeBonus = 1.0;
        
        // Formation and group behavior
        this.formation = null;
        this.formationPosition = null;
        this.groupId = null;
        
        // Transport capabilities
        this.transport = 0;
        this.passengers = [];
        this.transportedBy = null;
        
        // Resource gathering
        this.canHarvest = false;
        this.carryingSpice = 0;
        this.maxSpice = 0;
        
        // Visual properties (sprite already initialized above)
        this.animationFrame = 0;
        this.animationSpeed = 0.1;
        
        // Selection state
        this.selected = false;
        this.hovered = false;
        
        // Initialize based on type
        this.initializeUnitType();
    }

    applyUnitData(unitData) {
        this.name = unitData.name || this.type;
        this.health = unitData.health || 100;
        this.maxHealth = unitData.health || 100;
        this.damage = unitData.damage || 10;
        this.range = unitData.range || 50;
        this.speed = unitData.speed || 2;
        this.width = unitData.width || 20;
        this.height = unitData.height || 20;
        this.color = unitData.color || '#888888';
        this.maxAttackCooldown = unitData.attackSpeed || 1000;
        
        // Special properties
        this.canHarvest = unitData.canHarvest || false;
        this.maxSpice = unitData.maxSpice || 0;
        this.transport = unitData.transport || 0;
        this.canHeal = unitData.canHeal || false;
        this.canRepair = unitData.canRepair || false;
        this.stealth = unitData.stealth || false;
        this.deployable = unitData.deployable || false;
    }

    initializeUnitType() {
        // Set team-specific colors
        switch (this.team) {
            case 'player':
                this.color = '#0066CC';
                break;
            case 'enemy':
                this.color = '#CC0000';
                break;
            case 'neutral':
                this.color = '#CCCC00';
                break;
        }
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        // Update movement
        this.updateMovement(deltaTime);
        
        // Update combat
        this.updateCombat(deltaTime);
        
        // Update abilities
        this.updateAbilities(deltaTime);
        
        // Update animation
        this.updateAnimation(deltaTime);
        
        // Update cooldowns
        this.updateCooldowns(deltaTime);
    }

    updateMovement(deltaTime) {
        if (!this.isMoving || this.path.length === 0) return;
        
        const currentTarget = this.path[this.pathIndex];
        if (!currentTarget) return;
        
        const dx = currentTarget.x - this.x;
        const dy = currentTarget.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 5) {
            // Reached current waypoint
            this.pathIndex++;
            if (this.pathIndex >= this.path.length) {
                // Reached final destination
                this.isMoving = false;
                this.path = [];
                this.pathIndex = 0;
                this.emit('moveComplete');
            }
        } else {
            // Move towards current waypoint
            const moveDistance = this.speed * deltaTime;
            const moveX = (dx / distance) * Math.min(moveDistance, distance);
            const moveY = (dy / distance) * Math.min(moveDistance, distance);
            
            this.x += moveX;
            this.y += moveY;
            
            // Update rotation to face movement direction
            this.angle = Math.atan2(dy, dx);
            this.rotation = this.angle; // Ensure both angle and rotation are set
        }
    }

    updateCombat(deltaTime) {
        if (this.attackCooldown > 0) {
            this.attackCooldown -= deltaTime * 1000;
        }
        
        // Auto-attack if we have a target
        if (this.target && this.canAttack()) {
            this.performAttack(this.target);
        }
        
        // Auto-attack aiTarget set by AI
        if (this.aiTarget && this.canAttack()) {
            const distance = this.getDistanceTo(this.aiTarget);
            if (distance <= this.range) {
                this.performAttack(this.aiTarget);
            } else if (this.state === 'attacking') {
                // Move towards target if not in range
                this.moveTo(this.aiTarget.x, this.aiTarget.y);
            }
        }
        
        // Auto-attack attackTarget property set by AI
        if (this.attackTarget && this.canAttack()) {
            const distance = this.getDistanceTo(this.attackTarget);
            if (distance <= this.range) {
                this.performAttack(this.attackTarget);
            } else if (this.state === 'attacking') {
                // Move towards target if not in range
                this.moveTo(this.attackTarget.x, this.attackTarget.y);
            }
        }
        
        // Auto-find enemies if idle and has damage
        if (this.damage > 0 && this.state === 'idle' && !this.aiTarget && this.team !== 'player') {
            const nearbyEnemy = this.findNearbyEnemy(this.range * 2);
            if (nearbyEnemy) {
                this.aiTarget = nearbyEnemy;
                this.state = 'attacking';
            }
        }
    }

    updateAbilities(deltaTime) {
        // Update ability cooldowns
        this.abilities.forEach(ability => {
            if (ability.cooldown > 0) {
                ability.cooldown -= deltaTime * 1000;
            }
        });
    }

    updateAnimation(deltaTime) {
        if (this.isMoving) {
            this.animationFrame += this.animationSpeed * deltaTime;
            if (this.animationFrame >= 1) {
                this.animationFrame = 0;
            }
        }
    }

    updateCooldowns(deltaTime) {
        // Update various cooldowns
        if (this.deployCooldown > 0) {
            this.deployCooldown -= deltaTime * 1000;
        }
        if (this.healCooldown > 0) {
            this.healCooldown -= deltaTime * 1000;
        }
        if (this.repairCooldown > 0) {
            this.repairCooldown -= deltaTime * 1000;
        }
        if (this.stealthCooldown > 0) {
            this.stealthCooldown -= deltaTime * 1000;
        }
    }

    // Movement commands
    moveTo(x, y, path = null) {
        this.targetX = x;
        this.targetY = y;
        
        if (path && path.length > 0) {
            this.path = path;
            this.pathIndex = 0;
            this.isMoving = true;
        } else {
            // Direct movement
            this.path = [{ x, y }];
            this.pathIndex = 0;
            this.isMoving = true;
        }
        
        this.emit('moveStarted', { x, y });
    }

    stop() {
        this.isMoving = false;
        this.path = [];
        this.pathIndex = 0;
        this.target = null;
        this.emit('stopped');
    }

    // Combat methods
    canAttack() {
        return this.damage > 0 && this.attackCooldown <= 0;
    }

    performAttack(target) {
        if (!this.canAttack() || !target || !target.isAlive()) return false;
        
        const distance = this.getDistanceTo(target);
        const effectiveRange = this.range * (this.deployedRangeBonus || 1);
        
        if (distance > effectiveRange) {
            console.log(`🎯 UNIT DEBUG: Target out of range - distance: ${distance.toFixed(1)}, range: ${effectiveRange.toFixed(1)}`);
            return false;
        }
        
        // Use CombatSystem if available
        if (window.game && window.game.combatSystem) {
            console.log(`⚔️ UNIT DEBUG: Starting combat via CombatSystem - ${this.type || 'unknown'} attacking ${target.type || 'unknown'}`);
            const success = window.game.combatSystem.startCombat(this, target);
            if (success) {
                this.attackCooldown = this.maxAttackCooldown;
                this.lastAttackTime = Date.now();
                
                // Face target
                this.angle = this.getAngleTo(target);
                
                this.emit('attacked', { target });
                return true;
            }
        } else {
            console.warn(`⚠️ UNIT DEBUG: CombatSystem not available, falling back to direct damage`);
            // Fallback to direct damage (legacy behavior)
            const actualDamage = target.takeDamage(this.damage, this);
            this.attackCooldown = this.maxAttackCooldown;
            this.lastAttackTime = Date.now();
            
            // Face target
            this.angle = this.getAngleTo(target);
            
            this.emit('attacked', { target, damage: actualDamage });
            
            // Gain experience
            if (actualDamage > 0) {
                this.gainExperience(Math.floor(actualDamage / 10));
            }
            
            return true;
        }
        
        return false;
    }

    setTarget(target) {
        this.target = target;
        this.emit('targetSet', { target });
    }

    // Experience and leveling
    gainExperience(amount) {
        this.experience += amount;
        const experienceToNext = this.getExperienceToNext();
        
        if (this.experience >= experienceToNext) {
            this.levelUp();
        }
        
        this.emit('experienceGained', { amount, total: this.experience });
    }

    getExperienceToNext() {
        return this.level * 100;
    }

    levelUp() {
        this.level++;
        this.experience = 0;
        
        // Improve stats
        this.maxHealth += 10;
        this.health = this.maxHealth; // Full heal on level up
        this.damage += 2;
        
        // Check for veterancy
        if (this.level >= 3 && !this.veteran) {
            this.veteran = true;
            this.damage += 5;
            this.maxHealth += 20;
            this.health = this.maxHealth;
        }
        
        this.emit('levelUp', { level: this.level });
    }

    // Special abilities
    deploy() {
        if (!this.deployable || this.deployCooldown > 0) return false;
        
        this.deployed = !this.deployed;
        this.deployCooldown = 5000; // 5 second cooldown
        
        if (this.deployed) {
            // Deployed units get bonuses but can't move
            this.canMove = false;
            this.deployedDamageBonus = 1.5;
            this.deployedRangeBonus = 1.3;
            this.speed *= 0.1; // Much slower when deployed
        } else {
            // Undeployed units can move but lose bonuses
            this.canMove = true;
            this.deployedDamageBonus = 1.0;
            this.deployedRangeBonus = 1.0;
            this.speed /= 0.1; // Restore speed
        }
        
        this.emit('deployed', { deployed: this.deployed });
        return true;
    }

    heal(target) {
        if (!this.canHeal || this.healCooldown > 0) return false;
        if (!target || target.health >= target.maxHealth) return false;
        
        const healAmount = 20;
        const actualHealing = target.heal(healAmount);
        this.healCooldown = 3000; // 3 second cooldown
        
        this.emit('healed', { target, amount: actualHealing });
        return true;
    }

    repair(target) {
        if (!this.canRepair || this.repairCooldown > 0) return false;
        if (!target || target.health >= target.maxHealth) return false;
        
        const repairAmount = 15;
        const actualRepair = target.heal(repairAmount);
        this.repairCooldown = 2000; // 2 second cooldown
        
        this.emit('repaired', { target, amount: actualRepair });
        return true;
    }

    toggleStealth() {
        if (!this.stealth || this.stealthCooldown > 0) return false;
        
        this.visible = !this.visible;
        this.stealthCooldown = 10000; // 10 second cooldown
        
        this.emit('stealthToggled', { stealthed: !this.visible });
        return true;
    }

    // Transport methods
    loadPassenger(unit) {
        if (this.passengers.length >= this.transport) return false;
        if (unit.transportedBy) return false;
        
        this.passengers.push(unit);
        unit.transportedBy = this;
        unit.visible = false;
        
        this.emit('passengerLoaded', { passenger: unit });
        return true;
    }

    unloadPassenger(index = 0) {
        if (index >= this.passengers.length) return null;
        
        const passenger = this.passengers.splice(index, 1)[0];
        passenger.transportedBy = null;
        passenger.visible = true;
        passenger.x = this.x + 30; // Offset from transport
        passenger.y = this.y + 30;
        
        this.emit('passengerUnloaded', { passenger });
        return passenger;
    }

    unloadAllPassengers() {
        const unloaded = [];
        while (this.passengers.length > 0) {
            const passenger = this.unloadPassenger(0);
            if (passenger) unloaded.push(passenger);
        }
        return unloaded;
    }

    // Resource gathering
    harvestSpice(amount) {
        if (!this.canHarvest) return 0;
        
        const canCarry = this.maxSpice - this.carryingSpice;
        const actualAmount = Math.min(amount, canCarry);
        
        this.carryingSpice += actualAmount;
        this.emit('spiceHarvested', { amount: actualAmount, total: this.carryingSpice });
        
        return actualAmount;
    }

    deliverSpice() {
        const delivered = this.carryingSpice;
        this.carryingSpice = 0;
        this.emit('spiceDelivered', { amount: delivered });
        return delivered;
    }

    // Drawing
    drawEntity(ctx) {
        
        if (this.sprite && this.sprite.complete && this.sprite.naturalWidth !== 0) {
            // Sprite is loaded and valid, draw it
            if (this.deployed) {
                // Potentially draw a different sprite or add overlay for deployed state
                ctx.drawImage(this.sprite, -this.width / 2, -this.height / 2, this.width, this.height);
                ctx.strokeStyle = '#FFFF00'; // Example: yellow border for deployed
                ctx.lineWidth = 2;
                ctx.strokeRect(-this.width / 2, -this.height / 2, this.width, this.height);
            } else {
                ctx.drawImage(this.sprite, -this.width / 2, -this.height / 2, this.width, this.height);
            }
        } else if (this.type) { // Fallback if sprite is not loaded or type is invalid (but not undefined)
            // Draw placeholder rectangle if sprite isn't ready or if there was an issue loading
            // This helps visualize units even if SVGs fail for some reason.
            ctx.fillStyle = this.color;
            if (this.deployed) {
                ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
                ctx.strokeStyle = '#FFFF00';
                ctx.lineWidth = 2;
                ctx.strokeRect(-this.width / 2, -this.height / 2, this.width, this.height);
            } else {
                ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
            }
            // Log if sprite was expected but not drawn
            // if (this.sprite) {
            // }
        } else {
            // Fallback for undefined type - still draw a default placeholder
            ctx.fillStyle = '#FF00FF'; // Magenta for undefined type
            ctx.fillRect(-this.width / 2 || -10, -this.height / 2 || -10, this.width || 20, this.height || 20);
        }
        
        // Draw direction indicator (can be kept if desired)
        if (this.isMoving) {
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(this.width / 2, 0); // Assumes unit faces right by default or angle is applied elsewhere
            ctx.stroke();
        }
        
        // Draw veterancy indicator (can be kept if desired)
        if (this.veteran) {
            ctx.fillStyle = '#FFD700'; // Gold
            ctx.fillRect(this.width / 2 - 6, -this.height / 2 - 4, 4, 2); // Top-right corner
        }
        
        // Draw carrying spice indicator (can be kept if desired)
        if (this.canHarvest && this.carryingSpice > 0) {
            ctx.fillStyle = '#FFA500'; // Orange
            ctx.fillRect(-this.width / 2, this.height / 2 + 2,
                        (this.carryingSpice / this.maxSpice) * this.width, 2); // Bottom edge
        }
    }

    // Serialization
    toJSON() {
        return {
            ...super.toJSON(),
            type: this.type,
            level: this.level,
            experience: this.experience,
            veteran: this.veteran,
            deployed: this.deployed,
            carryingSpice: this.carryingSpice,
            passengers: this.passengers.map(p => p.id)
        };
    }
    
    // Enemy detection method
    findNearbyEnemy(searchRange) {
        if (!window.game || !window.game.gameState) return null;
        
        let nearestEnemy = null;
        let minDistance = searchRange;
        
        // Check all teams except our own
        Object.keys(window.game.gameState.teams).forEach(teamId => {
            if (teamId === this.team) return; // Skip our own team
            
            const enemyTeam = window.game.gameState.teams[teamId];
            
            // Check enemy units
            if (enemyTeam.units) {
                enemyTeam.units.forEach(enemyUnit => {
                    if (enemyUnit.health <= 0) return;
                    
                    const distance = this.getDistanceTo(enemyUnit);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestEnemy = enemyUnit;
                    }
                });
            }
            
            // Check enemy buildings
            if (enemyTeam.buildings) {
                enemyTeam.buildings.forEach(enemyBuilding => {
                    if (enemyBuilding.health <= 0 || enemyBuilding.constructionProgress < 100) return;
                    
                    const distance = this.getDistanceTo(enemyBuilding);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestEnemy = enemyBuilding;
                    }
                });
            }
        });
        
        return nearestEnemy;
    }
}