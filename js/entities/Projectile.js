import { BaseEntity } from './BaseEntity.js';

export class Projectile extends BaseEntity {
    constructor(game, source, targetEntity, startX, startY, targetX, targetY, speed = 300, range = 500, damage = 10, type = 'bullet', visual = {}, soundFire = null, soundHit = null, onHitCallback = null, onExpireCallback = null, options = {}) {
        super(startX, startY, source?.team || 'neutral');
        
        this.game = game;
        this.type = type;
        this.damage = damage;
        this.source = source;
        this.sourceId = source?.id;
        this.targetEntity = targetEntity;
        
        // Target and movement
        this.targetX = targetX;
        this.targetY = targetY;
        this.speed = speed;
        this.range = range;
        this.traveled = 0;
        
        // Callbacks and options
        this.onHitCallback = onHitCallback;
        this.onExpireCallback = onExpireCallback;
        this.options = options || {};
        
        // Visual and sound properties
        this.visual = visual || {};
        this.soundFire = soundFire;
        this.soundHit = soundHit;
        
        // Calculate direction
        const dx = targetX - startX;
        const dy = targetY - startY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            this.velocityX = (dx / distance) * this.speed;
            this.velocityY = (dy / distance) * this.speed;
            this.angle = Math.atan2(dy, dx);
        } else {
            this.velocityX = 0;
            this.velocityY = 0;
            this.angle = 0;
        }
        
        // Projectile properties
        this.piercing = false;
        this.explosive = false;
        this.explosionRadius = 0;
        this.homing = false;
        this.homingTarget = null;
        this.homingStrength = 0;
        
        // Visual properties
        this.width = this.visual.size || 4;
        this.height = this.visual.size || 4;
        this.color = this.visual.color || '#FFFF00';
        this.trail = [];
        this.maxTrailLength = 5;
        
        // Collision
        this.collisionRadius = 2;
        this.hasHit = false;
        this.hitTargets = new Set(); // For piercing projectiles
        
        // Lifetime
        this.maxLifetime = 5000; // 5 seconds
        this.lifetime = 0;
        
        // Initialize based on type
        this.initializeProjectileType();
    }

    initializeProjectileType() {
        switch (this.type) {
            case 'bullet':
                // If a visual color was provided via the constructor's 'visual' argument,
                // it's already set in this.color. Otherwise, default to #FFFF00.
                if (!(this.visual && typeof this.visual.color !== 'undefined')) {
                    this.color = '#FFFF00';
                }

                // If the speed passed to the constructor is the generic default for that parameter (300),
                // then apply the bullet-specific default speed (120).
                // Otherwise, the speed passed to the constructor (e.g., 200 from CombatSystem) is kept.
                // Note: this.speed here refers to the value set from the constructor's speed argument.
                const constructorDefaultSpeed = 300; // Default value of 'speed' param in constructor
                if (this.speed === constructorDefaultSpeed) {
                    this.speed = 120;
                }
                // Visual size (this.width, this.height) is already set by the constructor
                // based on this.visual.size or defaults to 4.

                // Increase trail length for better visibility
                this.maxTrailLength = 8;
                break;
                
            case 'rocket':
                this.color = '#FF4500';
                // Reduce speed by 60%: was 200, now 80
                this.speed = 80;
                this.explosive = true;
                this.explosionRadius = 30;
                this.width = 6;
                this.height = 12;
                this.maxTrailLength = 12;
                break;
                
            case 'laser':
                this.color = '#FF0000';
                // Reduce speed by 75%: was 800, now 200
                this.speed = 200;
                this.piercing = true;
                this.width = 2;
                this.height = 2;
                this.maxTrailLength = 6;
                break;
                
            case 'plasma':
                this.color = '#00FFFF';
                // Reduce speed by 60%: was 250, now 100
                this.speed = 100;
                this.explosive = true;
                this.explosionRadius = 20;
                this.maxTrailLength = 10;
                break;
                
            case 'missile':
                this.color = '#FF6600';
                // Reduce speed by 50%: was 150, now 75
                this.speed = 75;
                this.explosive = true;
                this.explosionRadius = 40;
                this.homing = true;
                this.homingStrength = 0.1;
                this.width = 8;
                this.height = 16;
                this.maxTrailLength = 15;
                break;
                
            case 'artillery':
                this.color = '#8B4513';
                // Reduce speed by 50%: was 100, now 50
                this.speed = 50;
                this.explosive = true;
                this.explosionRadius = 50;
                this.width = 10;
                this.height = 10;
                this.maxTrailLength = 20;
                break;
                
            case 'energy':
                this.color = '#9932CC';
                // Reduce speed by 67%: was 600, now 200
                this.speed = 200;
                this.piercing = true;
                this.maxTrailLength = 8;
                break;
        }
    }

    update(deltaTime) {
        this.lifetime += deltaTime * 1000;
        
        // Check lifetime
        if (this.lifetime > this.maxLifetime) {
            if (this.onExpireCallback) {
                this.onExpireCallback(this);
            }
            this.destroy();
            return false;
        }
        
        // Update homing behavior
        if (this.homing && this.homingTarget) {
            this.updateHoming(deltaTime);
        }
        
        // Update position
        const moveDistance = this.speed * deltaTime;
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;
        this.traveled += moveDistance;
        
        // Update trail
        this.updateTrail();
        
        // Check range
        if (this.traveled > this.range) {
            if (this.onExpireCallback) {
                this.onExpireCallback(this);
            }
            this.destroy();
            return false;
        }
        
        // Check for collisions with entities
        if (this.game && this.game.gameState) {
            this.checkEntityCollisions();
        }
        
        // Update angle based on velocity
        this.angle = Math.atan2(this.velocityY, this.velocityX);
        
        super.update(deltaTime);
        return true;
    }

    updateHoming(deltaTime) {
        if (!this.homingTarget || !this.homingTarget.isAlive()) {
            this.homingTarget = null;
            return;
        }
        
        // Calculate direction to target
        const dx = this.homingTarget.x - this.x;
        const dy = this.homingTarget.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            const targetVelX = (dx / distance) * this.speed;
            const targetVelY = (dy / distance) * this.speed;
            
            // Gradually adjust velocity towards target
            this.velocityX += (targetVelX - this.velocityX) * this.homingStrength;
            this.velocityY += (targetVelY - this.velocityY) * this.homingStrength;
            
            // Normalize velocity to maintain speed
            const currentSpeed = Math.sqrt(this.velocityX * this.velocityX + this.velocityY * this.velocityY);
            if (currentSpeed > 0) {
                this.velocityX = (this.velocityX / currentSpeed) * this.speed;
                this.velocityY = (this.velocityY / currentSpeed) * this.speed;
            }
        }
    }

    updateTrail() {
        // Add current position to trail
        this.trail.push({ x: this.x, y: this.y });
        
        // Limit trail length
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }
    }

    // Collision detection
    checkCollision(target) {
        if (!target || !target.isAlive()) return false;
        if (target === this.source) return false;
        if (this.hitTargets.has(target.id)) return false;
        
        const dx = this.x - target.x;
        const dy = this.y - target.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        return distance < (this.collisionRadius + target.collisionRadius);
    }

    hit(target) {
        if (!target || this.hitTargets.has(target.id)) return false;
        
        // Use combat system for damage calculation if available
        let actualDamage;
        if (this.game && this.game.combatSystem) {
            actualDamage = this.game.combatSystem.applyDamage(target, this.damage, this.source);
        } else {
            // Fallback to direct damage
            actualDamage = target.takeDamage(this.damage, this.source);
        }
        
        // Mark as hit for piercing projectiles
        this.hitTargets.add(target.id);
        
        // Execute hit callback if provided
        if (this.onHitCallback) {
            this.onHitCallback(target, actualDamage, this);
        }
        
        // Create hit effects
        this.createHitEffects(target);
        
        this.emit('hit', { target, damage: actualDamage });
        
        // Destroy projectile unless it's piercing
        if (!this.piercing) {
            this.hasHit = true;
            
            // Create explosion if explosive
            if (this.explosive) {
                this.explode();
            }
            
            this.destroy();
        }
        
        return true;
    }

    explode() {
        this.emit('explode', {
            x: this.x,
            y: this.y,
            radius: this.explosionRadius,
            damage: this.damage,
            source: this.source
        });
    }

    createHitEffects(target) {
        // Emit event for visual effects system to handle
        this.emit('hitEffects', {
            x: this.x,
            y: this.y,
            target,
            projectileType: this.type
        });
    }

    // Set homing target
    setHomingTarget(target) {
        if (this.homing) {
            this.homingTarget = target;
        }
    }

    // Drawing
    draw(ctx, camera_unused_for_main_projectile = { x: 0, y: 0 }) { // Parameter kept for signature compatibility, but clarified.
        if (!this.visible) return;
        
        ctx.save();
        
        // Draw trail: Trail points are in world coordinates.
        // The context (ctx) is already transformed by the camera view by RenderingSystem.
        // So, drawing the trail at its world coordinates will position it correctly.
        this.drawTrail(ctx); // Pass ctx which is camera-transformed.
        
        // Draw projectile body:
        // Translate to the projectile's current world position.
        // Since ctx is already camera-transformed, this effectively positions it on screen.
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle);
        
        // drawProjectile draws relative to the new origin (0,0) of the translated & rotated context.
        this.drawProjectile(ctx);
        
        ctx.restore();
    }

    drawTrail(ctx) { // No camera parameter needed, ctx is already camera-transformed.
        if (this.trail.length < 2) return;
        
        ctx.save(); // Save context state before changing alpha, etc.
        
        for (let i = 1; i < this.trail.length; i++) {
            const point = this.trail[i];       // World coordinates
            const prevPoint = this.trail[i - 1]; // World coordinates
            
            // Calculate alpha and line width for tapering effect
            const trailAlpha = (i / this.trail.length) * 0.8;
            // Trail width relative to projectile size, ensuring a minimum width
            const lineWidth = Math.max(1, (i / this.trail.length) * (this.width * 0.5));
            
            ctx.globalAlpha = trailAlpha;
            ctx.strokeStyle = this.color; // Use projectile's color for the trail
            ctx.lineWidth = lineWidth;
            ctx.lineCap = 'round'; // Smooth line ends
            
            ctx.beginPath();
            // moveTo and lineTo use world coordinates. Since ctx is already camera-transformed by RenderingSystem,
            // these will be rendered correctly on screen.
            ctx.moveTo(prevPoint.x, prevPoint.y);
            ctx.lineTo(point.x, point.y);
            ctx.stroke();
        }
        
        ctx.restore(); // Restore context state (alpha, strokeStyle, etc.)
    }

    drawProjectile(ctx) {
        // Add glow effect for better visibility
        ctx.save();
        
        switch (this.type) {
            case 'bullet':
            case 'laser':
            case 'energy':
                // Enhanced circle with glow
                const glowGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width);
                glowGradient.addColorStop(0, this.color);
                glowGradient.addColorStop(0.5, this.color + '80');
                glowGradient.addColorStop(1, this.color + '20');
                
                ctx.fillStyle = glowGradient;
                ctx.beginPath();
                ctx.arc(0, 0, this.width, 0, Math.PI * 2);
                ctx.fill();
                
                // Inner bright core
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
                ctx.fill();
                break;
                
            case 'rocket':
            case 'missile':
                // Enhanced rocket shape with glow
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 8;
                ctx.fillStyle = this.color;
                ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
                
                // Enhanced rocket flame
                ctx.fillStyle = '#FF0000';
                ctx.beginPath();
                ctx.moveTo(-this.width / 2, -3);
                ctx.lineTo(-this.width / 2 - 12, 0);
                ctx.lineTo(-this.width / 2, 3);
                ctx.fill();
                
                // Flame glow
                ctx.fillStyle = '#FF4500';
                ctx.beginPath();
                ctx.moveTo(-this.width / 2, -2);
                ctx.lineTo(-this.width / 2 - 8, 0);
                ctx.lineTo(-this.width / 2, 2);
                ctx.fill();
                break;
                
            case 'artillery':
                // Enhanced artillery shell with glow
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 6;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
                ctx.fill();
                
                // Spinning effect
                ctx.strokeStyle = '#654321';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(0, 0, this.width / 2 - 1, 0, Math.PI * 2);
                ctx.stroke();
                break;
                
            case 'plasma':
                // Enhanced glowing plasma ball
                const plasmaGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width);
                plasmaGradient.addColorStop(0, this.color);
                plasmaGradient.addColorStop(0.4, this.color + 'CC');
                plasmaGradient.addColorStop(0.7, this.color + '80');
                plasmaGradient.addColorStop(1, this.color + '20');
                
                ctx.fillStyle = plasmaGradient;
                ctx.beginPath();
                ctx.arc(0, 0, this.width, 0, Math.PI * 2);
                ctx.fill();
                
                // Inner core
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
                ctx.fill();
                break;
                
            default:
                // Enhanced default rectangle with glow
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 4;
                ctx.fillStyle = this.color;
                ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
                break;
        }
        
        ctx.restore();
    }

    // Utility methods
    getDistanceToTarget() {
        const dx = this.targetX - this.x;
        const dy = this.targetY - this.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    isExpired() {
        return this.lifetime > this.maxLifetime || this.traveled > this.range || this.hasHit;
    }

    // Serialization
    toJSON() {
        return {
            ...super.toJSON(),
            type: this.type,
            damage: this.damage,
            sourceId: this.sourceId,
            targetX: this.targetX,
            targetY: this.targetY,
            speed: this.speed,
            traveled: this.traveled,
            explosive: this.explosive,
            explosionRadius: this.explosionRadius,
            piercing: this.piercing,
            homing: this.homing
        };
    }

    /**
     * Check for collisions with all entities in the game
     */
    checkEntityCollisions() {
        if (this.hasHit && !this.piercing) return;
        
        const allTargets = [
            ...this.game.gameState.entities.units,
            ...this.game.gameState.entities.buildings
        ];
        
        for (const target of allTargets) {
            if (this.checkCollision(target)) {
                console.log(`🎯 PROJECTILE DEBUG: Hit detected - ${this.type} hit ${target.type || 'unknown'} at (${target.x}, ${target.y})`);
                if (this.hit(target)) {
                    break; // Stop checking if projectile was destroyed
                }
            }
        }
    }
}