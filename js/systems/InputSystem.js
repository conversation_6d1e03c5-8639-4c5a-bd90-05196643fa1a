/**
 * InputSystem - Handles all user input (mouse, keyboard, touch)
 * Manages input events, coordinate conversion, and command dispatch
 */
export class InputSystem {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        
        // Input state
        this.keys = new Set();
        this.mouse = {
            x: 0,
            y: 0,
            buttons: new Set(),
            wheel: 0
        };
        
        // Selection state
        this.selection = {
            isSelecting: false,
            startX: 0,
            startY: 0,
            endX: 0,
            endY: 0,
            dragThreshold: 5
        };
        
        // Zoom settings
        this.zoom = {
            min: 0.25,
            max: 3.0,
            step: 0.02, // Made 5x slower (was 0.1)
            smoothing: 0.8
        };
        
        // Canvas references
        this.gameCanvas = document.getElementById('gameCanvas');
        this.gameAreaEl = document.getElementById('gameArea');
        
        // Input settings
        this.settings = {
            mouseSensitivity: 1.0,
            keyRepeatDelay: 200,
            doubleClickTime: 300,
            dragThreshold: 5
        };
        
        // Double-click detection
        this.lastClickTime = 0;
        this.lastClickPos = { x: 0, y: 0 };
        
        this.initializeEventListeners();
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Mouse events on game canvas
        this.gameCanvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.gameCanvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.gameCanvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.gameCanvas.addEventListener('wheel', (e) => this.handleWheel(e));
        this.gameCanvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Touch events for mobile support
        this.gameCanvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.gameCanvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.gameCanvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // Window events
        window.addEventListener('blur', () => this.handleWindowBlur());
        window.addEventListener('resize', () => this.handleWindowResize());
        
        this.eventBus.emit('input:initialized');
    }

    /**
     * Handle keyboard key down events
     */
    handleKeyDown(event) {
        const key = event.key.toLowerCase();
        
        // Prevent default for game keys
        if (this.isGameKey(key)) {
            event.preventDefault();
        }
        
        // Add to pressed keys
        this.keys.add(key);
        
        // Handle single-press commands
        this.handleKeyCommand(key, event);
        
        this.eventBus.emit('input:key_down', { key, event });
    }

    /**
     * Handle keyboard key up events
     */
    handleKeyUp(event) {
        const key = event.key.toLowerCase();
        this.keys.delete(key);
        
        this.eventBus.emit('input:key_up', { key, event });
    }

    /**
     * Handle key commands
     */
    handleKeyCommand(key, event) {
        // Pause/unpause
        if (key === 'p') {
            this.gameState.togglePause();
            return;
        }
        
        // Control groups
        if (event.ctrlKey && key >= '1' && key <= '9') {
            const groupNumber = parseInt(key);
            this.eventBus.emit('input:create_control_group', { groupNumber });
            return;
        }
        
        if (key >= '1' && key <= '9') {
            const groupNumber = parseInt(key);
            this.eventBus.emit('input:select_control_group', { groupNumber });
            return;
        }
        
        // Selection commands
        if (key === 'a' && event.ctrlKey) {
            this.eventBus.emit('input:select_all_units');
            return;
        }
        
        // Unit commands
        if (key === 's') {
            this.eventBus.emit('input:stop_units');
            return;
        }
        
        if (key === 'h') {
            this.eventBus.emit('input:center_on_selection');
            return;
        }
        
        // Building hotkeys
        if (key === 'b') {
            this.eventBus.emit('input:building_hotkey', { building: 'barracks' });
            return;
        }
        
        if (key === 'f') {
            this.eventBus.emit('input:building_hotkey', { building: 'factory' });
            return;
        }
        
        // Debug commands
        if (key === 'f1') {
            this.eventBus.emit('input:show_help');
            return;
        }
        
        if (key === 'f2') {
            this.eventBus.emit('input:toggle_debug');
            return;
        }
    }

    /**
     * Check if a key is used for game controls
     */
    isGameKey(key) {
        const gameKeys = [
            'w', 'a', 's', 'd', 'p', 'h', 'b', 'f',
            'arrowup', 'arrowdown', 'arrowleft', 'arrowright',
            '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'f1', 'f2', 'f3', 'f4', 'escape'
        ];
        return gameKeys.includes(key);
    }

    /**
     * Handle mouse down events
     */
    handleMouseDown(event) {
        const button = event.button;
        this.mouse.buttons.add(button);
        
        const coords = this.getCanvasCoords(event);
        this.mouse.x = coords.x;
        this.mouse.y = coords.y;
        
        // Check for double-click
        const now = Date.now();
        const isDoubleClick = (now - this.lastClickTime) < this.settings.doubleClickTime &&
                             Math.abs(coords.x - this.lastClickPos.x) < this.settings.dragThreshold &&
                             Math.abs(coords.y - this.lastClickPos.y) < this.settings.dragThreshold;
        
        if (button === 0) { // Left click
            if (isDoubleClick) {
                this.handleDoubleClick(coords);
            } else {
                this.handleLeftClick(coords, event);
            }
            
            this.lastClickTime = now;
            this.lastClickPos = coords;
        } else if (button === 2) { // Right click
            this.handleRightClick(coords, event);
        }
        
        this.eventBus.emit('input:mouse_down', { 
            button, 
            coords, 
            gameCoords: this.getGameCoords(coords.x, coords.y),
            isDoubleClick 
        });
    }

    /**
     * Handle mouse move events
     */
    handleMouseMove(event) {
        const coords = this.getCanvasCoords(event);
        this.mouse.x = coords.x;
        this.mouse.y = coords.y;
        
        // Update ghost building position if in building mode
        if (this.gameState.selection.buildingMode) {
            const gameCoords = this.getGameCoords(coords.x, coords.y);
            this.eventBus.emit('input:update_ghost_building', { 
                x: gameCoords.x, 
                y: gameCoords.y 
            });
        }
        
        // Handle selection rectangle
        if (this.selection.isSelecting) {
            this.selection.endX = coords.x;
            this.selection.endY = coords.y;
            
            // Only emit drag event if we've moved beyond threshold
            const dragDistance = Math.sqrt(
                Math.pow(this.selection.endX - this.selection.startX, 2) +
                Math.pow(this.selection.endY - this.selection.startY, 2)
            );
            
            if (dragDistance > this.selection.dragThreshold) {
                const startGameCoords = this.getGameCoords(this.selection.startX, this.selection.startY);
                const endGameCoords = this.getGameCoords(this.selection.endX, this.selection.endY);
                
                this.eventBus.emit('input:selection_drag', {
                    startX: this.selection.startX,
                    startY: this.selection.startY,
                    endX: this.selection.endX,
                    endY: this.selection.endY,
                    startGameX: startGameCoords.x,
                    startGameY: startGameCoords.y,
                    endGameX: endGameCoords.x,
                    endGameY: endGameCoords.y,
                    isDragging: true
                });
            }
        }
        
        this.eventBus.emit('input:mouse_move', { 
            coords, 
            gameCoords: this.getGameCoords(coords.x, coords.y) 
        });
    }

    /**
     * Handle mouse up events
     */
    handleMouseUp(event) {
        const button = event.button;
        this.mouse.buttons.delete(button);
        
        const coords = this.getCanvasCoords(event);
        
        if (button === 0 && this.selection.isSelecting) {
            // End selection
            this.selection.isSelecting = false;
            
            const dragDistance = Math.sqrt(
                Math.pow(coords.x - this.selection.startX, 2) +
                Math.pow(coords.y - this.selection.startY, 2)
            );
            
            const startGameCoords = this.getGameCoords(this.selection.startX, this.selection.startY);
            const endGameCoords = this.getGameCoords(coords.x, coords.y);
            
            // Determine if this was a drag selection or single click
            const isDragSelection = dragDistance > this.selection.dragThreshold;
            
            this.eventBus.emit('input:selection_end', {
                startX: this.selection.startX,
                startY: this.selection.startY,
                endX: coords.x,
                endY: coords.y,
                startGameX: startGameCoords.x,
                startGameY: startGameCoords.y,
                endGameX: endGameCoords.x,
                endGameY: endGameCoords.y,
                isDragSelection,
                selectionRect: isDragSelection ? {
                    left: Math.min(startGameCoords.x, endGameCoords.x),
                    right: Math.max(startGameCoords.x, endGameCoords.x),
                    top: Math.min(startGameCoords.y, endGameCoords.y),
                    bottom: Math.max(startGameCoords.y, endGameCoords.y)
                } : null
            });
        }
        
        this.eventBus.emit('input:mouse_up', { 
            button, 
            coords, 
            gameCoords: this.getGameCoords(coords.x, coords.y) 
        });
    }

    /**
     * Handle left click
     */
    handleLeftClick(coords, event) {
        // Check if clicking on UI elements first
        if (this.isClickOnUI(coords)) {
            return;
        }
        
        // Handle building placement
        if (this.gameState.selection.buildingMode) {
            const gameCoords = this.getGameCoords(coords.x, coords.y);
            
            this.eventBus.emit('input:place_building', {
                x: gameCoords.x,
                y: gameCoords.y
            });
            return;
        }
        
        // Start selection
        this.selection.isSelecting = true;
        this.selection.startX = coords.x;
        this.selection.startY = coords.y;
        this.selection.endX = coords.x;
        this.selection.endY = coords.y;
        
        // Check if this is a single click or drag start
        const gameCoords = this.getGameCoords(coords.x, coords.y);
        this.eventBus.emit('input:selection_start', {
            canvasX: coords.x,
            canvasY: coords.y,
            gameX: gameCoords.x,
            gameY: gameCoords.y,
            modifiers: {
                shift: event.shiftKey,
                ctrl: event.ctrlKey
            }
        });
    }

    /**
     * Handle right click
     */
    handleRightClick(coords, event) {
        // Cancel building mode
        if (this.gameState.selection.buildingMode) {
            this.eventBus.emit('input:cancel_building_mode');
            return;
        }
        
        const gameCoords = this.getGameCoords(coords.x, coords.y);
        this.eventBus.emit('input:right_click', {
            x: gameCoords.x,
            y: gameCoords.y,
            canvasCoords: coords
        });
    }

    /**
     * Handle double click
     */
    handleDoubleClick(coords) {
        const gameCoords = this.getGameCoords(coords.x, coords.y);
        this.eventBus.emit('input:double_click', { 
            x: gameCoords.x, 
            y: gameCoords.y 
        });
    }

    /**
     * Handle mouse wheel events
     */
    handleWheel(event) {
        event.preventDefault();
        
        const delta = -event.deltaY;
        this.mouse.wheel = delta;
        
        // Calculate zoom change
        const zoomDirection = delta > 0 ? 1 : -1;
        const currentZoom = this.gameState.world.zoom;
        const newZoom = Math.max(this.zoom.min,
            Math.min(this.zoom.max, currentZoom + (zoomDirection * this.zoom.step)));
        
        // Only zoom if there's a change
        if (Math.abs(newZoom - currentZoom) > 0.01) {
            const mouseCoords = this.getCanvasCoords(event);
            const gameCoords = this.getGameCoords(mouseCoords.x, mouseCoords.y);
            
            // Apply smooth zoom centered on mouse position
            this.applyZoom(newZoom, gameCoords.x, gameCoords.y, mouseCoords.x, mouseCoords.y);
        }
    }
    
    /**
     * Apply zoom transformation centered on a specific point
     */
    applyZoom(newZoom, worldX, worldY, screenX, screenY) {
        const oldZoom = this.gameState.world.zoom;
        const oldViewX = this.gameState.world.viewX;
        const oldViewY = this.gameState.world.viewY;
        
        // Calculate the world position under the mouse cursor before zoom
        const worldMouseX = (screenX / oldZoom) + oldViewX;
        const worldMouseY = (screenY / oldZoom) + oldViewY;
        
        // Apply new zoom
        this.gameState.world.zoom = newZoom;
        
        // Calculate new view position to keep the mouse point stationary
        const newViewX = worldMouseX - (screenX / newZoom);
        const newViewY = worldMouseY - (screenY / newZoom);
        
        this.gameState.world.viewX = newViewX;
        this.gameState.world.viewY = newViewY;
        
        // Constrain view to map bounds
        this.constrainView();
        
        this.eventBus.emit('input:zoom_applied', {
            oldZoom,
            newZoom,
            worldX: worldMouseX,
            worldY: worldMouseY,
            viewX: this.gameState.world.viewX,
            viewY: this.gameState.world.viewY
        });
    }

    /**
     * Handle touch start (mobile support)
     */
    handleTouchStart(event) {
        event.preventDefault();
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const coords = this.getTouchCoords(touch);
            this.handleLeftClick(coords, event);
        }
    }

    /**
     * Handle touch move (mobile support)
     */
    handleTouchMove(event) {
        event.preventDefault();
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const coords = this.getTouchCoords(touch);
            this.handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
        }
    }

    /**
     * Handle touch end (mobile support)
     */
    handleTouchEnd(event) {
        event.preventDefault();
        
        if (event.changedTouches.length === 1) {
            const touch = event.changedTouches[0];
            const coords = this.getTouchCoords(touch);
            this.handleMouseUp({ button: 0, clientX: touch.clientX, clientY: touch.clientY });
        }
    }

    /**
     * Handle window blur (release all keys)
     */
    handleWindowBlur() {
        this.keys.clear();
        this.mouse.buttons.clear();
        this.eventBus.emit('input:window_blur');
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        this.eventBus.emit('input:window_resize');
    }

    /**
     * Get canvas coordinates from mouse event
     */
    getCanvasCoords(event) {
        const rect = this.gameCanvas.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    /**
     * Get touch coordinates
     */
    getTouchCoords(touch) {
        const rect = this.gameCanvas.getBoundingClientRect();
        return {
            x: touch.clientX - rect.left,
            y: touch.clientY - rect.top
        };
    }

    /**
     * Convert canvas coordinates to game world coordinates
     */
    getGameCoords(canvasX, canvasY) {
        const gameX = (canvasX / this.gameState.world.zoom) + this.gameState.world.viewX;
        const gameY = (canvasY / this.gameState.world.zoom) + this.gameState.world.viewY;
        return { x: gameX, y: gameY };
    }

    /**
     * Check if click is on UI elements
     */
    isClickOnUI(coords) {
        // This would check against UI element bounds
        // For now, simple check for right panel
        const canvasRect = this.gameCanvas.getBoundingClientRect();
        return coords.x > canvasRect.width - 200; // Assuming 200px UI panel
    }

    /**
     * Constrain viewport to map bounds
     */
    constrainView() {
        const gameAreaRect = this.gameAreaEl.getBoundingClientRect();
        const viewPortWidth = gameAreaRect.width / this.gameState.world.zoom;
        const viewPortHeight = gameAreaRect.height / this.gameState.world.zoom;
        
        // Allow negative camera positions if viewport is larger than world
        // This enables proper centering when the game world is smaller than the viewport
        const minViewX = viewPortWidth > this.gameState.world.width ?
            (viewPortWidth - this.gameState.world.width) / -2 : 0;
        const minViewY = viewPortHeight > this.gameState.world.height ?
            (viewPortHeight - this.gameState.world.height) / -2 : 0;
        
        this.gameState.world.viewX = Math.max(minViewX,
            Math.min(this.gameState.world.width - viewPortWidth, this.gameState.world.viewX));
        this.gameState.world.viewY = Math.max(minViewY,
            Math.min(this.gameState.world.height - viewPortHeight, this.gameState.world.viewY));
        
        this.eventBus.emit('input:view_constrained', {
            viewX: this.gameState.world.viewX,
            viewY: this.gameState.world.viewY
        });
    }

    /**
     * Update system (called each frame for continuous input)
     */
    update(deltaTime) {
        this.handleContinuousInput(deltaTime);
    }

    /**
     * Handle continuous input (held keys)
     */
    handleContinuousInput(deltaTime) {
        const moveSpeed = 30 / this.gameState.world.zoom;
        let viewChanged = false;
        
        // WASD movement
        if (this.keys.has('w') || this.keys.has('arrowup')) {
            this.gameState.world.viewY -= moveSpeed * deltaTime * 60;
            viewChanged = true;
        }
        if (this.keys.has('s') || this.keys.has('arrowdown')) {
            this.gameState.world.viewY += moveSpeed * deltaTime * 60;
            viewChanged = true;
        }
        if (this.keys.has('a') || this.keys.has('arrowleft')) {
            this.gameState.world.viewX -= moveSpeed * deltaTime * 60;
            viewChanged = true;
        }
        if (this.keys.has('d') || this.keys.has('arrowright')) {
            this.gameState.world.viewX += moveSpeed * deltaTime * 60;
            viewChanged = true;
        }
        
        if (viewChanged) {
            this.constrainView();
            this.eventBus.emit('input:viewport_moved', {
                viewX: this.gameState.world.viewX,
                viewY: this.gameState.world.viewY
            });
        }
    }

    /**
     * Get current input state
     */
    getInputState() {
        return {
            keys: Array.from(this.keys),
            mouse: { ...this.mouse },
            selection: { ...this.selection }
        };
    }

    /**
     * Update input settings
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.eventBus.emit('input:settings_updated', { settings: this.settings });
    }
}