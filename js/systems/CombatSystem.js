/**
 * CombatSystem - Handles all combat-related operations
 * Manages damage calculation, combat mechanics, and battle effects
 */
export class CombatSystem {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        
        // Combat settings
        this.settings = {
            baseDamageMultiplier: 1.0,
            armorEffectiveness: 0.1,
            criticalHitChance: 0.05,
            criticalHitMultiplier: 2.0,
            splashDamageReduction: 0.7,
            maxSplashTargets: 8
        };
        
        // Combat state tracking
        this.combatEvents = [];
        this.activeCombats = new Map();
        
        this.initializeCombatSystem();
    }

    /**
     * Initialize combat system
     */
    initializeCombatSystem() {
        this.eventBus.emit('combat:initialized');
    }

    /**
     * Update combat system
     */
    update(deltaTime) {
        this.processCombatEvents(deltaTime);
        this.updateActiveCombats(deltaTime);
        this.cleanupFinishedCombats();
    }

    /**
     * Process combat events
     */
    processCombatEvents(deltaTime) {
        // Process any queued combat events
        this.combatEvents = this.combatEvents.filter(event => {
            event.timer -= deltaTime;
            if (event.timer <= 0) {
                this.executeCombatEvent(event);
                return false;
            }
            return true;
        });
    }

    /**
     * Execute a combat event
     */
    executeCombatEvent(event) {
        switch (event.type) {
            case 'damage':
                this.applyDamage(event.target, event.damage, event.attacker);
                break;
            case 'heal':
                this.applyHealing(event.target, event.amount, event.healer);
                break;
            case 'splash':
                this.applySplashDamage(event.centerX, event.centerY, event.damage, event.radius, event.attacker);
                break;
        }
    }

    /**
     * Calculate damage between attacker and target
     */
    calculateDamage(attacker, target) {
        let baseDamage = attacker.damage || 10;
        
        // Apply attacker upgrades
        if (attacker.team && this.gameState.teams[attacker.team]) {
            const damageUpgrade = this.gameState.teams[attacker.team].upgrades.damage || 0;
            baseDamage *= (1 + damageUpgrade * 0.2);
        }
        
        // Apply target armor
        let armorReduction = 0;
        if (target.armor) {
            armorReduction = target.armor * this.settings.armorEffectiveness;
        }
        
        // Apply target armor upgrades
        if (target.team && this.gameState.teams[target.team]) {
            const armorUpgrade = this.gameState.teams[target.team].upgrades.armor || 0;
            armorReduction += armorUpgrade * this.settings.armorEffectiveness;
        }
        
        // Calculate final damage
        let finalDamage = baseDamage * (1 - armorReduction) * this.settings.baseDamageMultiplier;
        
        // Check for critical hit
        if (Math.random() < this.settings.criticalHitChance) {
            finalDamage *= this.settings.criticalHitMultiplier;
            this.eventBus.emit('combat:critical_hit', { attacker, target, damage: finalDamage });
        }
        
        // Apply damage type modifiers
        finalDamage = this.applyDamageTypeModifiers(attacker, target, finalDamage);
        
        return Math.max(1, Math.floor(finalDamage));
    }

    /**
     * Apply damage type modifiers
     */
    applyDamageTypeModifiers(attacker, target, damage) {
        // Infantry vs vehicles
        if (attacker.category === 'infantry' && target.category === 'vehicle') {
            damage *= 0.7; // Infantry less effective vs vehicles
        }
        
        // Vehicles vs infantry
        if (attacker.category === 'vehicle' && target.category === 'infantry') {
            damage *= 1.3; // Vehicles more effective vs infantry
        }
        
        // Anti-air vs air units
        if (attacker.antiAir && target.category === 'air') {
            damage *= 2.0; // Anti-air very effective vs air
        }
        
        // Air vs ground
        if (attacker.category === 'air' && target.category !== 'air') {
            damage *= 1.2; // Air units effective vs ground
        }
        
        return damage;
    }

    /**
     * Apply damage to a target
     */
    applyDamage(target, damage, attacker = null) {
        if (!target || target.health <= 0) {
            return 0;
        }
        
        // Apply shield protection if available
        const actualDamage = this.applyShieldDamage(target, damage);
        
        target.health -= actualDamage;
        target.lastAttacker = attacker;
        
        // Create hit effect
        console.log(`💥 COMBAT DEBUG: Hit effect at (${target.x}, ${target.y}), damage: ${actualDamage}`);
        this.eventBus.emit('combat:hit', {
            target,
            damage: actualDamage,
            attacker,
            x: target.x,
            y: target.y
        });
        
        // Award combat XP to attacker
        if (attacker && attacker.team === 'player') {
            this.awardCombatXP(attacker, Math.floor(actualDamage / 5), 'damage_dealt');
        }
        
        // Award survival XP to defender
        if (target.team === 'player') {
            this.awardSurvivalXP(target, actualDamage);
        }
        
        // Check for death
        if (target.health <= 0) {
            this.handleEntityDeath(target, attacker);
        }
        
        return actualDamage;
    }

    /**
     * Apply shield damage reduction
     */
    applyShieldDamage(target, damage) {
        if (!target.shield || target.shield <= 0) {
            return damage;
        }
        
        const shieldAbsorbed = Math.min(target.shield, damage);
        target.shield -= shieldAbsorbed;
        const remainingDamage = damage - shieldAbsorbed;
        
        this.eventBus.emit('combat:shield_hit', {
            target,
            absorbed: shieldAbsorbed,
            remaining: remainingDamage
        });
        
        return remainingDamage;
    }

    /**
     * Apply healing to a target
     */
    applyHealing(target, amount, healer = null) {
        if (!target || target.health <= 0) {
            return 0;
        }
        
        const actualHealing = Math.min(amount, target.maxHealth - target.health);
        target.health += actualHealing;
        
        this.eventBus.emit('combat:healed', {
            target,
            amount: actualHealing,
            healer,
            x: target.x,
            y: target.y
        });
        
        // Award healing XP
        if (healer && healer.team === 'player') {
            this.awardCombatXP(healer, Math.floor(actualHealing / 3), 'healing');
        }
        
        return actualHealing;
    }

    /**
     * Apply splash damage
     */
    applySplashDamage(centerX, centerY, damage, radius, attacker = null) {
        const affectedEntities = [];
        const allTargets = [...this.gameState.entities.units, ...this.gameState.entities.buildings];
        
        allTargets.forEach(entity => {
            if (entity.health <= 0) return;
            
            const distance = Math.sqrt((entity.x - centerX) ** 2 + (entity.y - centerY) ** 2);
            if (distance <= radius) {
                // Calculate damage falloff
                const damageMultiplier = 1 - (distance / radius) * this.settings.splashDamageReduction;
                const finalDamage = Math.floor(damage * damageMultiplier);
                
                if (finalDamage > 0) {
                    affectedEntities.push({
                        entity,
                        damage: finalDamage,
                        distance
                    });
                }
            }
        });
        
        // Sort by distance and limit targets
        affectedEntities.sort((a, b) => a.distance - b.distance);
        const limitedTargets = affectedEntities.slice(0, this.settings.maxSplashTargets);
        
        // Apply damage to all affected entities
        limitedTargets.forEach(({ entity, damage: finalDamage }) => {
            this.applyDamage(entity, finalDamage, attacker);
        });
        
        this.eventBus.emit('combat:splash_damage', {
            centerX,
            centerY,
            radius,
            affectedCount: limitedTargets.length,
            attacker
        });
        
        return limitedTargets;
    }

    /**
     * Handle entity death
     */
    handleEntityDeath(entity, attacker) {
        // Award kill XP to attacker
        if (attacker && attacker.team === 'player') {
            const xpType = entity.isBuilding ? 'kill_building' : 'kill_unit';
            this.awardCombatXP(attacker, entity.maxHealth / 2, xpType);
        }
        
        // Create death effects
        console.log(`☠️ COMBAT DEBUG: Entity death at (${entity.x}, ${entity.y}), type: ${entity.type || 'unknown'}`);
        this.eventBus.emit('combat:entity_death', {
            entity,
            attacker,
            x: entity.x,
            y: entity.y
        });
        
        // Remove from active combats
        this.activeCombats.delete(entity.id);
    }

    /**
     * Award combat experience points
     */
    awardCombatXP(entity, amount, type) {
        this.eventBus.emit('combat:xp_awarded', {
            entity,
            amount,
            type
        });
    }

    /**
     * Award survival experience points
     */
    awardSurvivalXP(entity, damage) {
        const xpAmount = Math.floor(damage / 10);
        this.eventBus.emit('combat:survival_xp', {
            entity,
            amount: xpAmount
        });
    }

    /**
     * Toggle unit deployment state
     */
    toggleDeployment(unit) {
        if (!unit.deployable) {
            return false;
        }
        
        unit.deployed = !unit.deployed;
        
        if (unit.deployed) {
            // Deployed units get damage/range bonus but can't move
            unit.canMove = false;
            unit.deployedDamageBonus = 1.5;
            unit.deployedRangeBonus = 1.3;
        } else {
            // Undeployed units can move but lose bonuses
            unit.canMove = true;
            unit.deployedDamageBonus = 1.0;
            unit.deployedRangeBonus = 1.0;
        }
        
        this.eventBus.emit('combat:unit_deployed', {
            unit,
            deployed: unit.deployed
        });
        
        return true;
    }

    /**
     * Check if unit can attack target
     */
    canAttack(attacker, target) {
        if (!attacker || !target || attacker === target) {
            return false;
        }
        
        // Check if target is dead
        if (target.health <= 0) {
            return false;
        }
        
        // Check team relations
        if (attacker.team === target.team) {
            return false;
        }
        
        // Check if attacker has damage
        if (!attacker.damage || attacker.damage <= 0) {
            return false;
        }
        
        // Check range
        const distance = Math.sqrt((attacker.x - target.x) ** 2 + (attacker.y - target.y) ** 2);
        const attackRange = (attacker.range || 50) * (attacker.deployedRangeBonus || 1);
        
        // Allow some tolerance for close combat to prevent "too close to attack" issues
        const minRange = Math.min(10, attackRange * 0.1); // Minimum 10 units or 10% of range
        
        if (distance > attackRange) {
            console.log(`🎯 COMBAT DEBUG: Target out of range - distance: ${distance.toFixed(1)}, range: ${attackRange.toFixed(1)}`);
            return false;
        }
        
        if (distance < minRange && attacker.range > 100) {
            console.log(`🎯 COMBAT DEBUG: Target too close for ranged attack - distance: ${distance.toFixed(1)}, minRange: ${minRange.toFixed(1)}`);
            return false;
        }
        
        // Check line of sight (simplified)
        if (!this.hasLineOfSight(attacker, target)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check line of sight between two entities
     */
    hasLineOfSight(from, to) {
        // Simplified line of sight check
        // In a full implementation, this would check for terrain obstacles
        return true;
    }

    /**
     * Start combat between two entities
     */
    startCombat(attacker, target) {
        if (!this.canAttack(attacker, target)) {
            return false;
        }
        
        const combatId = `${attacker.id}_${target.id}`;
        this.activeCombats.set(combatId, {
            attacker,
            target,
            startTime: this.gameState.timing.gameTime,
            lastAttackTime: 0
        });
        
        this.eventBus.emit('combat:started', {
            attacker,
            target,
            combatId
        });
        
        return true;
    }

    /**
     * Update active combats
     */
    updateActiveCombats(deltaTime) {
        this.activeCombats.forEach((combat, combatId) => {
            this.updateCombat(combat, deltaTime);
        });
    }

    /**
     * Update individual combat
     */
    updateCombat(combat, deltaTime) {
        const { attacker, target } = combat;
        
        // Check if combat should continue
        if (!this.canAttack(attacker, target)) {
            this.activeCombats.delete(`${attacker.id}_${target.id}`);
            return;
        }
        
        // Check attack cooldown
        const attackSpeed = attacker.attackSpeed || 1.0;
        const attackInterval = 1.0 / attackSpeed;
        
        if (this.gameState.timing.gameTime - combat.lastAttackTime >= attackInterval) {
            this.executeAttack(attacker, target);
            combat.lastAttackTime = this.gameState.timing.gameTime;
        }
    }

    /**
     * Execute an attack
     */
    executeAttack(attacker, target) {
        const damage = this.calculateDamage(attacker, target);
        
        // Create projectile if ranged attack
        if (attacker.range > 50) {
            console.log(`🎯 COMBAT DEBUG: Firing projectile from ${attacker.type || 'unknown'} to ${target.type || 'unknown'}, damage: ${damage}`);
            this.eventBus.emit('combat:projectile_fired', {
                attacker,
                target,
                damage,
                // Add missing parameters that Game.js expects
                startX: attacker.x,
                startY: attacker.y,
                targetX: target.x,
                targetY: target.y,
                speed: attacker.projectileSpeed, // Use attacker's projectileSpeed, or undefined to use Projectile class defaults
                range: attacker.range,
                calculatedDamage: damage,
                projectileType: attacker.projectileType || 'bullet',
                visual: { color: '#00FF00', size: 6 }, // Bright green and larger
                soundFire: 'shoot',
                soundHit: 'hit',
                onHitCallback: null,
                onExpireCallback: null,
                options: {}
            });
        } else {
            console.log(`⚔️ COMBAT DEBUG: Melee attack from ${attacker.type || 'unknown'} to ${target.type || 'unknown'}, damage: ${damage}`);
            // Immediate melee damage
            this.applyDamage(target, damage, attacker);
        }
        
        this.eventBus.emit('combat:attack_executed', {
            attacker,
            target,
            damage
        });
    }

    /**
     * Clean up finished combats
     */
    cleanupFinishedCombats() {
        const toRemove = [];
        
        this.activeCombats.forEach((combat, combatId) => {
            const { attacker, target } = combat;
            
            if (attacker.health <= 0 || target.health <= 0 || 
                !this.canAttack(attacker, target)) {
                toRemove.push(combatId);
            }
        });
        
        toRemove.forEach(combatId => {
            this.activeCombats.delete(combatId);
        });
    }

    /**
     * Get combat statistics
     */
    getCombatStats() {
        return {
            activeCombats: this.activeCombats.size,
            combatEvents: this.combatEvents.length,
            settings: this.settings
        };
    }

    /**
     * Update combat settings
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.eventBus.emit('combat:settings_updated', { settings: this.settings });
    }

    /**
     * Emergency stop all combat
     */
    stopAllCombat() {
        this.activeCombats.clear();
        this.combatEvents = [];
        this.eventBus.emit('combat:all_stopped');
    }
}