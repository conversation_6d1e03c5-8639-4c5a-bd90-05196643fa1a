/**
 * RenderingSystem - Handles all game rendering operations
 * Manages canvas rendering, viewport, fog of war, and visual effects
 */
export class RenderingSystem {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        
        // Canvas references
        this.gameCanvas = document.getElementById('gameCanvas');
        this.ctx = this.gameCanvas.getContext('2d');
        this.minimapCanvas = document.getElementById('minimapCanvas');
        this.minimapCtx = this.minimapCanvas.getContext('2d');
        
        // Rendering settings
        this.settings = {
            enableFogOfWar: true,
            enableParticles: true,
            enableShadows: false,
            renderQuality: 'high', // 'low', 'medium', 'high'
            maxParticles: 1000
        };
        
        // Selection rectangle state
        this.selectionRect = null;
        
        // Setup event listeners
        this.setupEventListeners();
        
        this.initializeCanvas();
    }

    /**
     * Initialize canvas dimensions and settings
     */
    initializeCanvas() {
        // Set canvas internal dimensions to match CSS dimensions
        const canvasRect = this.gameCanvas.getBoundingClientRect();
        this.gameCanvas.width = canvasRect.width;
        this.gameCanvas.height = canvasRect.height;
        this.minimapCanvas.width = this.minimapCanvas.offsetWidth;
        this.minimapCanvas.height = this.minimapCanvas.offsetHeight;
        
        // Set rendering context properties
        this.ctx.imageSmoothingEnabled = this.settings.renderQuality !== 'low';
        this.minimapCtx.imageSmoothingEnabled = false;
        
        this.eventBus.emit('rendering:canvas_initialized');
    }
    
    /**
     * Setup event listeners for rendering system
     */
    setupEventListeners() {
        // Selection rectangle events
        this.eventBus.on('input:selection_drag', (data) => {
            if (data.isDragging) {
                this.selectionRect = {
                    startX: data.startX,
                    startY: data.startY,
                    endX: data.endX,
                    endY: data.endY
                };
            }
        });
        
        this.eventBus.on('input:selection_end', () => {
            this.selectionRect = null;
        });
        
        this.eventBus.on('input:selection_start', () => {
            this.selectionRect = null;
        });
    }

    /**
     * Update method called by GameLoop (replaces direct render calls)
     */
    update(deltaTime) {
        this.render();
    }

    /**
     * Main render function called each frame
     */
    render() {
        // Only log every 60 frames to avoid spam
        if (!this.frameCounter) this.frameCounter = 0;
        this.frameCounter++;
        
        
        this.clearCanvas();
        this.setupViewport();
        this.renderWorld();
        this.renderUI();
        this.finalizeFrame();
    }

    /**
     * Clear the main canvas
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.gameCanvas.width, this.gameCanvas.height);
    }

    /**
     * Setup viewport transformation
     */
    setupViewport() {
        this.ctx.save();
        
        // Apply screen shake if active
        const shakeOffset = this.getScreenShakeOffset();
        this.ctx.translate(
            -this.gameState.world.viewX * this.gameState.world.zoom + shakeOffset.x,
            -this.gameState.world.viewY * this.gameState.world.zoom + shakeOffset.y
        );
        this.ctx.scale(this.gameState.world.zoom, this.gameState.world.zoom);
    }

    /**
     * Render the game world
     */
    renderWorld() {
        this.renderBackground();
        this.renderTerrain();
        this.renderSpiceFields();
        this.renderFogOfWar();
        this.renderEntities();
        this.renderEffects();
        this.renderGhostBuilding();
    }

    /**
     * Render background
     */
    renderBackground() {
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.fillRect(0, 0, this.gameState.world.width, this.gameState.world.height);
    }

    /**
     * Render terrain obstacles
     */
    renderTerrain() {
        this.gameState.entities.terrain.forEach(obstacle => {
            this.renderTerrainObstacle(obstacle);
        });
    }

    /**
     * Render a single terrain obstacle
     */
    renderTerrainObstacle(obstacle) {
        switch(obstacle.type) {
            case 'rock':
                this.ctx.fillStyle = '#696969';
                this.ctx.strokeStyle = '#555555';
                this.ctx.lineWidth = 2;
                break;
            case 'water':
                this.ctx.fillStyle = '#4682B4';
                this.ctx.strokeStyle = '#87CEEB';
                this.ctx.lineWidth = 1;
                break;
            case 'cliff':
                this.ctx.fillStyle = '#8B7355';
                this.ctx.strokeStyle = '#654321';
                this.ctx.lineWidth = 3;
                break;
            default:
                this.ctx.fillStyle = '#666';
                this.ctx.strokeStyle = '#444';
                this.ctx.lineWidth = 1;
                break;
        }

        this.ctx.beginPath();
        this.ctx.arc(obstacle.x, obstacle.y, obstacle.radius, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // Add special effects for water
        if (obstacle.type === 'water') {
            this.renderWaterAnimation(obstacle);
        }
    }

    /**
     * Render water animation effects
     */
    renderWaterAnimation(obstacle) {
        for (let i = 0; i < 5; i++) {
            const angle = (this.gameState.timing.gameTime * 0.01 + i * Math.PI * 0.4) % (Math.PI * 2);
            const waveRadius = obstacle.radius * 0.3 + Math.sin(angle) * 5;
            this.ctx.strokeStyle = `rgba(135, 206, 235, ${0.5 - i * 0.1})`;
            this.ctx.beginPath();
            this.ctx.arc(obstacle.x, obstacle.y, waveRadius, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    /**
     * Render spice fields
     */
    renderSpiceFields() {
        this.gameState.entities.spiceFields.forEach(field => {
            if (field.amount > 0) {
                const opacity = Math.max(0.05, 0.3 * (field.amount / field.maxAmount));
                this.ctx.fillStyle = `rgba(255, 165, 0, ${opacity})`;
                this.ctx.beginPath();
                this.ctx.arc(field.x, field.y, field.radius, 0, Math.PI * 2);
                this.ctx.fill();
                
                // Draw spice particles
                this.renderSpiceParticles(field);
            }
        });
    }

    /**
     * Render spice particles
     */
    renderSpiceParticles(field) {
        const particleCount = this.settings.renderQuality === 'low' ? 5 : 10;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const dist = Math.random() * field.radius * 0.8;
            const x = field.x + Math.cos(angle) * dist;
            const y = field.y + Math.sin(angle) * dist;
            
            this.ctx.fillStyle = 'rgba(255, 140, 0, 0.5)';
            this.ctx.beginPath();
            this.ctx.arc(x, y, 1 + Math.random() * 2, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    /**
     * Render all game entities
     */
    renderEntities() {
        // Get visible and alive entities for performance
        const visibleBuildings = this.getVisibleEntities(this.gameState.entities.buildings).filter(entity => entity.isAlive());
        const visibleUnits = this.getVisibleEntities(this.gameState.entities.units).filter(entity => entity.isAlive());
        const aliveProjectiles = this.gameState.entities.projectiles.filter(entity => entity.isAlive && entity.isAlive());
        
        // Create camera object for entity rendering
        const camera = {
            x: this.gameState.world.viewX,
            y: this.gameState.world.viewY
        };
        
        // Render buildings first (background layer)
        visibleBuildings.forEach((building, index) => {
            if (building.draw && typeof building.draw === 'function') {
                building.draw(this.ctx, camera);
            }
        });
        
        // Render units (middle layer)
        visibleUnits.forEach((unit, index) => {
            if (unit.draw && typeof unit.draw === 'function') {
                unit.draw(this.ctx, camera);
            }
        });
        
        // Render projectiles (foreground layer)
        aliveProjectiles.forEach(projectile => {
            if (projectile.draw && typeof projectile.draw === 'function') {
                projectile.draw(this.ctx, camera);
            }
        });
    }

    /**
     * Get entities visible in current viewport
     */
    getVisibleEntities(entities) {
        const viewX = this.gameState.world.viewX;
        const viewY = this.gameState.world.viewY;
        const viewWidth = this.gameCanvas.width / this.gameState.world.zoom;
        const viewHeight = this.gameCanvas.height / this.gameState.world.zoom;
        
        return entities.filter(entity => {
            const margin = 100; // Extra margin for entities partially visible
            return entity.x + margin >= viewX &&
                   entity.x - margin <= viewX + viewWidth &&
                   entity.y + margin >= viewY &&
                   entity.y - margin <= viewY + viewHeight;
        });
    }

    /**
     * Render visual effects
     */
    renderEffects() {
        // This would integrate with the VisualEffects system
        this.eventBus.emit('rendering:effects_requested', { ctx: this.ctx });
    }

    /**
     * Render ghost building for placement preview
     */
    renderGhostBuilding() {
        const ghost = this.gameState.selection.ghostBuilding;
        if (!ghost) return;
        
        this.ctx.fillStyle = ghost.valid ? 'rgba(0, 255, 0, 0.5)' : 'rgba(255, 0, 0, 0.5)';
        this.ctx.strokeStyle = ghost.valid ? '#0f0' : '#f00';
        this.ctx.lineWidth = 2;
        
        const x = ghost.x - ghost.width / 2;
        const y = ghost.y - ghost.height / 2;
        
        this.ctx.fillRect(x, y, ghost.width, ghost.height);
        this.ctx.strokeRect(x, y, ghost.width, ghost.height);
    }

    /**
     * Render fog of war
     */
    renderFogOfWar() {
        if (!this.settings.enableFogOfWar) {
            return;
        }
        
        const fogData = this.gameState.fogOfWar.data;
        const gridSize = this.gameState.fogOfWar.gridSize;
        
        if (!fogData || fogData.length === 0) {
            return;
        }
        
        // Debug fog rendering every 60 frames
        
        this.ctx.fillStyle = 'black';
        const numFogCols = fogData[0] ? fogData[0].length : 0;
        const numFogRows = fogData.length;
        
        for (let y = 0; y < numFogRows; y++) {
            for (let x = 0; x < numFogCols; x++) {
                const cell = fogData[y][x];
                if (cell && cell.currentOpacity > 0) {
                    this.ctx.globalAlpha = cell.currentOpacity;
                    this.ctx.fillRect(
                        x * gridSize,
                        y * gridSize,
                        gridSize,
                        gridSize
                    );
                }
            }
        }
        
        this.ctx.globalAlpha = 1; // Reset alpha
    }

    /**
     * Render UI elements (overlays, selection boxes, etc.)
     */
    renderUI() {
        // Render world-space UI elements before restoring viewport transformation
        this.renderWorldSpaceUI();
        
        // This would be called after restoring the viewport transformation
        this.ctx.restore();
        
        // Render screen-space UI elements
        this.renderScreenSpaceUI();
    }

    /**
     * Render world-space UI elements (selection indicators, etc.)
     */
    renderWorldSpaceUI() {
        // Selection boxes, health bars, etc. are rendered in world space
        this.eventBus.emit('rendering:world_ui_requested', { ctx: this.ctx });
    }
    
    /**
     * Render screen-space UI elements (selection rectangle, etc.)
     */
    renderScreenSpaceUI() {
        // Render selection rectangle if active
        this.renderSelectionRectangle();
        
        // Render pathfinding debug visualization
        this.renderPathfindingDebug();
        
        // Other screen-space UI elements
        this.eventBus.emit('rendering:screen_ui_requested', { ctx: this.ctx });
    }
    
    /**
     * Render selection rectangle
     */
    renderSelectionRectangle() {
        if (!this.selectionRect) return;
        
        const { startX, startY, endX, endY } = this.selectionRect;
        
        // Calculate rectangle bounds
        const left = Math.min(startX, endX);
        const top = Math.min(startY, endY);
        const width = Math.abs(endX - startX);
        const height = Math.abs(endY - startY);
        
        // Draw selection rectangle
        this.ctx.save();
        
        // Fill with semi-transparent color
        this.ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';
        this.ctx.fillRect(left, top, width, height);
        
        // Draw border
        this.ctx.strokeStyle = '#00FF00';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.strokeRect(left, top, width, height);
        
        this.ctx.restore();
    }

    /**
     * Render pathfinding debug visualization
     */
    renderPathfindingDebug() {
        if (this.gameState.features && this.gameState.features.pathfindingDebug) {
            this.eventBus.emit('rendering:pathfinding_debug', { ctx: this.ctx });
        }
    }

    /**
     * Finalize the frame
     */
    finalizeFrame() {
        // Update minimap
        this.updateMinimap();
        
        // Emit render complete event
        this.eventBus.emit('rendering:frame_complete');
    }

    /**
     * Update minimap
     */
    updateMinimap() {
        // Clear minimap
        this.minimapCtx.clearRect(0, 0, this.minimapCanvas.width, this.minimapCanvas.height);
        
        // Calculate scale
        const scaleX = this.minimapCanvas.width / this.gameState.world.width;
        const scaleY = this.minimapCanvas.height / this.gameState.world.height;
        
        this.minimapCtx.save();
        this.minimapCtx.scale(scaleX, scaleY);
        
        // Render minimap content
        this.renderMinimapBackground();
        this.renderMinimapEntities();
        this.renderMinimapViewport();
        
        this.minimapCtx.restore();
    }

    /**
     * Render minimap background
     */
    renderMinimapBackground() {
        this.minimapCtx.fillStyle = '#2d5a2d';
        this.minimapCtx.fillRect(0, 0, this.gameState.world.width, this.gameState.world.height);
    }

    /**
     * Render entities on minimap
     */
    renderMinimapEntities() {
        // Render buildings (only alive ones)
        this.gameState.entities.buildings.forEach(building => {
            if (building.isAlive()) {
                this.minimapCtx.fillStyle = this.gameState.teams[building.team].color;
                this.minimapCtx.fillRect(building.x - 5, building.y - 5, 10, 10);
            }
        });
        
        // Render units (only alive ones)
        this.gameState.entities.units.forEach(unit => {
            if (unit && unit.team && unit.isAlive()) { // Check unit is alive
                if (unit.team !== 'neutral') {
                    const teamDetails = this.gameState.teams[unit.team];
                    if (teamDetails && typeof teamDetails.color !== 'undefined') { // Check teamDetails and color
                        this.minimapCtx.fillStyle = teamDetails.color;
                        this.minimapCtx.fillRect(unit.x - 2, unit.y - 2, 4, 4);
                    }
                }
                // Implicitly skips neutral units, no 'else' needed for unit.team === 'neutral'
            }
        });
        
        // Render spice fields
        this.gameState.entities.spiceFields.forEach(field => {
            if (field.amount > 0) {
                this.minimapCtx.fillStyle = 'orange';
                this.minimapCtx.beginPath();
                this.minimapCtx.arc(field.x, field.y, field.radius * 0.5, 0, Math.PI * 2);
                this.minimapCtx.fill();
            }
        });
    }

    /**
     * Render viewport indicator on minimap
     */
    renderMinimapViewport() {
        const viewWidth = this.gameCanvas.width / this.gameState.world.zoom;
        const viewHeight = this.gameCanvas.height / this.gameState.world.zoom;
        
        this.minimapCtx.strokeStyle = 'white';
        this.minimapCtx.lineWidth = 2;
        this.minimapCtx.strokeRect(
            this.gameState.world.viewX,
            this.gameState.world.viewY,
            viewWidth,
            viewHeight
        );
    }

    /**
     * Get screen shake offset (integrates with effects system)
     */
    getScreenShakeOffset() {
        // This would integrate with the VisualEffects system
        // For now, return no shake
        return { x: 0, y: 0 };
    }

    /**
     * Update rendering settings
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        // Apply settings changes
        this.ctx.imageSmoothingEnabled = this.settings.renderQuality !== 'low';
        
        this.eventBus.emit('rendering:settings_updated', { settings: this.settings });
    }

    /**
     * Resize canvas (called when window resizes)
     */
    resize() {
        this.initializeCanvas();
        this.eventBus.emit('rendering:resized');
    }

    /**
     * Get rendering statistics
     */
    getStats() {
        return {
            canvasWidth: this.gameCanvas.width,
            canvasHeight: this.gameCanvas.height,
            renderQuality: this.settings.renderQuality,
            fogOfWarEnabled: this.settings.enableFogOfWar,
            particlesEnabled: this.settings.enableParticles
        };
    }
}