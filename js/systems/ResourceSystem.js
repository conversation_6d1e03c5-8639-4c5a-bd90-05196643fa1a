/**
 * ResourceSystem - Manages game resources and economy
 * Handles spice collection, power generation, and resource distribution
 */
export class ResourceSystem {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        
        // Resource types and their properties
        this.resourceTypes = {
            spice: {
                name: 'Spice',
                icon: '🧡',
                maxStorage: 10000,
                regenerationRate: 0
            },
            power: {
                name: 'Power',
                icon: '⚡',
                maxStorage: 1000,
                regenerationRate: 0
            },
            water: {
                name: 'Water',
                icon: '💧',
                maxStorage: 5000,
                regenerationRate: 1 // Slow natural regeneration
            }
        };
        
        // Resource collection settings
        this.settings = {
            spiceRegenerationInterval: 5000, // 5 seconds
            spiceRegenerationAmount: 25,
            harvestingEfficiency: 1.0,
            powerUpdateInterval: 1000 // 1 second
        };
        
        // Timing
        this.lastSpiceRegenTime = 0;
        this.lastPowerUpdateTime = 0;
        
        this.initializeResources();
    }

    /**
     * Initialize resource system
     */
    initializeResources() {
        // Ensure legacy compatibility
        if (!this.gameState.resources.water) {
            this.gameState.resources.water = 1000;
        }
        
        this.eventBus.emit('resources:initialized', {
            resources: this.gameState.resources
        });
    }

    /**
     * Update resource system
     */
    update(deltaTime) {
        const currentTime = this.gameState.timing.gameTime;
        
        this.updateSpiceRegeneration(currentTime);
        this.updatePowerGeneration(currentTime);
        this.updateWaterRegeneration(deltaTime);
        this.processHarvesting(deltaTime);
    }

    /**
     * Update spice field regeneration
     */
    updateSpiceRegeneration(currentTime) {
        if (currentTime - this.lastSpiceRegenTime >= this.settings.spiceRegenerationInterval) {
            this.lastSpiceRegenTime = currentTime;
            
            let totalRegenerated = 0;
            this.gameState.entities.spiceFields.forEach(field => {
                if (field.amount < field.maxAmount) {
                    const regenAmount = Math.min(
                        this.settings.spiceRegenerationAmount,
                        field.maxAmount - field.amount
                    );
                    field.amount += regenAmount;
                    totalRegenerated += regenAmount;
                }
            });
            
            if (totalRegenerated > 0) {
                this.eventBus.emit('resources:spice_regenerated', {
                    amount: totalRegenerated
                });
            }
        }
    }

    /**
     * Update power generation and consumption
     */
    updatePowerGeneration(currentTime) {
        if (currentTime - this.lastPowerUpdateTime >= this.settings.powerUpdateInterval) {
            this.lastPowerUpdateTime = currentTime;
            
            let production = 0;
            let consumption = 0;
            
            this.gameState.teams.player.buildings.forEach(building => {
                if (building.constructionProgress >= 100) {
                    production += building.provides || 0;
                    consumption += building.power || 0;
                }
            });
            
            const oldPower = this.gameState.resources.power;
            this.gameState.resources.power = production - consumption;
            this.gameState.resources.maxPower = production;
            
            if (oldPower !== this.gameState.resources.power) {
                this.eventBus.emit('resources:power_updated', {
                    power: this.gameState.resources.power,
                    maxPower: this.gameState.resources.maxPower,
                    production,
                    consumption
                });
            }
        }
    }

    /**
     * Update water regeneration
     */
    updateWaterRegeneration(deltaTime) {
        const regenRate = this.resourceTypes.water.regenerationRate;
        if (regenRate > 0 && this.gameState.resources.water < this.resourceTypes.water.maxStorage) {
            const regenAmount = regenRate * deltaTime;
            this.addResource('water', regenAmount);
        }
    }

    /**
     * Process harvesting operations
     */
    processHarvesting(deltaTime) {
        const harvesters = this.gameState.entities.units.filter(unit => 
            unit.type === 'harvester' && unit.state === 'harvesting'
        );
        
        harvesters.forEach(harvester => {
            this.processHarvesterUnit(harvester, deltaTime);
        });
    }

    /**
     * Process individual harvester unit
     */
    processHarvesterUnit(harvester, deltaTime) {
        if (!harvester.targetSpiceField || harvester.targetSpiceField.amount <= 0) {
            return;
        }
        
        const field = harvester.targetSpiceField;
        const harvestRate = (harvester.harvestRate || 10) * this.settings.harvestingEfficiency;
        const harvestAmount = harvestRate * deltaTime;
        
        if (field.amount >= harvestAmount) {
            field.amount -= harvestAmount;
            harvester.cargo = (harvester.cargo || 0) + harvestAmount;
            
            // Apply team upgrades
            const upgradeBonus = this.gameState.teams[harvester.team]?.upgrades?.harvesting || 0;
            const bonusAmount = harvestAmount * (upgradeBonus * 0.1);
            harvester.cargo += bonusAmount;
            
            this.eventBus.emit('resources:harvested', {
                harvester,
                amount: harvestAmount + bonusAmount,
                field
            });
            
            // Check if harvester is full
            if (harvester.cargo >= (harvester.maxCargo || 100)) {
                harvester.state = 'returning';
                this.eventBus.emit('resources:harvester_full', { harvester });
            }
        }
    }

    /**
     * Deliver harvested resources
     */
    deliverResources(harvester, building) {
        if (!harvester.cargo || harvester.cargo <= 0) {
            return 0;
        }
        
        const deliveredAmount = harvester.cargo;
        this.addResource('spice', deliveredAmount);
        
        // Update team stats
        if (harvester.team === 'player') {
            this.gameState.teams.player.stats.spiceCollected += deliveredAmount;
        }
        
        harvester.cargo = 0;
        
        this.eventBus.emit('resources:delivered', {
            harvester,
            building,
            amount: deliveredAmount
        });
        
        return deliveredAmount;
    }

    /**
     * Add resources
     */
    addResource(type, amount) {
        if (!this.gameState.resources.hasOwnProperty(type)) {
            return false;
        }
        
        const resourceConfig = this.resourceTypes[type];
        const currentAmount = this.gameState.resources[type];
        const maxAmount = resourceConfig ? resourceConfig.maxStorage : Infinity;
        
        const addedAmount = Math.min(amount, maxAmount - currentAmount);
        this.gameState.resources[type] += addedAmount;
        
        this.eventBus.emit('resources:added', {
            type,
            amount: addedAmount,
            total: this.gameState.resources[type]
        });
        
        return addedAmount > 0;
    }

    /**
     * Spend resources
     */
    spendResource(type, amount) {
        if (!this.gameState.resources.hasOwnProperty(type)) {
            return false;
        }
        
        if (this.gameState.resources[type] >= amount) {
            this.gameState.resources[type] -= amount;
            
            this.eventBus.emit('resources:spent', {
                type,
                amount,
                remaining: this.gameState.resources[type]
            });
            
            return true;
        }
        
        return false;
    }

    /**
     * Spend multiple resources
     */
    spendResources(costs) {
        // Check if all resources are available
        for (const [type, amount] of Object.entries(costs)) {
            if (!this.canAfford(type, amount)) {
                return false;
            }
        }
        
        // Spend all resources
        for (const [type, amount] of Object.entries(costs)) {
            this.spendResource(type, amount);
        }
        
        return true;
    }

    /**
     * Check if player can afford a cost
     */
    canAfford(type, amount) {
        if (typeof type === 'object') {
            // Multiple resources
            for (const [resourceType, resourceAmount] of Object.entries(type)) {
                if (!this.canAfford(resourceType, resourceAmount)) {
                    return false;
                }
            }
            return true;
        }
        
        // Single resource
        return this.gameState.resources[type] >= amount;
    }

    /**
     * Get resource amount
     */
    getResource(type) {
        return this.gameState.resources[type] || 0;
    }

    /**
     * Get all resources
     */
    getAllResources() {
        return { ...this.gameState.resources };
    }

    /**
     * Check if unit/building requires water
     */
    requiresWater(entityType, category) {
        // Advanced units and buildings require water
        const waterRequiredUnits = ['tank', 'rocketeer', 'engineer'];
        const waterRequiredBuildings = ['factory', 'radar', 'turret'];
        
        if (category === 'unit') {
            return waterRequiredUnits.includes(entityType);
        } else if (category === 'building') {
            return waterRequiredBuildings.includes(entityType);
        }
        
        return false;
    }

    /**
     * Calculate resource costs with modifiers
     */
    calculateCost(baseCost, modifiers = {}) {
        const cost = { ...baseCost };
        
        // Apply cost reduction modifiers
        if (modifiers.costReduction) {
            for (const [type, amount] of Object.entries(cost)) {
                cost[type] = Math.floor(amount * (1 - modifiers.costReduction));
            }
        }
        
        // Apply team upgrades
        if (modifiers.teamUpgrades) {
            // Team upgrades could affect costs
        }
        
        return cost;
    }

    /**
     * Get resource efficiency stats
     */
    getEfficiencyStats() {
        const harvesters = this.gameState.entities.units.filter(unit => unit.type === 'harvester');
        const activeHarvesters = harvesters.filter(h => h.state === 'harvesting');
        
        return {
            totalHarvesters: harvesters.length,
            activeHarvesters: activeHarvesters.length,
            harvestingEfficiency: this.settings.harvestingEfficiency,
            spiceFields: this.gameState.entities.spiceFields.length,
            activeSpiceFields: this.gameState.entities.spiceFields.filter(f => f.amount > 0).length
        };
    }

    /**
     * Get resource income rate
     */
    getIncomeRate() {
        const harvesters = this.gameState.entities.units.filter(unit => 
            unit.type === 'harvester' && unit.state === 'harvesting'
        );
        
        let totalRate = 0;
        harvesters.forEach(harvester => {
            const baseRate = harvester.harvestRate || 10;
            const upgradeBonus = this.gameState.teams[harvester.team]?.upgrades?.harvesting || 0;
            totalRate += baseRate * (1 + upgradeBonus * 0.1) * this.settings.harvestingEfficiency;
        });
        
        return totalRate;
    }

    /**
     * Find nearest spice field to a unit
     */
    findNearestSpiceField(unit) {
        let nearest = null;
        let minDistSq = Infinity;
        
        this.gameState.entities.spiceFields.forEach(field => {
            if (field.amount > 0) {
                const distSq = (field.x - unit.x) ** 2 + (field.y - unit.y) ** 2;
                if (distSq < minDistSq) {
                    minDistSq = distSq;
                    nearest = field;
                }
            }
        });
        
        return nearest;
    }

    /**
     * Update resource system settings
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.eventBus.emit('resources:settings_updated', { settings: this.settings });
    }

    /**
     * Get resource system statistics
     */
    getStats() {
        return {
            resources: this.getAllResources(),
            efficiency: this.getEfficiencyStats(),
            incomeRate: this.getIncomeRate(),
            settings: this.settings
        };
    }

    /**
     * Emergency resource adjustment (for debugging/balancing)
     */
    adjustResources(adjustments) {
        for (const [type, amount] of Object.entries(adjustments)) {
            if (this.gameState.resources.hasOwnProperty(type)) {
                this.gameState.resources[type] = Math.max(0, this.gameState.resources[type] + amount);
            }
        }
        
        this.eventBus.emit('resources:adjusted', { adjustments });
    }
}