export class CombatSystem {
    constructor(game) {
        this.game = game;
        
        // Armor and damage type effectiveness matrix
        this.damageMatrix = {
            // [damageType][armorType] = effectiveness multiplier
            kinetic: {
                light: 1.2,     // Kinetic is effective vs light armor
                medium: 1.0,    // Normal effectiveness
                heavy: 0.7,     // Reduced vs heavy armor
                aircraft: 0.9   // Slightly reduced vs aircraft
            },
            explosive: {
                light: 0.8,     // Less effective vs light armor
                medium: 1.3,    // Very effective vs medium armor
                heavy: 1.5,     // Extremely effective vs heavy armor
                aircraft: 1.1   // Good vs aircraft
            },
            energy: {
                light: 1.4,     // Very effective vs light armor
                medium: 1.1,    // Good vs medium armor
                heavy: 0.6,     // Poor vs heavy armor
                aircraft: 1.2   // Good vs aircraft
            },
            sonic: {
                light: 1.3,     // Very effective vs light armor
                medium: 1.4,    // Extremely effective vs medium armor
                heavy: 1.2,     // Good vs heavy armor - sonic ignores most armor
                aircraft: 1.5   // Excellent vs aircraft
            },
            disruptor: {
                light: 1.1,     // Good vs light armor
                medium: 1.0,    // Normal vs medium armor
                heavy: 1.3,     // Very effective vs heavy armor - bypasses shields
                aircraft: 1.2   // Good vs aircraft
            },
            melee: {
                light: 1.5,     // Excellent vs light armor
                medium: 1.2,    // Good vs medium armor
                heavy: 0.8,     // Poor vs heavy armor
                aircraft: 0.3   // Very poor vs aircraft
            },
            fire: {
                light: 1.3,     // Very effective vs light armor
                medium: 1.1,    // Good vs medium armor
                heavy: 0.9,     // Slightly reduced vs heavy armor
                aircraft: 1.4   // Excellent vs aircraft
            },
            environmental: {
                light: 0.9,     // Slightly reduced vs light armor
                medium: 1.0,    // Normal vs medium armor
                heavy: 0.8,     // Poor vs heavy armor
                aircraft: 1.1   // Good vs aircraft - sandstorms affect visibility
            }
        };
        
        // Formation bonuses
        this.formations = {
            line: {
                name: 'Line Formation',
                damageBonus: 0.1,
                defenseBonus: 0.05,
                description: 'Units in a line get small combat bonuses'
            },
            wedge: {
                name: 'Wedge Formation',
                damageBonus: 0.15,
                defenseBonus: 0.0,
                description: 'Offensive formation with increased damage'
            },
            defensive: {
                name: 'Defensive Formation',
                damageBonus: 0.0,
                defenseBonus: 0.2,
                description: 'Defensive formation with increased armor'
            },
            scattered: {
                name: 'Scattered Formation',
                damageBonus: 0.05,
                defenseBonus: 0.1,
                splashResistance: 0.3,
                description: 'Reduces splash damage taken'
            }
        };
        
        // Veterancy system
        this.veterancyLevels = {
            1: { name: 'Recruit', xpRequired: 0, bonuses: {} },
            2: { name: 'Regular', xpRequired: 100, bonuses: { damage: 0.1, armor: 0.05 } },
            3: { name: 'Veteran', xpRequired: 250, bonuses: { damage: 0.2, armor: 0.1, speed: 0.1 } },
            4: { name: 'Elite', xpRequired: 500, bonuses: { damage: 0.35, armor: 0.15, speed: 0.15, range: 0.1 } },
            5: { name: 'Heroic', xpRequired: 1000, bonuses: { damage: 0.5, armor: 0.25, speed: 0.2, range: 0.15, health: 0.2 } }
        };
    }
    
    // Calculate damage with armor/damage type effectiveness
    calculateDamage(attacker, target, baseDamage) {
        let damage = baseDamage;
        
        // Apply damage type vs armor type effectiveness
        const damageType = attacker.damageType || 'kinetic';
        const armorType = target.armorType || 'medium';
        
        if (this.damageMatrix[damageType] && this.damageMatrix[damageType][armorType]) {
            damage *= this.damageMatrix[damageType][armorType];
        }
        
        // Apply veterancy bonuses
        const attackerVeterancy = this.getVeterancyLevel(attacker);
        if (attackerVeterancy > 1) {
            const bonuses = this.veterancyLevels[attackerVeterancy].bonuses;
            damage *= (1 + (bonuses.damage || 0));
        }
        
        // Apply formation bonuses
        const formation = this.getUnitFormation(attacker);
        if (formation) {
            damage *= (1 + (formation.damageBonus || 0));
        }
        
        // Apply target's defensive bonuses
        const targetVeterancy = this.getVeterancyLevel(target);
        if (targetVeterancy > 1) {
            const targetBonuses = this.veterancyLevels[targetVeterancy].bonuses;
            const armorBonus = targetBonuses.armor || 0;
            damage *= (1 - armorBonus * 0.5); // Armor reduces damage by 50% of the bonus
        }
        
        const targetFormation = this.getUnitFormation(target);
        if (targetFormation) {
            damage *= (1 - (targetFormation.defenseBonus || 0));
        }
        
        return Math.max(1, Math.floor(damage));
    }
    
    // Calculate splash damage with formation considerations
    calculateSplashDamage(attacker, centerX, centerY, baseDamage, radius) {
        const affectedUnits = [];
        
        [...this.game.units, ...this.game.buildings, ...this.game.neutralEnemies.neutralUnits].forEach(entity => {
            if (entity.team === attacker.team || entity.health <= 0) return;
            
            const distSq = (entity.x - centerX) ** 2 + (entity.y - centerY) ** 2;
            if (distSq < radius ** 2) {
                const distance = Math.sqrt(distSq);
                const damageFactor = 1 - (distance / radius);
                let damage = baseDamage * damageFactor;
                
                // Apply formation splash resistance
                const formation = this.getUnitFormation(entity);
                if (formation && formation.splashResistance) {
                    damage *= (1 - formation.splashResistance);
                }
                
                // Apply normal damage calculations
                damage = this.calculateDamage(attacker, entity, damage);
                
                affectedUnits.push({
                    entity: entity,
                    damage: damage,
                    distance: distance
                });
            }
        });
        
        return affectedUnits;
    }
    
    // Determine unit's current formation based on nearby friendly units
    getUnitFormation(unit) {
        if (!unit || unit.team === 'neutral') return null;
        
        const nearbyFriendlies = this.getNearbyFriendlyUnits(unit, 60);
        if (nearbyFriendlies.length < 2) return null;
        
        // Simple formation detection based on unit positions
        const positions = nearbyFriendlies.map(u => ({ x: u.x, y: u.y }));
        positions.push({ x: unit.x, y: unit.y });
        
        // Check for line formation (units roughly in a line)
        if (this.isLineFormation(positions)) {
            return this.formations.line;
        }
        
        // Check for wedge formation (triangular arrangement)
        if (this.isWedgeFormation(positions)) {
            return this.formations.wedge;
        }
        
        // Check for defensive formation (clustered together)
        if (this.isDefensiveFormation(positions)) {
            return this.formations.defensive;
        }
        
        // Check for scattered formation (spread out)
        if (this.isScatteredFormation(positions)) {
            return this.formations.scattered;
        }
        
        return null;
    }
    
    // Get nearby friendly units
    getNearbyFriendlyUnits(unit, radius) {
        if (!this.game.teams[unit.team]) return [];
        
        return this.game.teams[unit.team].units.filter(friendlyUnit => {
            if (friendlyUnit === unit || friendlyUnit.health <= 0) return false;
            const distSq = (friendlyUnit.x - unit.x) ** 2 + (friendlyUnit.y - unit.y) ** 2;
            return distSq < radius ** 2;
        });
    }
    
    // Formation detection algorithms
    isLineFormation(positions) {
        if (positions.length < 3) return false;
        
        // Check if units are roughly aligned
        const avgX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length;
        const avgY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length;
        
        // Calculate variance from center line
        let variance = 0;
        positions.forEach(pos => {
            const dist = Math.sqrt((pos.x - avgX) ** 2 + (pos.y - avgY) ** 2);
            variance += dist;
        });
        
        return variance / positions.length < 30; // Threshold for line formation
    }
    
    isWedgeFormation(positions) {
        if (positions.length < 3) return false;
        
        // Simple wedge detection: one unit forward, others behind
        positions.sort((a, b) => a.y - b.y); // Sort by Y position
        const front = positions[0];
        const back = positions.slice(1);
        
        // Check if back units are spread horizontally
        const backSpread = Math.max(...back.map(p => p.x)) - Math.min(...back.map(p => p.x));
        const frontDistance = Math.min(...back.map(p => Math.abs(p.y - front.y)));
        
        return backSpread > 40 && frontDistance > 20;
    }
    
    isDefensiveFormation(positions) {
        if (positions.length < 3) return false;
        
        // Check if units are clustered together
        const avgX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length;
        const avgY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length;
        
        const maxDistance = Math.max(...positions.map(pos => 
            Math.sqrt((pos.x - avgX) ** 2 + (pos.y - avgY) ** 2)
        ));
        
        return maxDistance < 40; // Tight clustering
    }
    
    isScatteredFormation(positions) {
        if (positions.length < 3) return false;
        
        // Check if units are spread out
        const avgX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length;
        const avgY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length;
        
        const avgDistance = positions.reduce((sum, pos) => 
            sum + Math.sqrt((pos.x - avgX) ** 2 + (pos.y - avgY) ** 2), 0
        ) / positions.length;
        
        return avgDistance > 50; // Well spread out
    }
    
    // Veterancy system
    getVeterancyLevel(unit) {
        if (!unit.combatXP) return 1;
        
        for (let level = 5; level >= 1; level--) {
            if (unit.combatXP >= this.veterancyLevels[level].xpRequired) {
                return level;
            }
        }
        return 1;
    }
    
    awardCombatXP(unit, amount, reason) {
        if (!unit || unit.team === 'neutral') return;
        
        if (!unit.combatXP) unit.combatXP = 0;
        const oldLevel = this.getVeterancyLevel(unit);
        
        unit.combatXP += amount;
        const newLevel = this.getVeterancyLevel(unit);
        
        if (newLevel > oldLevel) {
            this.promoteUnit(unit, newLevel);
        }
    }
    
    promoteUnit(unit, newLevel) {
        const veterancyData = this.veterancyLevels[newLevel];
        
        // Apply stat bonuses
        const bonuses = veterancyData.bonuses;
        if (bonuses.health) {
            const healthBonus = unit.maxHealth * bonuses.health;
            unit.maxHealth += healthBonus;
            unit.health += healthBonus; // Also heal the unit
        }
        
        // Show promotion notification
        if (unit.team === 'player') {
            this.game.ui.showNotification(
                `${unit.name || unit.type} promoted to ${veterancyData.name}!`, 
                'success'
            );
            this.game.soundManager.play('complete');
        }
        
        // Visual effect
        this.game.visualEffects.createLevelUpEffect(unit.x, unit.y);
    }
    
    // Get effective stat with veterancy bonuses
    getEffectiveStat(unit, statName, baseStat) {
        const level = this.getVeterancyLevel(unit);
        if (level <= 1) return baseStat;
        
        const bonuses = this.veterancyLevels[level].bonuses;
        const bonus = bonuses[statName] || 0;
        
        return baseStat * (1 + bonus);
    }
    
    // Unit deployment system for siege tanks
    toggleDeployment(unit) {
        if (!unit.deployable) return false;
        
        if (unit.isDeployed) {
            // Undeploy
            unit.isDeployed = false;
            unit.speed = unit.originalSpeed || unit.speed * 2; // Restore mobility
            unit.range = (unit.originalRange || unit.range) * 0.6; // Reduce range
            unit.damage = (unit.originalDamage || unit.damage) * 0.7; // Reduce damage
            
            this.game.ui.showNotification(`${unit.name || unit.type} undeployed`, 'info');
        } else {
            // Deploy
            unit.isDeployed = true;
            unit.originalSpeed = unit.speed;
            unit.originalRange = unit.range;
            unit.originalDamage = unit.damage;
            
            unit.speed = 0; // Cannot move when deployed
            unit.range *= 1.67; // Increase range significantly
            unit.damage *= 1.43; // Increase damage
            
            // Stop current movement
            unit.path = [];
            unit.targetX = unit.x;
            unit.targetY = unit.y;
            
            this.game.ui.showNotification(`${unit.name || unit.type} deployed`, 'info');
        }
        
        this.game.soundManager.play('select');
        return true;
    }
    
    // Phase 4 Integration Methods
    updateDamageEffectiveness(settings) {
        // Update damage matrix for balance changes
        if (settings.damageMatrix) {
            Object.assign(this.damageMatrix, settings.damageMatrix);
        }
        
        // Update veterancy bonuses
        if (settings.veterancyBonuses) {
            Object.assign(this.veterancyLevels, settings.veterancyBonuses);
        }
        
        // Update formation bonuses
        if (settings.formationBonuses) {
            Object.assign(this.formations, settings.formationBonuses);
        }
    }
    
    // Export state for save/load
    exportState() {
        return {
            damageMatrix: this.damageMatrix,
            formations: this.formations,
            veterancyLevels: this.veterancyLevels
        };
    }
    
    // Import state for save/load
    importState(state) {
        if (state.damageMatrix) {
            this.damageMatrix = state.damageMatrix;
        }
        if (state.formations) {
            this.formations = state.formations;
        }
        if (state.veterancyLevels) {
            this.veterancyLevels = state.veterancyLevels;
        }
    }
    
    // Get combat effectiveness rating for balance manager
    getCombatEffectivenessRating() {
        // Calculate overall combat balance based on damage matrix
        let totalEffectiveness = 0;
        let count = 0;
        
        Object.values(this.damageMatrix).forEach(damageType => {
            Object.values(damageType).forEach(effectiveness => {
                totalEffectiveness += effectiveness;
                count++;
            });
        });
        
        return count > 0 ? totalEffectiveness / count : 1.0;
    }
    
    // Apply difficulty modifiers to combat
    applyDifficultyModifiers(difficulty) {
        const modifiers = {
            easy: { playerDamageBonus: 1.2, enemyDamageReduction: 0.8 },
            normal: { playerDamageBonus: 1.0, enemyDamageReduction: 1.0 },
            hard: { playerDamageBonus: 0.8, enemyDamageReduction: 1.2 },
            nightmare: { playerDamageBonus: 0.6, enemyDamageReduction: 1.5 }
        };
        
        this.difficultyModifiers = modifiers[difficulty] || modifiers.normal;
    }
    
    // Enhanced damage calculation with difficulty modifiers
    calculateDamageWithDifficulty(attacker, target, baseDamage) {
        let damage = this.calculateDamage(attacker, target, baseDamage);
        
        if (this.difficultyModifiers) {
            if (attacker.team === 'player') {
                damage *= this.difficultyModifiers.playerDamageBonus;
            } else if (target.team === 'player') {
                damage *= this.difficultyModifiers.enemyDamageReduction;
            }
        }
        
        return Math.max(1, Math.floor(damage));
    }
    
    // Set experience multiplier for difficulty balancing
    setExperienceMultiplier(multiplier) {
        this.experienceMultiplier = multiplier;
    }
    
    // Award combat XP with multiplier
    awardCombatXPWithMultiplier(unit, amount, reason) {
        const finalAmount = amount * (this.experienceMultiplier || 1);
        this.awardCombatXP(unit, finalAmount, reason);
    }
    
    // Set veteran bonuses for difficulty balancing
    setVeteranBonuses(bonuses) {
        if (bonuses) {
            Object.keys(bonuses).forEach(level => {
                if (this.veterancyLevels[level]) {
                    Object.assign(this.veterancyLevels[level].bonuses, bonuses[level]);
                }
            });
        }
    }
    
    // Update method for Phase 4 integration
    update(deltaTime) {
        // Update combat-related systems
        this.updateVeterancyEffects(deltaTime);
        this.updateFormationBonuses(deltaTime);
    }
    
    updateVeterancyEffects(deltaTime) {
        // Apply ongoing veterancy effects
        this.game.teams.player.units.forEach(unit => {
            const level = this.getVeterancyLevel(unit);
            if (level > 1) {
                const bonuses = this.veterancyLevels[level].bonuses;
                
                // Apply speed bonuses if not already applied
                if (bonuses.speed && !unit.veterancySpeedApplied) {
                    unit.speed = this.getEffectiveStat(unit, 'speed', unit.baseSpeed || unit.speed);
                    unit.veterancySpeedApplied = true;
                }
                
                // Apply range bonuses if not already applied
                if (bonuses.range && !unit.veterancyRangeApplied) {
                    unit.range = this.getEffectiveStat(unit, 'range', unit.baseRange || unit.range);
                    unit.veterancyRangeApplied = true;
                }
            }
        });
    }
    
    updateFormationBonuses(deltaTime) {
        // Update formation bonuses for all player units
        this.game.teams.player.units.forEach(unit => {
            const formation = this.getUnitFormation(unit);
            unit.currentFormation = formation;
        });
    }
}