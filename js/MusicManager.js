export class MusicManager {
    constructor(soundManager) {
        this.soundManager = soundManager;
        this.audioContext = soundManager.audioContext;
        this.enabled = soundManager.enabled;
        
        // Music state management
        this.currentState = 'peaceful';
        this.previousState = 'peaceful';
        this.transitionDuration = 2.0; // seconds
        this.isTransitioning = false;
        this.transitionStartTime = 0;
        
        // Audio nodes for dynamic mixing
        this.masterGain = null;
        this.musicGains = {};
        this.oscillators = {};
        this.filters = {};
        this.delays = {};
        
        // Music layers and tracks
        this.musicLayers = {
            ambient: null,
            tension: null,
            combat: null,
            victory: null,
            defeat: null
        };
        
        // Faction-specific themes
        this.factionThemes = {
            'House Atreides': {
                baseFreq: 220,
                harmonies: [1, 1.25, 1.5, 2],
                timbre: 'orchestral',
                tempo: 0.8
            },
            'House Harkonnen': {
                baseFreq: 110,
                harmonies: [1, 1.2, 1.4, 1.8],
                timbre: 'industrial',
                tempo: 1.2
            },
            'Fremen': {
                baseFreq: 165,
                harmonies: [1, 1.33, 1.67, 2.25],
                timbre: 'ethnic',
                tempo: 0.9
            },
            'Imperial Sardaukar': {
                baseFreq: 196,
                harmonies: [1, 1.25, 1.5, 2, 2.5],
                timbre: 'military',
                tempo: 1.0
            }
        };
        
        // Game state tracking
        this.gameStateHistory = [];
        this.combatIntensity = 0;
        this.buildingActivity = 0;
        this.selectedFaction = null;
        
        // Volume controls
        this.masterVolume = 0.3;
        this.layerVolumes = {
            ambient: 0.8,
            tension: 0.0,
            combat: 0.0,
            victory: 0.0,
            defeat: 0.0
        };
        
        this.init();
    }
    
    init() {
        if (!this.enabled || !this.audioContext) return;
        
        try {
            // Create master gain node
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            this.masterGain.gain.setValueAtTime(this.masterVolume, this.audioContext.currentTime);
            
            // Initialize music layers
            this.initializeMusicLayers();
            
            // Start ambient music
            this.startAmbientMusic();
            
            console.log('MusicManager initialized successfully');
        } catch (error) {
            console.warn('Failed to initialize MusicManager:', error);
            this.enabled = false;
        }
    }
    
    initializeMusicLayers() {
        Object.keys(this.musicLayers).forEach(layerName => {
            // Create gain node for each layer
            this.musicGains[layerName] = this.audioContext.createGain();
            this.musicGains[layerName].connect(this.masterGain);
            this.musicGains[layerName].gain.setValueAtTime(0, this.audioContext.currentTime);
            
            // Create filter for each layer
            this.filters[layerName] = this.audioContext.createBiquadFilter();
            this.filters[layerName].connect(this.musicGains[layerName]);
            
            // Create delay for spatial effects
            this.delays[layerName] = this.audioContext.createDelay(1.0);
            this.delays[layerName].connect(this.filters[layerName]);
        });
    }
    
    startAmbientMusic() {
        if (!this.enabled) return;
        
        this.createDesertAmbience();
        this.fadeInLayer('ambient', 2.0);
    }
    
    createDesertAmbience() {
        // Create multiple oscillators for rich ambient texture
        const ambientOscillators = [];
        
        // Low frequency drone
        const droneOsc = this.audioContext.createOscillator();
        droneOsc.type = 'sine';
        droneOsc.frequency.setValueAtTime(55, this.audioContext.currentTime);
        
        const droneGain = this.audioContext.createGain();
        droneGain.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        
        droneOsc.connect(droneGain);
        droneGain.connect(this.delays.ambient);
        droneOsc.start();
        ambientOscillators.push(droneOsc);
        
        // Wind-like noise
        this.createWindNoise();
        
        // Subtle harmonic layers
        [110, 165, 220].forEach((freq, index) => {
            const harmOsc = this.audioContext.createOscillator();
            harmOsc.type = 'triangle';
            harmOsc.frequency.setValueAtTime(freq, this.audioContext.currentTime);
            
            const harmGain = this.audioContext.createGain();
            harmGain.gain.setValueAtTime(0.03 - index * 0.01, this.audioContext.currentTime);
            
            // Add subtle LFO modulation
            const lfo = this.audioContext.createOscillator();
            lfo.type = 'sine';
            lfo.frequency.setValueAtTime(0.1 + index * 0.05, this.audioContext.currentTime);
            
            const lfoGain = this.audioContext.createGain();
            lfoGain.gain.setValueAtTime(2, this.audioContext.currentTime);
            
            lfo.connect(lfoGain);
            lfoGain.connect(harmOsc.frequency);
            
            harmOsc.connect(harmGain);
            harmGain.connect(this.delays.ambient);
            
            harmOsc.start();
            lfo.start();
            
            ambientOscillators.push(harmOsc, lfo);
        });
        
        this.oscillators.ambient = ambientOscillators;
        
        // Configure ambient filter for desert atmosphere
        this.filters.ambient.type = 'lowpass';
        this.filters.ambient.frequency.setValueAtTime(800, this.audioContext.currentTime);
        this.filters.ambient.Q.setValueAtTime(0.5, this.audioContext.currentTime);
        
        // Add subtle delay for spatial depth
        this.delays.ambient.delayTime.setValueAtTime(0.3, this.audioContext.currentTime);
    }
    
    createWindNoise() {
        // Create filtered noise for wind effect
        const bufferSize = this.audioContext.sampleRate * 4; // 4 seconds of noise
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);
        
        // Generate pink noise (more natural than white noise)
        let b0 = 0, b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;
        for (let i = 0; i < bufferSize; i++) {
            const white = Math.random() * 2 - 1;
            b0 = 0.99886 * b0 + white * 0.0555179;
            b1 = 0.99332 * b1 + white * 0.0750759;
            b2 = 0.96900 * b2 + white * 0.1538520;
            b3 = 0.86650 * b3 + white * 0.3104856;
            b4 = 0.55000 * b4 + white * 0.5329522;
            b5 = -0.7616 * b5 - white * 0.0168980;
            output[i] = (b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362) * 0.11;
            b6 = white * 0.115926;
        }
        
        const windSource = this.audioContext.createBufferSource();
        windSource.buffer = buffer;
        windSource.loop = true;
        
        const windGain = this.audioContext.createGain();
        windGain.gain.setValueAtTime(0.05, this.audioContext.currentTime);
        
        const windFilter = this.audioContext.createBiquadFilter();
        windFilter.type = 'bandpass';
        windFilter.frequency.setValueAtTime(200, this.audioContext.currentTime);
        windFilter.Q.setValueAtTime(0.3, this.audioContext.currentTime);
        
        windSource.connect(windFilter);
        windFilter.connect(windGain);
        windGain.connect(this.delays.ambient);
        
        windSource.start();
        
        if (!this.oscillators.ambient) this.oscillators.ambient = [];
        this.oscillators.ambient.push(windSource);
    }
    
    // Game state change handlers
    onGameStateChange(newState, context = {}) {
        if (!this.enabled || newState === this.currentState) return;
        
        console.log(`Music state changing from ${this.currentState} to ${newState}`);
        
        this.gameStateHistory.push({
            state: newState,
            timestamp: this.audioContext.currentTime,
            context: context
        });
        
        // Keep only recent history
        if (this.gameStateHistory.length > 10) {
            this.gameStateHistory.shift();
        }
        
        this.previousState = this.currentState;
        this.currentState = newState;
        
        this.transitionToState(newState, context);
    }
    
    transitionToState(newState, context) {
        this.isTransitioning = true;
        this.transitionStartTime = this.audioContext.currentTime;
        
        switch (newState) {
            case 'combat':
                this.transitionToCombat(context.intensity || 0.7);
                break;
            case 'building':
                this.transitionToBuilding();
                break;
            case 'victory':
                this.transitionToVictory();
                break;
            case 'defeat':
                this.transitionToDefeat();
                break;
            case 'peaceful':
            default:
                this.transitionToPeaceful();
                break;
        }
        
        // Reset transition flag after duration
        setTimeout(() => {
            this.isTransitioning = false;
        }, this.transitionDuration * 1000);
    }
    
    transitionToCombat(intensity = 0.7) {
        // Fade out ambient, fade in tension and combat layers
        this.fadeOutLayer('ambient', 1.0);
        this.fadeInLayer('tension', 0.5);
        this.fadeInLayer('combat', 1.5);
        
        this.createCombatMusic(intensity);
        this.combatIntensity = intensity;
    }
    
    createCombatMusic(intensity) {
        // Create percussion-heavy combat music
        this.createCombatPercussion(intensity);
        this.createTensionStrings(intensity);
        this.createCombatBass(intensity);
    }
    
    createCombatPercussion(intensity) {
        const percussionOscillators = [];
        
        // Create rhythmic percussion using noise bursts
        const createDrumHit = (frequency, duration, gain) => {
            const osc = this.audioContext.createOscillator();
            const oscGain = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();
            
            osc.type = 'sawtooth';
            osc.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(frequency * 2, this.audioContext.currentTime);
            
            oscGain.gain.setValueAtTime(gain * intensity, this.audioContext.currentTime);
            oscGain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            osc.connect(filter);
            filter.connect(oscGain);
            oscGain.connect(this.delays.combat);
            
            osc.start();
            osc.stop(this.audioContext.currentTime + duration);
            
            return osc;
        };
        
        // Create rhythmic pattern
        const tempo = 120 * intensity; // BPM increases with intensity
        const beatInterval = 60 / tempo;
        
        let beatCount = 0;
        const createBeat = () => {
            if (this.currentState !== 'combat') return;
            
            // Kick drum on beats 1 and 3
            if (beatCount % 4 === 0 || beatCount % 4 === 2) {
                createDrumHit(60, 0.1, 0.3);
            }
            
            // Snare on beats 2 and 4
            if (beatCount % 4 === 1 || beatCount % 4 === 3) {
                createDrumHit(200, 0.05, 0.2);
            }
            
            // Hi-hat on every beat with intensity
            if (intensity > 0.5) {
                createDrumHit(8000, 0.02, 0.1);
            }
            
            beatCount++;
            
            setTimeout(createBeat, beatInterval * 1000);
        };
        
        createBeat();
    }
    
    createTensionStrings(intensity) {
        const stringOscillators = [];
        
        // Create dissonant string-like sounds
        const baseFreqs = [220, 277, 330, 415]; // Slightly dissonant intervals
        
        baseFreqs.forEach((freq, index) => {
            const osc = this.audioContext.createOscillator();
            osc.type = 'sawtooth';
            osc.frequency.setValueAtTime(freq, this.audioContext.currentTime);
            
            const gain = this.audioContext.createGain();
            gain.gain.setValueAtTime(0.05 * intensity, this.audioContext.currentTime);
            
            // Add tremolo for tension
            const tremolo = this.audioContext.createOscillator();
            tremolo.type = 'sine';
            tremolo.frequency.setValueAtTime(6 + index, this.audioContext.currentTime);
            
            const tremoloGain = this.audioContext.createGain();
            tremoloGain.gain.setValueAtTime(0.02, this.audioContext.currentTime);
            
            tremolo.connect(tremoloGain);
            tremoloGain.connect(gain.gain);
            
            osc.connect(gain);
            gain.connect(this.delays.tension);
            
            osc.start();
            tremolo.start();
            
            stringOscillators.push(osc, tremolo);
        });
        
        this.oscillators.tension = stringOscillators;
        
        // Configure tension filter for harsh sound
        this.filters.tension.type = 'bandpass';
        this.filters.tension.frequency.setValueAtTime(1200, this.audioContext.currentTime);
        this.filters.tension.Q.setValueAtTime(2, this.audioContext.currentTime);
    }
    
    createCombatBass(intensity) {
        const bassOsc = this.audioContext.createOscillator();
        bassOsc.type = 'square';
        bassOsc.frequency.setValueAtTime(55, this.audioContext.currentTime);
        
        const bassGain = this.audioContext.createGain();
        bassGain.gain.setValueAtTime(0.15 * intensity, this.audioContext.currentTime);
        
        // Add bass modulation
        const bassLFO = this.audioContext.createOscillator();
        bassLFO.type = 'sine';
        bassLFO.frequency.setValueAtTime(0.5, this.audioContext.currentTime);
        
        const lfoGain = this.audioContext.createGain();
        lfoGain.gain.setValueAtTime(10, this.audioContext.currentTime);
        
        bassLFO.connect(lfoGain);
        lfoGain.connect(bassOsc.frequency);
        
        bassOsc.connect(bassGain);
        bassGain.connect(this.delays.combat);
        
        bassOsc.start();
        bassLFO.start();
        
        if (!this.oscillators.combat) this.oscillators.combat = [];
        this.oscillators.combat.push(bassOsc, bassLFO);
        
        // Configure combat filter for punch
        this.filters.combat.type = 'lowpass';
        this.filters.combat.frequency.setValueAtTime(400, this.audioContext.currentTime);
        this.filters.combat.Q.setValueAtTime(1, this.audioContext.currentTime);
    }
    
    transitionToBuilding() {
        // Calm, meditative music for building phases
        this.fadeOutLayer('combat', 1.0);
        this.fadeOutLayer('tension', 1.0);
        this.fadeInLayer('ambient', 2.0);
        
        this.createBuildingMusic();
        this.buildingActivity = 1.0;
    }
    
    createBuildingMusic() {
        // Create gentle, rhythmic building music
        const buildingOscillators = [];
        
        // Soft arpeggiated pattern
        const arpeggioFreqs = [220, 277, 330, 440]; // Major 7th arpeggio
        let arpeggioIndex = 0;
        
        const createArpeggioNote = () => {
            if (this.currentState !== 'building') return;
            
            const osc = this.audioContext.createOscillator();
            osc.type = 'sine';
            osc.frequency.setValueAtTime(arpeggioFreqs[arpeggioIndex], this.audioContext.currentTime);
            
            const gain = this.audioContext.createGain();
            gain.gain.setValueAtTime(0.08, this.audioContext.currentTime);
            gain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.5);
            
            osc.connect(gain);
            gain.connect(this.delays.ambient);
            
            osc.start();
            osc.stop(this.audioContext.currentTime + 0.5);
            
            arpeggioIndex = (arpeggioIndex + 1) % arpeggioFreqs.length;
            
            setTimeout(createArpeggioNote, 600); // Gentle tempo
        };
        
        createArpeggioNote();
    }
    
    transitionToPeaceful() {
        // Return to ambient desert atmosphere
        this.fadeOutLayer('combat', 2.0);
        this.fadeOutLayer('tension', 2.0);
        this.fadeInLayer('ambient', 3.0);
        
        this.combatIntensity = 0;
        this.buildingActivity = 0;
    }
    
    transitionToVictory() {
        this.fadeOutAllLayers(1.0);
        this.fadeInLayer('victory', 2.0);
        this.createVictoryMusic();
    }
    
    createVictoryMusic() {
        // Triumphant fanfare
        const victoryOscillators = [];
        
        // Major chord progression
        const chordProgression = [
            [220, 277, 330, 440], // A major
            [247, 311, 370, 494], // B major
            [330, 415, 494, 659]  // E major
        ];
        
        let chordIndex = 0;
        
        const playChord = () => {
            const chord = chordProgression[chordIndex];
            
            chord.forEach((freq, index) => {
                const osc = this.audioContext.createOscillator();
                osc.type = 'square';
                osc.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                
                const gain = this.audioContext.createGain();
                gain.gain.setValueAtTime(0.1 - index * 0.02, this.audioContext.currentTime);
                gain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 2.0);
                
                osc.connect(gain);
                gain.connect(this.delays.victory);
                
                osc.start();
                osc.stop(this.audioContext.currentTime + 2.0);
                
                victoryOscillators.push(osc);
            });
            
            chordIndex = (chordIndex + 1) % chordProgression.length;
            
            if (this.currentState === 'victory' && chordIndex < chordProgression.length) {
                setTimeout(playChord, 2000);
            }
        };
        
        playChord();
        this.oscillators.victory = victoryOscillators;
    }
    
    transitionToDefeat() {
        this.fadeOutAllLayers(1.0);
        this.fadeInLayer('defeat', 2.0);
        this.createDefeatMusic();
    }
    
    createDefeatMusic() {
        // Somber, minor key music
        const defeatOsc = this.audioContext.createOscillator();
        defeatOsc.type = 'sine';
        defeatOsc.frequency.setValueAtTime(110, this.audioContext.currentTime); // Low A
        
        const defeatGain = this.audioContext.createGain();
        defeatGain.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        defeatGain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 5.0);
        
        defeatOsc.connect(defeatGain);
        defeatGain.connect(this.delays.defeat);
        
        defeatOsc.start();
        defeatOsc.stop(this.audioContext.currentTime + 5.0);
        
        this.oscillators.defeat = [defeatOsc];
    }
    
    // Faction-specific music methods
    playFactionTheme(factionName) {
        if (!this.enabled || !this.factionThemes[factionName]) return;
        
        const theme = this.factionThemes[factionName];
        this.selectedFaction = factionName;
        
        this.createFactionStinger(theme);
    }
    
    createFactionStinger(theme) {
        const stingerOscillators = [];
        
        theme.harmonies.forEach((harmonic, index) => {
            const freq = theme.baseFreq * harmonic;
            
            const osc = this.audioContext.createOscillator();
            osc.type = this.getFactionWaveform(theme.timbre);
            osc.frequency.setValueAtTime(freq, this.audioContext.currentTime);
            
            const gain = this.audioContext.createGain();
            gain.gain.setValueAtTime(0.08 - index * 0.015, this.audioContext.currentTime);
            gain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 1.5);
            
            osc.connect(gain);
            gain.connect(this.masterGain);
            
            osc.start();
            osc.stop(this.audioContext.currentTime + 1.5);
            
            stingerOscillators.push(osc);
        });
    }
    
    getFactionWaveform(timbre) {
        switch (timbre) {
            case 'orchestral': return 'sine';
            case 'industrial': return 'square';
            case 'ethnic': return 'triangle';
            case 'military': return 'sawtooth';
            default: return 'sine';
        }
    }
    
    // Layer management methods
    fadeInLayer(layerName, duration = 1.0) {
        if (!this.musicGains[layerName]) return;
        
        const targetVolume = this.layerVolumes[layerName] * this.masterVolume;
        this.musicGains[layerName].gain.linearRampToValueAtTime(
            targetVolume,
            this.audioContext.currentTime + duration
        );
    }
    
    fadeOutLayer(layerName, duration = 1.0) {
        if (!this.musicGains[layerName]) return;
        
        this.musicGains[layerName].gain.linearRampToValueAtTime(
            0,
            this.audioContext.currentTime + duration
        );
        
        // Stop oscillators after fade out
        setTimeout(() => {
            this.stopLayerOscillators(layerName);
        }, duration * 1000);
    }
    
    fadeOutAllLayers(duration = 1.0) {
        Object.keys(this.musicLayers).forEach(layerName => {
            this.fadeOutLayer(layerName, duration);
        });
    }
    
    stopLayerOscillators(layerName) {
        if (this.oscillators[layerName]) {
            this.oscillators[layerName].forEach(osc => {
                try {
                    osc.stop();
                } catch (e) {
                    // Oscillator might already be stopped
                }
            });
            this.oscillators[layerName] = [];
        }
    }
    
    // Volume control methods
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.setValueAtTime(this.masterVolume, this.audioContext.currentTime);
        }
    }
    
    setLayerVolume(layerName, volume) {
        this.layerVolumes[layerName] = Math.max(0, Math.min(1, volume));
        if (this.musicGains[layerName]) {
            const targetVolume = this.layerVolumes[layerName] * this.masterVolume;
            this.musicGains[layerName].gain.setValueAtTime(targetVolume, this.audioContext.currentTime);
        }
    }
    
    // Event-driven intensity updates
    updateCombatIntensity(intensity) {
        if (this.currentState === 'combat') {
            this.combatIntensity = Math.max(0, Math.min(1, intensity));
            
            // Adjust combat music based on intensity
            if (this.musicGains.combat) {
                const targetVolume = this.layerVolumes.combat * this.combatIntensity * this.masterVolume;
                this.musicGains.combat.gain.linearRampToValueAtTime(
                    targetVolume,
                    this.audioContext.currentTime + 0.5
                );
            }
            
            if (this.musicGains.tension) {
                const targetVolume = this.layerVolumes.tension * this.combatIntensity * this.masterVolume;
                this.musicGains.tension.gain.linearRampToValueAtTime(
                    targetVolume,
                    this.audioContext.currentTime + 0.5
                );
            }
        }
    }
    
    updateBuildingActivity(activity) {
        this.buildingActivity = Math.max(0, Math.min(1, activity));
        
        if (this.currentState === 'building' && this.musicGains.ambient) {
            const targetVolume = this.layerVolumes.ambient * (0.5 + this.buildingActivity * 0.5) * this.masterVolume;
            this.musicGains.ambient.gain.linearRampToValueAtTime(
                targetVolume,
                this.audioContext.currentTime + 1.0
            );
        }
    }
    
    // Cleanup method
    destroy() {
        // Stop all oscillators
        Object.keys(this.oscillators).forEach(layerName => {
            this.stopLayerOscillators(layerName);
        });
        
        // Disconnect audio nodes
        if (this.masterGain) {
            this.masterGain.disconnect();
        }
        
        Object.values(this.musicGains).forEach(gain => {
            if (gain) gain.disconnect();
        });
        
        Object.values(this.filters).forEach(filter => {
            if (filter) filter.disconnect();
        });
        
        Object.values(this.delays).forEach(delay => {
            if (delay) delay.disconnect();
        });
        
        console.log('MusicManager destroyed');
    }
    
    // Debug and state information
    getCurrentState() {
        return {
            currentState: this.currentState,
            previousState: this.previousState,
            isTransitioning: this.isTransitioning,
            combatIntensity: this.combatIntensity,
            buildingActivity: this.buildingActivity,
            selectedFaction: this.selectedFaction,
            masterVolume: this.masterVolume,
            layerVolumes: { ...this.layerVolumes }
        };
    }
    
    getStateHistory() {
        return [...this.gameStateHistory];
    }
}