import { BinaryHeap } from './BinaryHeap.js';

export class AdvancedPathfinder {
    constructor(game) {
        this.game = game;
        this.gridSize = 25;
        this.grid = [];
        
        // Access world dimensions from gameState
        const worldWidth = game.gameState ? game.gameState.world.width : 3000;
        const worldHeight = game.gameState ? game.gameState.world.height : 3000;
        
        this.width = Math.ceil(worldWidth / this.gridSize);
        this.height = Math.ceil(worldHeight / this.gridSize);
        
        // Advanced pathfinding features
        this.unitReservations = new Map(); // Track reserved tiles for units
        this.pathCache = new Map(); // Cache for frequently used paths
        this.hierarchicalGrid = null; // For hierarchical pathfinding
        this.flowField = null; // For flow field pathfinding
        this.trafficDensity = new Map(); // Track traffic density
        this.pathfindingQueue = []; // Queue for pathfinding requests
        this.maxPathfindingPerFrame = 5; // Limit pathfinding calculations per frame
        
        // Performance optimization
        this.lastGridUpdate = 0;
        this.gridUpdateInterval = 100; // Update grid every 100ms
        this.pathValidationCache = new Map();
        
        // Unit collision system
        this.unitPositions = new Map(); // Track unit positions on grid
        this.unitSizes = new Map(); // Track different unit sizes
        
        // Movement types
        this.movementTypes = {
            ground: { canCrossWater: false, canCrossCliffs: false, speedModifier: 1.0 },
            amphibious: { canCrossWater: true, canCrossCliffs: false, speedModifier: 0.8 },
            air: { canCrossWater: true, canCrossCliffs: true, speedModifier: 1.2 },
            hover: { canCrossWater: true, canCrossCliffs: false, speedModifier: 1.1 }
        };
        
        // Terrain movement costs
        this.terrainCosts = {
            desert: 1.0,
            rock: 2.5,
            water: 10.0, // High cost for ground units
            cliff: 999, // Nearly impassable
            spice: 0.9, // Slightly faster on spice
            road: 0.7 // Faster on roads
        };
        
        this.initGrid();
        this.initHierarchicalGrid();
    }
    
    initGrid() {
        this.grid = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(null).map(() => ({
                walkable: true,
                cost: 1.0,
                terrainType: 'desert',
                reserved: false,
                reservedBy: null,
                trafficDensity: 0
            }))
        );
    }
    
    initHierarchicalGrid() {
        // Create a hierarchical grid for long-distance pathfinding
        const clusterSize = 8;
        this.hierarchicalGrid = {
            clusterSize,
            clusters: [],
            connections: new Map()
        };
        
        const clustersX = Math.ceil(this.width / clusterSize);
        const clustersY = Math.ceil(this.height / clusterSize);
        
        for (let y = 0; y < clustersY; y++) {
            this.hierarchicalGrid.clusters[y] = [];
            for (let x = 0; x < clustersX; x++) {
                this.hierarchicalGrid.clusters[y][x] = {
                    x, y,
                    walkable: true,
                    entrances: [],
                    cost: 1.0
                };
            }
        }
    }
    
    updateGridObstacles() {
        const now = performance.now();
        if (now - this.lastGridUpdate < this.gridUpdateInterval) return;
        this.lastGridUpdate = now;
        
        // Reset grid
        this.initGrid();
        
        // Update buildings
        this.game.buildings.forEach(building => {
            if (building.constructionProgress < 100 && building.type !== 'wall') return;
            this.addBuildingToGrid(building);
        });
        
        // Update terrain
        this.game.terrain.forEach(obstacle => {
            this.addTerrainToGrid(obstacle);
        });
        
        // Update unit positions for collision detection
        this.updateUnitPositions();
        
        // Update hierarchical grid
        this.updateHierarchicalGrid();
        
        // Clear path cache when grid changes
        this.pathCache.clear();
        this.pathValidationCache.clear();
    }
    
    addBuildingToGrid(building) {
        const margin = building.type === 'wall' ? 0 : this.gridSize / 3;
        const startX = Math.floor((building.x - building.width/2 - margin) / this.gridSize);
        const startY = Math.floor((building.y - building.height/2 - margin) / this.gridSize);
        const endX = Math.ceil((building.x + building.width/2 + margin) / this.gridSize);
        const endY = Math.ceil((building.y + building.height/2 + margin) / this.gridSize);
        
        for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
            for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                this.grid[y][x].walkable = false;
                this.grid[y][x].cost = 999;
            }
        }
    }
    
    addTerrainToGrid(obstacle) {
        const startX = Math.floor((obstacle.x - obstacle.radius) / this.gridSize);
        const startY = Math.floor((obstacle.y - obstacle.radius) / this.gridSize);
        const endX = Math.ceil((obstacle.x + obstacle.radius) / this.gridSize);
        const endY = Math.ceil((obstacle.y + obstacle.radius) / this.gridSize);
        
        for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
            for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                const cellCenterX = x * this.gridSize + this.gridSize / 2;
                const cellCenterY = y * this.gridSize + this.gridSize / 2;
                const distance = Math.hypot(cellCenterX - obstacle.x, cellCenterY - obstacle.y);
                
                if (distance < obstacle.radius) {
                    const cell = this.grid[y][x];
                    cell.terrainType = obstacle.type;
                    cell.cost = this.terrainCosts[obstacle.type] || 1.0;
                    
                    // Some terrain types are impassable for certain movement types
                    if (obstacle.type === 'rock' || obstacle.type === 'cliff') {
                        cell.walkable = false;
                    }
                }
            }
        }
    }
    
    updateUnitPositions() {
        this.unitPositions.clear();
        
        this.game.units.forEach(unit => {
            const gridX = Math.floor(unit.x / this.gridSize);
            const gridY = Math.floor(unit.y / this.gridSize);
            
            if (this.isValidGridPosition(gridX, gridY)) {
                const key = `${gridX},${gridY}`;
                if (!this.unitPositions.has(key)) {
                    this.unitPositions.set(key, []);
                }
                this.unitPositions.get(key).push(unit);
                
                // Store unit size for collision detection
                this.unitSizes.set(unit.id, unit.size || 10);
            }
        });
    }
    
    updateHierarchicalGrid() {
        const clusterSize = this.hierarchicalGrid.clusterSize;
        
        // Update cluster walkability
        for (let cy = 0; cy < this.hierarchicalGrid.clusters.length; cy++) {
            for (let cx = 0; cx < this.hierarchicalGrid.clusters[cy].length; cx++) {
                const cluster = this.hierarchicalGrid.clusters[cy][cx];
                let walkableCells = 0;
                let totalCells = 0;
                
                for (let y = cy * clusterSize; y < Math.min((cy + 1) * clusterSize, this.height); y++) {
                    for (let x = cx * clusterSize; x < Math.min((cx + 1) * clusterSize, this.width); x++) {
                        totalCells++;
                        if (this.grid[y][x].walkable) {
                            walkableCells++;
                        }
                    }
                }
                
                cluster.walkable = walkableCells > totalCells * 0.3; // At least 30% walkable
                cluster.cost = totalCells > 0 ? (totalCells - walkableCells) / totalCells + 1 : 999;
            }
        }
    }
    
    // Enhanced A* pathfinding with optimizations
    findPath(startX, startY, endX, endY, unit = null, options = {}) {
        const request = {
            startX, startY, endX, endY, unit, options,
            priority: options.priority || 1,
            timestamp: performance.now()
        };
        
        // Check cache first
        const cacheKey = `${Math.floor(startX/this.gridSize)},${Math.floor(startY/this.gridSize)}-${Math.floor(endX/this.gridSize)},${Math.floor(endY/this.gridSize)}`;
        if (this.pathCache.has(cacheKey) && !options.ignoreCache) {
            const cachedPath = this.pathCache.get(cacheKey);
            if (this.isPathValid(cachedPath, unit)) {
                return this.adaptCachedPath(cachedPath, startX, startY, endX, endY);
            }
        }
        
        // Use hierarchical pathfinding for long distances
        const distance = Math.hypot(endX - startX, endY - startY);
        if (distance > this.gridSize * 20 && !options.forceDetailed) {
            return this.hierarchicalPathfind(startX, startY, endX, endY, unit, options);
        }
        
        // Standard A* pathfinding
        return this.aStarPathfind(startX, startY, endX, endY, unit, options);
    }
    
    aStarPathfind(startX, startY, endX, endY, unit = null, options = {}) {
        const startNode = {
            x: Math.min(this.width - 1, Math.max(0, Math.floor(startX / this.gridSize))),
            y: Math.min(this.height - 1, Math.max(0, Math.floor(startY / this.gridSize)))
        };
        const endNode = {
            x: Math.min(this.width - 1, Math.max(0, Math.floor(endX / this.gridSize))),
            y: Math.min(this.height - 1, Math.max(0, Math.floor(endY / this.gridSize)))
        };
        
        // Validate bounds
        if (!this.isValidGridPosition(startNode.x, startNode.y) || 
            !this.isValidGridPosition(endNode.x, endNode.y)) {
            return [{ x: endX, y: endY }];
        }
        
        // Find alternative end position if blocked
        if (!this.isWalkable(endNode.x, endNode.y, unit)) {
            const newEnd = this.findNearestWalkable(endNode.x, endNode.y, unit);
            if (newEnd) {
                endNode.x = newEnd.x;
                endNode.y = newEnd.y;
            } else {
                return [{ x: endX, y: endY }];
            }
        }
        
        const openSet = new BinaryHeap(node => node.f);
        const closedSet = new Set();
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();
        
        const getKey = pos => `${pos.x},${pos.y}`;
        const startKey = getKey(startNode);
        
        gScore.set(startKey, 0);
        fScore.set(startKey, this.heuristic(startNode, endNode));
        openSet.push({ ...startNode, f: fScore.get(startKey), g: 0 });
        
        let iterations = 0;
        const maxIterations = Math.min(this.width * this.height / 4, 2000);
        
        while (openSet.size() > 0 && iterations < maxIterations) {
            iterations++;
            const current = openSet.pop();
            const currentKey = getKey(current);
            
            if (current.x === endNode.x && current.y === endNode.y) {
                const path = this.reconstructPath(cameFrom, current, endX, endY);
                this.cachePathIfUseful(path, startX, startY, endX, endY);
                return this.optimizePath(path, unit);
            }
            
            closedSet.add(currentKey);
            
            const neighbors = this.getNeighbors(current, unit, options);
            for (const neighbor of neighbors) {
                const neighborKey = getKey(neighbor);
                
                if (closedSet.has(neighborKey)) continue;
                
                const tentativeG = gScore.get(currentKey) + neighbor.cost;
                
                if (!gScore.has(neighborKey) || tentativeG < gScore.get(neighborKey)) {
                    cameFrom.set(neighborKey, current);
                    gScore.set(neighborKey, tentativeG);
                    const h = this.heuristic(neighbor, endNode);
                    const f = tentativeG + h;
                    fScore.set(neighborKey, f);
                    
                    // Add traffic avoidance
                    const trafficPenalty = this.getTrafficPenalty(neighbor.x, neighbor.y);
                    const adjustedF = f + trafficPenalty;
                    
                    if (!openSet.contains(neighbor)) {
                        openSet.push({ ...neighbor, f: adjustedF, g: tentativeG });
                    }
                }
            }
        }
        
        return [{ x: endX, y: endY }]; // Fallback
    }
    
    hierarchicalPathfind(startX, startY, endX, endY, unit = null, options = {}) {
        const clusterSize = this.hierarchicalGrid.clusterSize;
        
        const startCluster = {
            x: Math.floor(Math.floor(startX / this.gridSize) / clusterSize),
            y: Math.floor(Math.floor(startY / this.gridSize) / clusterSize)
        };
        const endCluster = {
            x: Math.floor(Math.floor(endX / this.gridSize) / clusterSize),
            y: Math.floor(Math.floor(endY / this.gridSize) / clusterSize)
        };
        
        // Find high-level path between clusters
        const clusterPath = this.findClusterPath(startCluster, endCluster, unit);
        if (!clusterPath || clusterPath.length === 0) {
            return this.aStarPathfind(startX, startY, endX, endY, unit, options);
        }
        
        // Convert cluster path to detailed path
        const detailedPath = [];
        let currentX = startX, currentY = startY;
        
        for (let i = 0; i < clusterPath.length; i++) {
            const cluster = clusterPath[i];
            const targetX = (cluster.x + 0.5) * clusterSize * this.gridSize;
            const targetY = (cluster.y + 0.5) * clusterSize * this.gridSize;
            
            // Find detailed path to this cluster
            const segmentPath = this.aStarPathfind(currentX, currentY, targetX, targetY, unit, 
                { ...options, forceDetailed: true });
            
            if (segmentPath && segmentPath.length > 0) {
                detailedPath.push(...segmentPath.slice(i === 0 ? 0 : 1));
                currentX = segmentPath[segmentPath.length - 1].x;
                currentY = segmentPath[segmentPath.length - 1].y;
            }
        }
        
        // Final segment to exact destination
        if (detailedPath.length > 0) {
            const finalSegment = this.aStarPathfind(currentX, currentY, endX, endY, unit, 
                { ...options, forceDetailed: true });
            if (finalSegment && finalSegment.length > 1) {
                detailedPath.push(...finalSegment.slice(1));
            }
        }
        
        return detailedPath.length > 0 ? this.optimizePath(detailedPath, unit) : [{ x: endX, y: endY }];
    }
    
    findClusterPath(startCluster, endCluster, unit = null) {
        const openSet = new BinaryHeap(node => node.f);
        const closedSet = new Set();
        const cameFrom = new Map();
        const gScore = new Map();
        
        const getKey = pos => `${pos.x},${pos.y}`;
        const startKey = getKey(startCluster);
        
        gScore.set(startKey, 0);
        openSet.push({ 
            ...startCluster, 
            f: this.heuristic(startCluster, endCluster), 
            g: 0 
        });
        
        while (openSet.size() > 0) {
            const current = openSet.pop();
            const currentKey = getKey(current);
            
            if (current.x === endCluster.x && current.y === endCluster.y) {
                return this.reconstructClusterPath(cameFrom, current);
            }
            
            closedSet.add(currentKey);
            
            const neighbors = this.getClusterNeighbors(current);
            for (const neighbor of neighbors) {
                const neighborKey = getKey(neighbor);
                
                if (closedSet.has(neighborKey)) continue;
                
                const tentativeG = gScore.get(currentKey) + neighbor.cost;
                
                if (!gScore.has(neighborKey) || tentativeG < gScore.get(neighborKey)) {
                    cameFrom.set(neighborKey, current);
                    gScore.set(neighborKey, tentativeG);
                    const f = tentativeG + this.heuristic(neighbor, endCluster);
                    
                    if (!openSet.contains(neighbor)) {
                        openSet.push({ ...neighbor, f, g: tentativeG });
                    }
                }
            }
        }
        
        return null;
    }
    
    getClusterNeighbors(cluster) {
        const neighbors = [];
        const dirs = [
            { x: 0, y: -1 }, { x: 1, y: 0 }, { x: 0, y: 1 }, { x: -1, y: 0 },
            { x: -1, y: -1 }, { x: 1, y: -1 }, { x: 1, y: 1 }, { x: -1, y: 1 }
        ];
        
        for (const dir of dirs) {
            const x = cluster.x + dir.x;
            const y = cluster.y + dir.y;
            
            if (x >= 0 && x < this.hierarchicalGrid.clusters[0].length &&
                y >= 0 && y < this.hierarchicalGrid.clusters.length) {
                const neighborCluster = this.hierarchicalGrid.clusters[y][x];
                if (neighborCluster.walkable) {
                    neighbors.push({
                        x, y,
                        cost: neighborCluster.cost * (dir.x !== 0 && dir.y !== 0 ? 1.414 : 1.0)
                    });
                }
            }
        }
        
        return neighbors;
    }
    
    reconstructClusterPath(cameFrom, current) {
        const path = [];
        const getKey = pos => `${pos.x},${pos.y}`;
        
        while (current) {
            path.unshift(current);
            current = cameFrom.get(getKey(current));
        }
        
        return path;
    }
    
    // Enhanced neighbor detection with movement types and collision avoidance
    getNeighbors(pos, unit = null, options = {}) {
        const neighbors = [];
        const dirs = [
            { x: 0, y: -1, cost: 1.0 }, { x: 1, y: 0, cost: 1.0 }, 
            { x: 0, y: 1, cost: 1.0 }, { x: -1, y: 0, cost: 1.0 },
            { x: -1, y: -1, cost: 1.414 }, { x: 1, y: -1, cost: 1.414 }, 
            { x: 1, y: 1, cost: 1.414 }, { x: -1, y: 1, cost: 1.414 }
        ];
        
        const movementType = this.getUnitMovementType(unit);
        
        for (const dir of dirs) {
            const x = pos.x + dir.x;
            const y = pos.y + dir.y;
            
            if (this.isValidGridPosition(x, y) && this.isWalkable(x, y, unit, movementType)) {
                const cell = this.grid[y][x];
                let cost = dir.cost * cell.cost;
                
                // Apply movement type modifiers
                cost *= this.getTerrainCostForMovementType(cell.terrainType, movementType);
                
                // Add collision avoidance cost
                cost += this.getCollisionAvoidanceCost(x, y, unit);
                
                // Add traffic density penalty
                cost += this.getTrafficPenalty(x, y);
                
                neighbors.push({ x, y, cost });
            }
        }
        
        return neighbors;
    }
    
    getUnitMovementType(unit) {
        if (!unit) return this.movementTypes.ground;
        
        // Determine movement type based on unit properties
        if (unit.canFly || unit.movementType === 'air') return this.movementTypes.air;
        if (unit.amphibious || unit.movementType === 'amphibious') return this.movementTypes.amphibious;
        if (unit.hover || unit.movementType === 'hover') return this.movementTypes.hover;
        
        return this.movementTypes.ground;
    }
    
    getTerrainCostForMovementType(terrainType, movementType) {
        switch (terrainType) {
            case 'water':
                return movementType.canCrossWater ? 2.0 : 50.0;
            case 'cliff':
                return movementType.canCrossCliffs ? 3.0 : 100.0;
            default:
                return 1.0;
        }
    }
    
    getCollisionAvoidanceCost(x, y, unit) {
        const key = `${x},${y}`;
        const unitsAtPosition = this.unitPositions.get(key) || [];
        
        if (unitsAtPosition.length === 0) return 0;
        
        // Higher cost for positions with more units
        let cost = unitsAtPosition.length * 2.0;
        
        // Additional cost based on unit sizes
        if (unit) {
            const unitSize = this.unitSizes.get(unit.id) || 10;
            for (const otherUnit of unitsAtPosition) {
                if (otherUnit.id !== unit.id) {
                    const otherSize = this.unitSizes.get(otherUnit.id) || 10;
                    cost += (unitSize + otherSize) / 20.0;
                }
            }
        }
        
        return Math.min(cost, 10.0); // Cap the collision cost
    }
    
    getTrafficPenalty(x, y) {
        const key = `${x},${y}`;
        const density = this.trafficDensity.get(key) || 0;
        return Math.min(density * 0.5, 5.0); // Cap traffic penalty
    }
    
    updateTrafficDensity() {
        // Decay existing traffic density
        for (const [key, density] of this.trafficDensity.entries()) {
            const newDensity = density * 0.95; // 5% decay per update
            if (newDensity < 0.1) {
                this.trafficDensity.delete(key);
            } else {
                this.trafficDensity.set(key, newDensity);
            }
        }
        
        // Add current unit positions to traffic density
        for (const [key, units] of this.unitPositions.entries()) {
            const currentDensity = this.trafficDensity.get(key) || 0;
            this.trafficDensity.set(key, currentDensity + units.length * 0.1);
        }
    }
    
    isWalkable(x, y, unit = null, movementType = null) {
        if (!this.isValidGridPosition(x, y)) return false;
        
        const cell = this.grid[y][x];
        if (!cell.walkable) return false;
        
        // Check movement type restrictions
        if (movementType) {
            if (cell.terrainType === 'water' && !movementType.canCrossWater) return false;
            if (cell.terrainType === 'cliff' && !movementType.canCrossCliffs) return false;
        }
        
        // Check unit reservations
        if (cell.reserved && cell.reservedBy !== unit?.id) {
            return false;
        }
        
        return true;
    }
    
    isValidGridPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height;
    }
    
    findNearestWalkable(x, y, unit = null, maxRadius = 10) {
        for (let radius = 1; radius <= maxRadius; radius++) {
            for (let dy = -radius; dy <= radius; dy++) {
                for (let dx = -radius; dx <= radius; dx++) {
                    if (Math.abs(dx) !== radius && Math.abs(dy) !== radius) continue;
                    
                    const nx = x + dx;
                    const ny = y + dy;
                    
                    if (this.isWalkable(nx, ny, unit)) {
                        return { x: nx, y: ny };
                    }
                }
            }
        }
        return null;
    }
    
    // Unit reservation system for collision prevention
    reservePath(unit, path) {
        this.clearUnitReservations(unit);
        
        if (!path || path.length === 0) return;
        
        for (const waypoint of path) {
            const gridX = Math.floor(waypoint.x / this.gridSize);
            const gridY = Math.floor(waypoint.y / this.gridSize);
            
            if (this.isValidGridPosition(gridX, gridY)) {
                const cell = this.grid[gridY][gridX];
                cell.reserved = true;
                cell.reservedBy = unit.id;
            }
        }
    }
    
    clearUnitReservations(unit) {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const cell = this.grid[y][x];
                if (cell.reservedBy === unit.id) {
                    cell.reserved = false;
                    cell.reservedBy = null;
                }
            }
        }
    }
    
    // Path optimization and smoothing
    optimizePath(path, unit = null) {
        if (!path || path.length < 3) return path;
        
        // Apply path smoothing
        let smoothedPath = this.smoothPath(path);
        
        // Apply formation spacing for group movement
        if (unit && unit.formationPosition) {
            smoothedPath = this.applyFormationOffset(smoothedPath, unit.formationPosition);
        }
        
        return smoothedPath;
    }
    
    smoothPath(path) {
        if (path.length < 3) return path;
        
        const smoothed = [path[0]];
        let currentIdx = 0;
        
        while (currentIdx < path.length - 1) {
            let furthestIdx = currentIdx + 1;
            
            // Find the furthest point we can see directly
            for (let i = currentIdx + 2; i < path.length; i++) {
                if (this.hasLineOfSight(path[currentIdx], path[i])) {
                    furthestIdx = i;
                } else {
                    break;
                }
            }
            
            smoothed.push(path[furthestIdx]);
            currentIdx = furthestIdx;
        }
        
        return smoothed;
    }
    
    hasLineOfSight(p1, p2) {
        const x0 = Math.floor(p1.x / this.gridSize);
        const y0 = Math.floor(p1.y / this.gridSize);
        const x1 = Math.floor(p2.x / this.gridSize);
        const y1 = Math.floor(p2.y / this.gridSize);
        
        const dx = Math.abs(x1 - x0);
        const dy = -Math.abs(y1 - y0);
        const sx = x0 < x1 ? 1 : -1;
        const sy = y0 < y1 ? 1 : -1;
        let err = dx + dy;
        
        let x = x0, y = y0;
        
        while (true) {
            if (!this.isValidGridPosition(x, y) || !this.grid[y][x].walkable) {
                return false;
            }
            
            if (x === x1 && y === y1) break;
            
            const e2 = 2 * err;
            if (e2 >= dy) { err += dy; x += sx; }
            if (e2 <= dx) { err += dx; y += sy; }
        }
        
        return true;
    }
    
    applyFormationOffset(path, formationOffset) {
        return path.map(waypoint => ({
            x: waypoint.x + formationOffset.x,
            y: waypoint.y + formationOffset.y
        }));
    }
    
    // Path caching system
    cachePathIfUseful(path, startX, startY, endX, endY) {
        if (!path || path.length < 3) return;
        
        const cacheKey = `${Math.floor(startX/this.gridSize)},${Math.floor(startY/this.gridSize)}-${Math.floor(endX/this.gridSize)},${Math.floor(endY/this.gridSize)}`;
        
        // Only cache longer paths
        if (path.length > 5) {
            this.pathCache.set(cacheKey, {
                path: [...path],
                timestamp: performance.now(),
                useCount: 0
            });
            
            // Limit cache size
            if (this.pathCache.size > 100) {
                const oldestKey = Array.from(this.pathCache.keys())[0];
                this.pathCache.delete(oldestKey);
            }
        }
    }
    
    isPathValid(cachedPath, unit) {
        if (!cachedPath || performance.now() - cachedPath.timestamp > 5000) {
            return false; // Path too old
        }
        
        // Quick validation - check a few key points
        const path = cachedPath.path;
        const checkPoints = Math.min(5, path.length);
        
        for (let i = 0; i < checkPoints; i++) {
            const idx = Math.floor(i * (path.length - 1) / (checkPoints - 1));
            const point = path[idx];
            const gridX = Math.floor(point.x / this.gridSize);
            const gridY = Math.floor(point.y / this.gridSize);
            
            if (!this.isWalkable(gridX, gridY, unit)) {
                return false;
            }
        }
        
        return true;
    }
    
    adaptCachedPath(cachedPath, startX, startY, endX, endY) {
        const path = [...cachedPath.path];
        
        // Adjust start and end points
        if (path.length > 0) {
            path[0] = { x: startX, y: startY };
            path[path.length - 1] = { x: endX, y: endY };
        }
        
        cachedPath.useCount++;
        return path;
    }
    
    // Formation movement support
    calculateFormationPositions(units, centerX, centerY, formationType = 'line') {
        const positions = [];
        const spacing = 40;
        
        switch (formationType) {
            case 'line':
                units.forEach((unit, index) => {
                    const offset = (index - (units.length - 1) / 2) * spacing;
                    positions.push({
                        unit,
                        position: { x: centerX + offset, y: centerY },
                        formationOffset: { x: offset, y: 0 }
                    });
                });
                break;
                
            case 'column':
                units.forEach((unit, index) => {
                    const offset = (index - (units.length - 1) / 2) * spacing;
                    positions.push({
                        unit,
                        position: { x: centerX, y: centerY + offset },
                        formationOffset: { x: 0, y: offset }
                    });
                });
                break;
                
            case 'wedge':
                units.forEach((unit, index) => {
                    const row = Math.floor(Math.sqrt(index));
                    const col = index - row * row;
                    const offsetX = (col - row / 2) * spacing;
                    const offsetY = row * spacing;
                    positions.push({
                        unit,
                        position: { x: centerX + offsetX, y: centerY + offsetY },
                        formationOffset: { x: offsetX, y: offsetY }
                    });
                });
                break;
                
            default: // box formation
                const cols = Math.ceil(Math.sqrt(units.length));
                units.forEach((unit, index) => {
                    const row = Math.floor(index / cols);
                    const col = index % cols;
                    const offsetX = (col - (cols - 1) / 2) * spacing;
                    const offsetY = (row - Math.floor(units.length / cols) / 2) * spacing;
                    positions.push({
                        unit,
                        position: { x: centerX + offsetX, y: centerY + offsetY },
                        formationOffset: { x: offsetX, y: offsetY }
                    });
                });
        }
        
        return positions;
    }
    
    // Group pathfinding for coordinated movement
    findGroupPaths(units, targetX, targetY, formationType = 'box') {
        if (units.length === 0) return [];
        if (units.length === 1) {
            return [{ unit: units[0], path: this.findPath(units[0].x, units[0].y, targetX, targetY, units[0]) }];
        }
        
        // Calculate formation positions
        const formationPositions = this.calculateFormationPositions(units, targetX, targetY, formationType);
        
        // Find paths for each unit to their formation position
        const groupPaths = [];
        
        for (const { unit, position, formationOffset } of formationPositions) {
            unit.formationPosition = formationOffset;
            const path = this.findPath(unit.x, unit.y, position.x, position.y, unit, { groupMovement: true });
            groupPaths.push({ unit, path, formationPosition: position });
            
            // Reserve the path to prevent conflicts
            this.reservePath(unit, path);
        }
        
        return groupPaths;
    }
    
    // Utility methods
    heuristic(a, b) {
        // Octile distance (allows diagonal movement)
        const dx = Math.abs(a.x - b.x);
        const dy = Math.abs(a.y - b.y);
        return Math.max(dx, dy) + (1.414 - 1) * Math.min(dx, dy);
    }
    
    reconstructPath(cameFrom, current, finalTargetX, finalTargetY) {
        const path = [];
        const getKey = pos => `${pos.x},${pos.y}`;
        
        while (cameFrom.has(getKey(current))) {
            path.unshift({
                x: current.x * this.gridSize + this.gridSize / 2,
                y: current.y * this.gridSize + this.gridSize / 2
            });
            current = cameFrom.get(getKey(current));
        }
        
        // Adjust final destination
        if (path.length > 0) {
            path[path.length - 1] = { x: finalTargetX, y: finalTargetY };
        } else {
            path.push({ x: finalTargetX, y: finalTargetY });
        }
        
        return path;
    }
    
    // Performance monitoring
    getPerformanceStats() {
        return {
            cacheSize: this.pathCache.size,
            trafficDensitySize: this.trafficDensity.size,
            unitPositionsSize: this.unitPositions.size,
            queueLength: this.pathfindingQueue.length
        };
    }
    
    // Cleanup methods
    cleanup() {
        // Clear old cache entries
        const now = performance.now();
        for (const [key, cached] of this.pathCache.entries()) {
            if (now - cached.timestamp > 10000) { // 10 seconds
                this.pathCache.delete(key);
            }
        }
        
        // Clear traffic density
        this.updateTrafficDensity();
        
        // Clear reservations for dead units
        const activeUnitIds = new Set(this.game.units.map(u => u.id));
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const cell = this.grid[y][x];
                if (cell.reserved && !activeUnitIds.has(cell.reservedBy)) {
                    cell.reserved = false;
                    cell.reservedBy = null;
                }
            }
        }
    }
}