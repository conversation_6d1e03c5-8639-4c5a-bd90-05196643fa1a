import { MusicManager } from './MusicManager.js';

export class SoundManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.enabled = true;
        this.musicManager = null;
        this.musicEnabled = true;
        
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            if (this.audioContext) {
                this.initSounds();
                this.initMusicManager();
            }
        } catch (e) {
            console.warn('Web Audio API not supported or failed to initialize.');
            this.enabled = false;
        }
    }
    
    initMusicManager() {
        try {
            this.musicManager = new MusicManager(this);
            console.log('Music system initialized');
        } catch (e) {
            console.warn('Failed to initialize music system:', e);
            this.musicEnabled = false;
        }
    }
    initSounds() {
        this.sounds = {
            select: () => this.playTone(800, 0.05, 0.2),
            move: () => this.playTone(400, 0.05, 0.2),
            attack: () => this.playNoise(0.08, 0.1),
            build: () => this.playTone(600, 0.15, 0.25),
            complete: () => this.playTone(1200, 0.2, 0.3),
            warning: () => this.playTone(300, 0.3, 0.2),
            explosion: () => this.playExplosion(0.15)
        };
    }
    playTone(frequency, duration, gainValue = 0.3) {
        if (!this.enabled || !this.audioContext) return;
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
    playNoise(duration, gainValue = 0.1) {
        if (!this.enabled || !this.audioContext) return;
        const bufferSize = Math.floor(this.audioContext.sampleRate * duration);
        if (bufferSize <= 0) return;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);
        for (let i = 0; i < bufferSize; i++) output[i] = Math.random() * 2 - 1;
        const whiteNoise = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        whiteNoise.buffer = buffer;
        whiteNoise.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
        whiteNoise.start(this.audioContext.currentTime);
        whiteNoise.stop(this.audioContext.currentTime + duration);
    }
    playExplosion(baseGain = 0.15) {
        if (!this.enabled || !this.audioContext) return;
        this.playNoise(0.25, baseGain);
        setTimeout(() => this.playTone(100, 0.15, baseGain * 0.8), 50);
    }
    play(sound) {
        if (this.enabled && this.sounds[sound]) this.sounds[sound]();
    }
    
    // Music control methods
    onGameStateChange(newState, context = {}) {
        if (this.musicEnabled && this.musicManager) {
            this.musicManager.onGameStateChange(newState, context);
        }
    }
    
    playFactionTheme(factionName) {
        if (this.musicEnabled && this.musicManager) {
            this.musicManager.playFactionTheme(factionName);
        }
    }
    
    updateCombatIntensity(intensity) {
        if (this.musicEnabled && this.musicManager) {
            this.musicManager.updateCombatIntensity(intensity);
        }
    }
    
    updateBuildingActivity(activity) {
        if (this.musicEnabled && this.musicManager) {
            this.musicManager.updateBuildingActivity(activity);
        }
    }
    
    setMusicVolume(volume) {
        if (this.musicEnabled && this.musicManager) {
            this.musicManager.setMasterVolume(volume);
        }
    }
    
    toggleMusic() {
        this.musicEnabled = !this.musicEnabled;
        if (!this.musicEnabled && this.musicManager) {
            this.musicManager.fadeOutAllLayers(1.0);
        } else if (this.musicEnabled && this.musicManager) {
            this.musicManager.onGameStateChange('peaceful');
        }
        return this.musicEnabled;
    }
    
    getMusicState() {
        if (this.musicEnabled && this.musicManager) {
            return this.musicManager.getCurrentState();
        }
        return null;
    }
    
    // Cleanup method
    destroy() {
        if (this.musicManager) {
            this.musicManager.destroy();
        }
    }
}
