import { Unit } from './Unit.js';

class NeutralEnemies {
    constructor(game) {
        this.game = game;
        this.neutralUnits = [];
        this.neutralUnitTypes = this.getNeutralUnitTypes();

        // Augment game.unitTypes with neutral unit definitions
        // This allows the Unit class to find these types if it primarily looks in game.unitTypes
        if (!this.game.unitTypes) {
            this.game.unitTypes = {}; // Ensure it's initialized
        }
        for (const typeKey in this.neutralUnitTypes) {
            if (Object.prototype.hasOwnProperty.call(this.neutralUnitTypes, typeKey)) {
                // Add or overwrite in game.unitTypes. The spread ensures a copy.
                this.game.unitTypes[typeKey] = { ...this.neutralUnitTypes[typeKey] };
            }
        }

        this.spawnInitialNeutralUnits();
    }

    getNeutralUnitTypes() {
        return {
        marauder_scout: {
            name: 'Marauder Scout',
            health: 80,
            maxHealth: 80,
            speed: 2.2,
            damage: 3, // Reduced from 10
            armor: 3,
            regenRate: 0.3,
            range: 100,
            fireRate: 0.05,
            attackCooldown: 4.0,
            size: 12,
            visionRadius: 250,
            aggroRange: 200,
            color: '#8B4513',
            behavior: 'aggressive',
            xpReward: 75,
            level: 2
        },
        marauder_raider: {
            name: 'Marauder Raider',
            health: 150,
            maxHealth: 150,
            speed: 2.0,
            damage: 6, // Reduced from 18
            armor: 5,
            regenRate: 0.4,
            range: 120,
            fireRate: 0.05,
            attackCooldown: 6.0,
            size: 15,
            visionRadius: 280,
            aggroRange: 250,
            color: '#A0522D',
            behavior: 'aggressive',
            xpReward: 100,
            level: 3
        },
        marauder_heavy: {
            name: 'Marauder Heavy',
            health: 300,
            maxHealth: 300,
            speed: 1.5,
            damage: 10, // Reduced from 35
            armor: 8,
            regenRate: 0.6,
            range: 140,
            fireRate: 0.05,
            attackCooldown: 10.0,
            size: 20,
            visionRadius: 300,
            aggroRange: 280,
            color: '#654321',
            behavior: 'aggressive',
            xpReward: 150,
            level: 4
        },
        pirate_fighter: {
            name: 'Pirate Fighter',
            health: 100,
            maxHealth: 100,
            speed: 2.0,
            damage: 4, // Reduced from 14
            armor: 4,
            regenRate: 0.3,
            range: 110,
            fireRate: 0.05,
            attackCooldown: 5.0,
            size: 13,
            visionRadius: 260,
            aggroRange: 220,
            color: '#DC143C',
            behavior: 'hit_and_run',
            xpReward: 85,
            level: 2
        },
        pirate_corsair: {
            name: 'Pirate Corsair',
            health: 200,
            maxHealth: 200,
            speed: 1.8,
            damage: 8, // Reduced from 25
            armor: 6,
            regenRate: 0.5,
            range: 160,
            fireRate: 0.05,
            attackCooldown: 7.0,
            size: 16,
            visionRadius: 320,
            aggroRange: 300,
            color: '#B22222',
            behavior: 'hit_and_run',
            xpReward: 120,
            level: 3
        },
        pirate_captain: {
            name: 'Pirate Captain',
            health: 400,
            maxHealth: 400,
            speed: 1.6,
            damage: 15, // Reduced from 45
            armor: 10,
            regenRate: 0.8,
            range: 180,
            fireRate: 0.05,
            attackCooldown: 12.0,
            size: 22,
            visionRadius: 350,
            aggroRange: 320,
            color: '#8B0000',
            behavior: 'tactical',
            xpReward: 200,
            level: 5 // Added missing level
        }
    };
    }

    spawnInitialNeutralUnits() {
        // Example: Spawn a few neutral units
        // You can customize this logic to spawn units based on game progression, map areas, etc.
        const unitTypes = Object.keys(this.neutralUnitTypes);
        for (let i = 0; i < 10; i++) { // Spawn 10 random neutral units
            const randomTypeKey = unitTypes[Math.floor(Math.random() * unitTypes.length)];
            const unitData = this.neutralUnitTypes[randomTypeKey];
            
            // Find a valid spawn position (simple example, can be improved)
            let spawnX, spawnY, validPosition = false;
            let attempts = 0;
            while (!validPosition && attempts < 50) {
                spawnX = Math.random() * this.game.width;
                spawnY = Math.random() * this.game.height;
                // Basic check: not too close to player/enemy start or other neutrals
                const tooCloseToPlayer = Math.hypot(spawnX - 400, spawnY - 400) < 500;
                const tooCloseToEnemy = Math.hypot(spawnX - 2600, spawnY - 2600) < 500;
                let tooCloseToOtherNeutral = false;
                for (const nu of this.neutralUnits) {
                    if (Math.hypot(spawnX - nu.x, spawnY - nu.y) < 100) {
                        tooCloseToOtherNeutral = true;
                        break;
                    }
                }
                if (!tooCloseToPlayer && !tooCloseToEnemy && !tooCloseToOtherNeutral) {
                    validPosition = true;
                }
                attempts++;
            }

            if (validPosition) {
                const neutralUnit = new Unit(this.game, {
                    type: randomTypeKey, // This will be used by Unit to get base stats
                    x: spawnX,
                    y: spawnY,
                    team: 'neutral', // Special team for neutral entities
                    ...unitData // Spread the specific stats for this neutral type
                });
                this.game.levelingSystem.initializeEntityLevel(neutralUnit, unitData.level); // Initialize level
                this.neutralUnits.push(neutralUnit);
            }
        }
    }

    update(deltaTime) {
        for (let i = this.neutralUnits.length - 1; i >= 0; i--) {
            const unit = this.neutralUnits[i];
            unit.update(deltaTime); // Assuming Unit class has an update method
            if (unit.health <= 0) {
                // Handle neutral unit death (e.g., remove from array, drop loot)
                if (unit.lastAttacker && unit.lastAttacker.team === 'player') {
                    this.game.levelingSystem.awardActionXP(unit.lastAttacker, 'kill_neutral');
                     this.game.teams.player.stats.enemiesDestroyed++; // Count as enemy destroyed
                }
                this.game.lootSystem.dropLoot(unit, 'neutral'); // Drop neutral-specific loot
                this.game.createExplosion(unit.x, unit.y, unit.size * 1.5, 'normal'); // Death explosion
                this.neutralUnits.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.neutralUnits.forEach(unit => {
            if (unit.health > 0) {
                unit.draw(ctx); // Assuming Unit class has a draw method
            }
        });
    }
}

// Export the class
export default NeutralEnemies;
