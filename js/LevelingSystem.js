export class LevelingSystem {
    constructor() {
        this.maxLevel = 10;
        this.baseXPRequired = 100;
        this.xpMultiplier = 1.5;
    }

    // Calculate XP required for next level
    getXPRequiredForLevel(level) {
        if (level <= 1) return this.baseXPRequired;
        return Math.floor(this.baseXPRequired * Math.pow(this.xpMultiplier, level - 1));
    }

    // Calculate total XP required to reach a level
    getTotalXPForLevel(level) {
        let total = 0;
        for (let i = 1; i < level; i++) {
            total += this.getXPRequiredForLevel(i);
        }
        return total;
    }

    // Add XP to an entity and handle level ups
    addXP(entity, amount) {
        if (!entity.level) this.initializeEntityLevel(entity);
        
        entity.xp += amount;
        
        // Check for level up
        while (entity.xp >= this.getXPRequiredForLevel(entity.level + 1) && entity.level < this.maxLevel) {
            entity.xp -= this.getXPRequiredForLevel(entity.level + 1);
            entity.level++;
            this.applyLevelUpBonuses(entity);
            
            // Show level up notification
            if (entity.team === 'player') {
                entity.game.ui.showNotification(`${entity.name || entity.type} reached level ${entity.level}!`, 'success', 3000);
                entity.game.soundManager.play('complete');
            }
        }
    }

    // Initialize leveling stats for an entity
    initializeEntityLevel(entity) {
        entity.level = 1;
        entity.xp = 0;
        entity.baseStats = {
            health: entity.maxHealth,
            damage: entity.damage || 0,
            speed: entity.speed || 0,
            range: entity.range || 0,
            fireRate: entity.fireRate || 0,
            harvestRate: entity.harvestRate || 0,
            maxCarry: entity.maxCarry || 0,
            repairAmount: entity.repairAmount || 0,
            repairRate: entity.repairRate || 0
        };
    }

    // Apply stat bonuses when leveling up
    applyLevelUpBonuses(entity) {
        if (!entity.baseStats) return;

        const levelBonus = entity.level - 1;
        const bonusMultiplier = 0.1; // 10% increase per level

        // Health bonus
        if (entity.baseStats.health > 0) {
            const oldMaxHealth = entity.maxHealth;
            entity.maxHealth = Math.floor(entity.baseStats.health * (1 + levelBonus * bonusMultiplier));
            // Heal the entity proportionally
            const healthRatio = entity.health / oldMaxHealth;
            entity.health = Math.floor(entity.maxHealth * healthRatio);
        }

        // Damage bonus
        if (entity.baseStats.damage > 0) {
            entity.damage = Math.floor(entity.baseStats.damage * (1 + levelBonus * bonusMultiplier));
        }

        // Speed bonus
        if (entity.baseStats.speed > 0) {
            entity.speed = entity.baseStats.speed * (1 + levelBonus * bonusMultiplier * 0.5); // Smaller speed bonus
        }

        // Range bonus
        if (entity.baseStats.range > 0) {
            entity.range = Math.floor(entity.baseStats.range * (1 + levelBonus * bonusMultiplier * 0.3)); // Smaller range bonus
        }

        // Fire rate bonus
        if (entity.baseStats.fireRate > 0) {
            entity.fireRate = entity.baseStats.fireRate * (1 + levelBonus * bonusMultiplier * 0.2); // Smaller fire rate bonus
        }

        // Harvester-specific bonuses
        if (entity.type === 'harvester') {
            if (entity.baseStats.harvestRate > 0) {
                entity.harvestRate = Math.floor(entity.baseStats.harvestRate * (1 + levelBonus * bonusMultiplier));
            }
            if (entity.baseStats.maxCarry > 0) {
                entity.maxCarry = Math.floor(entity.baseStats.maxCarry * (1 + levelBonus * bonusMultiplier));
            }
        }

        // Engineer-specific bonuses
        if (entity.canRepair) {
            if (entity.baseStats.repairAmount > 0) {
                entity.repairAmount = Math.floor(entity.baseStats.repairAmount * (1 + levelBonus * bonusMultiplier));
            }
            if (entity.baseStats.repairRate > 0) {
                entity.repairRate = entity.baseStats.repairRate * (1 + levelBonus * bonusMultiplier * 0.3);
            }
        }
    }

    // Award XP for various actions
    awardActionXP(entity, action) {
        const xpRewards = {
            kill_unit: 50,
            kill_building: 100,
            harvest: 5,
            repair: 10,
            build: 25,
            damage_dealt: 1, // Per point of damage
            damage_taken: 0.5 // Per point of damage taken
        };

        const xpAmount = xpRewards[action] || 0;
        if (xpAmount > 0) {
            this.addXP(entity, xpAmount);
        }
    }

    // Award XP based on damage dealt
    awardDamageXP(entity, damageDealt) {
        this.addXP(entity, Math.floor(damageDealt * 1));
    }

    // Award XP based on damage taken (survival XP)
    awardSurvivalXP(entity, damageTaken) {
        this.addXP(entity, Math.floor(damageTaken * 0.5));
    }

    // Get level progress as percentage
    getLevelProgress(entity) {
        if (!entity.level) return 0;
        if (entity.level >= this.maxLevel) return 100;
        
        const xpForNextLevel = this.getXPRequiredForLevel(entity.level + 1);
        return (entity.xp / xpForNextLevel) * 100;
    }

    // Get entity's power rating based on level and stats
    getPowerRating(entity) {
        if (!entity.level) return 1;
        
        let rating = entity.level;
        rating += (entity.maxHealth / 100);
        rating += (entity.damage || 0) / 10;
        rating += (entity.speed || 0);
        
        return Math.floor(rating);
    }
}
