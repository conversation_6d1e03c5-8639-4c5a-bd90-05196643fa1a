/**
 * Game SVG definitions and utilities
 * Contains all SVG strings for units, buildings, and tiles
 * Renders them on the fly using data URLs
 */

// Unit SVGs
export const UnitSVGs = {
    soldier: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="soldierBody"><stop offset="0%" style="stop-color:#8B7355"/><stop offset="100%" style="stop-color:#654321"/></radialGradient></defs><ellipse cx="20" cy="21" rx="8" ry="6" fill="#000" opacity="0.2"/><circle cx="20" cy="20" r="7" fill="url(#soldierBody)"/><circle cx="20" cy="20" r="5" fill="#556B2F"/><rect x="24" y="19" width="10" height="2" fill="#2C2C2C"/><circle cx="33" cy="20" r="1.5" fill="#1A1A1A"/><circle cx="14" cy="20" r="2.5" fill="#654321"/><circle cx="26" cy="20" r="2.5" fill="#654321"/><circle cx="20" cy="20" r="3" fill="#4A5D23"/></svg>`,
    
    rocketeer: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="rocketeerBody"><stop offset="0%" style="stop-color:#A0522D"/><stop offset="100%" style="stop-color:#8B4513"/></radialGradient></defs><ellipse cx="20" cy="21" rx="9" ry="7" fill="#000" opacity="0.2"/><circle cx="20" cy="20" r="8" fill="url(#rocketeerBody)"/><circle cx="20" cy="20" r="6" fill="#696969"/><rect x="24" y="17" width="12" height="6" rx="3" fill="#8B0000"/><circle cx="35" cy="20" r="3" fill="#DC143C"/><circle cx="13" cy="20" r="3" fill="#8B4513"/><circle cx="27" cy="20" r="3" fill="#8B4513"/><circle cx="20" cy="20" r="3" fill="#4A4A4A"/></svg>`,
    
    engineer: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="engineerBody"><stop offset="0%" style="stop-color:#3CB371"/><stop offset="100%" style="stop-color:#228B22"/></radialGradient></defs><ellipse cx="20" cy="21" rx="8" ry="6" fill="#000" opacity="0.2"/><circle cx="20" cy="20" r="7" fill="url(#engineerBody)"/><circle cx="20" cy="20" r="5" fill="#228B22" stroke="#8B4513" stroke-width="1"/><rect x="25" y="19" width="8" height="2" fill="#C0C0C0"/><circle cx="32" cy="20" r="2" fill="#A9A9A9"/><circle cx="14" cy="20" r="2.5" fill="#228B22"/><circle cx="26" cy="20" r="2.5" fill="#228B22"/><circle cx="20" cy="20" r="3" fill="#FFD700"/></svg>`,
    
    harvester: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="harvesterBody"><stop offset="0%" style="stop-color:#DAA520"/><stop offset="100%" style="stop-color:#B8860B"/></linearGradient></defs><ellipse cx="30" cy="32" rx="20" ry="15" fill="#000" opacity="0.2"/><rect x="10" y="15" width="6" height="30" rx="3" fill="#2F2F2F"/><rect x="44" y="15" width="6" height="30" rx="3" fill="#2F2F2F"/><rect x="14" y="12" width="32" height="36" rx="4" fill="url(#harvesterBody)"/><rect x="18" y="18" width="24" height="18" rx="2" fill="#8B7355"/><circle cx="24" cy="25" r="3" fill="#FFA500" opacity="0.7"/><circle cx="30" cy="28" r="3" fill="#FF8C00" opacity="0.7"/><circle cx="36" cy="26" r="3" fill="#FFA500" opacity="0.7"/><rect x="44" y="20" width="10" height="16" rx="2" fill="#696969"/><rect x="52" y="23" width="4" height="10" fill="#A9A9A9"/><rect x="24" y="38" width="12" height="6" rx="2" fill="#4682B4"/></svg>`,
    
    tank: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="tankBody"><stop offset="0%" style="stop-color:#6B8E23"/><stop offset="100%" style="stop-color:#556B2F"/></radialGradient><radialGradient id="tankTurret"><stop offset="0%" style="stop-color:#708090"/><stop offset="100%" style="stop-color:#4A4A4A"/></radialGradient></defs><ellipse cx="30" cy="32" rx="18" ry="14" fill="#000" opacity="0.2"/><rect x="12" y="12" width="6" height="36" rx="3" fill="#2F2F2F"/><rect x="42" y="12" width="6" height="36" rx="3" fill="#2F2F2F"/><rect x="16" y="15" width="28" height="30" rx="2" fill="url(#tankBody)"/><circle cx="30" cy="30" r="10" fill="url(#tankTurret)"/><rect x="36" y="28" width="18" height="4" fill="#2F2F2F"/><rect x="52" y="27" width="4" height="6" rx="1" fill="#1A1A1A"/><circle cx="30" cy="30" r="5" fill="#5A5A5A"/></svg>`,
    
    artillery: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="artilleryBody"><stop offset="0%" style="stop-color:#8B7D6B"/><stop offset="100%" style="stop-color:#6B5D4B"/></radialGradient></defs><ellipse cx="30" cy="32" rx="16" ry="12" fill="#000" opacity="0.2"/><circle cx="18" cy="20" r="5" fill="#2F2F2F"/><circle cx="18" cy="40" r="5" fill="#2F2F2F"/><circle cx="42" cy="20" r="5" fill="#2F2F2F"/><circle cx="42" cy="40" r="5" fill="#2F2F2F"/><rect x="20" y="18" width="20" height="24" rx="2" fill="url(#artilleryBody)"/><rect x="24" y="24" width="12" height="12" rx="2" fill="#778899"/><rect x="34" y="28.5" width="24" height="3" fill="#3F3F3F"/><rect x="56" y="27" width="4" height="6" rx="1" fill="#2A2A2A"/><circle cx="30" cy="30" r="4" fill="#5F5F5F"/><rect x="15" y="28" width="5" height="4" fill="#5F5F5F"/><rect x="40" y="28" width="5" height="4" fill="#5F5F5F"/></svg>`,
    
    apc: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="apcBody"><stop offset="0%" style="stop-color:#5F9EA0"/><stop offset="100%" style="stop-color:#4682B4"/></radialGradient></defs><ellipse cx="30" cy="32" rx="20" ry="15" fill="#000" opacity="0.2"/><circle cx="18" cy="15" r="4" fill="#2F2F2F"/><circle cx="18" cy="45" r="4" fill="#2F2F2F"/><circle cx="30" cy="15" r="4" fill="#2F2F2F"/><circle cx="30" cy="45" r="4" fill="#2F2F2F"/><circle cx="42" cy="15" r="4" fill="#2F2F2F"/><circle cx="42" cy="45" r="4" fill="#2F2F2F"/><rect x="14" y="12" width="32" height="36" rx="4" fill="url(#apcBody)"/><path d="M44 16 L50 22 L50 38 L44 44 L44 16" fill="#4682B4"/><circle cx="32" cy="30" r="6" fill="#5F5F5F"/><rect x="36" y="29" width="10" height="2" fill="#2F2F2F"/><circle cx="45" cy="30" r="1.5" fill="#1A1A1A"/><circle cx="22" cy="22" r="1.5" fill="#2F4F4F"/><circle cx="38" cy="22" r="1.5" fill="#2F4F4F"/><circle cx="22" cy="38" r="1.5" fill="#2F4F4F"/><circle cx="38" cy="38" r="1.5" fill="#2F4F4F"/><rect x="14" y="25" width="2" height="10" fill="#3A5A6A"/></svg>`,
    
    // NEW AIR UNITS
    ornithopter: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="ornithopterBody"><stop offset="0%" style="stop-color:#87CEEB"/><stop offset="100%" style="stop-color:#4682B4"/></linearGradient><radialGradient id="cockpit"><stop offset="0%" style="stop-color:#FFFFFF" stop-opacity="0.8"/><stop offset="100%" style="stop-color:#87CEEB" stop-opacity="0.3"/></radialGradient></defs><ellipse cx="25" cy="27" rx="18" ry="12" fill="#000" opacity="0.2"/><ellipse cx="25" cy="25" rx="16" ry="10" fill="url(#ornithopterBody)" stroke="#4682B4" stroke-width="1"/><path d="M10 25 Q5 15 15 20 Q20 18 25 25 Q30 18 35 20 Q45 15 40 25 Q35 35 25 25 Q15 35 10 25" fill="#B0C4DE" opacity="0.7"/><circle cx="25" cy="25" r="8" fill="url(#cockpit)" stroke="#4682B4" stroke-width="1"/><circle cx="25" cy="25" r="4" fill="#000080" opacity="0.3"/><rect x="23" y="15" width="4" height="8" fill="#4682B4"/><circle cx="25" cy="15" r="2" fill="#FF0000"/><path d="M15 30 L20 35 L30 35 L35 30" fill="#4682B4" opacity="0.6"/></svg>`,
    
    carryall: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="carryallBody"><stop offset="0%" style="stop-color:#4169E1"/><stop offset="100%" style="stop-color:#191970"/></linearGradient></defs><ellipse cx="30" cy="32" rx="22" ry="16" fill="#000" opacity="0.2"/><rect x="10" y="15" width="40" height="30" rx="8" fill="url(#carryallBody)" stroke="#191970" stroke-width="2"/><path d="M5 30 Q2 20 12 25 Q18 23 30 30 Q42 23 48 25 Q58 20 55 30 Q48 40 30 30 Q12 40 5 30" fill="#6495ED" opacity="0.8"/><rect x="15" y="20" width="30" height="20" rx="4" fill="#4682B4" stroke="#191970" stroke-width="1"/><rect x="18" y="23" width="24" height="14" rx="2" fill="#87CEEB" opacity="0.6"/><circle cx="22" cy="30" r="2" fill="#000080"/><circle cx="30" cy="30" r="2" fill="#000080"/><circle cx="38" cy="30" r="2" fill="#000080"/><rect x="28" y="10" width="4" height="8" fill="#191970"/><circle cx="30" cy="10" r="2" fill="#FF0000"/></svg>`,
    
    // NEW NAVAL UNITS
    frigate: `<svg viewBox="0 0 70 70" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="frigateHull"><stop offset="0%" style="stop-color:#4682B4"/><stop offset="100%" style="stop-color:#2F4F4F"/></linearGradient></defs><ellipse cx="35" cy="37" rx="28" ry="18" fill="#000" opacity="0.2"/><path d="M10 35 Q15 20 35 25 Q55 20 60 35 Q55 50 35 45 Q15 50 10 35" fill="url(#frigateHull)" stroke="#2F4F4F" stroke-width="2"/><rect x="20" y="30" width="30" height="10" rx="2" fill="#708090" stroke="#2F4F4F" stroke-width="1"/><rect x="30" y="15" width="10" height="20" rx="2" fill="#708090" stroke="#2F4F4F" stroke-width="1"/><rect x="32" y="10" width="6" height="8" fill="#A9A9A9"/><rect x="25" y="25" width="4" height="15" fill="#2F2F2F"/><rect x="41" y="25" width="4" height="15" fill="#2F2F2F"/><circle cx="27" cy="32" r="2" fill="#1A1A1A"/><circle cx="43" cy="32" r="2" fill="#1A1A1A"/><path d="M15 40 L20 45 L50 45 L55 40" fill="#4682B4" opacity="0.5"/></svg>`,
    
    submarine: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="subHull"><stop offset="0%" style="stop-color:#2F4F4F"/><stop offset="100%" style="stop-color:#1C1C1C"/></linearGradient></defs><ellipse cx="30" cy="32" rx="22" ry="14" fill="#000" opacity="0.2"/><ellipse cx="30" cy="30" rx="20" ry="12" fill="url(#subHull)" stroke="#1C1C1C" stroke-width="2"/><rect x="25" y="18" width="10" height="15" rx="3" fill="#2F4F4F" stroke="#1C1C1C" stroke-width="1"/><rect x="28" y="12" width="4" height="8" fill="#A9A9A9"/><circle cx="30" cy="12" r="1.5" fill="#FF0000"/><rect x="15" y="28" width="8" height="4" fill="#1C1C1C"/><rect x="37" y="28" width="8" height="4" fill="#1C1C1C"/><circle cx="19" cy="30" r="1.5" fill="#000"/><circle cx="41" cy="30" r="1.5" fill="#000"/><path d="M12 35 L18 40 L42 40 L48 35" fill="#2F4F4F" opacity="0.6"/></svg>`,
    
    // NEW SPECIALIZED GROUND UNITS
    stealth_trooper: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="stealthBody"><stop offset="0%" style="stop-color:#483D8B"/><stop offset="100%" style="stop-color:#2F2F4F"/></radialGradient><filter id="stealthEffect"><feGaussianBlur stdDeviation="1" result="blur"/><feComponentTransfer result="fade"><feFuncA type="linear" slope="0.7"/></feComponentTransfer></filter></defs><ellipse cx="20" cy="21" rx="8" ry="6" fill="#000" opacity="0.1"/><g filter="url(#stealthEffect)"><circle cx="20" cy="20" r="7" fill="url(#stealthBody)" opacity="0.8"/><circle cx="20" cy="20" r="5" fill="#483D8B" opacity="0.6"/><rect x="24" y="19" width="10" height="2" fill="#4B0082" opacity="0.7"/><circle cx="33" cy="20" r="1.5" fill="#8A2BE2" opacity="0.8"/></g><circle cx="20" cy="20" r="3" fill="#6A5ACD" opacity="0.5"/></svg>`,
    
    medic: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="medicBody"><stop offset="0%" style="stop-color:#FFFFFF"/><stop offset="100%" style="stop-color:#F0F8FF"/></radialGradient></defs><ellipse cx="20" cy="21" rx="8" ry="6" fill="#000" opacity="0.2"/><circle cx="20" cy="20" r="7" fill="url(#medicBody)" stroke="#DC143C" stroke-width="2"/><circle cx="20" cy="20" r="5" fill="#FFFFFF"/><path d="M18 16 L22 16 L22 18 L24 18 L24 22 L22 22 L22 24 L18 24 L18 22 L16 22 L16 18 L18 18 Z" fill="#DC143C"/><rect x="24" y="19" width="8" height="2" fill="#C0C0C0"/><circle cx="31" cy="20" r="1.5" fill="#A9A9A9"/><circle cx="20" cy="20" r="3" fill="#FFB6C1" opacity="0.5"/></svg>`,
    
    scout: `<svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="scoutBody"><stop offset="0%" style="stop-color:#32CD32"/><stop offset="100%" style="stop-color:#228B22"/></radialGradient></defs><ellipse cx="17.5" cy="18.5" rx="6" ry="4" fill="#000" opacity="0.2"/><circle cx="17.5" cy="17.5" r="5" fill="url(#scoutBody)"/><circle cx="17.5" cy="17.5" r="3" fill="#90EE90"/><rect x="20" y="16.5" width="8" height="2" fill="#2F2F2F"/><circle cx="27" cy="17.5" r="1" fill="#1A1A1A"/><circle cx="17.5" cy="17.5" r="2" fill="#ADFF2F"/></svg>`,
    
    // NEW ADVANCED UNITS
    siege_tank: `<svg viewBox="0 0 70 70" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="siegeBody"><stop offset="0%" style="stop-color:#8B4513"/><stop offset="100%" style="stop-color:#654321"/></radialGradient></defs><ellipse cx="35" cy="37" rx="25" ry="18" fill="#000" opacity="0.2"/><rect x="15" y="15" width="8" height="40" rx="4" fill="#2F2F2F"/><rect x="47" y="15" width="8" height="40" rx="4" fill="#2F2F2F"/><rect x="20" y="18" width="30" height="34" rx="3" fill="url(#siegeBody)"/><circle cx="35" cy="35" r="12" fill="#A0522D"/><rect x="41" y="33" width="25" height="4" fill="#2F2F2F"/><rect x="64" y="31" width="4" height="8" rx="1" fill="#1A1A1A"/><circle cx="35" cy="35" r="6" fill="#654321"/><rect x="25" y="25" width="20" height="8" rx="2" fill="#8B4513"/><rect x="25" y="42" width="20" height="8" rx="2" fill="#8B4513"/></svg>`,
    
    flamethrower: `<svg viewBox="0 0 45 45" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="flameBody"><stop offset="0%" style="stop-color:#FF4500"/><stop offset="100%" style="stop-color:#DC143C"/></radialGradient></defs><ellipse cx="22.5" cy="24" rx="9" ry="7" fill="#000" opacity="0.2"/><circle cx="22.5" cy="22.5" r="8" fill="url(#flameBody)"/><circle cx="22.5" cy="22.5" r="6" fill="#FF6347"/><rect x="27" y="21" width="12" height="3" fill="#2F2F2F"/><rect x="37" y="20" width="4" height="5" rx="1" fill="#FF8C00"/><circle cx="22.5" cy="22.5" r="4" fill="#FFD700" opacity="0.7"/><path d="M39 19 Q42 16 45 20 Q42 24 39 21" fill="#FF4500" opacity="0.8"/></svg>`
};

// Building SVGs
export const BuildingSVGs = {
    barracks: `<svg viewBox="0 0 90 90" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="barracksGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#2F4F2F"/><stop offset="100%" style="stop-color:#228B22"/></linearGradient></defs><rect x="3" y="3" width="84" height="84" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="80" height="80" fill="url(#barracksGrad)" stroke="#1F3F1F" stroke-width="2" rx="5"/><rect x="10" y="10" width="35" height="35" fill="#228B22" stroke="#1F3F1F" stroke-width="1" rx="2"/><rect x="50" y="10" width="30" height="35" fill="#228B22" stroke="#1F3F1F" stroke-width="1" rx="2"/><rect x="10" y="50" width="35" height="30" fill="#228B22" stroke="#1F3F1F" stroke-width="1" rx="2"/><rect x="50" y="50" width="30" height="30" fill="#3F5F3F" stroke="#2F4F2F" stroke-width="1" rx="2"/><line x1="15" y1="20" x2="40" y2="20" stroke="#808080" stroke-width="3"/><line x1="15" y1="30" x2="40" y2="30" stroke="#808080" stroke-width="3"/><rect x="35" y="70" width="20" height="15" fill="#4F4F4F" stroke="#3F3F3F" stroke-width="1"/><rect x="55" y="15" width="20" height="8" fill="#87CEEB" opacity="0.6"/><rect x="15" y="55" width="8" height="20" fill="#87CEEB" opacity="0.6"/><line x1="70" y1="60" x2="70" y2="50" stroke="#606060" stroke-width="2"/><path d="M70 50 L78 53 L70 56 Z" fill="#FF0000"/></svg>`,

    command_center: `<svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="baseGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#B0B0B0"/><stop offset="50%" style="stop-color:#909090"/><stop offset="100%" style="stop-color:#808080"/></linearGradient><pattern id="basePlate" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><rect width="10" height="10" fill="#909090"/><rect x="1" y="1" width="8" height="8" fill="#A0A0A0"/></pattern></defs><rect x="5" y="5" width="110" height="110" fill="#000" opacity="0.2" rx="5"/><rect x="10" y="10" width="100" height="100" fill="url(#baseGrad)" stroke="#606060" stroke-width="2" rx="5"/><rect x="15" y="15" width="90" height="90" fill="url(#basePlate)" opacity="0.5" rx="3"/><rect x="35" y="35" width="50" height="50" fill="#707070" stroke="#505050" stroke-width="2" rx="3"/><circle cx="60" cy="60" r="20" fill="none" stroke="#00FF00" stroke-width="3" opacity="0.8"/><circle cx="60" cy="60" r="15" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.6"/><circle cx="60" cy="60" r="10" fill="none" stroke="#00FF00" stroke-width="1" opacity="0.4"/><circle cx="60" cy="60" r="5" fill="#606060"/><line x1="60" y1="60" x2="60" y2="40" stroke="#505050" stroke-width="2"/><rect x="20" y="20" width="20" height="20" fill="#808080" stroke="#606060" stroke-width="1" rx="2"/><rect x="80" y="20" width="20" height="20" fill="#808080" stroke="#606060" stroke-width="1" rx="2"/><rect x="20" y="80" width="20" height="20" fill="#808080" stroke="#606060" stroke-width="1" rx="2"/><rect x="80" y="80" width="20" height="20" fill="#808080" stroke="#606060" stroke-width="1" rx="2"/><circle cx="30" cy="30" r="3" fill="#00FF00"/><circle cx="90" cy="30" r="3" fill="#00FF00"/><circle cx="30" cy="90" r="3" fill="#00FF00"/><circle cx="90" cy="90" r="3" fill="#00FF00"/></svg>`,

    factory: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="factoryGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#696969"/><stop offset="100%" style="stop-color:#2F2F2F"/></linearGradient></defs><rect x="3" y="3" width="94" height="94" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="90" height="90" fill="url(#factoryGrad)" stroke="#1F1F1F" stroke-width="2" rx="5"/><rect x="10" y="10" width="80" height="60" fill="#505050" stroke="#2F2F2F" stroke-width="1" rx="3"/><rect x="15" y="75" width="70" height="15" fill="#404040" stroke="#2F2F2F" stroke-width="1"/><rect x="20" y="20" width="25" height="40" fill="#606060" stroke="#404040" stroke-width="1" rx="2"/><rect x="55" y="20" width="25" height="40" fill="#606060" stroke="#404040" stroke-width="1" rx="2"/><rect x="25" y="25" width="15" height="10" fill="#FFD700" opacity="0.7"/><rect x="60" y="25" width="15" height="10" fill="#FFD700" opacity="0.7"/><rect x="25" y="45" width="15" height="10" fill="#FF6347" opacity="0.7"/><rect x="60" y="45" width="15" height="10" fill="#FF6347" opacity="0.7"/><rect x="70" y="5" width="8" height="25" fill="#404040"/><rect x="68" y="3" width="12" height="4" fill="#2F2F2F"/><rect x="80" y="5" width="8" height="30" fill="#404040"/><rect x="78" y="3" width="12" height="4" fill="#2F2F2F"/><rect x="40" y="78" width="20" height="12" fill="#2F2F2F" stroke="#1F1F1F" stroke-width="1"/></svg>`,

    power_plant: `<svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="powerGrad"><stop offset="0%" style="stop-color:#FFD700"/><stop offset="100%" style="stop-color:#FFA500"/></radialGradient><filter id="powerGlow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><rect x="3" y="3" width="74" height="74" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="70" height="70" fill="url(#powerGrad)" stroke="#CC8400" stroke-width="2" rx="5"/><rect x="10" y="10" width="60" height="60" fill="#FFA500" stroke="#CC8400" stroke-width="1" rx="3"/><circle cx="40" cy="40" r="20" fill="#FFD700" stroke="#FFA500" stroke-width="2" filter="url(#powerGlow)"/><circle cx="40" cy="40" r="15" fill="#FFFF00" opacity="0.8"/><circle cx="40" cy="40" r="10" fill="#FFFFFF" opacity="0.6"/><path d="M35 30 L45 40 L35 50 L40 40 Z" fill="#FF8C00"/><rect x="15" y="15" width="8" height="8" fill="#FFA500"/><rect x="57" y="15" width="8" height="8" fill="#FFA500"/><rect x="15" y="57" width="8" height="8" fill="#FFA500"/><rect x="57" y="57" width="8" height="8" fill="#FFA500"/><line x1="25" y1="40" x2="15" y2="40" stroke="#FFD700" stroke-width="3"/><line x1="55" y1="40" x2="65" y2="40" stroke="#FFD700" stroke-width="3"/><line x1="40" y1="25" x2="40" y2="15" stroke="#FFD700" stroke-width="3"/><line x1="40" y1="55" x2="40" y2="65" stroke="#FFD700" stroke-width="3"/></svg>`,

    radar: `<svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="radarGrad"><stop offset="0%" style="stop-color:#00FF00"/><stop offset="100%" style="stop-color:#008000"/></radialGradient><filter id="radarSweep"><feGaussianBlur stdDeviation="1" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><rect x="3" y="3" width="74" height="74" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="70" height="70" fill="#2F4F2F" stroke="#1F3F1F" stroke-width="2" rx="5"/><circle cx="40" cy="40" r="30" fill="url(#radarGrad)" stroke="#00FF00" stroke-width="2" opacity="0.3"/><circle cx="40" cy="40" r="25" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.5"/><circle cx="40" cy="40" r="20" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.7"/><circle cx="40" cy="40" r="15" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.9"/><circle cx="40" cy="40" r="10" fill="none" stroke="#00FF00" stroke-width="2"/><circle cx="40" cy="40" r="5" fill="#00FF00"/><path d="M40 40 L65 25" stroke="#00FF00" stroke-width="3" opacity="0.8" filter="url(#radarSweep)"/><circle cx="25" cy="25" r="2" fill="#FFFF00"/><circle cx="55" cy="30" r="2" fill="#FFFF00"/><circle cx="30" cy="55" r="2" fill="#FFFF00"/><rect x="35" y="70" width="10" height="5" fill="#404040"/><rect x="30" y="75" width="20" height="3" fill="#2F2F2F"/></svg>`,

    refinery: `<svg viewBox="0 0 90 90" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="refineryGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8B4513"/><stop offset="100%" style="stop-color:#A0522D"/></linearGradient></defs><rect x="3" y="3" width="84" height="84" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="80" height="80" fill="url(#refineryGrad)" stroke="#654321" stroke-width="2" rx="5"/><rect x="10" y="10" width="70" height="50" fill="#A0522D" stroke="#8B4513" stroke-width="1" rx="3"/><rect x="15" y="15" width="60" height="40" fill="#CD853F" stroke="#A0522D" stroke-width="1" rx="2"/><circle cx="30" cy="35" r="8" fill="#FFA500" opacity="0.7"/><circle cx="60" cy="35" r="8" fill="#FF8C00" opacity="0.7"/><rect x="20" y="65" width="50" height="15" fill="#8B4513" stroke="#654321" stroke-width="1"/><rect x="25" y="68" width="40" height="9" fill="#A0522D"/><rect x="70" y="20" width="8" height="40" fill="#696969"/><rect x="68" y="18" width="12" height="4" fill="#505050"/><rect x="78" y="25" width="8" height="30" fill="#696969"/><rect x="76" y="23" width="12" height="4" fill="#505050"/><circle cx="45" cy="25" r="3" fill="#FFD700"/><circle cx="45" cy="45" r="3" fill="#FFD700"/><rect x="40" y="72" width="10" height="8" fill="#654321" stroke="#4B2F20" stroke-width="1"/></svg>`,

    rocket_turret: `<svg viewBox="0 0 70 70" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="rocketTurretGrad"><stop offset="0%" style="stop-color:#B22222"/><stop offset="100%" style="stop-color:#8B0000"/></radialGradient><linearGradient id="rocketTubeGrad" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#8B0000"/><stop offset="100%" style="stop-color:#660000"/></linearGradient></defs><circle cx="36" cy="36" r="32" fill="#000" opacity="0.2"/><circle cx="35" cy="35" r="32" fill="#696969" stroke="#505050" stroke-width="2"/><circle cx="35" cy="35" r="26" fill="url(#rocketTurretGrad)" stroke="#8B0000" stroke-width="2"/><g id="rocketLauncher"><rect x="25" y="25" width="20" height="20" fill="#606060" stroke="#404040" stroke-width="1" rx="2"/><rect x="20" y="15" width="6" height="20" fill="url(#rocketTubeGrad)" rx="1"/><rect x="28" y="15" width="6" height="20" fill="url(#rocketTubeGrad)" rx="1"/><rect x="36" y="15" width="6" height="20" fill="url(#rocketTubeGrad)" rx="1"/><rect x="44" y="15" width="6" height="20" fill="url(#rocketTubeGrad)" rx="1"/><circle cx="23" cy="15" r="2" fill="#000"/><circle cx="31" cy="15" r="2" fill="#000"/><circle cx="39" cy="15" r="2" fill="#000"/><circle cx="47" cy="15" r="2" fill="#000"/><circle cx="35" cy="35" r="8" fill="#707070" stroke="#505050" stroke-width="1"/><circle cx="35" cy="35" r="4" fill="#505050"/></g><path d="M10 50 L15 45 L20 50 L15 55 Z" fill="#FFFF00" opacity="0.8"/><path d="M50 50 L55 45 L60 50 L55 55 Z" fill="#FFFF00" opacity="0.8"/></svg>`,

    techlab: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="techlabGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#9370DB"/><stop offset="100%" style="stop-color:#7B68EE"/></linearGradient><radialGradient id="energyCore"><stop offset="0%" style="stop-color:#00FFFF"/><stop offset="50%" style="stop-color:#0080FF"/><stop offset="100%" style="stop-color:#0040FF"/></radialGradient></defs><rect x="3" y="3" width="94" height="94" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="90" height="90" fill="url(#techlabGrad)" stroke="#6B5B9B" stroke-width="2" rx="5"/><rect x="10" y="10" width="35" height="35" fill="#8B7BB8" stroke="#6B5B9B" stroke-width="1" rx="3"/><rect x="55" y="10" width="35" height="35" fill="#8B7BB8" stroke="#6B5B9B" stroke-width="1" rx="3"/><rect x="10" y="55" width="35" height="35" fill="#8B7BB8" stroke="#6B5B9B" stroke-width="1" rx="3"/><rect x="55" y="55" width="35" height="35" fill="#8B7BB8" stroke="#6B5B9B" stroke-width="1" rx="3"/><circle cx="50" cy="50" r="15" fill="url(#energyCore)" stroke="#0040FF" stroke-width="2"/><circle cx="50" cy="50" r="10" fill="#FFFFFF" opacity="0.5"/><line x1="35" y1="50" x2="27" y2="27" stroke="#00FFFF" stroke-width="3" opacity="0.7"/><line x1="65" y1="50" x2="73" y2="27" stroke="#00FFFF" stroke-width="3" opacity="0.7"/><line x1="35" y1="50" x2="27" y2="73" stroke="#00FFFF" stroke-width="3" opacity="0.7"/><line x1="65" y1="50" x2="73" y2="73" stroke="#00FFFF" stroke-width="3" opacity="0.7"/><circle cx="27" cy="27" r="5" fill="#FFD700"/><circle cx="73" cy="27" r="5" fill="#FFD700"/><circle cx="27" cy="73" r="5" fill="#FFD700"/><circle cx="73" cy="73" r="5" fill="#FFD700"/><rect x="15" y="15" width="25" height="5" fill="#00FF00" opacity="0.5"/><rect x="60" y="15" width="25" height="5" fill="#00FF00" opacity="0.5"/></svg>`,

    turret: `<svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="turretBaseGrad"><stop offset="0%" style="stop-color:#A9A9A9"/><stop offset="100%" style="stop-color:#808080"/></radialGradient><linearGradient id="barrelGrad" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#606060"/><stop offset="100%" style="stop-color:#404040"/></linearGradient></defs><circle cx="31" cy="31" r="28" fill="#000" opacity="0.2"/><circle cx="30" cy="30" r="28" fill="#696969" stroke="#505050" stroke-width="2"/><circle cx="30" cy="30" r="22" fill="url(#turretBaseGrad)" stroke="#606060" stroke-width="1"/><g id="turretHead"><circle cx="30" cy="30" r="15" fill="#707070" stroke="#505050" stroke-width="2"/><rect x="28" y="10" width="4" height="22" fill="url(#barrelGrad)"/><rect x="27" y="8" width="6" height="4" fill="#505050"/><rect x="26" y="6" width="8" height="2" fill="#404040"/><circle cx="30" cy="30" r="8" fill="#606060"/><circle cx="30" cy="30" r="5" fill="#505050"/></g><circle cx="15" cy="15" r="3" fill="#505050"/><circle cx="45" cy="15" r="3" fill="#505050"/><circle cx="15" cy="45" r="3" fill="#505050"/><circle cx="45" cy="45" r="3" fill="#505050"/></svg>`,

    wall: `<svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="wallTexture" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><rect width="10" height="5" fill="#888888"/><rect y="5" width="10" height="5" fill="#808080"/><line x1="0" y1="5" x2="10" y2="5" stroke="#707070" stroke-width="0.5"/><line x1="5" y1="0" x2="5" y2="5" stroke="#707070" stroke-width="0.5"/><line x1="5" y1="5" x2="5" y2="10" stroke="#909090" stroke-width="0.5"/></pattern></defs><rect x="2" y="2" width="36" height="36" fill="#000" opacity="0.15"/><rect x="3" y="3" width="34" height="34" fill="url(#wallTexture)" stroke="#606060" stroke-width="2"/><rect x="5" y="5" width="30" height="30" fill="none" stroke="#707070" stroke-width="1"/><circle cx="8" cy="8" r="1.5" fill="#505050"/><circle cx="32" cy="8" r="1.5" fill="#505050"/><circle cx="8" cy="32" r="1.5" fill="#505050"/><circle cx="32" cy="32" r="1.5" fill="#505050"/></svg>`,

    // NEW ADVANCED PRODUCTION BUILDINGS
    starport: `<svg viewBox="0 0 110 110" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="starportGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#87CEEB"/><stop offset="100%" style="stop-color:#4682B4"/></linearGradient><radialGradient id="landingPad"><stop offset="0%" style="stop-color:#B0C4DE"/><stop offset="100%" style="stop-color:#778899"/></radialGradient></defs><rect x="3" y="3" width="104" height="104" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="100" height="100" fill="url(#starportGrad)" stroke="#4682B4" stroke-width="2" rx="5"/><circle cx="55" cy="55" r="40" fill="url(#landingPad)" stroke="#4682B4" stroke-width="2"/><circle cx="55" cy="55" r="35" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-dasharray="5,5"/><circle cx="55" cy="55" r="25" fill="none" stroke="#FFFFFF" stroke-width="1" stroke-dasharray="3,3"/><circle cx="55" cy="55" r="15" fill="none" stroke="#FFFFFF" stroke-width="1"/><circle cx="55" cy="55" r="5" fill="#4682B4"/><rect x="15" y="15" width="20" height="20" fill="#6495ED" stroke="#4682B4" stroke-width="1" rx="2"/><rect x="75" y="15" width="20" height="20" fill="#6495ED" stroke="#4682B4" stroke-width="1" rx="2"/><rect x="15" y="75" width="20" height="20" fill="#6495ED" stroke="#4682B4" stroke-width="1" rx="2"/><rect x="75" y="75" width="20" height="20" fill="#6495ED" stroke="#4682B4" stroke-width="1" rx="2"/><rect x="50" y="10" width="10" height="15" fill="#4682B4"/><circle cx="55" cy="10" r="3" fill="#FF0000"/></svg>`,

    naval_yard: `<svg viewBox="0 0 130 130" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="navalYardGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#4682B4"/><stop offset="100%" style="stop-color:#2F4F4F"/></linearGradient><pattern id="waterPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="#4682B4"/><path d="M0 10 Q5 5 10 10 Q15 15 20 10" stroke="#87CEEB" stroke-width="2" fill="none" opacity="0.6"/></pattern></defs><rect x="3" y="3" width="124" height="124" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="120" height="120" fill="url(#navalYardGrad)" stroke="#2F4F4F" stroke-width="2" rx="5"/><rect x="10" y="10" width="110" height="60" fill="#708090" stroke="#2F4F4F" stroke-width="1" rx="3"/><rect x="10" y="75" width="110" height="40" fill="url(#waterPattern)" stroke="#2F4F4F" stroke-width="1" rx="3"/><rect x="20" y="20" width="30" height="40" fill="#4682B4" stroke="#2F4F4F" stroke-width="1" rx="2"/><rect x="60" y="20" width="30" height="40" fill="#4682B4" stroke="#2F4F4F" stroke-width="1" rx="2"/><rect x="100" y="20" width="15" height="40" fill="#4682B4" stroke="#2F4F4F" stroke-width="1" rx="2"/><rect x="40" y="85" width="50" height="25" rx="8" fill="#2F4F4F" stroke="#1C1C1C" stroke-width="1"/><circle cx="25" cy="97" r="3" fill="#FFD700"/><circle cx="105" cy="97" r="3" fill="#FFD700"/></svg>`,

    // NEW SPECIALIZED BUILDINGS
    research_lab: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="researchGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#FF69B4"/><stop offset="100%" style="stop-color:#FF1493"/></linearGradient><radialGradient id="researchCore"><stop offset="0%" style="stop-color:#FFFFFF"/><stop offset="50%" style="stop-color:#FF69B4"/><stop offset="100%" style="stop-color:#8B008B"/></radialGradient></defs><rect x="3" y="3" width="94" height="94" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="90" height="90" fill="url(#researchGrad)" stroke="#FF1493" stroke-width="2" rx="5"/><circle cx="50" cy="50" r="30" fill="url(#researchCore)" stroke="#FF1493" stroke-width="2"/><circle cx="50" cy="50" r="20" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.8"/><circle cx="50" cy="50" r="10" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.6"/><path d="M20 30 Q30 20 40 30 Q50 40 50 50 Q50 60 40 70 Q30 80 20 70" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.7"/><path d="M80 30 Q70 20 60 30 Q50 40 50 50 Q50 60 60 70 Q70 80 80 70" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.7"/><circle cx="30" cy="30" r="3" fill="#FFFF00"/><circle cx="70" cy="30" r="3" fill="#FFFF00"/><circle cx="30" cy="70" r="3" fill="#FFFF00"/><circle cx="70" cy="70" r="3" fill="#FFFF00"/></svg>`,

    repair_bay: `<svg viewBox="0 0 90 90" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="repairGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#FFD700"/><stop offset="100%" style="stop-color:#FFA500"/></linearGradient></defs><rect x="3" y="3" width="84" height="84" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="80" height="80" fill="url(#repairGrad)" stroke="#FFA500" stroke-width="2" rx="5"/><rect x="10" y="10" width="70" height="50" fill="#FFFF00" stroke="#FFA500" stroke-width="1" rx="3"/><rect x="15" y="15" width="60" height="40" fill="#FFD700" stroke="#FFA500" stroke-width="1" rx="2"/><circle cx="30" cy="35" r="8" fill="#FF8C00"/><circle cx="60" cy="35" r="8" fill="#FF8C00"/><rect x="20" y="65" width="50" height="15" fill="#FFA500" stroke="#FF8C00" stroke-width="1"/><rect x="25" y="68" width="40" height="9" fill="#FFD700"/><path d="M40 25 L50 25 L50 30 L55 30 L55 40 L50 40 L50 45 L40 45 L40 40 L35 40 L35 30 L40 30 Z" fill="#DC143C"/><circle cx="45" cy="35" r="3" fill="#FFFFFF"/></svg>`,

    shield_generator: `<svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="shieldGrad"><stop offset="0%" style="stop-color:#00FF7F"/><stop offset="100%" style="stop-color:#228B22"/></radialGradient><filter id="shieldGlow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><rect x="3" y="3" width="74" height="74" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="70" height="70" fill="#2F4F2F" stroke="#228B22" stroke-width="2" rx="5"/><circle cx="40" cy="40" r="30" fill="url(#shieldGrad)" stroke="#00FF7F" stroke-width="2" opacity="0.3" filter="url(#shieldGlow)"/><circle cx="40" cy="40" r="25" fill="none" stroke="#00FF7F" stroke-width="2" opacity="0.5"/><circle cx="40" cy="40" r="20" fill="none" stroke="#00FF7F" stroke-width="2" opacity="0.7"/><circle cx="40" cy="40" r="15" fill="none" stroke="#00FF7F" stroke-width="2" opacity="0.9"/><circle cx="40" cy="40" r="10" fill="none" stroke="#00FF7F" stroke-width="2"/><circle cx="40" cy="40" r="5" fill="#00FF7F"/><rect x="35" y="70" width="10" height="5" fill="#404040"/><rect x="30" y="75" width="20" height="3" fill="#2F2F2F"/></svg>`,

    // NEW ECONOMIC BUILDINGS
    spice_refinery_upgrade: `<svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="advancedRefineryGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#FF8C00"/><stop offset="100%" style="stop-color:#FF4500"/></linearGradient></defs><rect x="3" y="3" width="114" height="114" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="110" height="110" fill="url(#advancedRefineryGrad)" stroke="#FF4500" stroke-width="2" rx="5"/><rect x="10" y="10" width="100" height="70" fill="#FFA500" stroke="#FF4500" stroke-width="1" rx="3"/><rect x="15" y="15" width="90" height="60" fill="#FFD700" stroke="#FFA500" stroke-width="1" rx="2"/><circle cx="35" cy="45" r="12" fill="#FF8C00" opacity="0.8"/><circle cx="85" cy="45" r="12" fill="#FF6347" opacity="0.8"/><circle cx="60" cy="30" r="8" fill="#FFA500" opacity="0.7"/><circle cx="60" cy="60" r="8" fill="#FFA500" opacity="0.7"/><rect x="20" y="85" width="80" height="25" fill="#FF4500" stroke="#DC143C" stroke-width="1"/><rect x="25" y="88" width="70" height="19" fill="#FFA500"/><rect x="90" y="20" width="12" height="50" fill="#696969"/><rect x="88" y="18" width="16" height="6" fill="#505050"/><rect x="100" y="25" width="12" height="40" fill="#696969"/><rect x="98" y="23" width="16" height="6" fill="#505050"/><circle cx="60" cy="45" r="5" fill="#FFFF00"/></svg>`,

    water_extraction: `<svg viewBox="0 0 90 90" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="waterGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#40E0D0"/><stop offset="100%" style="stop-color:#008B8B"/></linearGradient><pattern id="waterFlow" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><rect width="10" height="10" fill="#40E0D0"/><path d="M0 5 Q2.5 2.5 5 5 Q7.5 7.5 10 5" stroke="#87CEEB" stroke-width="1" fill="none"/></pattern></defs><rect x="3" y="3" width="84" height="84" fill="#000" opacity="0.2" rx="5"/><rect x="5" y="5" width="80" height="80" fill="url(#waterGrad)" stroke="#008B8B" stroke-width="2" rx="5"/><rect x="10" y="10" width="70" height="50" fill="#20B2AA" stroke="#008B8B" stroke-width="1" rx="3"/><rect x="15" y="15" width="60" height="40" fill="url(#waterFlow)" stroke="#20B2AA" stroke-width="1" rx="2"/><circle cx="30" cy="35" r="8" fill="#00CED1" opacity="0.8"/><circle cx="60" cy="35" r="8" fill="#00CED1" opacity="0.8"/><rect x="20" y="65" width="50" height="15" fill="#008B8B" stroke="#006666" stroke-width="1"/><rect x="25" y="68" width="40" height="9" fill="#20B2AA"/><rect x="70" y="20" width="8" height="40" fill="#696969"/><rect x="68" y="18" width="12" height="4" fill="#505050"/><path d="M35 25 Q40 20 45 25 Q45 30 40 35 Q35 30 35 25" fill="#87CEEB" opacity="0.6"/><path d="M55 25 Q60 20 65 25 Q65 30 60 35 Q55 30 55 25" fill="#87CEEB" opacity="0.6"/></svg>`
};

// Tile SVGs
export const TileSVGs = {
    sand_tile: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="sandPattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><rect width="50" height="50" fill="#C19A6B"/><circle cx="10" cy="10" r="1" fill="#B8956A" opacity="0.5"/><circle cx="30" cy="20" r="1" fill="#B8956A" opacity="0.5"/><circle cx="20" cy="35" r="1" fill="#B8956A" opacity="0.5"/><circle cx="40" cy="40" r="1" fill="#B8956A" opacity="0.5"/><circle cx="5" cy="25" r="0.5" fill="#D2AA7C" opacity="0.4"/><circle cx="45" cy="15" r="0.5" fill="#D2AA7C" opacity="0.4"/><circle cx="25" cy="45" r="0.5" fill="#D2AA7C" opacity="0.4"/><ellipse cx="15" cy="30" rx="8" ry="2" fill="#BF9968" opacity="0.3"/><ellipse cx="35" cy="15" rx="10" ry="2" fill="#BF9968" opacity="0.3"/></pattern></defs><rect width="50" height="50" fill="url(#sandPattern)"/></svg>`,

    rock_tile: `<svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="rockGrad"><stop offset="0%" style="stop-color:#888888"/><stop offset="100%" style="stop-color:#555555"/></radialGradient><filter id="rockShadow"><feGaussianBlur in="SourceAlpha" stdDeviation="2"/><feOffset dx="2" dy="2" result="offsetblur"/><feComponentTransfer><feFuncA type="linear" slope="0.5"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#rockShadow)"><ellipse cx="40" cy="40" rx="30" ry="25" fill="url(#rockGrad)"/><ellipse cx="30" cy="35" rx="15" ry="12" fill="#666666" opacity="0.8"/><ellipse cx="50" cy="45" rx="18" ry="15" fill="#777777" opacity="0.7"/><path d="M25 30 L35 40 L30 50" fill="none" stroke="#444444" stroke-width="2"/><path d="M45 25 L50 35 L55 30" fill="none" stroke="#444444" stroke-width="1.5"/><ellipse cx="35" cy="30" rx="8" ry="5" fill="#999999" opacity="0.5"/><ellipse cx="50" cy="35" rx="6" ry="4" fill="#999999" opacity="0.5"/></g></svg>`,

    spice_tile: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="spiceGrad"><stop offset="0%" style="stop-color:#FFA500" stop-opacity="0.8"/><stop offset="50%" style="stop-color:#FF8C00" stop-opacity="0.5"/><stop offset="100%" style="stop-color:#FF6347" stop-opacity="0.2"/></radialGradient><filter id="spiceGlow"><feGaussianBlur stdDeviation="3" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><rect width="100" height="100" fill="#C19A6B"/><circle cx="50" cy="50" r="40" fill="url(#spiceGrad)" filter="url(#spiceGlow)"/><g opacity="0.8"><polygon points="20,30 22,25 24,30" fill="#FFA500"/><polygon points="35,40 37,35 39,40" fill="#FF8C00"/><polygon points="50,25 52,20 54,25" fill="#FFA500"/><polygon points="65,35 67,30 69,35" fill="#FF8C00"/><polygon points="30,60 32,55 34,60" fill="#FFA500"/><polygon points="55,55 57,50 59,55" fill="#FF8C00"/><polygon points="70,65 72,60 74,65" fill="#FFA500"/><polygon points="40,70 42,65 44,70" fill="#FF8C00"/></g><circle cx="30" cy="35" r="2" fill="#FFFF00" opacity="0.6"/><circle cx="60" cy="45" r="2" fill="#FFFF00" opacity="0.6"/><circle cx="45" cy="60" r="2" fill="#FFFF00" opacity="0.6"/></svg>`,

    empty_tile: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><rect width="50" height="50" fill="#8B7355"/><rect x="1" y="1" width="48" height="48" fill="none" stroke="#654321" stroke-width="0.5" opacity="0.3"/></svg>`,

    // NEW TERRAIN TILES
    water_tile: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="waterPattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><rect width="25" height="25" fill="#4682B4"/><path d="M0 12.5 Q6.25 6.25 12.5 12.5 Q18.75 18.75 25 12.5" stroke="#87CEEB" stroke-width="2" fill="none" opacity="0.7"/><path d="M0 25 Q6.25 18.75 12.5 25" stroke="#87CEEB" stroke-width="2" fill="none" opacity="0.7"/><path d="M12.5 0 Q18.75 6.25 25 0" stroke="#87CEEB" stroke-width="2" fill="none" opacity="0.7"/></pattern></defs><rect width="50" height="50" fill="url(#waterPattern)"/><circle cx="15" cy="15" r="2" fill="#B0E0E6" opacity="0.6"/><circle cx="35" cy="25" r="2" fill="#B0E0E6" opacity="0.6"/><circle cx="25" cy="40" r="2" fill="#B0E0E6" opacity="0.6"/></svg>`,

    cliff_tile: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="cliffGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#A0522D"/><stop offset="50%" style="stop-color:#8B7355"/><stop offset="100%" style="stop-color:#654321"/></linearGradient><filter id="cliffShadow"><feGaussianBlur in="SourceAlpha" stdDeviation="1"/><feOffset dx="1" dy="1" result="offsetblur"/><feComponentTransfer><feFuncA type="linear" slope="0.3"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><rect width="50" height="50" fill="url(#cliffGrad)"/><g filter="url(#cliffShadow)"><path d="M5 45 L15 25 L25 35 L35 15 L45 30 L50 50 L0 50 Z" fill="#8B7355"/><path d="M10 40 L20 30 L30 25 L40 20 L50 35" fill="none" stroke="#654321" stroke-width="2"/><path d="M0 35 L10 25 L20 40 L30 20 L40 35" fill="none" stroke="#654321" stroke-width="1.5"/></g><circle cx="12" cy="35" r="1.5" fill="#A0522D"/><circle cx="28" cy="28" r="1.5" fill="#A0522D"/><circle cx="38" cy="25" r="1.5" fill="#A0522D"/></svg>`,

    forest_tile: `<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="forestGround"><stop offset="0%" style="stop-color:#8FBC8F"/><stop offset="100%" style="stop-color:#556B2F"/></radialGradient></defs><rect width="50" height="50" fill="url(#forestGround)"/><g opacity="0.8"><circle cx="12" cy="15" r="8" fill="#228B22"/><circle cx="12" cy="12" r="6" fill="#32CD32"/><rect x="10" y="20" width="4" height="8" fill="#8B4513"/><circle cx="35" cy="20" r="10" fill="#228B22"/><circle cx="35" cy="17" r="7" fill="#32CD32"/><rect x="33" y="25" width="4" height="10" fill="#8B4513"/><circle cx="20" cy="35" r="7" fill="#228B22"/><circle cx="20" cy="32" r="5" fill="#32CD32"/><rect x="18" y="38" width="4" height="7" fill="#8B4513"/><circle cx="40" cy="40" r="6" fill="#228B22"/><circle cx="40" cy="38" r="4" fill="#32CD32"/><rect x="38" y="42" width="4" height="6" fill="#8B4513"/></g><circle cx="8" cy="40" r="1" fill="#8B4513" opacity="0.7"/><circle cx="25" cy="25" r="1" fill="#8B4513" opacity="0.7"/><circle cx="45" cy="30" r="1" fill="#8B4513" opacity="0.7"/></svg>`
};

/**
 * Convert SVG string to data URL that can be used with Image constructor
 * @param {string} svgString - The SVG string
 * @returns {string} Data URL for the SVG
 */
export function svgToDataURL(svgString) {
    // Encode the SVG string for use in data URL
    const encodedSvg = encodeURIComponent(svgString);
    return `data:image/svg+xml,${encodedSvg}`;
}

/**
 * Get SVG data URL for a specific unit type
 * @param {string} unitType - The unit type (e.g., 'soldier', 'tank', etc.)
 * @returns {string|null} Data URL for the unit SVG, or null if not found
 */
export function getUnitSVG(unitType) {
    const svgString = UnitSVGs[unitType];
    if (!svgString) {
        console.warn(`Unit SVG not found for type: ${unitType}`);
        return null;
    }
    return svgToDataURL(svgString);
}

/**
 * Get SVG data URL for a specific building type
 * @param {string} buildingType - The building type (e.g., 'barracks', 'factory', etc.)
 * @returns {string|null} Data URL for the building SVG, or null if not found
 */
export function getBuildingSVG(buildingType) {
    const svgString = BuildingSVGs[buildingType];
    if (!svgString) {
        console.warn(`Building SVG not found for type: ${buildingType}`);
        return null;
    }
    return svgToDataURL(svgString);
}

/**
 * Get SVG data URL for a specific tile type
 * @param {string} tileType - The tile type (e.g., 'sand_tile', 'rock_tile', etc.)
 * @returns {string|null} Data URL for the tile SVG, or null if not found
 */
export function getTileSVG(tileType) {
    const svgString = TileSVGs[tileType];
    if (!svgString) {
        console.warn(`Tile SVG not found for type: ${tileType}`);
        return null;
    }
    return svgToDataURL(svgString);
}

/**
 * Create a new Image object with the SVG for a specific unit type
 * @param {string} unitType - The unit type
 * @param {Function} onLoad - Optional callback when image loads
 * @param {Function} onError - Optional callback when image fails to load
 * @returns {Image} Image object with SVG data
 */
export function createUnitImage(unitType, onLoad, onError) {
    const image = new Image();
    
    if (onLoad) {
        image.onload = onLoad;
    }
    
    if (onError) {
        image.onerror = onError;
    } else {
        image.onerror = () => console.error(`Failed to load SVG for unit type: ${unitType}`);
    }
    
    const dataURL = getUnitSVG(unitType);
    if (dataURL) {
        image.src = dataURL;
    } else {
        // Trigger error callback if SVG not found
        setTimeout(() => image.onerror(), 0);
    }
    
    return image;
}

/**
 * Create a new Image object with the SVG for a specific building type
 * @param {string} buildingType - The building type
 * @param {Function} onLoad - Optional callback when image loads
 * @param {Function} onError - Optional callback when image fails to load
 * @returns {Image} Image object with SVG data
 */
export function createBuildingImage(buildingType, onLoad, onError) {
    const image = new Image();
    
    if (onLoad) {
        image.onload = onLoad;
    }
    
    if (onError) {
        image.onerror = onError;
    } else {
        image.onerror = () => console.error(`Failed to load SVG for building type: ${buildingType}`);
    }
    
    const dataURL = getBuildingSVG(buildingType);
    if (dataURL) {
        image.src = dataURL;
    } else {
        // Trigger error callback if SVG not found
        setTimeout(() => image.onerror(), 0);
    }
    
    return image;
}

/**
 * Get all available unit types that have SVGs
 * @returns {string[]} Array of unit type names
 */
export function getAvailableUnitTypes() {
    return Object.keys(UnitSVGs);
}

/**
 * Get all available building types that have SVGs
 * @returns {string[]} Array of building type names
 */
export function getAvailableBuildingTypes() {
    return Object.keys(BuildingSVGs);
}

/**
 * Get all available tile types that have SVGs
 * @returns {string[]} Array of tile type names
 */
export function getAvailableTileTypes() {
    return Object.keys(TileSVGs);
}