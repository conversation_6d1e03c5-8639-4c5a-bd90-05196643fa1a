// Base effect class
export class Effect {
    constructor(x, y, duration) {
        this.x = x;
        this.y = y;
        this.duration = duration;
        this.timeLeft = duration;
        this.alpha = 1;
    }

    update(deltaTime) {
        this.timeLeft -= deltaTime * 1000;
        this.alpha = Math.max(0, this.timeLeft / this.duration);
    }

    isDead() {
        return this.timeLeft <= 0;
    }

    draw(ctx) {
        // Override in subclasses
    }
}

// Explosion effect
export class ExplosionEffect extends Effect {
    constructor(x, y, size, type) {
        super(x, y, 500);
        this.size = size;
        this.maxSize = size * 2;
        this.currentSize = size * 0.2;
        this.type = type;
        
        // Different color schemes for different explosion types
        if (type === 'building') {
            this.colors = ['#FF6600', '#FF4400', '#FF2200', '#CC1100'];
        } else if (type === 'vehicle') {
            this.colors = ['#FF8800', '#FF6600', '#FF4400', '#FF2200', '#CC1100'];
            this.duration = 600; // Slightly longer for vehicles
            this.timeLeft = this.duration;
        } else {
            this.colors = ['#FFAA00', '#FF6600', '#FF4400', '#FF2200'];
        }
    }

    update(deltaTime) {
        super.update(deltaTime);
        const progress = 1 - (this.timeLeft / this.duration);
        this.currentSize = this.size * 0.2 + (this.maxSize - this.size * 0.2) * progress;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        
        // Draw multiple explosion rings
        for (let i = 0; i < this.colors.length; i++) {
            const ringSize = this.currentSize * (1 - i * 0.2);
            const ringAlpha = this.alpha * (1 - i * 0.3);
            
            ctx.globalAlpha = ringAlpha;
            ctx.fillStyle = this.colors[i];
            ctx.beginPath();
            ctx.arc(this.x, this.y, ringSize, 0, Math.PI * 2);
            ctx.fill();
        }
        
        ctx.restore();
    }
}

// Blood splatter effect
export class BloodSplatterEffect extends Effect {
    constructor(x, y, direction) {
        super(x, y, 800);
        this.direction = direction;
        this.splatters = [];
        
        // Create blood splatters
        const numSplatters = 5 + Math.floor(Math.random() * 8);
        for (let i = 0; i < numSplatters; i++) {
            const angle = direction !== null ?
                direction + (Math.random() - 0.5) * Math.PI * 0.8 :
                Math.random() * Math.PI * 2;
            const distance = 10 + Math.random() * 30;
            const size = 2 + Math.random() * 6;
            
            this.splatters.push({
                x: x + Math.cos(angle) * distance,
                y: y + Math.sin(angle) * distance,
                size: size,
                alpha: 0.8 + Math.random() * 0.2
            });
        }
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        
        this.splatters.forEach(splatter => {
            ctx.globalAlpha = this.alpha * splatter.alpha;
            ctx.fillStyle = '#8B0000';
            ctx.beginPath();
            ctx.arc(splatter.x, splatter.y, splatter.size, 0, Math.PI * 2);
            ctx.fill();
        });
        
        ctx.restore();
    }
}

// Debris effect
export class DebrisEffect extends Effect {
    constructor(x, y, color, size) {
        super(x, y, 1000);
        this.color = color;
        this.size = size;
    }

    draw(ctx) {
        // Debris is handled by particles, this is just a placeholder
    }
}

// Muzzle flash effect
export class MuzzleFlashEffect extends Effect {
    constructor(x, y, direction, size) {
        super(x, y, 100);
        this.direction = direction;
        this.size = size;
        this.length = 15 * size;
        this.width = 8 * size;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.direction);
        
        // Draw muzzle flash
        const gradient = ctx.createLinearGradient(0, 0, this.length, 0);
        gradient.addColorStop(0, '#FFFF88');
        gradient.addColorStop(0.5, '#FFAA44');
        gradient.addColorStop(1, 'rgba(255, 68, 0, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.ellipse(this.length / 2, 0, this.length / 2, this.width / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
    }
}

// Hit spark effect
export class HitSparkEffect extends Effect {
    constructor(x, y, direction) {
        super(x, y, 200);
        this.direction = direction;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = '#FFFF00';
        ctx.beginPath();
        ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}