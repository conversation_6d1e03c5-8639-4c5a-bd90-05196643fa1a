import { Particle } from './BaseParticle.js';

// Environmental particle classes
export class SpiceParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#FFA500', 800);
        this.gravity = 30;
        this.friction = 0.95;
    }
}

export class SinkholeParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#8B4513', 600);
        this.gravity = 20;
        this.friction = 0.98;
    }
}

export class ElectricalSparkParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#00FFFF', 300);
        this.friction = 0.85;
        this.twinkle = Math.random() * Math.PI * 2;
    }

    update(deltaTime) {
        super.update(deltaTime);
        this.twinkle += deltaTime * 15;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        const twinkleAlpha = (Math.sin(this.twinkle) + 1) * 0.5;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * twinkleAlpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

export class HazardParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#FF4500', 500);
        this.gravity = 40;
        this.friction = 0.92;
    }
}

// Enhanced vehicle debris particle with realistic physics
export class VehicleDebrisParticle extends Particle {
    constructor(x, y, angle, speed, size, color, vehicleType) {
        super(x, y, angle, speed, size, color, 2000 + Math.random() * 1000);
        this.gravity = 120;
        this.friction = 0.88;
        this.rotation = 0;
        this.rotationSpeed = (Math.random() - 0.5) * 15;
        this.vehicleType = vehicleType;
        this.bounces = 0;
        this.maxBounces = 2 + Math.floor(Math.random() * 3);
        this.settled = false;
        this.settleTime = 0;
        
        // Different debris types based on vehicle
        this.debrisType = this.getDebrisType(vehicleType);
    }

    getDebrisType(vehicleType) {
        const types = {
            'tank': ['armor_plate', 'track_piece', 'turret_fragment'],
            'harvester': ['hull_piece', 'harvester_arm', 'tank_fragment'],
            'apc': ['door_panel', 'wheel', 'armor_piece'],
            'artillery': ['barrel_piece', 'chassis_part', 'wheel'],
            'rocketeer': ['launcher_tube', 'armor_shard', 'equipment']
        };
        
        const vehicleTypes = types[vehicleType] || types['tank'];
        return vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];
    }

    update(deltaTime) {
        if (this.settled) {
            this.settleTime += deltaTime * 1000;
            // Debris stays visible for a while after settling
            this.alpha = Math.max(0, 1 - (this.settleTime - 30000) / 15000); // Fade after 30s, gone after 45s
            this.timeLeft = this.alpha > 0 ? 1000 : 0;
            return;
        }

        const oldY = this.y;
        super.update(deltaTime);
        this.rotation += this.rotationSpeed * deltaTime;
        
        // Ground collision detection (simplified)
        if (this.vy > 0 && this.y > oldY && this.bounces < this.maxBounces) {
            // Simple ground bounce
            this.vy *= -0.4; // Bounce with energy loss
            this.vx *= 0.7;   // Friction on bounce
            this.rotationSpeed *= 0.6;
            this.bounces++;
            
            if (Math.abs(this.vy) < 20 && this.bounces >= 2) {
                // Settle on ground
                this.settled = true;
                this.vy = 0;
                this.vx *= 0.1;
                this.rotationSpeed *= 0.1;
            }
        }
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        // Draw different shapes based on debris type
        ctx.fillStyle = this.color;
        
        switch (this.debrisType) {
            case 'armor_plate':
                ctx.fillRect(-this.size, -this.size * 0.5, this.size * 2, this.size);
                break;
            case 'track_piece':
                ctx.fillRect(-this.size * 1.5, -this.size * 0.3, this.size * 3, this.size * 0.6);
                break;
            case 'wheel':
                ctx.beginPath();
                ctx.arc(0, 0, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;
                ctx.stroke();
                break;
            case 'turret_fragment':
                ctx.beginPath();
                ctx.moveTo(-this.size, -this.size);
                ctx.lineTo(this.size, -this.size * 0.5);
                ctx.lineTo(this.size * 0.5, this.size);
                ctx.lineTo(-this.size * 0.5, this.size);
                ctx.closePath();
                ctx.fill();
                break;
            default:
                // Generic rectangular debris
                ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
                break;
        }
        
        // Add some wear/damage texture
        if (this.settled && Math.random() < 0.3) {
            ctx.fillStyle = '#1A1A1A';
            ctx.fillRect(-this.size * 0.2, -this.size * 0.2, this.size * 0.4, this.size * 0.1);
        }
        
        ctx.restore();
    }
}