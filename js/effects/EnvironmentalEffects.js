import { Effect } from './BaseEffect.js';

// Environmental effect classes
export class SpiceGeyserEffect extends Effect {
    constructor(x, y) {
        super(x, y, 1000);
        this.maxRadius = 40;
        this.pulseSpeed = 5;
    }

    update(deltaTime) {
        super.update(deltaTime);
        this.currentRadius = this.maxRadius * Math.sin(this.pulseSpeed * (this.duration - this.timeLeft) / 1000);
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * 0.6;
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.arc(this.x, this.y, Math.abs(this.currentRadius), 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

export class SinkholeEffect extends Effect {
    constructor(x, y) {
        super(x, y, 800);
        this.maxRadius = 35;
    }

    update(deltaTime) {
        super.update(deltaTime);
        const progress = 1 - (this.timeLeft / this.duration);
        this.currentRadius = this.maxRadius * progress;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * 0.7;
        ctx.fillStyle = '#8B4513';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw swirling effect
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.beginPath();
        for (let i = 0; i < 3; i++) {
            const angle = (this.duration - this.timeLeft) * 0.01 + i * Math.PI * 2 / 3;
            const spiralRadius = this.currentRadius * 0.8;
            ctx.arc(this.x, this.y, spiralRadius - i * 5, angle, angle + Math.PI);
        }
        ctx.stroke();
        ctx.restore();
    }
}

export class ElectricalStormEffect extends Effect {
    constructor(x, y) {
        super(x, y, 600);
        this.lightningBolts = [];
        this.nextBolt = 0;
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        // Generate lightning bolts
        this.nextBolt -= deltaTime * 1000;
        if (this.nextBolt <= 0) {
            this.generateLightningBolt();
            this.nextBolt = 100 + Math.random() * 200;
        }
        
        // Update existing bolts
        this.lightningBolts = this.lightningBolts.filter(bolt => {
            bolt.life -= deltaTime * 1000;
            return bolt.life > 0;
        });
    }

    generateLightningBolt() {
        const bolt = {
            points: [],
            life: 150,
            maxLife: 150
        };
        
        // Generate zigzag points
        const numPoints = 5;
        for (let i = 0; i < numPoints; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 10 + Math.random() * 30;
            bolt.points.push({
                x: this.x + Math.cos(angle) * distance,
                y: this.y + Math.sin(angle) * distance
            });
        }
        
        this.lightningBolts.push(bolt);
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        
        // Draw lightning bolts
        this.lightningBolts.forEach(bolt => {
            const boltAlpha = bolt.life / bolt.maxLife;
            ctx.globalAlpha = this.alpha * boltAlpha;
            ctx.strokeStyle = '#00FFFF';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            if (bolt.points.length > 0) {
                ctx.moveTo(bolt.points[0].x, bolt.points[0].y);
                for (let i = 1; i < bolt.points.length; i++) {
                    ctx.lineTo(bolt.points[i].x, bolt.points[i].y);
                }
            }
            ctx.stroke();
        });
        
        ctx.restore();
    }
}

export class GenericHazardEffect extends Effect {
    constructor(x, y) {
        super(x, y, 600);
        this.pulseSpeed = 3;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        const pulse = 0.5 + 0.5 * Math.sin(this.pulseSpeed * (this.duration - this.timeLeft) / 1000);
        ctx.globalAlpha = this.alpha * pulse * 0.5;
        ctx.fillStyle = '#FF4500';
        ctx.beginPath();
        ctx.arc(this.x, this.y, 20, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}