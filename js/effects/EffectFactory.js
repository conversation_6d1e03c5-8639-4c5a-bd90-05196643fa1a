import {
    BloodParticle,
    DebrisParticle,
    ExplosionParticle,
    LevelUpParticle,
    SparkParticle,
    SparkleParticle
} from './BaseParticle.js';
import {
    BloodSplatterEffect,
    DebrisEffect,
    ExplosionEffect,
    HitSparkEffect,
    MuzzleFlashEffect
} from './BaseEffect.js';
import {
    CraterEffect,
    LevelUpEffect,
    LootPickupEffect,
    LootSpawnEffect
} from './SpecialEffects.js';
import {
    ElectricalSparkParticle,
    HazardParticle,
    SinkholeParticle,
    SpiceParticle,
    VehicleDebrisParticle
} from './SpecializedParticles.js';
import {
    ElectricalStormEffect,
    GenericHazardEffect,
    SinkholeEffect,
    SpiceGeyserEffect
} from './EnvironmentalEffects.js';

export class EffectFactory {
    constructor(game) {
        this.game = game;
    }

    // Effect creation methods
    createExplosion(x, y, size, type) {
        return new ExplosionEffect(x, y, size, type);
    }

    createBloodSplatter(x, y, direction) {
        return new BloodSplatterEffect(x, y, direction);
    }

    createDebris(x, y, color, size) {
        return new DebrisEffect(x, y, color, size);
    }

    createMuzzleFlash(x, y, direction, size) {
        return new MuzzleFlashEffect(x, y, direction, size);
    }

    createHitSpark(x, y, direction) {
        return new HitSparkEffect(x, y, direction);
    }

    createLootSpawn(x, y, color) {
        return new LootSpawnEffect(x, y, color);
    }

    createLootPickup(x, y, color) {
        return new LootPickupEffect(x, y, color);
    }

    createLevelUp(x, y) {
        return new LevelUpEffect(x, y);
    }

    createCrater(x, y, size) {
        return new CraterEffect(x, y, size);
    }

    createEnvironmentalEffect(x, y, hazardType) {
        switch (hazardType) {
            case 'spice_geyser':
                return new SpiceGeyserEffect(x, y);
            case 'sinkhole':
                return new SinkholeEffect(x, y);
            case 'electrical_storm':
                return new ElectricalStormEffect(x, y);
            default:
                return new GenericHazardEffect(x, y);
        }
    }

    // Particle creation methods
    createExplosionParticles(x, y, size, type) {
        const particles = [];
        // Increase particle count by 2-3x: minimum 40 particles, scale with size
        const numParticles = Math.min(80, Math.floor(size / 2) + 40);
        
        for (let i = 0; i < numParticles; i++) {
            const angle = (Math.PI * 2 * i) / numParticles + (Math.random() - 0.5) * 0.8;
            // Reduce speed by 60-70%: was 50-150, now 15-60
            const speed = 15 + Math.random() * 45;
            // Add variety in particle sizes
            const particleSize = 1 + Math.random() * 6;
            
            // Add more color variety for realism
            const colors = type === 'building' ?
                ['#FFA500', '#FF8C00', '#FF6347', '#FF4500', '#DC143C'] :
                ['#FF4500', '#FF6347', '#DC143C', '#B22222', '#8B0000'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            particles.push(new ExplosionParticle(x, y, angle, speed, particleSize, color));
        }
        
        // Add additional debris particles for more realistic explosion
        const debrisCount = Math.floor(numParticles * 0.3);
        for (let i = 0; i < debrisCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 10 + Math.random() * 30;
            const particleSize = 2 + Math.random() * 4;
            const debrisColors = ['#8B4513', '#A0522D', '#654321', '#4A4A4A'];
            const color = debrisColors[Math.floor(Math.random() * debrisColors.length)];
            
            particles.push(new ExplosionParticle(x, y, angle, speed, particleSize, color));
        }
        
        return particles;
    }

    createBloodParticles(x, y, direction) {
        const particles = [];
        const numDrops = 8 + Math.floor(Math.random() * 12);
        
        for (let i = 0; i < numDrops; i++) {
            const angle = direction !== null ? 
                direction + (Math.random() - 0.5) * Math.PI :
                Math.random() * Math.PI * 2;
            const speed = 30 + Math.random() * 70;
            const size = 1 + Math.random() * 3;
            
            particles.push(new BloodParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createDebrisParticles(x, y, color, size) {
        const particles = [];
        const numPieces = size === 'large' ? 15 : size === 'medium' ? 10 : 6;
        
        for (let i = 0; i < numPieces; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 40 + Math.random() * 80;
            const pieceSize = size === 'large' ? 3 + Math.random() * 5 : 
                            size === 'medium' ? 2 + Math.random() * 3 : 
                            1 + Math.random() * 2;
            
            particles.push(new DebrisParticle(x, y, angle, speed, pieceSize, color));
        }
        
        return particles;
    }

    createSparkParticles(x, y, direction) {
        const particles = [];
        const numSparks = 5 + Math.floor(Math.random() * 8);
        
        for (let i = 0; i < numSparks; i++) {
            const angle = direction !== null ?
                direction + Math.PI + (Math.random() - 0.5) * Math.PI * 0.5 :
                Math.random() * Math.PI * 2;
            const speed = 60 + Math.random() * 40;
            const size = 0.5 + Math.random() * 1.5;
            
            particles.push(new SparkParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createSparkleParticles(x, y, color) {
        const particles = [];
        const numSparkles = 8;
        
        for (let i = 0; i < numSparkles; i++) {
            const angle = (Math.PI * 2 * i) / numSparkles;
            const speed = 30 + Math.random() * 20;
            const size = 1 + Math.random() * 2;
            
            particles.push(new SparkleParticle(x, y, angle, speed, size, color));
        }
        
        return particles;
    }

    createLevelUpParticles(x, y) {
        const particles = [];
        const numParticles = 12;
        
        for (let i = 0; i < numParticles; i++) {
            const angle = (Math.PI * 2 * i) / numParticles;
            const speed = 40 + Math.random() * 30;
            const size = 2 + Math.random() * 2;
            
            particles.push(new LevelUpParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createEnvironmentalParticles(x, y, hazardType) {
        const particles = [];
        
        switch (hazardType) {
            case 'spice_geyser':
                return this.createSpiceGeyserParticles(x, y);
            case 'sinkhole':
                return this.createSinkholeParticles(x, y);
            case 'electrical_storm':
                return this.createElectricalStormParticles(x, y);
            default:
                return this.createGenericHazardParticles(x, y);
        }
    }

    createSpiceGeyserParticles(x, y) {
        const particles = [];
        const numParticles = 15;
        
        for (let i = 0; i < numParticles; i++) {
            const angle = -Math.PI/2 + (Math.random() - 0.5) * Math.PI * 0.5; // Upward spray
            const speed = 80 + Math.random() * 60;
            const size = 2 + Math.random() * 3;
            
            particles.push(new SpiceParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createSinkholeParticles(x, y) {
        const particles = [];
        const numParticles = 12;
        
        for (let i = 0; i < numParticles; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 30 + Math.random() * 40;
            const startX = x + Math.cos(angle) * distance;
            const startY = y + Math.sin(angle) * distance;
            const speed = 40 + Math.random() * 30;
            const size = 1 + Math.random() * 2;
            
            particles.push(new SinkholeParticle(startX, startY, Math.atan2(y - startY, x - startX), speed, size));
        }
        
        return particles;
    }

    createElectricalStormParticles(x, y) {
        const particles = [];
        const numSparks = 8;
        
        for (let i = 0; i < numSparks; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 60 + Math.random() * 40;
            const size = 1 + Math.random() * 2;
            
            particles.push(new ElectricalSparkParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createGenericHazardParticles(x, y) {
        const particles = [];
        const numParticles = 10;
        
        for (let i = 0; i < numParticles; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 50 + Math.random() * 50;
            const size = 1 + Math.random() * 2;
            
            particles.push(new HazardParticle(x, y, angle, speed, size));
        }
        
        return particles;
    }

    createVehicleDebrisParticles(x, y, vehicleType, size) {
        const particles = [];
        const numDebrisPieces = Math.min(25, Math.floor(size / 2));
        
        for (let i = 0; i < numDebrisPieces; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 60 + Math.random() * 120;
            const pieceSize = 2 + Math.random() * 4;
            const scatterDistance = 20 + Math.random() * 40;
            
            const debrisX = x + Math.cos(angle) * scatterDistance * Math.random();
            const debrisY = y + Math.sin(angle) * scatterDistance * Math.random();
            const debrisColor = this.getVehicleDebrisColor(vehicleType);
            
            particles.push(new VehicleDebrisParticle(debrisX, debrisY, angle, speed, pieceSize, debrisColor, vehicleType));
        }
        
        return particles;
    }

    // Get appropriate debris color based on vehicle type
    getVehicleDebrisColor(vehicleType) {
        const vehicleColors = {
            'tank': '#4A4A4A',
            'harvester': '#8B7355',
            'apc': '#556B2F',
            'artillery': '#2F4F4F',
            'rocketeer': '#8B4513',
            'default': '#696969'
        };
        return vehicleColors[vehicleType] || vehicleColors.default;
    }
}