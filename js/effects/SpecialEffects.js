import { Effect } from './BaseEffect.js';

// Loot spawn effect
export class LootSpawnEffect extends Effect {
    constructor(x, y, color) {
        super(x, y, 600);
        this.color = color;
        this.maxRadius = 25;
    }

    update(deltaTime) {
        super.update(deltaTime);
        this.currentRadius = this.maxRadius * (1 - this.timeLeft / this.duration);
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * 0.5;
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        ctx.stroke();
        ctx.restore();
    }
}

// Loot pickup effect
export class LootPickupEffect extends Effect {
    constructor(x, y, color) {
        super(x, y, 400);
        this.color = color;
        this.startY = y;
    }

    update(deltaTime) {
        super.update(deltaTime);
        const progress = 1 - (this.timeLeft / this.duration);
        this.y = this.startY - progress * 30;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('+', this.x, this.y);
        ctx.restore();
    }
}

// Level up effect
export class LevelUpEffect extends Effect {
    constructor(x, y) {
        super(x, y, 1000);
        this.startY = y;
    }

    update(deltaTime) {
        super.update(deltaTime);
        const progress = 1 - (this.timeLeft / this.duration);
        this.y = this.startY - progress * 40;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = '#FFD700';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.strokeText('LEVEL UP!', this.x, this.y);
        ctx.fillText('LEVEL UP!', this.x, this.y);
        ctx.restore();
    }
}

// Crater effect that persists on the battlefield
export class CraterEffect extends Effect {
    constructor(x, y, size) {
        // Craters last 45-60 seconds
        super(x, y, 45000 + Math.random() * 15000);
        this.size = size;
        this.maxRadius = size === 'large' ? 35 : size === 'medium' ? 25 : 15;
        this.currentRadius = 0;
        this.depth = size === 'large' ? 8 : size === 'medium' ? 5 : 3;
        this.fadingStartTime = this.duration * 0.7; // Start fading at 70% of lifetime
    }

    update(deltaTime) {
        super.update(deltaTime);
        const progress = Math.min(1, (this.duration - this.timeLeft) / 1000); // Expand over 1 second
        this.currentRadius = this.maxRadius * Math.min(1, progress);
        
        // Start fading in the last 30% of lifetime
        if (this.timeLeft < this.fadingStartTime) {
            this.alpha = this.timeLeft / this.fadingStartTime;
        }
    }

    draw(ctx) {
        if (this.alpha <= 0 || this.currentRadius <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * 0.7;
        
        // Draw crater rim (raised edge)
        ctx.fillStyle = '#8B7355';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius + 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw crater depression (darker center)
        ctx.fillStyle = '#654321';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw inner shadow for depth effect
        ctx.fillStyle = '#4A4A4A';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius * 0.6, 0, Math.PI * 2);
        ctx.fill();
        
        // Add some texture with small debris spots
        ctx.fillStyle = '#2F2F2F';
        for (let i = 0; i < 5; i++) {
            const angle = (Math.PI * 2 * i) / 5 + Math.sin(this.timeLeft * 0.001) * 0.1;
            const distance = this.currentRadius * 0.3 + Math.random() * this.currentRadius * 0.2;
            const spotX = this.x + Math.cos(angle) * distance;
            const spotY = this.y + Math.sin(angle) * distance;
            ctx.beginPath();
            ctx.arc(spotX, spotY, 1 + Math.random() * 2, 0, Math.PI * 2);
            ctx.fill();
        }
        
        ctx.restore();
    }
}