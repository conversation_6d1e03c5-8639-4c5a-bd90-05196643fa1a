export class FloatingTextManager {
    constructor() {
        this.floatingTexts = [];
        this.maxTexts = 50;
    }

    update(deltaTime) {
        // Update all floating texts
        for (let i = this.floatingTexts.length - 1; i >= 0; i--) {
            const text = this.floatingTexts[i];
            text.update(deltaTime);
            
            if (text.isDead()) {
                this.floatingTexts.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        // Draw all floating texts
        this.floatingTexts.forEach(text => text.draw(ctx));
    }

    createDamageNumber(x, y, damage, isCritical = false) {
        // Remove oldest text if we have too many
        if (this.floatingTexts.length >= this.maxTexts) {
            this.floatingTexts.shift();
        }

        const text = new FloatingText(
            x, y,
            `-${damage}`,
            isCritical ? '#FF4444' : '#FFAA00',
            isCritical ? 18 : 14,
            isCritical ? 2000 : 1500
        );

        this.floatingTexts.push(text);
    }

    createHealingNumber(x, y, healing) {
        if (this.floatingTexts.length >= this.maxTexts) {
            this.floatingTexts.shift();
        }

        const text = new FloatingText(
            x, y,
            `+${healing}`,
            '#44FF44',
            14,
            1500
        );

        this.floatingTexts.push(text);
    }

    createFloatingText(x, y, message, color = '#FFFFFF', fontSize = 12, duration = 1000) {
        if (this.floatingTexts.length >= this.maxTexts) {
            this.floatingTexts.shift();
        }

        const text = new FloatingText(x, y, message, color, fontSize, duration);
        this.floatingTexts.push(text);
    }
}

class FloatingText {
    constructor(x, y, text, color, fontSize, duration) {
        this.startX = x;
        this.startY = y;
        this.x = x;
        this.y = y;
        this.text = text;
        this.color = color;
        this.fontSize = fontSize;
        this.duration = duration;
        this.maxDuration = duration;
        
        // Animation properties
        this.velocityY = -30; // Move upward
        this.velocityX = (Math.random() - 0.5) * 20; // Small horizontal drift
        this.alpha = 1.0;
        this.scale = 1.0;
        
        // Start with a small scale-up animation
        this.scaleAnimation = 0.5;
        this.scaleAnimationSpeed = 3.0;
    }

    update(deltaTime) {
        // Update position
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;
        
        // Slow down vertical movement over time
        this.velocityY *= 0.98;
        this.velocityX *= 0.95;
        
        // Update scale animation
        if (this.scaleAnimation < 1.0) {
            this.scaleAnimation += this.scaleAnimationSpeed * deltaTime;
            this.scale = 0.5 + (this.scaleAnimation * 0.5);
        }
        
        // Update duration and alpha
        this.duration -= deltaTime * 1000;
        
        // Fade out in the last 30% of lifetime
        const fadeThreshold = this.maxDuration * 0.3;
        if (this.duration < fadeThreshold) {
            this.alpha = this.duration / fadeThreshold;
        }
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        
        // Apply transparency
        ctx.globalAlpha = this.alpha;
        
        // Set font
        ctx.font = `bold ${this.fontSize * this.scale}px Arial`;
        ctx.fillStyle = this.color;
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Draw text with outline
        ctx.strokeText(this.text, this.x, this.y);
        ctx.fillText(this.text, this.x, this.y);
        
        ctx.restore();
    }

    isDead() {
        return this.duration <= 0 || this.alpha <= 0;
    }
}