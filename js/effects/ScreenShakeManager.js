export class ScreenShakeManager {
    constructor() {
        this.shake = { 
            x: 0, 
            y: 0, 
            intensity: 0, 
            duration: 0, 
            timeLeft: 0 
        };
    }

    update(deltaTime) {
        if (this.shake.timeLeft > 0) {
            this.shake.timeLeft -= deltaTime * 1000;
            const progress = 1 - (this.shake.timeLeft / this.shake.duration);
            const currentIntensity = this.shake.intensity * (1 - progress);
            
            this.shake.x = (Math.random() - 0.5) * currentIntensity * 2;
            this.shake.y = (Math.random() - 0.5) * currentIntensity * 2;
            
            if (this.shake.timeLeft <= 0) {
                this.shake.x = 0;
                this.shake.y = 0;
            }
        }
    }

    create(intensity, duration) {
        this.shake.intensity = intensity;
        this.shake.duration = duration;
        this.shake.timeLeft = duration;
    }

    getOffset() {
        return { x: this.shake.x, y: this.shake.y };
    }

    isActive() {
        return this.shake.timeLeft > 0;
    }
}