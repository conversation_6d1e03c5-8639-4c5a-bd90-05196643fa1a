// Base particle class
export class Particle {
    constructor(x, y, angle, speed, size, color, lifetime = 1000) {
        this.x = x;
        this.y = y;
        this.vx = Math.cos(angle) * speed;
        this.vy = Math.sin(angle) * speed;
        this.size = size;
        this.color = color;
        this.lifetime = lifetime;
        this.timeLeft = lifetime;
        this.alpha = 1;
        this.gravity = 0;
        this.friction = 0.98;
    }

    update(deltaTime) {
        this.timeLeft -= deltaTime * 1000;
        this.alpha = Math.max(0, this.timeLeft / this.lifetime);
        
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        this.vy += this.gravity * deltaTime;
        this.vx *= this.friction;
        this.vy *= this.friction;
    }

    isDead() {
        return this.timeLeft <= 0;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// Explosion particle
export class ExplosionParticle extends Particle {
    constructor(x, y, angle, speed, size, color) {
        // Increase lifetime for more realistic particle behavior
        super(x, y, angle, speed, size, color, 1200);
        // Increase gravity for more realistic falling motion
        this.gravity = 120;
        // Reduce friction for more air resistance effect
        this.friction = 0.88;
        // Add wind resistance that increases with speed
        this.airResistance = 0.02;
        // Add slight random rotation for visual variety
        this.rotation = 0;
        this.rotationSpeed = (Math.random() - 0.5) * 8;
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        // Apply air resistance based on current velocity
        const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
        const resistanceFactor = 1 - (this.airResistance * currentSpeed * deltaTime);
        this.vx *= Math.max(0.1, resistanceFactor);
        this.vy *= Math.max(0.1, resistanceFactor);
        
        // Update rotation
        this.rotation += this.rotationSpeed * deltaTime;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// Blood particle
export class BloodParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#8B0000', 1200);
        this.gravity = 80;
        this.friction = 0.92;
    }
}

// Debris particle
export class DebrisParticle extends Particle {
    constructor(x, y, angle, speed, size, color) {
        super(x, y, angle, speed, size, color, 1500);
        this.gravity = 100;
        this.friction = 0.90;
        this.rotation = 0;
        this.rotationSpeed = (Math.random() - 0.5) * 10;
    }

    update(deltaTime) {
        super.update(deltaTime);
        this.rotation += this.rotationSpeed * deltaTime;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
        ctx.restore();
    }
}

// Spark particle
export class SparkParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#FFFF00', 300);
        this.friction = 0.85;
    }
}

// Sparkle particle
export class SparkleParticle extends Particle {
    constructor(x, y, angle, speed, size, color) {
        super(x, y, angle, speed, size, color, 600);
        this.friction = 0.90;
        this.twinkle = Math.random() * Math.PI * 2;
    }

    update(deltaTime) {
        super.update(deltaTime);
        this.twinkle += deltaTime * 10;
    }

    draw(ctx) {
        if (this.alpha <= 0) return;
        
        const twinkleAlpha = (Math.sin(this.twinkle) + 1) * 0.5;
        
        ctx.save();
        ctx.globalAlpha = this.alpha * twinkleAlpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// Level up particle
export class LevelUpParticle extends Particle {
    constructor(x, y, angle, speed, size) {
        super(x, y, angle, speed, size, '#FFD700', 1000);
        this.gravity = -30; // Float upward
        this.friction = 0.95;
    }
}