import { EffectFactory } from './EffectFactory.js';
import { FloatingTextManager } from './FloatingTextManager.js';
import { ParticleManager } from './ParticleManager.js';
import { ScreenShakeManager } from './ScreenShakeManager.js';

export class VisualEffectsManager {
    constructor(game) {
        this.game = game;
        this.effects = [];
        // Increase particle capacity to handle more realistic explosions
        this.maxParticles = 1200;
        
        // Initialize subsystems
        this.screenShake = new ScreenShakeManager();
        this.effectFactory = new EffectFactory(game);
        this.particleManager = new ParticleManager(this.maxParticles);
        this.floatingTextManager = new FloatingTextManager();
    }

    update(deltaTime) {
        // Update screen shake
        this.screenShake.update(deltaTime);

        // Update effects
        for (let i = this.effects.length - 1; i >= 0; i--) {
            const effect = this.effects[i];
            effect.update(deltaTime);
            
            if (effect.isDead()) {
                this.effects.splice(i, 1);
            }
        }

        // Update particles
        this.particleManager.update(deltaTime);

        // Update floating text
        this.floatingTextManager.update(deltaTime);
    }

    draw(ctx) {
        ctx.save();
        
        // Apply screen shake
        const shakeOffset = this.screenShake.getOffset();
        if (shakeOffset.x !== 0 || shakeOffset.y !== 0) {
            ctx.translate(shakeOffset.x, shakeOffset.y);
        }
        
        // Draw particles first (behind effects)
        this.particleManager.draw(ctx);
        
        // Draw effects
        this.effects.forEach(effect => effect.draw(ctx));
        
        // Draw floating text (on top of everything)
        this.floatingTextManager.draw(ctx);
        
        ctx.restore();
    }

    // Screen shake effect
    createScreenShake(intensity, duration) {
        this.screenShake.create(intensity, duration);
    }

    // Explosion effect
    createExplosion(x, y, size, type = 'normal') {
        const explosion = this.effectFactory.createExplosion(x, y, size, type);
        this.effects.push(explosion);
        
        // Create explosion particles
        const particles = this.effectFactory.createExplosionParticles(x, y, size, type);
        particles.forEach(particle => this.particleManager.addParticle(particle));
        
        // Screen shake based on explosion size
        const shakeIntensity = Math.min(15, size / 4);
        this.createScreenShake(shakeIntensity, 200 + size * 2);
    }

    // Blood splatter effect for human units
    createBloodSplatter(x, y, direction = null) {
        const bloodSplatter = this.effectFactory.createBloodSplatter(x, y, direction);
        this.effects.push(bloodSplatter);
        
        // Create blood particles
        const particles = this.effectFactory.createBloodParticles(x, y, direction);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    // Debris effect for destroyed units/buildings
    createDebris(x, y, color, size = 'medium') {
        const debris = this.effectFactory.createDebris(x, y, color, size);
        this.effects.push(debris);
        
        // Create debris particles
        const particles = this.effectFactory.createDebrisParticles(x, y, color, size);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    // Crater effect for destroyed vehicles
    createCrater(x, y, size = 'medium') {
        const crater = this.effectFactory.createCrater(x, y, size);
        this.effects.push(crater);
    }

    // Enhanced vehicle destruction effect
    createVehicleDestruction(x, y, vehicleType, size) {
        // Create main explosion
        this.createExplosion(x, y, size * 1.5, 'vehicle');
        
        // Create debris based on vehicle type
        const debrisColor = this.effectFactory.getVehicleDebrisColor(vehicleType);
        this.createDebris(x, y, debrisColor, size > 25 ? 'large' : 'medium');
        
        // Create crater that persists longer
        this.createCrater(x, y, size > 25 ? 'large' : 'medium');
        
        // Create additional scattered debris particles
        const scatteredParticles = this.effectFactory.createVehicleDebrisParticles(x, y, vehicleType, size);
        scatteredParticles.forEach(particle => this.particleManager.addParticle(particle));
        
        // Enhanced screen shake for vehicle destruction
        const shakeIntensity = Math.min(25, size / 2);
        this.createScreenShake(shakeIntensity, 400 + size * 3);
    }

    // Muzzle flash effect
    createMuzzleFlash(x, y, direction, size = 1) {
        const muzzleFlash = this.effectFactory.createMuzzleFlash(x, y, direction, size);
        this.effects.push(muzzleFlash);
    }

    // Hit spark effect
    createHitSpark(x, y, direction = null) {
        const hitSpark = this.effectFactory.createHitSpark(x, y, direction);
        this.effects.push(hitSpark);
        
        // Create spark particles
        const particles = this.effectFactory.createSparkParticles(x, y, direction);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    // Loot spawn effect
    createLootSpawnEffect(x, y, color) {
        const lootSpawn = this.effectFactory.createLootSpawn(x, y, color);
        this.effects.push(lootSpawn);
        
        // Create sparkle particles
        const particles = this.effectFactory.createSparkleParticles(x, y, color);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    // Loot pickup effect
    createLootPickupEffect(x, y, color) {
        const lootPickup = this.effectFactory.createLootPickup(x, y, color);
        this.effects.push(lootPickup);
    }

    // Level up effect
    createLevelUpEffect(x, y) {
        const levelUp = this.effectFactory.createLevelUp(x, y);
        this.effects.push(levelUp);
        
        // Create level up particles
        const particles = this.effectFactory.createLevelUpParticles(x, y);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    // Environmental effect for hazards
    createEnvironmentalEffect(x, y, hazardType) {
        const effect = this.effectFactory.createEnvironmentalEffect(x, y, hazardType);
        this.effects.push(effect);
        
        // Create environmental particles
        const particles = this.effectFactory.createEnvironmentalParticles(x, y, hazardType);
        particles.forEach(particle => this.particleManager.addParticle(particle));
    }

    getScreenShakeOffset() {
        return this.screenShake.getOffset();
    }

    // Damage number display
    createDamageNumber(x, y, damage, isCritical = false) {
        this.floatingTextManager.createDamageNumber(x, y, damage, isCritical);
    }

    // Healing number display
    createHealingNumber(x, y, healing) {
        this.floatingTextManager.createHealingNumber(x, y, healing);
    }

    // Generic floating text
    createFloatingText(x, y, message, color = '#FFFFFF', fontSize = 12, duration = 1000) {
        this.floatingTextManager.createFloatingText(x, y, message, color, fontSize, duration);
    }
}