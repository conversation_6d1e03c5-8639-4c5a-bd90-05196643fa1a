export { VisualEffectsManager } from './VisualEffectsManager.js';
export { ScreenShakeManager } from './ScreenShakeManager.js';
export { ParticleManager } from './ParticleManager.js';
export { EffectFactory } from './EffectFactory.js';
export { FloatingTextManager } from './FloatingTextManager.js';

// Export base classes
export { Effect } from './BaseEffect.js';
export { Particle } from './BaseParticle.js';

// Export specific effect classes
export {
    ExplosionEffect,
    BloodSplatterEffect,
    DebrisEffect,
    MuzzleFlashEffect,
    HitSparkEffect
} from './BaseEffect.js';

export {
    LootSpawnEffect,
    LootPickupEffect,
    LevelUpEffect,
    CraterEffect
} from './SpecialEffects.js';

export {
    SpiceGeyserEffect,
    SinkholeEffect,
    ElectricalStormEffect,
    GenericHazardEffect
} from './EnvironmentalEffects.js';

// Export particle classes
export {
    ExplosionParticle,
    BloodParticle,
    DebrisParticle,
    SparkParticle,
    SparkleParticle,
    LevelUpParticle
} from './BaseParticle.js';

export {
    SpiceParticle,
    SinkholeParticle,
    ElectricalSparkParticle,
    HazardParticle,
    VehicleDebrisParticle
} from './SpecializedParticles.js';