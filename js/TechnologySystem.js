export class TechnologySystem {
    constructor(game) {
        this.game = game;
        
        // Research state
        this.researchedTechnologies = new Set();
        this.currentResearch = null;
        this.researchProgress = 0;
        this.researchQueue = [];
        
        // Technology tree definition
        this.technologies = {
            // Tier 1 - Basic Technologies
            advanced_materials: {
                name: 'Advanced Materials',
                description: 'Improves armor for all units by 15%',
                cost: { techPoints: 100, spice: 500 },
                researchTime: 60,
                prerequisites: [],
                effects: { armorBonus: 0.15 },
                tier: 1,
                category: 'military'
            },
            
            improved_engines: {
                name: 'Improved Engines',
                description: 'Increases movement speed for all vehicles by 20%',
                cost: { techPoints: 120, spice: 600 },
                researchTime: 75,
                prerequisites: [],
                effects: { vehicleSpeedBonus: 0.20 },
                tier: 1,
                category: 'mobility'
            },
            
            efficient_harvesting: {
                name: 'Efficient Harvesting',
                description: 'Increases spice harvesting rate by 25%',
                cost: { techPoints: 80, spice: 400 },
                researchTime: 45,
                prerequisites: [],
                effects: { harvestingBonus: 0.25 },
                tier: 1,
                category: 'economic'
            },
            
            water_recycling: {
                name: 'Water Recycling',
                description: 'Reduces water consumption by 30%',
                cost: { techPoints: 90, spice: 450 },
                researchTime: 50,
                prerequisites: [],
                effects: { waterEfficiency: 0.30 },
                tier: 1,
                category: 'economic'
            },
            
            // Tier 2 - Advanced Technologies
            composite_armor: {
                name: 'Composite Armor',
                description: 'Advanced armor technology, +25% armor and damage resistance',
                cost: { techPoints: 200, spice: 1000, water: 100 },
                researchTime: 120,
                prerequisites: ['advanced_materials'],
                effects: { armorBonus: 0.25, damageResistance: 0.15 },
                tier: 2,
                category: 'military'
            },
            
            stealth_technology: {
                name: 'Stealth Technology',
                description: 'Unlocks stealth detection and improves stealth units',
                cost: { techPoints: 250, spice: 1200, water: 150 },
                researchTime: 150,
                prerequisites: ['advanced_materials'],
                effects: { stealthDetection: true, stealthBonus: 0.20 },
                tier: 2,
                category: 'military'
            },
            
            enhanced_weaponry: {
                name: 'Enhanced Weaponry',
                description: 'Increases damage for all units by 20%',
                cost: { techPoints: 180, spice: 900 },
                researchTime: 100,
                prerequisites: ['advanced_materials'],
                effects: { damageBonus: 0.20 },
                tier: 2,
                category: 'military'
            },
            
            advanced_logistics: {
                name: 'Advanced Logistics',
                description: 'Increases unit build speed by 30% and reduces costs by 10%',
                cost: { techPoints: 160, spice: 800, water: 80 },
                researchTime: 90,
                prerequisites: ['efficient_harvesting'],
                effects: { buildSpeedBonus: 0.30, costReduction: 0.10 },
                tier: 2,
                category: 'economic'
            },
            
            hover_technology: {
                name: 'Hover Technology',
                description: 'Vehicles can traverse water and rough terrain',
                cost: { techPoints: 220, spice: 1100, water: 200 },
                researchTime: 130,
                prerequisites: ['improved_engines'],
                effects: { hoverMovement: true },
                tier: 2,
                category: 'mobility'
            },
            
            // Tier 3 - Elite Technologies
            shield_technology: {
                name: 'Shield Technology',
                description: 'Unlocks personal shields for infantry units',
                cost: { techPoints: 300, spice: 1500, water: 250 },
                researchTime: 180,
                prerequisites: ['composite_armor', 'stealth_technology'],
                effects: { personalShields: true },
                tier: 3,
                category: 'military'
            },
            
            plasma_weaponry: {
                name: 'Plasma Weaponry',
                description: 'Unlocks devastating plasma weapons with splash damage',
                cost: { techPoints: 350, spice: 1800, water: 300 },
                researchTime: 200,
                prerequisites: ['enhanced_weaponry', 'stealth_technology'],
                effects: { plasmaWeapons: true, splashDamageBonus: 0.50 },
                tier: 3,
                category: 'military'
            },
            
            orbital_support: {
                name: 'Orbital Support',
                description: 'Enables orbital strikes and reconnaissance',
                cost: { techPoints: 400, spice: 2000, water: 400 },
                researchTime: 240,
                prerequisites: ['advanced_logistics', 'hover_technology'],
                effects: { orbitalStrike: true, globalVision: true },
                tier: 3,
                category: 'strategic'
            },
            
            matter_conversion: {
                name: 'Matter Conversion',
                description: 'Convert spice directly into other resources',
                cost: { techPoints: 320, spice: 1600, water: 350 },
                researchTime: 220,
                prerequisites: ['advanced_logistics', 'water_recycling'],
                effects: { resourceConversion: true },
                tier: 3,
                category: 'economic'
            },
            
            // Tier 4 - Legendary Technologies
            quantum_computing: {
                name: 'Quantum Computing',
                description: 'Enables advanced AI and predictive systems',
                cost: { techPoints: 500, spice: 2500, water: 500 },
                researchTime: 300,
                prerequisites: ['orbital_support', 'matter_conversion'],
                effects: { quantumTech: true, researchSpeedBonus: 0.50 },
                tier: 4,
                category: 'advanced'
            },
            
            neural_interface: {
                name: 'Neural Interface',
                description: 'Direct mind-machine connection for enhanced unit control',
                cost: { techPoints: 450, spice: 2200, water: 400 },
                researchTime: 280,
                prerequisites: ['shield_technology', 'plasma_weaponry'],
                effects: { neuralControl: true, unitEfficiencyBonus: 0.30 },
                tier: 4,
                category: 'advanced'
            },
            
            // Additional Tier 2-3 Technologies
            energy_shields: {
                name: 'Energy Shields',
                description: 'Advanced energy barriers for buildings',
                cost: { techPoints: 180, spice: 900, water: 120 },
                researchTime: 110,
                prerequisites: ['advanced_materials'],
                effects: { buildingShields: true, buildingArmorBonus: 0.25 },
                tier: 2,
                category: 'defensive'
            },
            
            automated_defenses: {
                name: 'Automated Defenses',
                description: 'Self-operating defensive turrets and systems',
                cost: { techPoints: 200, spice: 1000, water: 150 },
                researchTime: 120,
                prerequisites: ['enhanced_weaponry'],
                effects: { autoDefenses: true, turretDamageBonus: 0.40 },
                tier: 2,
                category: 'defensive'
            },
            
            resource_optimization: {
                name: 'Resource Optimization',
                description: 'Advanced resource processing and efficiency',
                cost: { techPoints: 140, spice: 700, water: 100 },
                researchTime: 80,
                prerequisites: ['efficient_harvesting'],
                effects: { resourceEfficiency: 0.20, spiceCapacityBonus: 0.30 },
                tier: 2,
                category: 'economic'
            },
            
            trade_networks: {
                name: 'Trade Networks',
                description: 'Enables resource trading and economic diplomacy',
                cost: { techPoints: 160, spice: 800, water: 120 },
                researchTime: 90,
                prerequisites: ['advanced_logistics'],
                effects: { tradeEnabled: true, diplomaticBonus: 0.15 },
                tier: 2,
                category: 'economic'
            },
            
            advanced_reconnaissance: {
                name: 'Advanced Reconnaissance',
                description: 'Enhanced scouting and intelligence gathering',
                cost: { techPoints: 170, spice: 850, water: 100 },
                researchTime: 95,
                prerequisites: ['stealth_technology'],
                effects: { enhancedVision: true, stealthDetectionBonus: 0.50 },
                tier: 2,
                category: 'military'
            },
            
            tactical_coordination: {
                name: 'Tactical Coordination',
                description: 'Improved unit coordination and formation bonuses',
                cost: { techPoints: 190, spice: 950, water: 130 },
                researchTime: 105,
                prerequisites: ['enhanced_weaponry'],
                effects: { formationBonus: 0.25, groupCombatBonus: 0.20 },
                tier: 2,
                category: 'military'
            },
            
            environmental_adaptation: {
                name: 'Environmental Adaptation',
                description: 'Units adapt to harsh environmental conditions',
                cost: { techPoints: 210, spice: 1050, water: 180 },
                researchTime: 115,
                prerequisites: ['water_recycling', 'hover_technology'],
                effects: { environmentalResistance: 0.40, weatherImmunity: true },
                tier: 2,
                category: 'defensive'
            },
            
            // Tier 3 Advanced Technologies
            nanobots: {
                name: 'Nanobots',
                description: 'Self-repairing systems and enhanced healing',
                cost: { techPoints: 280, spice: 1400, water: 250 },
                researchTime: 160,
                prerequisites: ['automated_defenses', 'resource_optimization'],
                effects: { selfRepair: true, healingBonus: 0.50 },
                tier: 3,
                category: 'advanced'
            },
            
            fusion_power: {
                name: 'Fusion Power',
                description: 'Clean, unlimited energy generation',
                cost: { techPoints: 320, spice: 1600, water: 300 },
                researchTime: 180,
                prerequisites: ['energy_shields', 'environmental_adaptation'],
                effects: { fusionPower: true, powerGenerationBonus: 1.0 },
                tier: 3,
                category: 'economic'
            },
            
            psi_warfare: {
                name: 'Psi Warfare',
                description: 'Psychic abilities for mind control and confusion',
                cost: { techPoints: 340, spice: 1700, water: 320 },
                researchTime: 190,
                prerequisites: ['advanced_reconnaissance', 'tactical_coordination'],
                effects: { psiAbilities: true, enemyConfusion: 0.30 },
                tier: 3,
                category: 'military'
            },
            
            terraforming: {
                name: 'Terraforming',
                description: 'Ability to modify terrain and create new resources',
                cost: { techPoints: 360, spice: 1800, water: 400 },
                researchTime: 210,
                prerequisites: ['fusion_power', 'nanobots'],
                effects: { terraformingAbility: true, terrainModification: true },
                tier: 3,
                category: 'strategic'
            },
            
            quantum_entanglement: {
                name: 'Quantum Entanglement',
                description: 'Instantaneous communication and coordination',
                cost: { techPoints: 380, spice: 1900, water: 350 },
                researchTime: 220,
                prerequisites: ['psi_warfare', 'orbital_support'],
                effects: { instantCommunication: true, globalCoordination: true },
                tier: 3,
                category: 'strategic'
            }
        };
        
        // Research modifiers
        this.researchModifiers = {
            speed: 1.0,
            cost: 1.0
        };
    }
    
    // Start researching a technology
    startResearch(techId) {
        const tech = this.technologies[techId];
        if (!tech) {
            console.error(`Technology ${techId} not found`);
            return false;
        }
        
        // Check if already researched
        if (this.researchedTechnologies.has(techId)) {
            this.game.ui.showNotification('Technology already researched!', 'warning');
            return false;
        }
        
        // Check prerequisites
        if (!this.hasPrerequisites(techId)) {
            this.game.ui.showNotification('Prerequisites not met!', 'warning');
            return false;
        }
        
        // Check if we can afford it
        const adjustedCost = this.getAdjustedCost(tech.cost);
        if (!this.game.resourceManager.canAfford(adjustedCost)) {
            this.game.ui.showNotification('Insufficient resources!', 'warning');
            return false;
        }
        
        // Check if research lab is available
        const researchLab = this.game.teams.player.buildings.find(b => 
            b.type === 'research_lab' && b.constructionProgress >= 100
        );
        
        if (!researchLab) {
            this.game.ui.showNotification('Research Lab required!', 'warning');
            return false;
        }
        
        // Pay the cost
        this.game.resourceManager.spendResources(adjustedCost);
        
        // Start research
        this.currentResearch = techId;
        this.researchProgress = 0;
        
        this.game.ui.showNotification(`Research started: ${tech.name}`, 'info');
        this.game.soundManager.play('select');
        
        return true;
    }
    
    // Update research progress
    update(deltaTime) {
        if (!this.currentResearch) return;
        
        const tech = this.technologies[this.currentResearch];
        if (!tech) return;
        
        // Check if research lab still exists
        const researchLab = this.game.teams.player.buildings.find(b => 
            b.type === 'research_lab' && b.constructionProgress >= 100
        );
        
        if (!researchLab) {
            this.game.ui.showNotification('Research halted - Research Lab destroyed!', 'error');
            this.currentResearch = null;
            this.researchProgress = 0;
            return;
        }
        
        // Calculate research speed
        const adjustedTime = tech.researchTime / this.researchModifiers.speed;
        const progressPerSecond = 100 / adjustedTime;
        
        this.researchProgress += progressPerSecond * deltaTime;
        
        // Check if research is complete
        if (this.researchProgress >= 100) {
            this.completeResearch(this.currentResearch);
        }
    }
    
    // Complete research
    completeResearch(techId) {
        const tech = this.technologies[techId];
        
        this.researchedTechnologies.add(techId);
        this.currentResearch = null;
        this.researchProgress = 0;
        
        // Apply technology effects
        this.applyTechnologyEffects(techId, tech.effects);
        
        // Notifications and effects
        this.game.ui.showNotification(`Research complete: ${tech.name}!`, 'success');
        this.game.soundManager.play('complete');
        
        // Award tech points for completion
        this.game.resourceManager.awardTechPoints(50, 'research completion');
        
        // Start next research in queue if any
        if (this.researchQueue.length > 0) {
            const nextTech = this.researchQueue.shift();
            this.startResearch(nextTech);
        }
    }
    
    // Apply technology effects to the game
    applyTechnologyEffects(techId, effects) {
        // Store effects for easy access
        if (!this.game.technologyEffects) {
            this.game.technologyEffects = {};
        }
        this.game.technologyEffects[techId] = effects;
        
        // Apply immediate effects
        if (effects.armorBonus) {
            this.game.teams.player.upgrades.armor += effects.armorBonus;
        }
        
        if (effects.damageBonus) {
            this.game.teams.player.upgrades.damage += effects.damageBonus;
        }
        
        if (effects.vehicleSpeedBonus) {
            // Apply to all existing vehicles
            this.game.teams.player.units.forEach(unit => {
                if (unit.category === 'vehicle') {
                    unit.speed *= (1 + effects.vehicleSpeedBonus);
                }
            });
        }
        
        if (effects.harvestingBonus) {
            this.game.teams.player.upgrades.harvesting += effects.harvestingBonus;
        }
        
        if (effects.stealthDetection) {
            // Enable stealth detection for all player units
            this.game.teams.player.units.forEach(unit => {
                unit.canDetectStealth = true;
            });
        }
        
        if (effects.personalShields) {
            // Add shields to infantry units
            this.game.teams.player.units.forEach(unit => {
                if (unit.category === 'infantry') {
                    unit.shield = unit.maxHealth * 0.5;
                    unit.maxShield = unit.shield;
                }
            });
        }
        
        if (effects.hoverMovement) {
            // Allow vehicles to move over water
            this.game.teams.player.units.forEach(unit => {
                if (unit.category === 'vehicle') {
                    unit.canHover = true;
                }
            });
        }
        
        if (effects.buildingShields) {
            // Add shields to all buildings
            this.game.teams.player.buildings.forEach(building => {
                if (!building.shield) {
                    building.shield = building.maxHealth * 0.3;
                    building.maxShield = building.shield;
                }
            });
        }
        
        if (effects.autoDefenses) {
            // Enable automated defense systems
            this.game.teams.player.buildings.forEach(building => {
                if (building.type === 'turret') {
                    building.automated = true;
                    building.damage *= (1 + effects.turretDamageBonus);
                }
            });
        }
        
        if (effects.tradeEnabled) {
            // Enable diplomatic trading
            this.game.diplomacyEnabled = true;
        }
        
        if (effects.enhancedVision) {
            // Increase vision range for all units
            this.game.teams.player.units.forEach(unit => {
                unit.visionRadius *= 1.5;
            });
        }
        
        if (effects.formationBonus) {
            // Apply formation combat bonuses
            this.game.teams.player.formationBonusActive = true;
        }
        
        if (effects.environmentalResistance) {
            // Make units resistant to environmental damage
            this.game.teams.player.units.forEach(unit => {
                unit.environmentalResistance = effects.environmentalResistance;
            });
        }
        
        if (effects.selfRepair) {
            // Enable self-repair for all units and buildings
            this.game.teams.player.units.forEach(unit => {
                unit.selfRepair = true;
            });
            this.game.teams.player.buildings.forEach(building => {
                building.selfRepair = true;
            });
        }
        
        if (effects.fusionPower) {
            // Dramatically increase power generation
            this.game.teams.player.buildings.forEach(building => {
                if (building.type === 'powerplant') {
                    building.provides *= 2;
                }
            });
        }
        
        if (effects.psiAbilities) {
            // Enable psychic warfare abilities
            this.game.teams.player.units.forEach(unit => {
                if (['soldier', 'rocketeer'].includes(unit.type)) {
                    unit.psiAbilities = true;
                }
            });
        }
        
        if (effects.terraformingAbility) {
            // Enable terrain modification
            this.game.terraformingEnabled = true;
        }
        
        if (effects.instantCommunication) {
            // Enable global unit coordination
            this.game.teams.player.globalCoordination = true;
        }
        
        if (effects.quantumTech) {
            // Boost research speed globally
            this.researchModifiers.speed *= (1 + effects.researchSpeedBonus);
        }
        
        if (effects.neuralControl) {
            // Enhance unit efficiency
            this.game.teams.player.units.forEach(unit => {
                unit.efficiency = (unit.efficiency || 1.0) * (1 + effects.unitEfficiencyBonus);
            });
        }
    }
    
    // Check if technology prerequisites are met
    hasPrerequisites(techId) {
        const tech = this.technologies[techId];
        if (!tech || !tech.prerequisites) return true;
        
        return tech.prerequisites.every(prereq => this.researchedTechnologies.has(prereq));
    }
    
    // Get adjusted cost based on research modifiers
    getAdjustedCost(baseCost) {
        const adjustedCost = {};
        for (const [resource, cost] of Object.entries(baseCost)) {
            adjustedCost[resource] = Math.floor(cost * this.researchModifiers.cost);
        }
        return adjustedCost;
    }
    
    // Get available technologies for research
    getAvailableTechnologies() {
        return Object.keys(this.technologies).filter(techId => {
            return !this.researchedTechnologies.has(techId) && 
                   this.hasPrerequisites(techId);
        });
    }
    
    // Get technologies by tier
    getTechnologiesByTier(tier) {
        return Object.entries(this.technologies)
            .filter(([id, tech]) => tech.tier === tier)
            .map(([id, tech]) => ({ id, ...tech }));
    }
    
    // Get technologies by category
    getTechnologiesByCategory(category) {
        return Object.entries(this.technologies)
            .filter(([id, tech]) => tech.category === category)
            .map(([id, tech]) => ({ id, ...tech }));
    }
    
    // Get research progress info
    getResearchInfo() {
        return {
            currentResearch: this.currentResearch,
            progress: this.researchProgress,
            researchedCount: this.researchedTechnologies.size,
            totalCount: Object.keys(this.technologies).length,
            availableCount: this.getAvailableTechnologies().length
        };
    }
    
    // Queue research
    queueResearch(techId) {
        if (this.researchQueue.includes(techId)) {
            this.game.ui.showNotification('Technology already queued!', 'warning');
            return false;
        }
        
        if (this.researchQueue.length >= 5) {
            this.game.ui.showNotification('Research queue full!', 'warning');
            return false;
        }
        
        this.researchQueue.push(techId);
        const tech = this.technologies[techId];
        this.game.ui.showNotification(`${tech.name} added to research queue`, 'info');
        return true;
    }
    
    // Cancel current research
    cancelResearch() {
        if (!this.currentResearch) return false;
        
        const tech = this.technologies[this.currentResearch];
        
        // Refund partial resources based on progress
        const refundRate = Math.max(0.5, 1 - (this.researchProgress / 100));
        const adjustedCost = this.getAdjustedCost(tech.cost);
        
        for (const [resource, cost] of Object.entries(adjustedCost)) {
            const refund = Math.floor(cost * refundRate);
            this.game.resourceManager.addResource(resource, refund);
        }
        
        this.game.ui.showNotification(`Research cancelled: ${tech.name}`, 'info');
        
        this.currentResearch = null;
        this.researchProgress = 0;
        
        return true;
    }
    
    // Check if technology is researched
    isResearched(techId) {
        return this.researchedTechnologies.has(techId);
    }
    
    // Get technology effect value
    getTechnologyEffect(effectName) {
        let totalEffect = 0;
        
        for (const techId of this.researchedTechnologies) {
            const tech = this.technologies[techId];
            if (tech.effects[effectName]) {
                totalEffect += tech.effects[effectName];
            }
        }
        
        return totalEffect;
    }
    
    // Special abilities unlocked by research
    canUseOrbitalStrike() {
        return this.isResearched('orbital_support');
    }
    
    hasGlobalVision() {
        return this.isResearched('orbital_support');
    }
    
    canConvertResources() {
        return this.isResearched('matter_conversion');
    }
    
    // Convert resources (if technology allows)
    convertSpiceToResource(targetResource, spiceAmount) {
        if (!this.canConvertResources()) {
            this.game.ui.showNotification('Matter Conversion technology required!', 'warning');
            return false;
        }
        
        if (this.game.resourceManager.getResource('spice') < spiceAmount) {
            this.game.ui.showNotification('Insufficient spice!', 'warning');
            return false;
        }
        
        // Conversion rates (spice to other resources)
        const conversionRates = {
            water: 0.8,     // 1 spice = 0.8 water
            techPoints: 0.3  // 1 spice = 0.3 tech points
        };
        
        if (!conversionRates[targetResource]) {
            this.game.ui.showNotification('Cannot convert to that resource!', 'warning');
            return false;
        }
        
        const convertedAmount = Math.floor(spiceAmount * conversionRates[targetResource]);
        
        this.game.resourceManager.spendResource('spice', spiceAmount);
        this.game.resourceManager.addResource(targetResource, convertedAmount);
        
        this.game.ui.showNotification(
            `Converted ${spiceAmount} spice to ${convertedAmount} ${targetResource}`, 
            'success'
        );
        
        return true;
    }
    
    // Phase 4 Integration Methods
    setBuildSpeedMultiplier(multiplier) {
        this.researchModifiers.speed = multiplier;
    }
    
    // Get research progress for specific technology
    getResearchProgress(techId) {
        if (this.currentResearch === techId) {
            return this.researchProgress;
        }
        return this.isResearched(techId) ? 100 : 0;
    }
    
    // Check if can research specific technology
    canResearch(techId) {
        const tech = this.technologies[techId];
        if (!tech) return false;
        
        if (this.researchedTechnologies.has(techId)) return false;
        if (!this.hasPrerequisites(techId)) return false;
        
        const adjustedCost = this.getAdjustedCost(tech.cost);
        return this.game.resourceManager.canAfford(adjustedCost);
    }
    
    // Export state for save/load
    exportState() {
        return {
            researchedTechnologies: Array.from(this.researchedTechnologies),
            currentResearch: this.currentResearch,
            researchProgress: this.researchProgress,
            researchQueue: [...this.researchQueue],
            researchModifiers: { ...this.researchModifiers }
        };
    }
    
    // Import state for save/load
    importState(state) {
        this.researchedTechnologies = new Set(state.researchedTechnologies || []);
        this.currentResearch = state.currentResearch;
        this.researchProgress = state.researchProgress || 0;
        this.researchQueue = state.researchQueue || [];
        this.researchModifiers = { ...this.researchModifiers, ...state.researchModifiers };
        
        // Reapply all researched technology effects
        this.researchedTechnologies.forEach(techId => {
            const tech = this.technologies[techId];
            if (tech) {
                this.applyTechnologyEffects(techId, tech.effects);
            }
        });
    }
}