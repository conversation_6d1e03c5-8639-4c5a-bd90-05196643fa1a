// js/debug/TestScenarios.js

/**
 * Defines and sets up a test battle scene by spawning units for multiple factions.
 * This function is intended for debugging and testing combat scenarios.
 * 
 * @param {object} game - The main game instance. Must have a `createUnit(type, x, y, team)` method
 *                        and potentially `gameData` for unit type validation.
 * @param {object} [options={}] - Optional parameters to customize the battle scene.
 * @param {boolean} [options.logDetails=true] - Whether to log detailed information.
 */
export function setupTestBattleScene(game, options = {}) {
    const { logDetails = true } = options;

    if (!game || typeof game.createUnit !== 'function') {
        return;
    }

    // Configuration for the test battle.
    // Positions are chosen to place factions relatively close for engagement.
    // Assumes (0,0) is top-left.
    const battleConfig = {
        factions: {
            'player': {
                name: 'Player Alliance',
                units: [
                    { type: 'soldier', count: 3, startX: 1000, startY: 1000, spacingX: 50, spacingY: 0 },
                    { type: 'tank',    count: 2, startX: 1000, startY: 1080, spacingX: 60, spacingY: 0 },
                ],
            },
            'enemy': {
                name: 'Crimson Axis',
                units: [
                    { type: 'soldier', count: 3, startX: 1300, startY: 1000, spacingX: 50, spacingY: 0 },
                    { type: 'tank',    count: 2, startX: 1300, startY: 1080, spacingX: 60, spacingY: 0 },
                ],
            },
            // Example: Add a third, smaller faction if desired for more complex scenarios
            /*
            'neutral_aggressive': {
                name: 'Marauders',
                units: [
                    { type: 'soldier', count: 2, startX: 1150, startY: 1200, spacingX: 50, spacingY: 0 },
                ]
            }
            */
        },
        // It's assumed that gameState.config.loadInitialUnits is set to false by the caller
        // to prevent default unit spawns from interfering with this test setup.
    };

    let unitsSpawnedCount = 0;

    for (const teamId in battleConfig.factions) {
        const factionDetails = battleConfig.factions[teamId];

        factionDetails.units.forEach(unitGroup => {
            // Basic validation for unit type existence if gameData is available
            // This assumes game.gameData.units is a map/object where keys are unit types.
            if (game.gameData && game.gameData.units && !game.gameData.units[unitGroup.type]) {
                return; // Skip this unit group
            }

            for (let i = 0; i < unitGroup.count; i++) {
                const x = unitGroup.startX + (i * unitGroup.spacingX);
                const y = unitGroup.startY + (i * unitGroup.spacingY); // Allows for vertical spacing too if needed

                try {
                    game.createUnit(unitGroup.type, x, y, teamId);
                    unitsSpawnedCount++;
                } catch (error) {
                    // Error spawning unit - continue silently
                }
            }
        });
    }

}