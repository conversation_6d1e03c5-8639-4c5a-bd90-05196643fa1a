export class UnitMovementManager {
    constructor(game) {
        this.game = game;
        this.movingUnits = new Map(); // Track all moving units
        this.unitCollisions = new Map(); // Track unit collision states
        this.formationGroups = new Map(); // Track formation movement groups
        this.rerouteQueue = []; // Queue for units needing rerouting
        this.movementGrid = new Map(); // Spatial grid for collision detection
        this.gridSize = 25; // Same as pathfinder grid size
        
        // Movement parameters
        this.collisionRadius = 8; // Reduced collision radius to prevent excessive spacing
        this.separationForce = 20; // Reduced separation force to prevent bouncing
        this.cohesionRadius = 40; // Tighter formation cohesion
        this.maxRerouteAttempts = 3; // Maximum reroute attempts per unit
        
        // Performance settings
        this.updateInterval = 16; // Update every 16ms (60fps)
        this.lastUpdate = 0;
        this.maxUnitsPerFrame = 20; // Limit collision checks per frame
        
        // Traffic management
        this.congestionThreshold = 3; // Units per grid cell before congestion
        this.congestionPenalty = 2.0; // Speed penalty for congested areas
        
        this.init();
    }
    
    init() {
        // Initialize spatial grid
        this.updateSpatialGrid();
    }
    
    update(deltaTime) {
        const now = performance.now();
        if (now - this.lastUpdate < this.updateInterval) return;
        this.lastUpdate = now;
        
        // Update spatial grid for collision detection
        this.updateSpatialGrid();
        
        // Process movement for all units
        this.processUnitMovement(deltaTime);
        
        // Handle collision detection and resolution
        this.handleCollisions(deltaTime);
        
        // Process formation movement
        this.updateFormationMovement(deltaTime);
        
        // Handle rerouting for stuck units
        this.processRerouteQueue();
        
        // Clean up completed movements
        this.cleanupCompletedMovements();
    }
    
    updateSpatialGrid() {
        this.movementGrid.clear();
        
        // Add all units to spatial grid
        this.game.gameState.entities.units.forEach(unit => {
            if (unit.health <= 0) return;
            
            const gridX = Math.floor(unit.x / this.gridSize);
            const gridY = Math.floor(unit.y / this.gridSize);
            const key = `${gridX},${gridY}`;
            
            if (!this.movementGrid.has(key)) {
                this.movementGrid.set(key, []);
            }
            this.movementGrid.get(key).push(unit);
        });
    }
    
    processUnitMovement(deltaTime) {
        let processedUnits = 0;
        
        for (const unit of this.game.gameState.entities.units) {
            if (unit.health <= 0 || processedUnits >= this.maxUnitsPerFrame) break;
            
            // Check both state formats for compatibility
            const isMoving = unit.state === 'moving' || unit.isMoving;
            const hasPath = unit.path && unit.path.length > 0;
            
            if (isMoving && hasPath) {
                this.processIndividualUnitMovement(unit, deltaTime);
                processedUnits++;
            }
        }
    }
    
    processIndividualUnitMovement(unit, deltaTime) {
        if (!unit.path || unit.pathIndex >= unit.path.length) {
            this.completeUnitMovement(unit);
            return;
        }
        
        const waypoint = unit.path[unit.pathIndex];
        const dx = waypoint.x - unit.x;
        const dy = waypoint.y - unit.y;
        const distance = Math.hypot(dx, dy);
        
        // Calculate movement speed with modifiers
        let effectiveSpeed = this.calculateEffectiveSpeed(unit);
        
        // Apply traffic congestion penalty
        const congestionPenalty = this.getTrafficCongestionPenalty(unit);
        effectiveSpeed *= congestionPenalty;
        
        const stepSize = effectiveSpeed * deltaTime;
        
        // Check for collision before moving
        const newX = unit.x + (dx / distance) * Math.min(stepSize, distance);
        const newY = unit.y + (dy / distance) * Math.min(stepSize, distance);
        
        if (this.canMoveToPosition(unit, newX, newY)) {
            // Update rotation
            unit.rotation = Math.atan2(dy, dx);
            
            // Move unit
            if (distance > stepSize * 0.5) {
                unit.x = newX;
                unit.y = newY;
            } else {
                // Reached waypoint
                unit.x = waypoint.x;
                unit.y = waypoint.y;
                unit.pathIndex++;
                
                if (unit.pathIndex >= unit.path.length) {
                    this.completeUnitMovement(unit);
                }
            }
        } else {
            // Collision detected, handle it
            this.handleMovementCollision(unit, newX, newY);
        }
    }
    
    calculateEffectiveSpeed(unit) {
        let speed = this.game.lootSystem.getEffectiveStat(unit, 'speed', unit.speed);
        
        // Apply environmental modifiers
        const envModifiers = this.game.environmentalSystem.getEnvironmentalModifiers();
        speed *= envModifiers.movement;
        
        // Apply terrain modifiers
        const terrainModifier = this.getTerrainSpeedModifier(unit);
        speed *= terrainModifier;
        
        return speed;
    }
    
    getTerrainSpeedModifier(unit) {
        const gridX = Math.floor(unit.x / this.gridSize);
        const gridY = Math.floor(unit.y / this.gridSize);
        
        if (gridX < 0 || gridX >= this.game.pathfinder.width || 
            gridY < 0 || gridY >= this.game.pathfinder.height) {
            return 1.0;
        }
        
        const cell = this.game.pathfinder.grid[gridY][gridX];
        const terrainType = cell.terrainType || 'desert';
        
        // Get movement type for unit
        const movementType = this.getUnitMovementType(unit);
        
        switch (terrainType) {
            case 'desert': return 1.0;
            case 'spice': return 1.1; // Slightly faster on spice
            case 'water': return movementType.canCrossWater ? 0.6 : 0.1;
            case 'rock': return 0.7;
            case 'cliff': return movementType.canCrossCliffs ? 0.5 : 0.1;
            default: return 1.0;
        }
    }
    
    getUnitMovementType(unit) {
        if (unit.canFly || unit.movementType === 'air') {
            return { canCrossWater: true, canCrossCliffs: true };
        }
        if (unit.amphibious || unit.movementType === 'amphibious') {
            return { canCrossWater: true, canCrossCliffs: false };
        }
        if (unit.hover || unit.movementType === 'hover') {
            return { canCrossWater: true, canCrossCliffs: false };
        }
        return { canCrossWater: false, canCrossCliffs: false };
    }
    
    getTrafficCongestionPenalty(unit) {
        const gridX = Math.floor(unit.x / this.gridSize);
        const gridY = Math.floor(unit.y / this.gridSize);
        const key = `${gridX},${gridY}`;
        
        const unitsInCell = this.movementGrid.get(key) || [];
        if (unitsInCell.length <= this.congestionThreshold) {
            return 1.0;
        }
        
        // Apply congestion penalty
        const congestionLevel = Math.min(unitsInCell.length / this.congestionThreshold, 3.0);
        return 1.0 / (1.0 + (congestionLevel - 1.0) * 0.5);
    }
    
    canMoveToPosition(unit, newX, newY) {
        // Check bounds
        if (newX < 0 || newX >= this.game.gameState.world.width || newY < 0 || newY >= this.game.gameState.world.height) {
            return false;
        }
        
        // Check for unit collisions
        const nearbyUnits = this.getNearbyUnits(newX, newY);
        for (const otherUnit of nearbyUnits) {
            if (otherUnit.id === unit.id) continue;
            
            const distance = Math.hypot(newX - otherUnit.x, newY - otherUnit.y);
            const minDistance = this.getCollisionRadius(unit) + this.getCollisionRadius(otherUnit);
            
            if (distance < minDistance) {
                return false;
            }
        }
        
        // Check for building collisions
        for (const building of this.game.gameState.entities.buildings) {
            if (building.health <= 0 || building.constructionProgress < 100) continue;
            
            const dx = Math.abs(newX - building.x);
            const dy = Math.abs(newY - building.y);
            
            if (dx < building.width / 2 + this.getCollisionRadius(unit) &&
                dy < building.height / 2 + this.getCollisionRadius(unit)) {
                return false;
            }
        }
        
        return true;
    }
    
    getNearbyUnits(x, y) {
        const nearbyUnits = [];
        const gridX = Math.floor(x / this.gridSize);
        const gridY = Math.floor(y / this.gridSize);
        
        // Check surrounding grid cells
        for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
                const key = `${gridX + dx},${gridY + dy}`;
                const unitsInCell = this.movementGrid.get(key) || [];
                nearbyUnits.push(...unitsInCell);
            }
        }
        
        return nearbyUnits;
    }
    
    getCollisionRadius(unit) {
        return (unit.size || 10) + this.collisionRadius;
    }
    
    handleMovementCollision(unit, targetX, targetY) {
        // Try to find alternative path around collision
        const alternativePath = this.findCollisionAvoidancePath(unit, targetX, targetY);
        
        if (alternativePath) {
            unit.path = alternativePath;
            unit.pathIndex = 0;
        } else {
            // Add to reroute queue if no immediate solution
            this.addToRerouteQueue(unit);
        }
    }
    
    findCollisionAvoidancePath(unit, targetX, targetY) {
        // Try small detours around the collision
        const detourAngles = [Math.PI/4, -Math.PI/4, Math.PI/2, -Math.PI/2];
        const detourDistance = this.getCollisionRadius(unit) * 2;
        
        for (const angle of detourAngles) {
            const detourX = unit.x + Math.cos(angle) * detourDistance;
            const detourY = unit.y + Math.sin(angle) * detourDistance;
            
            if (this.canMoveToPosition(unit, detourX, detourY)) {
                // Create a short detour path
                return [
                    { x: detourX, y: detourY },
                    { x: targetX, y: targetY }
                ];
            }
        }
        
        return null;
    }
    
    addToRerouteQueue(unit) {
        if (!this.rerouteQueue.find(item => item.unit.id === unit.id)) {
            this.rerouteQueue.push({
                unit,
                attempts: 0,
                lastAttempt: performance.now()
            });
        }
    }
    
    processRerouteQueue() {
        const now = performance.now();
        
        for (let i = this.rerouteQueue.length - 1; i >= 0; i--) {
            const item = this.rerouteQueue[i];
            
            // Wait 500ms between reroute attempts
            if (now - item.lastAttempt < 500) continue;
            
            item.attempts++;
            item.lastAttempt = now;
            
            if (item.attempts > this.maxRerouteAttempts) {
                // Give up and stop the unit
                item.unit.state = 'idle';
                item.unit.path = [];
                this.rerouteQueue.splice(i, 1);
                continue;
            }
            
            // Try to find a new path
            const newPath = this.game.pathfinder.findPath(
                item.unit.x, item.unit.y,
                item.unit.targetX, item.unit.targetY,
                item.unit,
                { ignoreCache: true, priority: 2 }
            );
            
            if (newPath && newPath.length > 1) {
                item.unit.path = newPath;
                item.unit.pathIndex = 0;
                this.rerouteQueue.splice(i, 1);
            }
        }
    }
    
    handleCollisions(deltaTime) {
        // Handle unit-to-unit collisions with separation forces
        const processedPairs = new Set();
        
        for (const [key, unitsInCell] of this.movementGrid.entries()) {
            if (unitsInCell.length < 2) continue;
            
            for (let i = 0; i < unitsInCell.length; i++) {
                for (let j = i + 1; j < unitsInCell.length; j++) {
                    const unit1 = unitsInCell[i];
                    const unit2 = unitsInCell[j];
                    
                    const pairKey = `${Math.min(unit1.id, unit2.id)}-${Math.max(unit1.id, unit2.id)}`;
                    if (processedPairs.has(pairKey)) continue;
                    processedPairs.add(pairKey);
                    
                    this.resolveUnitCollision(unit1, unit2, deltaTime);
                }
            }
        }
    }
    
    resolveUnitCollision(unit1, unit2, deltaTime) {
        const dx = unit2.x - unit1.x;
        const dy = unit2.y - unit1.y;
        const distance = Math.hypot(dx, dy);
        
        const minDistance = this.getCollisionRadius(unit1) + this.getCollisionRadius(unit2);
        
        if (distance < minDistance && distance > 0.1) { // Avoid division by very small numbers
            // Calculate gentle separation force
            const overlap = Math.min(minDistance - distance, 10); // Cap overlap to prevent excessive force
            const separationX = (dx / distance) * overlap * 0.3; // Reduced multiplier
            const separationY = (dy / distance) * overlap * 0.3;
            
            // Apply gentle separation with reduced force
            const force = Math.min(this.separationForce * deltaTime, 2); // Cap maximum force
            
            // Only apply separation if units are actually overlapping significantly
            if (overlap > 2) {
                // Only move units that are not in critical states
                if ((unit1.state === 'moving' || unit1.state === 'idle' || unit1.isMoving) && !unit1.isAttacking) {
                    unit1.x -= separationX * force * 0.5;
                    unit1.y -= separationY * force * 0.5;
                }
                
                if ((unit2.state === 'moving' || unit2.state === 'idle' || unit2.isMoving) && !unit2.isAttacking) {
                    unit2.x += separationX * force * 0.5;
                    unit2.y += separationY * force * 0.5;
                }
                
                // Ensure units stay within bounds
                unit1.x = Math.max(0, Math.min(this.game.gameState.world.width, unit1.x));
                unit1.y = Math.max(0, Math.min(this.game.gameState.world.height, unit1.y));
                unit2.x = Math.max(0, Math.min(this.game.gameState.world.width, unit2.x));
                unit2.y = Math.max(0, Math.min(this.game.gameState.world.height, unit2.y));
            }
        }
    }
    
    // Formation movement system
    createFormationGroup(units, leadUnit, formationType = 'box') {
        const groupId = `formation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const formation = {
            id: groupId,
            units: [...units],
            leadUnit,
            formationType,
            positions: new Map(),
            lastUpdate: performance.now(),
            cohesionRadius: this.cohesionRadius
        };
        
        // Calculate formation positions
        this.calculateFormationPositions(formation);
        
        // Assign formation to units
        units.forEach(unit => {
            unit.formationId = groupId;
            unit.formationPosition = formation.positions.get(unit.id);
        });
        
        this.formationGroups.set(groupId, formation);
        return groupId;
    }
    
    calculateFormationPositions(formation) {
        const { units, leadUnit, formationType } = formation;
        const spacing = 40;
        
        formation.positions.clear();
        
        switch (formationType) {
            case 'line':
                units.forEach((unit, index) => {
                    const offset = (index - (units.length - 1) / 2) * spacing;
                    formation.positions.set(unit.id, {
                        offsetX: offset,
                        offsetY: 0,
                        priority: index === 0 ? 1 : 2
                    });
                });
                break;
                
            case 'column':
                units.forEach((unit, index) => {
                    const offset = index * spacing;
                    formation.positions.set(unit.id, {
                        offsetX: 0,
                        offsetY: offset,
                        priority: index === 0 ? 1 : 2
                    });
                });
                break;
                
            case 'wedge':
                units.forEach((unit, index) => {
                    const row = Math.floor(Math.sqrt(index));
                    const col = index - row * row;
                    const offsetX = (col - row / 2) * spacing;
                    const offsetY = row * spacing;
                    formation.positions.set(unit.id, {
                        offsetX,
                        offsetY,
                        priority: index === 0 ? 1 : 2
                    });
                });
                break;
                
            default: // box formation
                const cols = Math.ceil(Math.sqrt(units.length));
                units.forEach((unit, index) => {
                    const row = Math.floor(index / cols);
                    const col = index % cols;
                    const offsetX = (col - (cols - 1) / 2) * spacing;
                    const offsetY = (row - Math.floor(units.length / cols) / 2) * spacing;
                    formation.positions.set(unit.id, {
                        offsetX,
                        offsetY,
                        priority: index === 0 ? 1 : 2
                    });
                });
        }
    }
    
    updateFormationMovement(deltaTime) {
        for (const [groupId, formation] of this.formationGroups.entries()) {
            // Check if formation is still valid
            formation.units = formation.units.filter(unit => 
                unit.health > 0 && this.game.gameState.entities.units.includes(unit));
            
            if (formation.units.length === 0) {
                this.formationGroups.delete(groupId);
                continue;
            }
            
            // Update lead unit if it's dead
            if (!formation.units.includes(formation.leadUnit)) {
                formation.leadUnit = formation.units[0];
            }
            
            // Apply formation cohesion forces
            this.applyFormationCohesion(formation, deltaTime);
        }
    }
    
    applyFormationCohesion(formation, deltaTime) {
        const { leadUnit, units, positions } = formation;
        const cohesionForce = 30 * deltaTime;
        
        units.forEach(unit => {
            if (unit.id === leadUnit.id) return;
            
            const formationPos = positions.get(unit.id);
            if (!formationPos) return;
            
            // Calculate desired position relative to lead unit
            const desiredX = leadUnit.x + formationPos.offsetX;
            const desiredY = leadUnit.y + formationPos.offsetY;
            
            // Calculate cohesion force
            const dx = desiredX - unit.x;
            const dy = desiredY - unit.y;
            const distance = Math.hypot(dx, dy);
            
            if (distance > formation.cohesionRadius) {
                // Apply gentle force towards formation position
                const forceX = (dx / distance) * cohesionForce;
                const forceY = (dy / distance) * cohesionForce;
                
                if (unit.state === 'moving' || unit.state === 'idle') {
                    unit.x += forceX;
                    unit.y += forceY;
                    
                    // Ensure unit stays within bounds
                    unit.x = Math.max(0, Math.min(this.game.gameState.world.width, unit.x));
                    unit.y = Math.max(0, Math.min(this.game.gameState.world.height, unit.y));
                }
            }
        });
    }
    
    moveFormation(groupId, targetX, targetY) {
        const formation = this.formationGroups.get(groupId);
        if (!formation) return false;
        
        // Calculate formation paths
        const formationPaths = this.game.pathfinder.findGroupPaths(
            formation.units, targetX, targetY, formation.formationType);
        
        // Assign paths to units
        formationPaths.forEach(({ unit, path }) => {
            unit.path = path;
            unit.pathIndex = 0;
            unit.targetX = targetX;
            unit.targetY = targetY;
            unit.state = 'moving';
        });
        
        return true;
    }
    
    disbandFormation(groupId) {
        const formation = this.formationGroups.get(groupId);
        if (!formation) return false;
        
        // Clear formation data from units
        formation.units.forEach(unit => {
            unit.formationId = null;
            unit.formationPosition = null;
        });
        
        this.formationGroups.delete(groupId);
        return true;
    }
    
    completeUnitMovement(unit) {
        unit.state = 'idle';
        unit.path = [];
        unit.pathIndex = 0;
        
        // Clear any pathfinder reservations
        if (this.game.pathfinder.clearUnitReservations) {
            this.game.pathfinder.clearUnitReservations(unit);
        }
        
        // Remove from moving units tracking
        this.movingUnits.delete(unit.id);
    }
    
    cleanupCompletedMovements() {
        // Remove completed movements from tracking
        for (const [unitId, unit] of this.movingUnits.entries()) {
            if (unit.health <= 0 || unit.state !== 'moving') {
                this.movingUnits.delete(unitId);
            }
        }
        
        // Clean up dead units from reroute queue
        this.rerouteQueue = this.rerouteQueue.filter(item => 
            item.unit.health > 0 && this.game.gameState.entities.units.includes(item.unit));
    }
    
    // Utility methods
    getMovementStats() {
        return {
            movingUnits: this.movingUnits.size,
            formationGroups: this.formationGroups.size,
            rerouteQueue: this.rerouteQueue.length,
            gridCells: this.movementGrid.size
        };
    }
    
    // Emergency stop for all units
    emergencyStop() {
        this.game.gameState.entities.units.forEach(unit => {
            if (unit.state === 'moving') {
                this.completeUnitMovement(unit);
            }
        });
        
        this.rerouteQueue = [];
        this.movingUnits.clear();
    }
}