import { AIController } from './AIController.js';
import { AdvancedPathfinder } from './AdvancedPathfinder.js';
import { AnimationManager } from './AnimationManager.js';
import { Building } from './Building.js';
import { CombatSystem } from './CombatSystem.js';
import { DiplomacySystem } from './DiplomacySystem.js';
import { DynamicMapSystem } from './DynamicMapSystem.js';
import { EnvironmentalSystem } from './EnvironmentalSystem.js';
import { FlowFieldPathfinder } from './FlowFieldPathfinder.js';
import { GameBalanceManager } from './GameBalanceManager.js';
import { GameData } from './GameData.js';
import { InputHandler } from './InputHandler.js';
import { LevelingSystem } from './LevelingSystem.js';
import { LootSystem } from './LootSystem.js';
import NeutralEnemies from './NeutralEnemies.js';
import { Pathfinder } from './Pathfinder.js';
// Phase 4 Polish & Integration Systems
import { PerformanceManager } from './PerformanceManager.js';
import { Projectile } from './Projectile.js';
import { ResourceManager } from './ResourceManager.js';
import { SaveLoadManager } from './SaveLoadManager.js';
import { SoundManager } from './SoundManager.js';
import { TechnologySystem } from './TechnologySystem.js';
import { UI } from './UI.js';
import { Unit } from './Unit.js';
import { UnitMovementManager } from './UnitMovementManager.js';
import { UserExperienceManager } from './UserExperienceManager.js';
import { VictorySystem } from './VictorySystem.js';
import { VisualEffects } from './VisualEffects.js';

export class Game {
    constructor() {
        this.gameCanvas = document.getElementById('gameCanvas');
        this.ctx = this.gameCanvas.getContext('2d');
        this.gameAreaEl = document.getElementById('gameArea');

        this.minimapCanvas = document.getElementById('minimapCanvas');
        this.minimapCtx = this.minimapCanvas.getContext('2d');

        this.width = 3000; this.height = 3000;
        this.viewX = 0; this.viewY = 0; this.zoom = 1;
        
        // Legacy resources object for backward compatibility
        this.resources = { spice: 3000, power: 0, maxPower: 0 };
        this.buildings = []; this.units = []; this.projectiles = [];
        this.spiceFields = []; this.terrain = [];
        this.selectedUnits = []; this.selectedBuilding = null;
        this.buildingMode = null; this.ghostBuilding = null;
        
        this.teams = {
            player: { color: '#00f', buildings: [], units: [], upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 }, stats: { unitsBuilt: 0, unitsLost: 0, buildingsBuilt: 0, buildingsLost: 0, spiceCollected: 0, enemiesDestroyed: 0 }},
            enemy: { color: '#f00', buildings: [], units: [], upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 }}
        };
        
        this.buildingTypes = GameData.getBuildingTypes();
        this.unitTypes = GameData.getUnitTypes();

        this.paused = false; this.gameTime = 0; this.lastTimestamp = 0;
        this.lastSpiceRegenTime = 0;
        
        this.soundManager = new SoundManager();
        
        // Music system state tracking
        this.musicState = {
            currentState: 'peaceful',
            combatUnits: 0,
            buildingActivity: 0,
            lastCombatTime: 0,
            lastBuildTime: 0
        };
        // Initialize pathfinding systems
        this.pathfinder = new Pathfinder(this); // Keep legacy for compatibility
        this.advancedPathfinder = new AdvancedPathfinder(this);
        this.flowFieldPathfinder = new FlowFieldPathfinder(this);
        this.unitMovementManager = new UnitMovementManager(this);
        
        // Pathfinding mode selection
        this.pathfindingMode = 'advanced'; // 'legacy', 'advanced', 'flowfield'
        this.useGroupPathfinding = true; // Enable group pathfinding for formations
        
        this.ui = new UI(this);
        this.inputHandler = new InputHandler(this);
        this.aiController = new AIController(this, 'enemy');
        
        // Initialize new systems
        this.levelingSystem = new LevelingSystem();
        this.lootSystem = new LootSystem(this);
        this.neutralEnemies = new NeutralEnemies(this);
        this.visualEffects = new VisualEffects(this);
        
        // Initialize Phase 2 Advanced Mechanics systems
        this.resourceManager = new ResourceManager(this);
        this.combatSystem = new CombatSystem(this);
        this.environmentalSystem = new EnvironmentalSystem(this);
        this.technologySystem = new TechnologySystem(this);
        
        // Initialize Phase 3 Strategic Depth systems
        this.diplomacySystem = new DiplomacySystem(this);
        this.dynamicMapSystem = new DynamicMapSystem(this);
        this.victorySystem = new VictorySystem(this);
        
        // Initialize Phase 4 Polish & Integration systems
        this.performanceManager = new PerformanceManager(this);
        this.balanceManager = new GameBalanceManager(this);
        this.userExperienceManager = new UserExperienceManager(this);
        this.saveLoadManager = new SaveLoadManager(this);
        this.animationManager = new AnimationManager(this);
        
        // Technology effects storage
        this.technologyEffects = {};
        
        // Strategic features enabled flags
        this.diplomacyEnabled = false;
        this.terraformingEnabled = false;
        
        this.fogOfWarData = []; this.fogGridSize = 50; this.visionRadius = 250;
        this.currentBuildTab = 'buildings';
        this.productionQueues = new Map();
        this.controlGroups = {};
        
        this.effectPool = { explosion: [], hit: [], moveMarker: [], rallyMarker: [], repair: [], harvest: [] };
        this.maxEffectsPerType = { explosion: 10, hit: 20, moveMarker: 5, rallyMarker:1, repair:10, harvest:15 };
        this.minimapUnitElements = new Map(); this.minimapBuildingElements = new Map(); this.minimapSpiceElements = new Map();

        this.init();
    }
    
    init() {
        // Set canvas internal dimensions to match CSS dimensions for proper coordinate mapping
        const canvasRect = this.gameCanvas.getBoundingClientRect();
        this.gameCanvas.width = canvasRect.width;
        this.gameCanvas.height = canvasRect.height;
        this.minimapCanvas.width = this.minimapCanvas.offsetWidth;
        this.minimapCanvas.height = this.minimapCanvas.offsetHeight;

        this.createEffectPools();
        this.generateMap(); 
        this.createInitialBase();
        this.initFogOfWarVisuals(); 
        this.updateFogOfWar();
        this.ui.updateResourceDisplay();
        this.ui.updateBuildPanel();
        
        // Initialize all pathfinding systems
        this.pathfinder.updateGridObstacles();
        this.advancedPathfinder.updateGridObstacles();
        this.flowFieldPathfinder.updateCostField();
        
        this.ui.drawMinimapStatic();
        this.updateViewport();

        this.gameLoop(0);
    }

    createEffectPools() { /* ... (copied from previous optimized game class) ... */ Object.keys(this.effectPool).forEach(type => { for (let i = 0; i < (this.maxEffectsPerType[type] || 10); i++) { let element; if (type === 'explosion' || type === 'hit' || type === 'harvest') { /* No direct SVG element for these, they will be drawn on canvas */ } else if (type === 'moveMarker') { /* No direct SVG element */ } else if (type === 'rallyMarker') { /* No direct SVG element */ } else if (type === 'repair') { /* No direct SVG element */ } if (element) { /* No element to append */ this.effectPool[type].push({ element, inUse: false }); } } }); }
    getEffectFromPool(type) { /* ... (copied from previous optimized game class) ... */ const pool = this.effectPool[type]; if (!pool) return null; const effect = pool.find(e => !e.inUse); if (effect) { effect.inUse = true; /* No element to display */ return effect; } return null;  }
    returnEffectToPool(effectInstance, duration = 500) { /* ... (copied from previous optimized game class) ... */ if(!effectInstance) return; setTimeout(() => { /* No element to hide */ effectInstance.inUse = false; }, duration); }
    generateMap() {
        // Use dynamic map system for procedural generation
        this.dynamicMapSystem.generateProceduralTerrain();
        
        // Initialize terrain grid for pathfinding
        this.terrainGrid = [];
        const gridSize = 100;
        const gridWidth = Math.ceil(this.width / gridSize);
        const gridHeight = Math.ceil(this.height / gridSize);
        
        for (let y = 0; y < gridHeight; y++) {
            this.terrainGrid[y] = [];
            for (let x = 0; x < gridWidth; x++) {
                this.terrainGrid[y][x] = 'desert';
            }
        }
        
        // Update terrain grid based on generated terrain
        this.terrain.forEach(obstacle => {
            const gridX = Math.floor(obstacle.x / gridSize);
            const gridY = Math.floor(obstacle.y / gridSize);
            const radius = Math.ceil(obstacle.radius / gridSize);
            
            for (let dy = -radius; dy <= radius; dy++) {
                for (let dx = -radius; dx <= radius; dx++) {
                    const gx = gridX + dx;
                    const gy = gridY + dy;
                    if (gx >= 0 && gx < gridWidth && gy >= 0 && gy < gridHeight) {
                        if (dx*dx + dy*dy <= radius*radius) {
                            this.terrainGrid[gy][gx] = obstacle.type;
                        }
                    }
                }
            }
        });
    }
    initFogOfWarVisuals() { /* ... (copied from previous optimized game class) ... */ this.fogOfWarData = []; /* Fog of War will be drawn directly on canvas */ const numCols = Math.ceil(this.width / this.fogGridSize); const numRows = Math.ceil(this.height / this.fogGridSize); for (let y = 0; y < numRows; y++) { this.fogOfWarData[y] = []; for (let x = 0; x < numCols; x++) { this.fogOfWarData[y][x] = { explored: false, visible: false, currentOpacity: 0.8  }; } } }
    updateFogOfWar() { 
        const numFogCols = this.fogOfWarData[0] ? this.fogOfWarData[0].length : 0; 
        const numFogRows = this.fogOfWarData.length; 
        if (numFogCols === 0 || numFogRows === 0) return; 
        for (let y = 0; y < numFogRows; y++) { 
            for (let x = 0; x < numFogCols; x++) { // Changed numCols to numFogCols
                this.fogOfWarData[y][x].visibleThisFrame = false; 
            } 
        } 
        [...this.teams.player.units, ...this.teams.player.buildings].forEach(entity => { 
            const visionRange = (entity.type === 'radar' && entity.constructionProgress >= 100) ? this.visionRadius + (this.buildingTypes.radar.visionBonus || 0) : (entity.visionRadius || this.visionRadius); 
            const entityFogX = Math.floor(entity.x / this.fogGridSize); 
            const entityFogY = Math.floor(entity.y / this.fogGridSize); 
            const rangeInFogCells = Math.ceil(visionRange / this.fogGridSize); 
            for (let yOff = -rangeInFogCells; yOff <= rangeInFogCells; yOff++) { 
                for (let xOff = -rangeInFogCells; xOff <= rangeInFogCells; xOff++) { 
                    const fogX = entityFogX + xOff; 
                    const fogY = entityFogY + yOff; 
                    if (fogX >= 0 && fogX < numFogCols && fogY >= 0 && fogY < numFogRows) { 
                        const cell = this.fogOfWarData[fogY][fogX]; 
                        if (cell.visibleThisFrame) continue; 
                        const cellCenterX = fogX * this.fogGridSize + this.fogGridSize / 2; 
                        const cellCenterY = fogY * this.fogGridSize + this.fogGridSize / 2; 
                        const distSq = (entity.x - cellCenterX)**2 + (entity.y - cellCenterY)**2; 
                        if (distSq <= visionRange**2) { 
                            cell.visibleThisFrame = true; 
                            cell.explored = true; 
                        } 
                    } 
                } 
            } 
        }); 
        for (let y = 0; y < numFogRows; y++) { 
            for (let x = 0; x < numFogCols; x++) { // Changed numCols to numFogCols
                const cell = this.fogOfWarData[y][x]; 
                let newOpacity; 
                if (cell.visibleThisFrame) newOpacity = 0; 
                else if (cell.explored) newOpacity = 0.5; 
                else newOpacity = 0.8; 
                if (cell.currentOpacity !== newOpacity) { 
                    /* No element to set opacity on */ 
                    cell.currentOpacity = newOpacity; 
                } 
                cell.visible = cell.visibleThisFrame; 
            } 
        } 
    }

    createInitialBase() {
        this.addBuilding({ type: 'base', x: 400, y: 400, team: 'player', instant: true });
        this.createUnit('harvester', 500, 400, 'player');
        this.createUnit('soldier', 350, 350, 'player');
        this.createUnit('soldier', 350, 450, 'player');
        this.createUnit('soldier', 300, 400, 'player');
        
        this.addBuilding({ type: 'base', x: 2600, y: 2600, team: 'enemy', instant: true });
        this.addBuilding({ type: 'powerplant', x: 2500, y: 2600, team: 'enemy', instant: true });
        this.createUnit('soldier', 2550, 2550, 'enemy');
    }
    
    addBuilding(config) {
        const building = new Building(this, config);
        this.buildings.push(building);
        this.teams[building.team].buildings.push(building);
        if (building.team === 'player' && !config.instant) {
            this.teams.player.stats.buildingsBuilt++;
            // Award XP for building
            this.levelingSystem.awardActionXP(building, 'build');
        }
        this.updatePower();
        if (building.constructionProgress >= 100) {
            // Update all pathfinding systems when buildings are completed
            this.pathfinder.updateGridObstacles();
            this.advancedPathfinder.updateGridObstacles();
            this.flowFieldPathfinder.updateCostField();
        }
        if (building.constructionProgress === 100 && !config.instant && building.team === 'player') {
            this.soundManager.play('complete');
            // Update music for building completion
            this.updateMusicForBuilding();
        }
    }

    createUnit(type, x, y, team) {
        const unit = new Unit(this, { type, x, y, team });
        
        // Initialize leveling system for the unit
        this.levelingSystem.initializeEntityLevel(unit);
        
        this.units.push(unit);
        
        // Only add to teams array if it's not a neutral unit
        if (team !== 'neutral' && this.teams[team]) {
            this.teams[team].units.push(unit);
        }
        
        if (team === 'player') this.teams.player.stats.unitsBuilt++;
        this.ui.updateResourceDisplay(); // For unit count
    }

    createProjectile(shooter, target) {
        const projectile = new Projectile(this, shooter, target);
        this.projectiles.push(projectile);
    }

    // New effect methods for visual effects system
    createScreenShake(intensity, duration) {
        this.visualEffects.createScreenShake(intensity, duration);
    }

    createLootSpawnEffect(x, y, color) {
        this.visualEffects.createLootSpawnEffect(x, y, color);
    }

    createLootPickupEffect(x, y, color) {
        this.visualEffects.createLootPickupEffect(x, y, color);
    }

    createDebrisEffect(x, y, color) {
        this.visualEffects.createDebris(x, y, color, 'medium');
    }

    // --- Effect Creation Methods (Using Pool) ---
    createMoveMarker(x, y) { const effect = this.getEffectFromPool('moveMarker'); if (!effect) return; const marker = effect.element; marker.setAttribute('cx', x); marker.setAttribute('cy', y); marker.setAttribute('r', 5); marker.setAttribute('opacity', '1'); let currentRadius = 5; let currentOpacity = 1; const animInterval = setInterval(() => { currentRadius += 1; currentOpacity -= 0.05; if (currentOpacity <= 0) { clearInterval(animInterval); this.returnEffectToPool(effect, 0); } else { marker.setAttribute('r', currentRadius); marker.setAttribute('opacity', currentOpacity); } }, 25); }
    createRallyPointMarker(building, x, y) { if (building.rallyMarkerInstance) { this.returnEffectToPool(building.rallyMarkerInstance, 0); } const effect = this.getEffectFromPool('rallyMarker'); if (!effect) return; const group = effect.element; group.lineEl.setAttribute('x1', building.x); group.lineEl.setAttribute('y1', building.y); group.lineEl.setAttribute('x2', x); group.lineEl.setAttribute('y2', y); group.flagEl.setAttribute('d', `M ${x} ${y} L ${x} ${y-20} L ${x+10} ${y-15} L ${x} ${y-10} Z`); building.rallyMarkerInstance = effect; }
    createHitEffect(x, y) { 
        // Use new visual effects system
        this.visualEffects.createHitSpark(x, y);
    }
    createExplosion(x, y, size, type = 'normal') { 
        // Use new visual effects system
        this.visualEffects.createExplosion(x, y, size, type);
        this.soundManager.play('explosion');
    }
    createHarvestEffect(x, y) { const effect = this.getEffectFromPool('harvest'); if (!effect) return; const sparkle = effect.element; const startX = x + (Math.random() - 0.5) * 15; const startY = y + (Math.random() - 0.5) * 15; sparkle.setAttribute('cx', startX); sparkle.setAttribute('cy', startY); sparkle.setAttribute('r', 1 + Math.random()); sparkle.setAttribute('opacity', '1'); let currentY = startY; let opacity = 1; const interval = setInterval(() => { currentY -= 1; opacity -= 0.05; if (opacity <= 0) { clearInterval(interval); this.returnEffectToPool(effect, 0); } else { sparkle.setAttribute('cy', currentY); sparkle.setAttribute('opacity', opacity); } }, 25); }
    createRepairEffect(x1, y1, x2, y2) { const effect = this.getEffectFromPool('repair'); if(!effect) return; const beam = effect.element; beam.setAttribute('x1', x1); beam.setAttribute('y1', y1); beam.setAttribute('x2', x2 + (Math.random()-0.5)*20); beam.setAttribute('y2', y2 + (Math.random()-0.5)*20); beam.setAttribute('opacity', '0.6'); this.returnEffectToPool(effect, 100); }
    
    // --- Core Game Logic Methods ---
    moveUnit(unit, deltaTime) {
        // Use the new unit movement manager for advanced movement
        return this.unitMovementManager.processIndividualUnitMovement(unit, deltaTime);
    }
    
    // Enhanced pathfinding method that chooses the best algorithm
    findPathForUnit(unit, startX, startY, endX, endY, options = {}) {
        switch (this.pathfindingMode) {
            case 'flowfield':
                // Use flow field for group movement
                if (options.groupMovement && options.groupSize > 5) {
                    return this.flowFieldPathfinder.generateFlowField(endX, endY);
                }
                // Fall through to advanced for individual units
            case 'advanced':
                return this.advancedPathfinder.findPath(startX, startY, endX, endY, unit, options);
            case 'legacy':
            default:
                return this.pathfinder.findPath(startX, startY, endX, endY);
        }
    }
    
    // Group pathfinding for formations
    findGroupPaths(units, targetX, targetY, formationType = 'box') {
        if (this.useGroupPathfinding && units.length > 1) {
            return this.advancedPathfinder.findGroupPaths(units, targetX, targetY, formationType);
        } else {
            // Individual pathfinding for each unit
            return units.map(unit => ({
                unit,
                path: this.findPathForUnit(unit, unit.x, unit.y, targetX, targetY)
            }));
        }
    }
    
    applySplashDamage(centerX, centerY, damage, radius, attackerTeam, shooter) {
        // Use enhanced combat system for splash damage calculation
        const affectedUnits = this.combatSystem.calculateSplashDamage(shooter, centerX, centerY, damage, radius);
        
        affectedUnits.forEach(({ entity, damage: finalDamage }) => {
            // Apply shield damage reduction
            const shieldedDamage = this.lootSystem.applyShieldDamage(entity, finalDamage);
            
            entity.health -= shieldedDamage;
            entity.lastAttacker = shooter;
            
            // Award combat XP to attacker
            if (shooter.team === 'player') {
                this.combatSystem.awardCombatXP(shooter, Math.floor(shieldedDamage / 10), 'splash_damage');
            }
            
            // Award survival XP to defender
            if (entity.team === 'player') {
                this.levelingSystem.awardSurvivalXP(entity, shieldedDamage);
            }
            
            // Update music for combat activity
            this.updateMusicForCombat();
            
            if(typeof entity.updateHealthBar === 'function') entity.updateHealthBar();
        });
    }
    
    updatePower() {
        let production = 0;
        let consumption = 0;
        this.teams.player.buildings.forEach(b => {
            if (b.constructionProgress >= 100) {
                production += b.provides || 0;
                consumption += b.power || 0;
            }
        });
        
        // Update both legacy and new resource systems
        this.resources.power = production - consumption;
        this.resources.maxPower = production;
        this.resourceManager.resources.power = this.resources.power;
        this.resourceManager.resources.maxPower = this.resources.maxPower;
        
        this.ui.updateResourceDisplay();
    }
    updateSpiceFieldVisual(field){ /* ... (copied from previous optimized game class) ... */ if (field.mainCircleElement) { const opacity = Math.max(0.05, 0.3 * (field.amount / field.maxAmount)); field.mainCircleElement.setAttribute('opacity', opacity); } const mmSpice = this.minimapSpiceElements.get(this.spiceFields.indexOf(field)); if (mmSpice) { if (field.amount > 0) { mmSpice.setAttribute('opacity', Math.max(0.1, 0.5 * (field.amount / field.maxAmount))); mmSpice.style.display = ''; } else { mmSpice.style.display = 'none'; } } }
    
    // --- Entity Finders ---
    findNearestPlayerEntity(sourceEntity, maxRange) { /* ... (copied from previous optimized game class) ... */ let closest = null; let minDistSq = maxRange**2; [...this.teams.player.units, ...this.teams.player.buildings].forEach(target => { if (target.health <= 0 || (target.constructionProgress !== undefined && target.constructionProgress < 100) ) return; const distSq = (target.x - sourceEntity.x)**2 + (target.y - sourceEntity.y)**2; if (distSq < minDistSq) { minDistSq = distSq; closest = target; } }); return closest; }
    findNearestEnemy(sourceEntity, maxRange) { 
        let closest = null; 
        let minDistSq = maxRange**2; 
        const targetTeam = sourceEntity.team === 'player' ? 'enemy' : 'player'; 
        
        // Include neutral enemies in search
        const allTargets = [...this.teams[targetTeam].units, ...this.teams[targetTeam].buildings];
        if (sourceEntity.team !== 'neutral') {
            allTargets.push(...this.neutralEnemies.neutralUnits);
        }
        
        allTargets.forEach(target => { 
            if (target.health <= 0 || (target.constructionProgress !== undefined && target.constructionProgress < 100)) return; 
            const distSq = (target.x - sourceEntity.x)**2 + (target.y - sourceEntity.y)**2; 
            if (distSq < minDistSq) { 
                minDistSq = distSq; 
                closest = target; 
            } 
        }); 
        return closest; 
    }
    findNearestSpiceField(unit) { /* ... (copied from previous optimized game class) ... */ let nearest = null; let minDistSq = Infinity; this.spiceFields.forEach(field => { if (field.amount > 0) { const distSq = (field.x - unit.x)**2 + (field.y - unit.y)**2; if (distSq < minDistSq) { minDistSq = distSq; nearest = field; } } }); return nearest; }
    findNearestFriendlyBuilding(unit, type) {
        // Neutral units don't have friendly buildings
        if (unit.team === 'neutral' || !this.teams[unit.team]) {
            return null;
        }
        
        let nearest = null;
        let minDistSq = Infinity;
        this.teams[unit.team].buildings.forEach(b => {
            if (b.type === type && b.constructionProgress >= 100) {
                const distSq = (b.x - unit.x)**2 + (b.y - unit.y)**2;
                if (distSq < minDistSq) {
                    minDistSq = distSq;
                    nearest = b;
                }
            }
        });
        return nearest;
    }

    findNearestInjuredFriendly(unit, maxRange) {
        if (unit.team === 'neutral' || !this.teams[unit.team]) {
            return null;
        }
        
        let nearest = null;
        let minDistSq = maxRange**2;
        this.teams[unit.team].units.forEach(friendlyUnit => {
            if (friendlyUnit !== unit && friendlyUnit.health > 0 && friendlyUnit.health < friendlyUnit.maxHealth) {
                const distSq = (friendlyUnit.x - unit.x)**2 + (friendlyUnit.y - unit.y)**2;
                if (distSq < minDistSq) {
                    minDistSq = distSq;
                    nearest = friendlyUnit;
                }
            }
        });
        return nearest;
    }

    createHealEffect(x1, y1, x2, y2) {
        // Create a healing beam effect similar to repair effect
        const effect = this.getEffectFromPool('repair');
        if(!effect) return;
        const beam = effect.element;
        if (beam) {
            beam.setAttribute('x1', x1);
            beam.setAttribute('y1', y1);
            beam.setAttribute('x2', x2 + (Math.random()-0.5)*10);
            beam.setAttribute('y2', y2 + (Math.random()-0.5)*10);
            beam.setAttribute('opacity', '0.8');
            beam.setAttribute('stroke', '#00FF00'); // Green healing beam
        }
        this.returnEffectToPool(effect, 150);
    }

    // --- Advanced Unit Abilities ---
    toggleUnitDeployment(unit) {
        if (!unit.deployable) {
            this.ui.showNotification('Unit cannot be deployed!', 'warning');
            return false;
        }
        
        return this.combatSystem.toggleDeployment(unit);
    }
    
    // Stealth detection system
    canDetectStealth(detector, stealthUnit) {
        // Check if detector has stealth detection capability
        if (!detector.canDetectStealth && !this.technologySystem.isResearched('stealth_technology')) {
            return false;
        }
        
        // Calculate detection range
        const detectionRange = detector.visionRadius * 0.5; // Half vision range for stealth detection
        const distSq = (detector.x - stealthUnit.x) ** 2 + (detector.y - stealthUnit.y) ** 2;
        
        return distSq < detectionRange ** 2;
    }
    
    // Check if stealth unit is visible to any enemy units
    isStealthUnitDetected(stealthUnit) {
        if (!stealthUnit.stealth || !stealthUnit.stealthActive) return true;
        
        const enemyTeam = stealthUnit.team === 'player' ? 'enemy' : 'player';
        if (!this.teams[enemyTeam]) return false;
        
        return this.teams[enemyTeam].units.some(unit =>
            this.canDetectStealth(unit, stealthUnit)
        );
    }

    // --- Phase 3 Strategic Features ---
    
    // Diplomacy methods
    proposeDiplomaticAction(factionId, actionType, terms = {}) {
        return this.diplomacySystem.proposeDiplomaticAction(factionId, actionType, terms);
    }
    
    acceptDiplomaticProposal(proposalId) {
        return this.diplomacySystem.acceptProposal(proposalId);
    }
    
    rejectDiplomaticProposal(proposalId) {
        return this.diplomacySystem.rejectProposal(proposalId);
    }
    
    getDiplomaticStatus(factionId) {
        return this.diplomacySystem.getDiplomaticStatus(factionId);
    }
    
    // Technology research methods
    startTechnologyResearch(techId) {
        if (this.technologySystem.canResearch(techId)) {
            return this.technologySystem.startResearch(techId);
        }
        return false;
    }
    
    getTechnologyProgress(techId) {
        return this.technologySystem.getResearchProgress(techId);
    }
    
    // Dynamic map interaction
    captureNeutralStructure(structureId, capturingUnit) {
        return this.dynamicMapSystem.captureStructure(structureId, capturingUnit);
    }
    
    triggerMapEvent(eventType, location) {
        return this.dynamicMapSystem.triggerEvent(eventType, location);
    }
    
    // Victory condition methods
    checkCustomVictoryCondition(conditionType) {
        return this.victorySystem.checkCondition(conditionType);
    }
    
    setVictoryCondition(conditionType, enabled = true) {
        return this.victorySystem.setCondition(conditionType, enabled);
    }
    
    // Strategic resource management
    enableDiplomacy() {
        this.diplomacyEnabled = true;
        this.diplomacySystem.initialize();
        this.ui.showNotification('Diplomacy system activated!', 'info');
    }
    
    enableTerraforming() {
        this.terraformingEnabled = true;
        this.ui.showNotification('Terraforming technology unlocked!', 'info');
    }
    
    // Advanced AI faction management
    addAIFaction(factionConfig) {
        const factionId = factionConfig.id || `faction_${Date.now()}`;
        
        // Create AI team
        this.teams[factionId] = {
            color: factionConfig.color || '#ff0',
            buildings: [],
            units: [],
            upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 },
            personality: factionConfig.personality || 'balanced',
            resources: { spice: factionConfig.startingSpice || 1000 }
        };
        
        // Create AI controller
        const aiController = new AIController(this, factionId);
        aiController.personality = factionConfig.personality;
        aiController.initializeAIPersonality();
        
        // Add to diplomacy system
        if (this.diplomacyEnabled) {
            this.diplomacySystem.addFaction(factionId, factionConfig);
        }
        
        // Create starting base and units
        if (factionConfig.startingPosition) {
            this.addBuilding({
                type: 'base',
                x: factionConfig.startingPosition.x,
                y: factionConfig.startingPosition.y,
                team: factionId,
                instant: true
            });
            
            // Add some starting units
            for (let i = 0; i < 3; i++) {
                this.createUnit('soldier',
                    factionConfig.startingPosition.x + (Math.random() - 0.5) * 200,
                    factionConfig.startingPosition.y + (Math.random() - 0.5) * 200,
                    factionId
                );
            }
        }
        
        return factionId;
    }

    // --- Game State & UI Interaction ---
    togglePause() { this.paused = !this.paused; this.ui.togglePauseOverlay(this.paused); if (!this.paused) this.lastTimestamp = performance.now(); }
    showHelp() { this.ui.showNotification("Help: Left Click Select, Right Click Command. P to Pause. Destroy enemy Command Centers!", "info", 5000); } // Longer duration
    restart() { if (confirm('Restart game?')) location.reload(); }
    showBuildTab(tabName) { this.currentBuildTab = tabName; document.querySelectorAll('#buildPanel .tab-button').forEach(btn => btn.classList.remove('active')); const activeButton = Array.from(document.querySelectorAll('#buildPanel .tab-button')).find(btn => btn.getAttribute('onclick') === `game.showBuildTab('${tabName}')`); if (activeButton) activeButton.classList.add('active'); this.ui.updateBuildPanel(); }
    purchaseUpgrade(key, cost) { if (this.resources.spice >= cost && this.teams.player.upgrades[key] < 3 ) { this.resources.spice -= cost; this.teams.player.upgrades[key]++; this.ui.updateResourceDisplay(); this.ui.updateBuildPanel(); this.soundManager.play('complete'); this.ui.showNotification(`${key.charAt(0).toUpperCase() + key.slice(1)} upgraded!`); } }
    
    // --- Unit & Building Commands ---
    handleRightClick(targetGameX, targetGameY) { 
        if (this.selectedUnits.length > 0) { 
            let commandIssued = false; 
            const potentialTargets = [...this.units, ...this.buildings, ...this.neutralEnemies.neutralUnits] 
                .filter(e => e.team !== 'player' && (e.health > 0 || e.constructionProgress < 100)) 
                .sort((a,b) => Math.hypot(a.x-targetGameX, a.y-targetGameY) - Math.hypot(b.x-targetGameX, b.y-targetGameY)); 
            const clickedTarget = potentialTargets.find(e => { 
                const size = e.size || (e.width + e.height) / 4; 
                return Math.hypot(e.x - targetGameX, e.y - targetGameY) < size; 
            }); 
            if (clickedTarget) { 
                this.selectedUnits.forEach(unit => { 
                    if (unit.damage > 0 || unit.type === 'rocketeer') { 
                        unit.attackTarget = clickedTarget; 
                        unit.repairTarget = null; 
                        unit.state = 'attacking'; 
                        commandIssued = true; 
                    } 
                }); 
                if(commandIssued) this.ui.showNotification(`Targeting ${clickedTarget.name || clickedTarget.type}!`, 'info'); 
            } else { 
                const potentialRepairTargets = this.buildings 
                    .filter(b => b.team === 'player' && b.health < b.maxHealth && b.constructionProgress >=100) 
                    .sort((a,b) => Math.hypot(a.x-targetGameX, a.y-targetGameY) - Math.hypot(b.x-targetGameX, b.y-targetGameY)); 
                const clickedRepairTarget = potentialRepairTargets.find(b => Math.abs(targetGameX - b.x) < b.width / 2 && Math.abs(targetGameY - b.y) < b.height / 2 ); 
                if (clickedRepairTarget) { 
                    this.selectedUnits.forEach(unit => { 
                        if (unit.canRepair) { 
                            unit.repairTarget = clickedRepairTarget; 
                            unit.attackTarget = null; 
                            unit.state = 'repairing'; 
                            commandIssued = true; 
                        } 
                    }); 
                    if(commandIssued) this.ui.showNotification(`Repairing ${clickedRepairTarget.name}!`, 'info'); 
                } 
            } 
            if (!commandIssued) {
                // Use advanced group pathfinding for formations
                if (this.selectedUnits.length > 1 && this.useGroupPathfinding) {
                    const groupPaths = this.findGroupPaths(this.selectedUnits, targetGameX, targetGameY);
                    groupPaths.forEach(({ unit, path }) => {
                        unit.targetX = targetGameX;
                        unit.targetY = targetGameY;
                        unit.attackTarget = null;
                        unit.repairTarget = null;
                        unit.state = 'moving';
                        unit.path = path;
                        unit.pathIndex = 0;
                    });
                    
                    // Create formation group for coordinated movement
                    if (this.unitMovementManager) {
                        this.unitMovementManager.createFormationGroup(this.selectedUnits, this.selectedUnits[0]);
                    }
                } else {
                    // Individual pathfinding for single units or when group pathfinding is disabled
                    this.selectedUnits.forEach((unit) => {
                        unit.targetX = targetGameX;
                        unit.targetY = targetGameY;
                        unit.attackTarget = null;
                        unit.repairTarget = null;
                        unit.state = 'moving';
                        unit.path = this.findPathForUnit(unit, unit.x, unit.y, unit.targetX, unit.targetY);
                        unit.pathIndex = 0;
                    });
                }
                this.createMoveMarker(targetGameX, targetGameY);
                commandIssued = true;
            }
            if (commandIssued) this.soundManager.play('move'); 
        } else if (this.selectedBuilding) { 
            if (['barracks', 'factory', 'base'].includes(this.selectedBuilding.type)) { 
                this.selectedBuilding.rallyPoint = { x: targetGameX, y: targetGameY }; 
                this.ui.showNotification('Rally point set'); 
                this.createRallyPointMarker(this.selectedBuilding, targetGameX, targetGameY); 
                this.soundManager.play('select'); 
            } 
        } 
    }
    calculateFormation(centerX, centerY, unitCount, avgAngle = 0) { /* ... (copied) ... */ const formation = []; const spacing = 40; if (unitCount <= 1) return [{x: centerX, y: centerY}]; const cols = Math.ceil(Math.sqrt(unitCount)); const rows = Math.ceil(unitCount / cols); let idx = 0; for (let r = 0; r < rows && idx < unitCount; r++) { const unitsInThisRow = Math.min(cols, unitCount - (r * cols)); for (let c = 0; c < unitsInThisRow && idx < unitCount; c++) { const offsetX = (c - (unitsInThisRow -1) / 2) * spacing; const offsetY = (r - (rows -1) / 2) * spacing; const rotatedX = offsetX * Math.cos(avgAngle) - offsetY * Math.sin(avgAngle); const rotatedY = offsetX * Math.sin(avgAngle) + offsetY * Math.cos(avgAngle); formation.push({ x: centerX + rotatedX, y: centerY + rotatedY }); idx++; } } return formation; }
    startBuilding(type) { /* ... (copied) ... */ const buildingType = this.buildingTypes[type]; if (this.resources.spice >= buildingType.cost) { this.buildingMode = type; this.createGhostBuilding(type); this.soundManager.play('select'); } else { this.ui.showNotification('Not enough resources!', 'warning'); } }
    createGhostBuilding(type) {
        const buildingType = this.buildingTypes[type];
        this.ghostBuilding = {
            type: type,
            width: buildingType.width,
            height: buildingType.height,
            color: GameData.getBuildingColor(type),
            x: 0, y: 0, // Will be updated by updateGhostBuilding
            valid: false
        };
    }
    updateGhostBuilding(x, y) {
        if (this.ghostBuilding && this.buildingMode) {
            const buildingType = this.buildingTypes[this.buildingMode];
            const snappedX = Math.round(x / (this.pathfinder.gridSize/2)) * (this.pathfinder.gridSize/2);
            const snappedY = Math.round(y / (this.pathfinder.gridSize/2)) * (this.pathfinder.gridSize/2);
            this.ghostBuilding.x = snappedX;
            this.ghostBuilding.y = snappedY;
            this.ghostBuilding.valid = this.isValidBuildingPlacement(snappedX, snappedY, buildingType);
        }
    }
    isValidBuildingPlacement(x, y, buildingType) { /* ... (copied) ... */ const halfWidth = buildingType.width / 2; const halfHeight = buildingType.height / 2; if (x - halfWidth < 0 || x + halfWidth > this.width || y - halfHeight < 0 || y + halfHeight > this.height) return false; const buildingBuffer = 5; for (const b of this.buildings) { if (Math.abs(b.x - x) < (b.width/2 + halfWidth + buildingBuffer) && Math.abs(b.y - y) < (b.height/2 + halfHeight + buildingBuffer)) { return false; } } for (const t of this.terrain) { const closestX = Math.max(x - halfWidth, Math.min(t.x, x + halfWidth)); const closestY = Math.max(y - halfHeight, Math.min(t.y, y + halfHeight)); if (Math.hypot(closestX - t.x, closestY - t.y) < t.radius) return false; } return true; }
    placeBuilding(x, y) { /* ... (copied) ... */ if (!this.buildingMode) return; const buildingType = this.buildingTypes[this.buildingMode]; const snappedX = Math.round(x / (this.pathfinder.gridSize/2)) * (this.pathfinder.gridSize/2); const snappedY = Math.round(y / (this.pathfinder.gridSize/2)) * (this.pathfinder.gridSize/2); if (this.resources.spice >= buildingType.cost && this.isValidBuildingPlacement(snappedX, snappedY, buildingType)) { this.resources.spice -= buildingType.cost; this.addBuilding({ type: this.buildingMode, x: snappedX, y: snappedY, team: 'player' }); this.cancelBuildingMode(); this.ui.updateResourceDisplay(); this.ui.updateBuildPanel(); this.soundManager.play('build'); } else { this.soundManager.play('warning'); this.ui.showNotification('Cannot build here!', 'error'); } }
    cancelBuildingMode() {
        this.buildingMode = null;
        this.ghostBuilding = null;
    }
    deselectAll() {
        this.selectedUnits.forEach(unit => {
            unit.selected = false;
        });
        this.selectedUnits = [];
        this.selectedBuilding = null;
        this.ui.updateUnitInfoPanel();
    }
    trainUnit(type, buildingId) {
        const building = this.buildings.find(b => b.id === buildingId);
        if (!building) {
            console.error(`Train unit: Building with ID ${buildingId} not found.`);
            return;
        }
        const unitTypeData = this.unitTypes[type] || GameData.getUnitTypes()[type];
        if (!unitTypeData) {
            console.error(`Train unit: Unit type data for '${type}' not found.`);
            return;
        }
        
        // Check resource requirements including water
        const costs = { spice: unitTypeData.cost };
        if (this.resourceManager.requiresWater(type, 'unit')) {
            costs.water = Math.floor(unitTypeData.cost * 0.1); // 10% of spice cost in water
        }
        
        const unitCap = parseInt(document.getElementById('unitMax').textContent) || 50;
        if (this.resourceManager.canAfford(costs) && this.teams.player.units.length < unitCap) {
            this.resourceManager.spendResources(costs);
            
            // Apply technology cost reduction
            const costReduction = this.technologySystem.getTechnologyEffect('costReduction');
            if (costReduction > 0) {
                const refund = Math.floor(costs.spice * costReduction);
                this.resourceManager.addResource('spice', refund);
            }
            
            const queue = this.productionQueues.get(buildingId) || [];
            if (queue.length < 5) {
                // Apply technology build speed bonus
                const speedBonus = this.technologySystem.getTechnologyEffect('buildSpeedBonus');
                const adjustedBuildTime = unitTypeData.buildTime / (1 + speedBonus);
                
                queue.push({ type, progress: 0, buildTime: adjustedBuildTime });
                this.productionQueues.set(buildingId, queue);
                this.ui.updateResourceDisplay();
                this.ui.updateUnitInfoPanel();
                this.soundManager.play('select');
            } else {
                this.ui.showNotification('Production queue full!', 'warning');
                this.resourceManager.addResource('spice', costs.spice);
                if (costs.water) this.resourceManager.addResource('water', costs.water);
            }
        } else if (this.teams.player.units.length >= unitCap) {
            this.ui.showNotification('Unit limit reached!', 'warning');
        } else {
            this.ui.showNotification('Insufficient resources!', 'warning');
        }
    }
    setFormation(type) { /* ... (copied) ... */ if (this.selectedUnits.length > 1) { const centerX = this.selectedUnits.reduce((s, u) => s + u.x, 0) / this.selectedUnits.length; const centerY = this.selectedUnits.reduce((s, u) => s + u.y, 0) / this.selectedUnits.length; const avgAngle = this.selectedUnits.reduce((s, u) => s + u.rotation, 0) / this.selectedUnits.length; let positions = this.calculateFormation(centerX, centerY, this.selectedUnits.length, avgAngle); this.selectedUnits.forEach((unit, i) => { if (positions[i]) { unit.targetX = positions[i].x; unit.targetY = positions[i].y; unit.path = this.pathfinder.findPath(unit.x, unit.y, unit.targetX, unit.targetY); unit.pathIndex = 0; unit.state = 'moving'; } }); this.soundManager.play('move'); } }
    createControlGroup(number) { /* ... (copied) ... */ if (this.selectedUnits.length > 0) { this.controlGroups[number] = [...this.selectedUnits]; this.ui.showNotification(`Control group ${number} set.`); } }
    selectControlGroup(number) {
        if (this.controlGroups && this.controlGroups[number]) {
            this.deselectAll();
            this.controlGroups[number] = this.controlGroups[number].filter(u => u.health > 0 && this.units.includes(u));
            this.selectedUnits = [...this.controlGroups[number]];
            this.selectedUnits.forEach(unit => {
                unit.selected = true;
            });
            this.ui.updateUnitInfoPanel();
            if (this.selectedUnits.length > 0) this.soundManager.play('select');
            else this.ui.showNotification(`Control group ${number} is empty.`);
        }
    }
    selectAllPlayerUnitsOnScreen() {
        this.deselectAll();
        const gameAreaRect = this.gameAreaEl.getBoundingClientRect();
        const viewWidthGame = gameAreaRect.width / this.zoom;
        const viewHeightGame = gameAreaRect.height / this.zoom;
        this.teams.player.units.forEach(unit => {
            if (unit.x >= this.viewX && unit.x <= this.viewX + viewWidthGame && unit.y >= this.viewY && unit.y <= this.viewY + viewHeightGame) {
                unit.selected = true;
                this.selectedUnits.push(unit);
            }
        });
        if (this.selectedUnits.length > 0) {
            this.ui.updateUnitInfoPanel();
            this.soundManager.play('select');
        }
    }
    stopSelectedUnits() { /* ... (copied) ... */ this.selectedUnits.forEach(unit => { unit.targetX = unit.x; unit.targetY = unit.y; unit.path = []; unit.attackTarget = null; unit.repairTarget = null; unit.state = 'idle'; }); if (this.selectedUnits.length > 0) this.soundManager.play('move'); }
    centerOnSelectedUnits() {
        if (this.selectedUnits.length === 0) return;
        
        // Calculate center position of selected units
        let centerX = 0;
        let centerY = 0;
        this.selectedUnits.forEach(unit => {
            centerX += unit.x;
            centerY += unit.y;
        });
        centerX /= this.selectedUnits.length;
        centerY /= this.selectedUnits.length;
        
        // Get viewport dimensions
        const gameAreaRect = this.gameAreaEl.getBoundingClientRect();
        const viewWidth = gameAreaRect.width / this.zoom;
        const viewHeight = gameAreaRect.height / this.zoom;
        
        // Center the view on the calculated position
        this.viewX = centerX - (viewWidth / 2);
        this.viewY = centerY - (viewHeight / 2);
        
        // Constrain view to map bounds
        this.inputHandler.constrainView();
        this.updateViewport();
        
        // Play sound feedback
        this.soundManager.play('select');
    }
    destroySelectedUnits() { /* ... (copied) ... */ if (this.selectedUnits.length > 0 && confirm('Self-destruct selected units?')) this.selectedUnits.forEach(unit => unit.health = 0); }
    endSelection(startX, startY, endX, endY) {
        // Use InputHandler's getGameCoords for consistent coordinate conversion
        const startGameCoords = this.inputHandler.getGameCoords(startX, startY);
        const endGameCoords = this.inputHandler.getGameCoords(endX, endY);
        
        const selLeft = Math.min(startGameCoords.x, endGameCoords.x);
        const selTop = Math.min(startGameCoords.y, endGameCoords.y);
        const selRight = Math.max(startGameCoords.x, endGameCoords.x);
        const selBottom = Math.max(startGameCoords.y, endGameCoords.y);
        
        // Check if this is a drag selection (not just a click)
        const isDragSelection = Math.abs(endX - startX) > 5 || Math.abs(endY - startY) > 5;
        
        this.deselectAll();
        let selectedSomething = false;

        if (isDragSelection) {
            // Rectangle selection - select all units within the rectangle
            this.units.forEach(unit => {
                if (unit.team === 'player' && unit.health > 0 &&
                    unit.x >= selLeft && unit.x <= selRight &&
                    unit.y >= selTop && unit.y <= selBottom) {
                    unit.selected = true;
                    this.selectedUnits.push(unit);
                    selectedSomething = true;
                }
            });
        } else {
            // Point selection - find the closest unit or building to the click point
            const clickGameCoords = this.inputHandler.getGameCoords(startX, startY);
            
            // First try to select a unit
            let closestUnit = null;
            let closestDistance = Infinity;
            
            this.units.forEach(unit => {
                if (unit.team === 'player' && unit.health > 0) {
                    const distance = Math.hypot(unit.x - clickGameCoords.x, unit.y - clickGameCoords.y);
                    if (distance < unit.size + 10 && distance < closestDistance) {
                        closestDistance = distance;
                        closestUnit = unit;
                    }
                }
            });
            
            if (closestUnit) {
                closestUnit.selected = true;
                this.selectedUnits.push(closestUnit);
                selectedSomething = true;
            } else {
                // Try to select a building
                this.selectedBuilding = this.buildings.find(b =>
                    b.team === 'player' &&
                    Math.abs(clickGameCoords.x - b.x) < b.width/2 &&
                    Math.abs(clickGameCoords.y - b.y) < b.height/2
                );
                if (this.selectedBuilding) selectedSomething = true;
            }
        }
        
        if (selectedSomething) this.soundManager.play('select');
        this.ui.updateUnitInfoPanel();
    }
    updateViewport() {
        const gameAreaRect = this.gameAreaEl.getBoundingClientRect();
        const viewPortWidth = gameAreaRect.width / this.zoom;
        const viewPortHeight = gameAreaRect.height / this.zoom;
        this.viewX = Math.max(0, Math.min(this.width - viewPortWidth, this.viewX));
        this.viewY = Math.max(0, Math.min(this.height - viewPortHeight, this.viewY));
        // No longer setting viewBox on SVG, drawing will handle viewport
        this.ui.updateViewportRect(this.viewX, this.viewY, viewPortWidth, viewPortHeight);
    }
    
    // --- Main Game Loop & Updates ---
    updateEntityList(list, deltaTime) {
        for (let i = list.length - 1; i >= 0; i--) {
            const entity = list[i];
            entity.update(deltaTime);
            if (entity.health <= 0) {
                // Create death effects
                if (entity.isUnit) {
                    // Blood splatter for human units
                    if (['soldier', 'rocketeer', 'engineer'].includes(entity.type)) {
                        this.visualEffects.createBloodSplatter(entity.x, entity.y);
                    } else {
                        // Enhanced vehicle destruction effects
                        this.visualEffects.createVehicleDestruction(entity.x, entity.y, entity.type, entity.size);
                    }
                    
                    // Award XP to attacker if applicable
                    if (entity.lastAttacker && entity.lastAttacker.team === 'player') {
                        this.levelingSystem.awardActionXP(entity.lastAttacker, 'kill_unit');
                    }
                    
                    // Drop loot
                    this.lootSystem.dropLoot(entity);
                    
                    // Handle destruction stats and UI updates
                    if(entity.team === 'player') this.teams.player.stats.unitsLost++;
                    else this.teams.player.stats.enemiesDestroyed++;
                    
                    // Only remove from team arrays if it's not a neutral unit
                    if (entity.team !== 'neutral' && this.teams[entity.team]) {
                        this.teams[entity.team].units.splice(this.teams[entity.team].units.indexOf(entity), 1);
                    }
                    
                    if (this.selectedUnits.includes(entity)) this.selectedUnits.splice(this.selectedUnits.indexOf(entity), 1);
                } else if (entity.isBuilding) {
                    // Enhanced building explosion with debris and crater
                    const buildingSize = Math.max(entity.width, entity.height);
                    this.createExplosion(entity.x, entity.y, buildingSize, 'building');
                    
                    // Create building debris
                    this.visualEffects.createDebris(entity.x, entity.y, '#8B7355', buildingSize > 60 ? 'large' : 'medium');
                    
                    // Create crater for large buildings
                    if (buildingSize > 40) {
                        this.visualEffects.createCrater(entity.x, entity.y, buildingSize > 80 ? 'large' : 'medium');
                    }
                    
                    // Award XP to attacker if applicable
                    if (entity.lastAttacker && entity.lastAttacker.team === 'player') {
                        this.levelingSystem.awardActionXP(entity.lastAttacker, 'kill_building');
                    }
                    
                    // Drop loot
                    this.lootSystem.dropLoot(entity);
                    
                    if(entity.team === 'player') this.teams.player.stats.buildingsLost++;
                    this.teams[entity.team].buildings.splice(this.teams[entity.team].buildings.indexOf(entity), 1);
                    if (this.selectedBuilding === entity) this.selectedBuilding = null;
                    if (this.productionQueues.has(entity.id)) this.productionQueues.delete(entity.id);
                    
                    // Update all pathfinding systems when buildings are destroyed
                    this.pathfinder.updateGridObstacles();
                    this.advancedPathfinder.updateGridObstacles();
                    this.flowFieldPathfinder.updateCostField();
                    
                    this.updatePower();
                }
                entity.destroy(); // Removes SVG element
                list.splice(i, 1);
            }
        }
    }

    updateProductionQueues(deltaTime) {
        this.productionQueues.forEach((queue, buildingId) => {
            if (queue.length === 0) return;
            const building = this.buildings.find(b => b.id === buildingId);
            if (!building || building.constructionProgress < 100 || 
                (building.power < 0 && this.resources.power < 0 && building.team === 'player')) return;

            const item = queue[0];
            item.progress += (100 / item.buildTime) * deltaTime;
            
            if (item.progress >= 100) {
                queue.shift();
                const spawnOffset = building.width / 2 + 20;
                const spawnAngle = Math.random() * Math.PI * 2;
                const spawnX = building.rallyPoint ? building.rallyPoint.x : building.x + Math.cos(spawnAngle) * spawnOffset;
                const spawnY = building.rallyPoint ? building.rallyPoint.y : building.y + Math.sin(spawnAngle) * spawnOffset;
                this.createUnit(item.type, spawnX, spawnY, building.team);
                if (queue.length > 0) queue[0].progress = 0;
                if (building.team === 'player') this.soundManager.play('complete');
            }
        });
        if (this.selectedBuilding && this.productionQueues.has(this.selectedBuilding.id)) {
            this.ui.updateUnitInfoPanel();
        }
    }

    checkWinConditions() { /* ... (copied) ... */ const enemyBases = this.teams.enemy.buildings.filter(b => b.type === 'base' && b.health > 0); const playerBases = this.teams.player.buildings.filter(b => b.type === 'base' && b.health > 0); if (enemyBases.length === 0 && this.teams.enemy.buildings.length === 0 && this.teams.enemy.units.length === 0 && document.getElementById('victoryScreen').style.display !== 'flex') { this.showVictory(); } else if (playerBases.length === 0 && this.teams.player.buildings.length === 0 && document.getElementById('defeatScreen').style.display !== 'flex') { this.showDefeat(); } }
    showVictory() {
        this.paused = true;
        document.getElementById('victoryStats').innerHTML = this.ui.formatStats();
        document.getElementById('victoryScreen').style.display = 'flex';
        // Play victory music
        this.soundManager.onGameStateChange('victory');
    }
    showDefeat() {
        this.paused = true;
        document.getElementById('defeatStats').innerHTML = this.ui.formatStats();
        document.getElementById('defeatScreen').style.display = 'flex';
        // Play defeat music
        this.soundManager.onGameStateChange('defeat');
    }

    // Music system integration methods
    updateMusicForCombat() {
        const currentTime = this.gameTime;
        this.musicState.lastCombatTime = currentTime;
        
        // Count units in combat
        let combatUnits = 0;
        this.units.forEach(unit => {
            if (unit.attackTarget || unit.state === 'attacking') {
                combatUnits++;
            }
        });
        
        this.musicState.combatUnits = combatUnits;
        
        // Calculate combat intensity based on number of units fighting
        const maxCombatUnits = Math.max(10, this.units.length * 0.3);
        const intensity = Math.min(1.0, combatUnits / maxCombatUnits);
        
        if (combatUnits > 0 && this.musicState.currentState !== 'combat') {
            this.musicState.currentState = 'combat';
            this.soundManager.onGameStateChange('combat', { intensity });
        } else if (combatUnits > 0) {
            this.soundManager.updateCombatIntensity(intensity);
        }
    }
    
    updateMusicForBuilding() {
        const currentTime = this.gameTime;
        this.musicState.lastBuildTime = currentTime;
        this.musicState.buildingActivity = Math.min(1.0, this.musicState.buildingActivity + 0.2);
        
        // Check if we should transition to building music
        if (this.musicState.currentState === 'peaceful' ||
            (this.musicState.currentState === 'building' && this.musicState.buildingActivity > 0.5)) {
            this.musicState.currentState = 'building';
            this.soundManager.onGameStateChange('building');
            this.soundManager.updateBuildingActivity(this.musicState.buildingActivity);
        }
    }
    
    updateMusicState(deltaTime) {
        const currentTime = this.gameTime;
        const combatTimeout = 10; // seconds
        const buildingTimeout = 15; // seconds
        
        // Decay building activity over time
        this.musicState.buildingActivity = Math.max(0, this.musicState.buildingActivity - deltaTime * 0.1);
        
        // Check for transitions back to peaceful state
        const timeSinceLastCombat = currentTime - this.musicState.lastCombatTime;
        const timeSinceLastBuild = currentTime - this.musicState.lastBuildTime;
        
        if (this.musicState.currentState === 'combat' &&
            (this.musicState.combatUnits === 0 || timeSinceLastCombat > combatTimeout)) {
            this.musicState.currentState = 'peaceful';
            this.musicState.combatUnits = 0;
            this.soundManager.onGameStateChange('peaceful');
        } else if (this.musicState.currentState === 'building' &&
                   timeSinceLastBuild > buildingTimeout &&
                   this.musicState.buildingActivity < 0.1) {
            this.musicState.currentState = 'peaceful';
            this.soundManager.onGameStateChange('peaceful');
        }
        
        // Update music system with current building activity
        if (this.musicState.currentState === 'building') {
            this.soundManager.updateBuildingActivity(this.musicState.buildingActivity);
        }
    }
    
    // Faction selection music
    playFactionTheme(factionName) {
        this.soundManager.playFactionTheme(factionName);
    }
    
    gameLoop(timestamp) {
        const deltaTime = this.paused ? 0 : Math.min(0.1, (timestamp - this.lastTimestamp) / 1000); // Cap deltaTime
        this.lastTimestamp = timestamp;
        
        if (!this.paused) {
            this.gameTime += deltaTime * 60;
            
            // Update Phase 4 Polish & Integration systems
            this.performanceManager.updateFrameTiming(deltaTime);
            this.userExperienceManager.update(deltaTime);
            this.animationManager.update(deltaTime);
            
            // Update Phase 2 Advanced Mechanics systems
            this.resourceManager.update(deltaTime);
            this.combatSystem.update && this.combatSystem.update(deltaTime);
            this.environmentalSystem.update(deltaTime);
            this.technologySystem.update(deltaTime);
            
            // Update Phase 3 Strategic Depth systems
            this.diplomacySystem.update(deltaTime);
            this.dynamicMapSystem.update(deltaTime);
            this.victorySystem.update(deltaTime);
            
            // Update existing systems
            this.lootSystem.update(deltaTime);
            this.neutralEnemies.update(deltaTime);
            this.visualEffects.update(deltaTime);
            
            // Update advanced pathfinding systems
            this.unitMovementManager.update(deltaTime);
            this.advancedPathfinder.updateTrafficDensity();
            this.flowFieldPathfinder.cleanup();
            
            // Update music system
            this.updateMusicState(deltaTime);
            
            // Update game state with performance optimization
            const visibleUnits = this.performanceManager.updateEntitiesOptimized(this.units, deltaTime);
            const visibleBuildings = this.performanceManager.updateEntitiesOptimized(this.buildings, deltaTime);
            this.projectiles = this.projectiles.filter(p => p.update(deltaTime));

            // Handle continuous map movement
            const moveSpeed = 30 / this.zoom;
            let viewChanged = false;
            if (this.inputHandler && this.inputHandler.keys) { // Check if inputHandler and keys are defined
                if (this.inputHandler.keys.has('w') || this.inputHandler.keys.has('arrowup')) { this.viewY -= moveSpeed * deltaTime * 60; viewChanged = true; }
                if (this.inputHandler.keys.has('s') || this.inputHandler.keys.has('arrowdown')) { this.viewY += moveSpeed * deltaTime * 60; viewChanged = true; }
                if (this.inputHandler.keys.has('a') || this.inputHandler.keys.has('arrowleft')) { this.viewX -= moveSpeed * deltaTime * 60; viewChanged = true; }
                if (this.inputHandler.keys.has('d') || this.inputHandler.keys.has('arrowright')) { this.viewX += moveSpeed * deltaTime * 60; viewChanged = true; }
                if (viewChanged) {
                    this.updateViewport();
                }
            }

            this.updateProductionQueues(deltaTime);
            this.updateFogOfWar();
            
            if (deltaTime > 0) {
                 this.aiController.update(deltaTime);
            }
            
            if (!this.lastSpiceRegenTime || timestamp - this.lastSpiceRegenTime > 5000) {
                this.lastSpiceRegenTime = timestamp;
                this.spiceFields.forEach(field => {
                    if (field.amount < field.maxAmount) {
                        field.amount = Math.min(field.amount + 25, field.maxAmount);
                        // No longer updating SVG element, will be redrawn
                    }
                });
            }
            
            // Check victory conditions using the new victory system
            this.victorySystem.checkVictoryConditions();
        }

        // --- Rendering ---
        this.ctx.clearRect(0, 0, this.gameCanvas.width, this.gameCanvas.height); // Clear canvas
        this.ctx.save();
        
        // Apply screen shake
        const shakeOffset = this.visualEffects.getScreenShakeOffset();
        this.ctx.translate(-this.viewX * this.zoom + shakeOffset.x, -this.viewY * this.zoom + shakeOffset.y);
        this.ctx.scale(this.zoom, this.zoom);

        // Draw background (terrain)
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Draw terrain obstacles
        this.terrain.forEach(obstacle => {
            switch(obstacle.type) {
                case 'rock':
                    this.ctx.fillStyle = '#696969';
                    this.ctx.strokeStyle = '#555555';
                    this.ctx.lineWidth = 2;
                    this.ctx.beginPath();
                    this.ctx.arc(obstacle.x, obstacle.y, obstacle.radius, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.stroke();
                    break;
                case 'water':
                    this.ctx.fillStyle = '#4682B4';
                    this.ctx.strokeStyle = '#87CEEB';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.arc(obstacle.x, obstacle.y, obstacle.radius, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.stroke();
                    // Add water animation effect
                    for (let i = 0; i < 5; i++) {
                        const angle = (this.gameTime * 0.01 + i * Math.PI * 0.4) % (Math.PI * 2);
                        const waveRadius = obstacle.radius * 0.3 + Math.sin(angle) * 5;
                        this.ctx.strokeStyle = `rgba(135, 206, 235, ${0.5 - i * 0.1})`;
                        this.ctx.beginPath();
                        this.ctx.arc(obstacle.x, obstacle.y, waveRadius, 0, Math.PI * 2);
                        this.ctx.stroke();
                    }
                    break;
                case 'cliff':
                    this.ctx.fillStyle = '#8B7355';
                    this.ctx.strokeStyle = '#654321';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(obstacle.x, obstacle.y, obstacle.radius, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.stroke();
                    // Add cliff texture
                    this.ctx.fillStyle = '#A0522D';
                    this.ctx.beginPath();
                    this.ctx.arc(obstacle.x - obstacle.radius * 0.3, obstacle.y - obstacle.radius * 0.3, obstacle.radius * 0.4, 0, Math.PI * 2);
                    this.ctx.fill();
                    break;
                default:
                    this.ctx.fillStyle = '#666';
                    this.ctx.beginPath();
                    this.ctx.arc(obstacle.x, obstacle.y, obstacle.radius, 0, Math.PI * 2);
                    this.ctx.fill();
                    break;
            }
        });

        // Draw spice fields
        this.spiceFields.forEach(field => {
            if (field.amount > 0) {
                this.ctx.fillStyle = `rgba(255, 165, 0, ${Math.max(0.05, 0.3 * (field.amount / field.maxAmount))})`;
                this.ctx.beginPath();
                this.ctx.arc(field.x, field.y, field.radius, 0, Math.PI * 2);
                this.ctx.fill();
                // Draw particles
                for (let i = 0; i < 10; i++) { // Simplified particle drawing
                    const angle = Math.random() * Math.PI * 2;
                    const dist = Math.random() * field.radius * 0.8;
                    this.ctx.fillStyle = `rgba(255, 140, 0, 0.5)`;
                    this.ctx.beginPath();
                    this.ctx.arc(field.x + Math.cos(angle) * dist, field.y + Math.sin(angle) * dist, 1 + Math.random() * 2, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }
        });

        // Draw loot items
        this.lootSystem.draw(this.ctx);

        // Draw buildings with performance optimization
        const visibleBuildingsForRender = this.buildings.filter(building =>
            this.performanceManager.isEntityVisible(building));
        this.performanceManager.batchRenderEntities(this.ctx, visibleBuildingsForRender,
            (ctx, building) => building.draw(ctx));

        // Draw units with performance optimization
        const visibleUnitsForRender = this.units.filter(unit =>
            this.performanceManager.isEntityVisible(unit));
        this.performanceManager.batchRenderEntities(this.ctx, visibleUnitsForRender,
            (ctx, unit) => unit.draw(ctx));

        // Draw neutral enemies
        this.neutralEnemies.draw(this.ctx);

        // Draw projectiles
        this.projectiles.forEach(projectile => {
            projectile.draw(this.ctx);
        });

        // Draw visual effects
        this.visualEffects.draw(this.ctx);

        // Draw environmental effects (weather, hazards)
        this.environmentalSystem.draw(this.ctx);
        
        // Draw dynamic map elements (neutral structures, objectives)
        this.dynamicMapSystem.draw(this.ctx);

        // Draw pathfinding debug visualization
        if (this.pathfindingDebug) {
            this.drawPathfindingDebug(this.ctx);
        }

        // Draw ghost building
        if (this.ghostBuilding) {
            this.ctx.fillStyle = this.ghostBuilding.valid ? 'rgba(0, 255, 0, 0.5)' : 'rgba(255, 0, 0, 0.5)';
            this.ctx.strokeStyle = this.ghostBuilding.valid ? '#0f0' : '#f00';
            this.ctx.lineWidth = 2;
            this.ctx.fillRect(this.ghostBuilding.x - this.ghostBuilding.width / 2, this.ghostBuilding.y - this.ghostBuilding.height / 2, this.ghostBuilding.width, this.ghostBuilding.height);
            this.ctx.strokeRect(this.ghostBuilding.x - this.ghostBuilding.width / 2, this.ghostBuilding.y - this.ghostBuilding.height / 2, this.ghostBuilding.width, this.ghostBuilding.height);
        }

        // Draw Fog of War
        this.ctx.fillStyle = 'black';
        const numFogCols = this.fogOfWarData[0] ? this.fogOfWarData[0].length : 0;
        const numFogRows = this.fogOfWarData.length;
        for (let y = 0; y < numFogRows; y++) {
            for (let x = 0; x < numFogCols; x++) {
                const cell = this.fogOfWarData[y][x];
                if (cell.currentOpacity > 0) {
                    this.ctx.globalAlpha = cell.currentOpacity;
                    this.ctx.fillRect(x * this.fogGridSize, y * this.fogGridSize, this.fogGridSize, this.fogGridSize);
                }
            }
        }
        this.ctx.globalAlpha = 1; // Reset alpha

        this.ctx.restore();

        this.ui.updateMinimapDynamic(); // Update minimap after main canvas is drawn

        requestAnimationFrame((ts) => this.gameLoop(ts));
    }
    
    // Phase 4 Polish & Integration Methods
    
    // Save/Load functionality
    saveGameState(slotNumber = 0, description = '') {
        return this.saveLoadManager.saveGame(slotNumber, description);
    }
    
    loadGameState(slotNumber = 0) {
        return this.saveLoadManager.loadGame(slotNumber);
    }
    
    // Performance management
    getPerformanceStats() {
        return this.performanceManager.getPerformanceStats();
    }
    
    setPerformanceSettings(settings) {
        this.performanceManager.settings = { ...this.performanceManager.settings, ...settings };
    }
    
    // Game balance
    setDifficulty(difficulty) {
        this.balanceManager.setDifficulty(difficulty);
    }
    
    adjustUnitBalance(unitType, costMultiplier) {
        this.balanceManager.adjustUnitCost(unitType, costMultiplier);
    }
    
    adjustBuildingBalance(buildingType, costMultiplier) {
        this.balanceManager.adjustBuildingCost(buildingType, costMultiplier);
    }
    
    getBalanceReport() {
        return this.balanceManager.validateBalance();
    }
    
    // User Experience enhancements
    setUXSettings(settings) {
        this.userExperienceManager.updateSettings(settings);
    }
    
    showContextualHelp(trigger) {
        this.userExperienceManager.helpSystem.showContextualHelp(trigger);
    }
    
    // Animation system
    animateUnitSpawn(unit) {
        this.animationManager.animateUnitSpawn(unit);
    }
    
    animateBuildingPlacement(building) {
        this.animationManager.animateBuildingPlacement(building);
    }
    
    animateResourceCollection(x, y, amount) {
        this.animationManager.animateResourceCollection(x, y, amount);
    }
    
    setCameraTarget(x, y, smooth = true) {
        this.animationManager.setCameraTarget(x, y, smooth);
    }
    
    // Enhanced unit abilities
    useUnitAbility(unit, abilityType) {
        switch (abilityType) {
            case 'deploy':
                return this.toggleUnitDeployment(unit);
            case 'heal':
                return this.activateHealAbility(unit);
            case 'repair':
                return this.activateRepairAbility(unit);
            case 'stealth':
                return this.toggleUnitStealth(unit);
            default:
                return false;
        }
    }
    
    activateHealAbility(unit) {
        if (!unit.canHeal) return false;
        
        const target = this.findNearestInjuredFriendly(unit, unit.range || 80);
        if (target) {
            const healAmount = unit.healAmount || 15;
            target.health = Math.min(target.maxHealth, target.health + healAmount);
            this.createHealEffect(unit.x, unit.y, target.x, target.y);
            this.levelingSystem.awardActionXP(unit, 'heal');
            return true;
        }
        return false;
    }
    
    activateRepairAbility(unit) {
        if (!unit.canRepair) return false;
        
        const target = this.findNearestFriendlyBuilding(unit, 'any');
        if (target && target.health < target.maxHealth) {
            const repairAmount = unit.repairAmount || 12;
            target.health = Math.min(target.maxHealth, target.health + repairAmount);
            this.createRepairEffect(unit.x, unit.y, target.x, target.y);
            this.levelingSystem.awardActionXP(unit, 'repair');
            return true;
        }
        return false;
    }
    
    toggleUnitStealth(unit) {
        if (!unit.stealth) return false;
        
        unit.stealthActive = !unit.stealthActive;
        this.ui.showNotification(
            `${unit.name} stealth ${unit.stealthActive ? 'activated' : 'deactivated'}`,
            'info'
        );
        return true;
    }
    
    // Enhanced command system
    setUnitCommand(command) {
        if (this.selectedUnits.length === 0) return;
        
        switch (command) {
            case 'move':
                this.ui.showNotification('Right-click to move units', 'info');
                break;
            case 'attack':
                this.ui.showNotification('Right-click enemy to attack', 'info');
                break;
            case 'patrol':
                this.setUnitsToPatrol();
                break;
            case 'stop':
                this.stopSelectedUnits();
                break;
            case 'load':
                this.loadUnitsIntoTransport();
                break;
            case 'unload':
                this.unloadUnitsFromTransport();
                break;
        }
    }
    
    setUnitsToPatrol() {
        this.selectedUnits.forEach(unit => {
            unit.state = 'patrol';
            unit.patrolStart = { x: unit.x, y: unit.y };
        });
        this.ui.showNotification('Units set to patrol mode', 'info');
    }
    
    loadUnitsIntoTransport() {
        const transports = this.selectedUnits.filter(unit => unit.transport > 0);
        const infantry = this.selectedUnits.filter(unit =>
            unit.category === 'infantry' && !unit.transported);
        
        transports.forEach(transport => {
            let loaded = 0;
            infantry.forEach(unit => {
                if (loaded < transport.transport && !unit.transported) {
                    unit.transported = true;
                    unit.transportedBy = transport;
                    transport.passengers = transport.passengers || [];
                    transport.passengers.push(unit);
                    loaded++;
                }
            });
        });
        
        this.ui.showNotification('Units loaded into transports', 'info');
    }
    
    unloadUnitsFromTransport() {
        const transports = this.selectedUnits.filter(unit =>
            unit.transport > 0 && unit.passengers && unit.passengers.length > 0);
        
        transports.forEach(transport => {
            if (transport.passengers) {
                transport.passengers.forEach(passenger => {
                    passenger.transported = false;
                    passenger.transportedBy = null;
                    passenger.x = transport.x + (Math.random() - 0.5) * 100;
                    passenger.y = transport.y + (Math.random() - 0.5) * 100;
                });
                transport.passengers = [];
            }
        });
        
        this.ui.showNotification('Units unloaded from transports', 'info');
    }
    
    // Enhanced building methods
    enhancedAddBuilding(config) {
        const building = this.addBuilding(config);
        if (building && !config.instant) {
            this.animateBuildingPlacement(building);
            this.animationManager.animateConstruction(building);
        }
        return building;
    }
    
    // Debug and development methods
    enableDebugMode() {
        this.debugMode = true;
        this.ui.showNotification('Debug mode enabled', 'info');
        
        // Add debug UI elements
        this.createDebugPanel();
    }
    
    createDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'debugPanel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
        `;
        
        debugPanel.innerHTML = `
            <h3>Debug Info</h3>
            <div id="debugStats"></div>
            <button onclick="game.performanceManager.enableEmergencyMode()">Emergency Mode</button>
            <button onclick="game.performanceManager.resetToHighQuality()">High Quality</button>
        `;
        
        document.body.appendChild(debugPanel);
        
        // Update debug stats regularly
        setInterval(() => {
            const stats = this.getPerformanceStats();
            const debugStats = document.getElementById('debugStats');
            if (debugStats) {
                debugStats.innerHTML = `
                    FPS: ${stats.fps}<br>
                    Frame Time: ${stats.frameTime.toFixed(2)}ms<br>
                    Entities: ${stats.entityCount}<br>
                    Particles: ${stats.particleCount}<br>
                    Units: ${this.units.length}<br>
                    Buildings: ${this.buildings.length}<br>
                    Projectiles: ${this.projectiles.length}
                `;
            }
        }, 1000);
    }
    
    // Emergency performance recovery
    handlePerformanceIssues() {
        const stats = this.getPerformanceStats();
        if (stats.fps < 20) {
            this.performanceManager.enableEmergencyMode();
            this.ui.showNotification('Performance mode activated', 'warning');
        }
    }
    
    // Game state validation
    validateGameState() {
        const issues = [];
        
        // Check for orphaned units
        this.units.forEach(unit => {
            if (unit.team !== 'neutral' && !this.teams[unit.team]) {
                issues.push(`Unit ${unit.id} has invalid team: ${unit.team}`);
            }
        });
        
        // Check for orphaned buildings
        this.buildings.forEach(building => {
            if (!this.teams[building.team]) {
                issues.push(`Building ${building.id} has invalid team: ${building.team}`);
            }
        });
        
        // Check resource consistency
        if (this.resources.spice < 0) {
            issues.push('Negative spice detected');
        }
        
        return {
            valid: issues.length === 0,
            issues: issues
        };
    }
    
    // Cleanup and optimization
    performCleanup() {
        // Remove dead projectiles
        this.projectiles = this.projectiles.filter(p => p.health > 0);
        
        // Clean up visual effects
        this.visualEffects.particles = this.visualEffects.particles.filter(p => !p.isDead());
        this.visualEffects.effects = this.visualEffects.effects.filter(e => !e.isDead());
        
        // Clean up empty production queues
        this.productionQueues.forEach((queue, buildingId) => {
            if (queue.length === 0) {
                this.productionQueues.delete(buildingId);
            }
        });
        
        // Validate and clean up control groups
        Object.keys(this.controlGroups).forEach(groupNum => {
            this.controlGroups[groupNum] = this.controlGroups[groupNum].filter(unit =>
                unit.health > 0 && this.units.includes(unit));
            
            if (this.controlGroups[groupNum].length === 0) {
                delete this.controlGroups[groupNum];
            }
        });
        
        console.log('Game cleanup completed');
    }
    // Technology Tree UI Methods
    openTechTree() {
        const techTreeModal = document.getElementById('techTreeModal');
        if (techTreeModal) {
            techTreeModal.style.display = 'flex';
            this.paused = true;
            this.ui.openTechTree();
            this.soundManager.play('select');
        }
    }
    
    closeTechTree() {
        const techTreeModal = document.getElementById('techTreeModal');
        if (techTreeModal) {
            techTreeModal.style.display = 'none';
            this.paused = false;
            this.soundManager.play('select');
        }
    }
    
    // Advanced Pathfinding Control Methods
    setPathfindingMode(mode) {
        const validModes = ['legacy', 'advanced', 'flowfield'];
        if (validModes.includes(mode)) {
            this.pathfindingMode = mode;
            this.ui.showNotification(`Pathfinding mode set to: ${mode}`, 'info');
            console.log(`Pathfinding mode changed to: ${mode}`);
        } else {
            console.warn(`Invalid pathfinding mode: ${mode}. Valid modes: ${validModes.join(', ')}`);
        }
    }
    
    toggleGroupPathfinding() {
        this.useGroupPathfinding = !this.useGroupPathfinding;
        this.ui.showNotification(`Group pathfinding: ${this.useGroupPathfinding ? 'enabled' : 'disabled'}`, 'info');
        console.log(`Group pathfinding: ${this.useGroupPathfinding ? 'enabled' : 'disabled'}`);
    }
    
    // Formation movement commands
    setFormationType(formationType) {
        const validTypes = ['line', 'column', 'wedge', 'box'];
        if (validTypes.includes(formationType) && this.selectedUnits.length > 1) {
            // Update existing formation or create new one
            if (this.unitMovementManager) {
                const leadUnit = this.selectedUnits[0];
                const groupId = this.unitMovementManager.createFormationGroup(
                    this.selectedUnits, leadUnit, formationType
                );
                this.ui.showNotification(`Formation set to: ${formationType}`, 'info');
                return groupId;
            }
        } else if (!validTypes.includes(formationType)) {
            console.warn(`Invalid formation type: ${formationType}. Valid types: ${validTypes.join(', ')}`);
        } else {
            this.ui.showNotification('Select multiple units to create formation', 'warning');
        }
        return null;
    }
    
    disbandSelectedFormation() {
        if (this.selectedUnits.length > 0 && this.selectedUnits[0].formationId) {
            const formationId = this.selectedUnits[0].formationId;
            if (this.unitMovementManager.disbandFormation(formationId)) {
                this.ui.showNotification('Formation disbanded', 'info');
            }
        } else {
            this.ui.showNotification('No formation to disband', 'warning');
        }
    }
    
    // Pathfinding debugging and visualization
    togglePathfindingDebug() {
        this.pathfindingDebug = !this.pathfindingDebug;
        this.ui.showNotification(`Pathfinding debug: ${this.pathfindingDebug ? 'enabled' : 'disabled'}`, 'info');
    }
    
    visualizeFlowField(targetX, targetY) {
        if (this.flowFieldPathfinder) {
            this.debugFlowField = this.flowFieldPathfinder.generateFlowField(targetX, targetY);
            this.debugFlowFieldTarget = { x: targetX, y: targetY };
            this.ui.showNotification('Flow field visualization enabled', 'info');
        }
    }
    
    clearPathfindingVisualization() {
        this.debugFlowField = null;
        this.debugFlowFieldTarget = null;
        this.ui.showNotification('Pathfinding visualization cleared', 'info');
    }
    
    // Performance monitoring for pathfinding
    getPathfindingStats() {
        const stats = {
            mode: this.pathfindingMode,
            groupPathfinding: this.useGroupPathfinding,
            movingUnits: this.unitMovementManager ? this.unitMovementManager.getMovementStats().movingUnits : 0,
            formationGroups: this.unitMovementManager ? this.unitMovementManager.getMovementStats().formationGroups : 0,
            advanced: this.advancedPathfinder ? this.advancedPathfinder.getPerformanceStats() : null,
            flowField: this.flowFieldPathfinder ? this.flowFieldPathfinder.getPerformanceStats() : null
        };
        
        console.log('Pathfinding Performance Stats:', stats);
        return stats;
    }
    
    // Emergency pathfinding reset
    resetPathfindingSystems() {
        console.log('Resetting pathfinding systems...');
        
        // Stop all unit movement
        if (this.unitMovementManager) {
            this.unitMovementManager.emergencyStop();
        }
        
        // Clear all pathfinding caches
        if (this.advancedPathfinder) {
            this.advancedPathfinder.pathCache.clear();
            this.advancedPathfinder.pathValidationCache.clear();
        }
        
        if (this.flowFieldPathfinder) {
            this.flowFieldPathfinder.flowFieldCache.clear();
        }
        
        // Update all grids
        this.pathfinder.updateGridObstacles();
        this.advancedPathfinder.updateGridObstacles();
        this.flowFieldPathfinder.updateCostField();
        
        this.ui.showNotification('Pathfinding systems reset', 'info');
    }
    
    // Stress test for pathfinding performance
    stressTestPathfinding(numUnits = 50) {
        console.log(`Starting pathfinding stress test with ${numUnits} units...`);
        const startTime = performance.now();
        
        // Create test units if needed
        const testUnits = [];
        for (let i = 0; i < numUnits; i++) {
            const x = Math.random() * this.width;
            const y = Math.random() * this.height;
            const targetX = Math.random() * this.width;
            const targetY = Math.random() * this.height;
            
            const path = this.findPathForUnit(null, x, y, targetX, targetY);
            testUnits.push({ path, length: path ? path.length : 0 });
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        const stats = {
            duration: duration.toFixed(2) + 'ms',
            unitsProcessed: numUnits,
            averageTime: (duration / numUnits).toFixed(2) + 'ms per unit',
            successfulPaths: testUnits.filter(u => u.length > 0).length,
            averagePathLength: testUnits.reduce((sum, u) => sum + u.length, 0) / testUnits.length
        };
        
        console.log('Pathfinding Stress Test Results:', stats);
        this.ui.showNotification(`Stress test completed: ${duration.toFixed(0)}ms for ${numUnits} units`, 'info');
        
        return stats;
    }
    
    // Debug visualization for pathfinding
    drawPathfindingDebug(ctx) {
        if (!this.pathfindingDebug) return;
        
        ctx.save();
        
        // Draw flow field if available
        if (this.debugFlowField && this.flowFieldPathfinder) {
            this.flowFieldPathfinder.visualizeFlowField(ctx, this.debugFlowField);
            
            // Draw target marker
            if (this.debugFlowFieldTarget) {
                ctx.fillStyle = '#ff0000';
                ctx.beginPath();
                ctx.arc(this.debugFlowFieldTarget.x, this.debugFlowFieldTarget.y, 10, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(this.debugFlowFieldTarget.x, this.debugFlowFieldTarget.y, 15, 0, Math.PI * 2);
                ctx.stroke();
            }
        }
        
        // Draw unit paths
        this.units.forEach(unit => {
            if (unit.path && unit.path.length > 1) {
                ctx.strokeStyle = unit.team === 'player' ? '#00ff00' : '#ff0000';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                
                ctx.beginPath();
                ctx.moveTo(unit.x, unit.y);
                
                for (let i = unit.pathIndex; i < unit.path.length; i++) {
                    const waypoint = unit.path[i];
                    ctx.lineTo(waypoint.x, waypoint.y);
                }
                
                ctx.stroke();
                ctx.setLineDash([]);
                
                // Draw waypoints
                for (let i = unit.pathIndex; i < unit.path.length; i++) {
                    const waypoint = unit.path[i];
                    ctx.fillStyle = i === unit.pathIndex ? '#ffff00' : '#ffffff';
                    ctx.beginPath();
                    ctx.arc(waypoint.x, waypoint.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // Draw unit collision radius
            if (unit.selected) {
                ctx.strokeStyle = '#00ffff';
                ctx.lineWidth = 1;
                ctx.setLineDash([3, 3]);
                ctx.beginPath();
                const collisionRadius = (unit.size || 10) + 15; // Same as UnitMovementManager
                ctx.arc(unit.x, unit.y, collisionRadius, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
            }
        });
        
        // Draw formation groups
        if (this.unitMovementManager) {
            this.unitMovementManager.formationGroups.forEach((formation, groupId) => {
                if (formation.units.length > 1) {
                    // Draw formation bounds
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    formation.units.forEach(unit => {
                        minX = Math.min(minX, unit.x);
                        minY = Math.min(minY, unit.y);
                        maxX = Math.max(maxX, unit.x);
                        maxY = Math.max(maxY, unit.y);
                    });
                    
                    ctx.strokeStyle = '#ffaa00';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([10, 5]);
                    ctx.strokeRect(minX - 20, minY - 20, maxX - minX + 40, maxY - minY + 40);
                    ctx.setLineDash([]);
                    
                    // Draw formation center
                    const centerX = (minX + maxX) / 2;
                    const centerY = (minY + maxY) / 2;
                    ctx.fillStyle = '#ffaa00';
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Draw formation ID
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`Formation ${groupId.slice(-4)}`, centerX, centerY - 10);
                }
            });
        }
        
        // Draw pathfinding grid overlay
        if (this.pathfindingDebug && this.advancedPathfinder) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            
            const gridSize = this.advancedPathfinder.gridSize;
            for (let x = 0; x <= this.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, this.height);
                ctx.stroke();
            }
            
            for (let y = 0; y <= this.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(this.width, y);
                ctx.stroke();
            }
        }
        
        ctx.restore();
    }
}
