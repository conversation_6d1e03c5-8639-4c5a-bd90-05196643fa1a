/**
 * GameState - Centralized game state management
 * Manages core game state, resources, teams, and game progression
 */
export class GameState {
    constructor(eventBus) {
        this.eventBus = eventBus;
        this.initializeState();
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.eventBus.on('combat:entity_death', (data) => {
            if (data.entity) {
                if (data.entity.isUnit) {
                    this.removeUnit(data.entity);
                } else if (data.entity.isBuilding) { // Assuming buildings have an isBuilding property
                    this.removeBuilding(data.entity);
                }
            }
        });
    }

    initializeState() {
        // Core game dimensions and viewport
        this.world = {
            width: 3000,
            height: 3000,
            viewX: 0,
            viewY: 0,
            zoom: 1
        };

        // Legacy resources object for backward compatibility
        this.resources = { 
            spice: 3000, 
            power: 0, 
            maxPower: 0 
        };

        // Game entities
        this.entities = {
            buildings: [],
            units: [],
            projectiles: [],
            spiceFields: [],
            terrain: []
        };

        // Selection state
        this.selection = {
            selectedUnits: [],
            selectedBuilding: null,
            buildingMode: null,
            ghostBuilding: null
        };

        // Team configuration - EXPANDED FOR FACTION-VS-FACTION COMBAT
        this.teams = {
            player: {
                color: '#00f',
                buildings: [],
                units: [],
                upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 },
                stats: {
                    unitsBuilt: 0,
                    unitsLost: 0,
                    buildingsBuilt: 0,
                    buildingsLost: 0,
                    spiceCollected: 0,
                    enemiesDestroyed: 0
                }
            },
            enemy: {
                color: '#f00',
                buildings: [],
                units: [],
                upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 }
            },
            faction2: {
                color: '#0f0',
                buildings: [],
                units: [],
                upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 }
            },
            faction3: {
                color: '#ff0',
                buildings: [],
                units: [],
                upgrades: { armor: 0, damage: 0, speed: 0, harvesting: 0 }
            }
        };
        
        console.log('🏗️ DUNE2 DEBUG: GameState teams initialized:', Object.keys(this.teams));

        // Game timing and state
        this.timing = {
            paused: false,
            gameTime: 0,
            lastTimestamp: 0,
            lastSpiceRegenTime: 0
        };

        // Music and audio state
        this.musicState = {
            currentState: 'peaceful',
            combatUnits: 0,
            buildingActivity: 0,
            lastCombatTime: 0,
            lastBuildTime: 0
        };

        // Fog of war
        this.fogOfWar = {
            data: [],
            gridSize: 50,
            visionRadius: 250
        };

        // UI state
        this.ui = {
            currentBuildTab: 'buildings',
            productionQueues: new Map(),
            controlGroups: {}
        };

        // Feature flags
        this.features = {
            diplomacyEnabled: false,
            terraformingEnabled: false,
            pathfindingMode: 'advanced',
            useGroupPathfinding: true,
            pathfindingDebug: false
        };

        // Technology effects storage
        this.technologyEffects = {};

        // General game configuration
        this.config = {
            defaultVisionRadius: 250, // Default vision radius for entities
            loadInitialUnits: true, // Controls if initial units are loaded
            // Add other game-wide configurations here
        };
        console.log('🔧 DUNE2 DEBUG: GameState.config initialized:', this.config);
    }

    /**
     * Add a building to the game state
     */
    addBuilding(building) {
        this.entities.buildings.push(building);
        this.teams[building.team].buildings.push(building);
        
        this.eventBus.emit('building:added', { building });
        
        if (building.team === 'player') {
            this.teams.player.stats.buildingsBuilt++;
            this.eventBus.emit('stats:updated', { 
                type: 'buildingsBuilt', 
                value: this.teams.player.stats.buildingsBuilt 
            });
        }
    }

    /**
     * Remove a building from the game state
     */
    removeBuilding(building) {
        const buildingIndex = this.entities.buildings.indexOf(building);
        if (buildingIndex !== -1) {
            this.entities.buildings.splice(buildingIndex, 1);
        }

        const teamIndex = this.teams[building.team].buildings.indexOf(building);
        if (teamIndex !== -1) {
            this.teams[building.team].buildings.splice(teamIndex, 1);
        }

        if (this.selection.selectedBuilding === building) {
            this.selection.selectedBuilding = null;
        }

        if (this.ui.productionQueues.has(building.id)) {
            this.ui.productionQueues.delete(building.id);
        }

        this.eventBus.emit('building:removed', { building });

        if (building.team === 'player') {
            this.teams.player.stats.buildingsLost++;
            this.eventBus.emit('stats:updated', { 
                type: 'buildingsLost', 
                value: this.teams.player.stats.buildingsLost 
            });
        }
    }

    /**
     * Add a unit to the game state
     */
    addUnit(unit) {
        this.entities.units.push(unit);
        
        // Only add to teams array if it's not a neutral unit
        if (unit.team !== 'neutral' && this.teams[unit.team]) {
            this.teams[unit.team].units.push(unit);
        }
        
        this.eventBus.emit('unit:added', { unit });
        
        if (unit.team === 'player') {
            this.teams.player.stats.unitsBuilt++;
            this.eventBus.emit('stats:updated', { 
                type: 'unitsBuilt', 
                value: this.teams.player.stats.unitsBuilt 
            });
        }
    }

    /**
     * Remove a unit from the game state
     */
    removeUnit(unit) {
        const unitIndex = this.entities.units.indexOf(unit);
        if (unitIndex !== -1) {
            this.entities.units.splice(unitIndex, 1);
        }

        // Only remove from team arrays if it's not a neutral unit
        if (unit.team !== 'neutral' && this.teams[unit.team]) {
            const teamIndex = this.teams[unit.team].units.indexOf(unit);
            if (teamIndex !== -1) {
                this.teams[unit.team].units.splice(teamIndex, 1);
            }
        }

        const selectedIndex = this.selection.selectedUnits.indexOf(unit);
        if (selectedIndex !== -1) {
            this.selection.selectedUnits.splice(selectedIndex, 1);
        }

        this.eventBus.emit('unit:removed', { unit });

        if (unit.team === 'player') {
            this.teams.player.stats.unitsLost++;
            this.eventBus.emit('stats:updated', { 
                type: 'unitsLost', 
                value: this.teams.player.stats.unitsLost 
            });
        } else if (unit.team !== 'neutral') {
            this.teams.player.stats.enemiesDestroyed++;
            this.eventBus.emit('stats:updated', { 
                type: 'enemiesDestroyed', 
                value: this.teams.player.stats.enemiesDestroyed 
            });
        }
    }

    /**
     * Add a projectile to the game state
     */
    addProjectile(projectile) {
        this.entities.projectiles.push(projectile);
        this.eventBus.emit('projectile:added', { projectile });
    }

    /**
     * Remove a projectile from the game state
     */
    removeProjectile(projectile) {
        const index = this.entities.projectiles.indexOf(projectile);
        if (index !== -1) {
            this.entities.projectiles.splice(index, 1);
            this.eventBus.emit('projectile:removed', { projectile });
        }
    }

    /**
     * Update power calculations
     */
    updatePower() {
        let production = 0;
        let consumption = 0;
        
        this.teams.player.buildings.forEach(building => {
            if (building.constructionProgress >= 100) {
                production += building.provides || 0;
                consumption += building.power || 0;
            }
        });
        
        this.resources.power = production - consumption;
        this.resources.maxPower = production;
        
        this.eventBus.emit('power:updated', {
            power: this.resources.power,
            maxPower: this.resources.maxPower,
            production,
            consumption
        });
    }

    /**
     * Update selection state
     */
    setSelectedUnits(units) {
        // Clear previous selection
        this.selection.selectedUnits.forEach(unit => {
            unit.selected = false;
        });
        
        this.selection.selectedUnits = [...units];
        this.selection.selectedBuilding = null;
        
        // Mark new selection
        this.selection.selectedUnits.forEach(unit => {
            unit.selected = true;
        });
        
        this.eventBus.emit('selection:changed', { 
            selectedUnits: this.selection.selectedUnits,
            selectedBuilding: null
        });
    }

    /**
     * Set selected building
     */
    setSelectedBuilding(building) {
        // Clear unit selection
        this.selection.selectedUnits.forEach(unit => {
            unit.selected = false;
        });
        this.selection.selectedUnits = [];
        
        this.selection.selectedBuilding = building;
        
        this.eventBus.emit('selection:changed', { 
            selectedUnits: [],
            selectedBuilding: building
        });
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        this.selection.selectedUnits.forEach(unit => {
            unit.selected = false;
        });
        this.selection.selectedUnits = [];
        this.selection.selectedBuilding = null;
        
        this.eventBus.emit('selection:changed', { 
            selectedUnits: [],
            selectedBuilding: null
        });
    }

    /**
     * Update game timing
     */
    updateTiming(timestamp, deltaTime) {
        this.timing.lastTimestamp = timestamp;
        if (!this.timing.paused) {
            this.timing.gameTime += deltaTime * 60;
        }
        
        this.eventBus.emit('timing:updated', {
            gameTime: this.timing.gameTime,
            deltaTime,
            paused: this.timing.paused
        });
    }

    /**
     * Toggle pause state
     */
    togglePause() {
        this.timing.paused = !this.timing.paused;
        this.eventBus.emit('game:pause', { paused: this.timing.paused });
        
        if (!this.timing.paused) {
            this.timing.lastTimestamp = performance.now();
        }
    }

    /**
     * Update viewport
     */
    updateViewport(viewX, viewY, zoom) {
        this.world.viewX = viewX;
        this.world.viewY = viewY;
        this.world.zoom = zoom;
        
        this.eventBus.emit('viewport:updated', {
            viewX,
            viewY,
            zoom
        });
    }

    /**
     * Get game statistics
     */
    getStats() {
        return {
            ...this.teams.player.stats,
            gameTime: this.timing.gameTime,
            totalUnits: this.entities.units.length,
            totalBuildings: this.entities.buildings.length
        };
    }

    /**
     * Check win conditions
     */
    checkWinConditions() {
        const enemyBases = this.teams.enemy.buildings.filter(b => 
            b.type === 'base' && b.health > 0
        );
        const playerBases = this.teams.player.buildings.filter(b => 
            b.type === 'base' && b.health > 0
        );
        
        if (enemyBases.length === 0 && 
            this.teams.enemy.buildings.length === 0 && 
            this.teams.enemy.units.length === 0) {
            this.eventBus.emit('game:victory');
            return 'victory';
        } else if (playerBases.length === 0 && 
                   this.teams.player.buildings.length === 0) {
            this.eventBus.emit('game:defeat');
            return 'defeat';
        }
        
        return null;
    }

    /**
     * Get current game state snapshot
     */
    getSnapshot() {
        return {
            world: { ...this.world },
            resources: { ...this.resources },
            timing: { ...this.timing },
            musicState: { ...this.musicState },
            features: { ...this.features },
            stats: this.getStats()
        };
    }
}