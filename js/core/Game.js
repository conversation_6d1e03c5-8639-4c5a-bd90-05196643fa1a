// Import existing systems for integration
import { AIController } from '../AIController.js';
import { AdvancedPathfinder } from '../AdvancedPathfinder.js';
import { AnimationManager } from '../AnimationManager.js';
import { Building } from '../entities/Building.js';
import { CombatSystem } from '../systems/CombatSystem.js';
import { DiplomacySystem } from '../DiplomacySystem.js';
import { DynamicMapSystem } from '../DynamicMapSystem.js';
import { EnvironmentalSystem } from '../EnvironmentalSystem.js';
/**
 * Game - Main game coordinator class
 * Orchestrates all game systems and maintains backward compatibility
 */
import { EventBus } from './EventBus.js';
import { FlowFieldPathfinder } from '../FlowFieldPathfinder.js';
import { GameBalanceManager } from '../GameBalanceManager.js';
import { GameData } from '../GameData.js';
import { GameLoop } from './GameLoop.js';
import { GameState } from './GameState.js';
import { InputSystem } from '../systems/InputSystem.js';
import { LevelingSystem } from '../LevelingSystem.js';
import { LootSystem } from '../LootSystem.js';
import NeutralEnemies from '../NeutralEnemies.js';
import { Pathfinder } from '../Pathfinder.js';
import { PerformanceManager } from '../PerformanceManager.js';
import { Projectile } from '../entities/Projectile.js';
import { RenderingSystem } from '../systems/RenderingSystem.js';
import { ResourceSystem } from '../systems/ResourceSystem.js';
import { SaveLoadManager } from '../SaveLoadManager.js';
import { SelectionManager } from '../ui/SelectionManager.js';
import { SoundManager } from '../SoundManager.js';
import { TechnologySystem } from '../TechnologySystem.js';
import { UI } from '../UI.js';
import { Unit } from '../entities/Unit.js';
import { UnitMovementManager } from '../UnitMovementManager.js';
import { UserExperienceManager } from '../UserExperienceManager.js';
import { VictorySystem } from '../VictorySystem.js';
import { VisualEffects } from '../VisualEffects.js';
import { createAIIntegration } from '../ai/AIIntegration.js'; // Added AIIntegration
import { setupTestBattleScene as setupTestBattleSceneExternal } from '../debug/TestScenarios.js'; // For test battle setup

export class Game {
    constructor() {
        try {
            console.log('🎮 DUNE2 DEBUG: Game constructor starting...');
            
            // Initialize core systems
            this.eventBus = new EventBus();
            console.log('🎮 DUNE2 DEBUG: EventBus created');
            
            this.gameState = new GameState(this.eventBus);
            console.log('🎮 DUNE2 DEBUG: GameState created, resources:', this.gameState.resources);

            // --- TEST BATTLE SCENE SETUP ---
            // Disable default unit loading for test battle
            this.gameState.config.loadInitialUnits = false;
            // --- END TEST BATTLE SCENE SETUP ---

            this.gameLoop = new GameLoop(this.eventBus, this.gameState);
            console.log('🎮 DUNE2 DEBUG: GameLoop created');

            // Initialize game systems
            this.renderingSystem = new RenderingSystem(this.eventBus, this.gameState);
            this.inputSystem = new InputSystem(this.eventBus, this.gameState);
            this.resourceSystem = new ResourceSystem(this.eventBus, this.gameState);
            this.combatSystem = new CombatSystem(this.eventBus, this.gameState);
            this.selectionManager = new SelectionManager(this.eventBus, this.gameState);

            // Legacy compatibility properties (must be set up before UI creation)
            this.setupLegacyCompatibility();

            // Initialize legacy systems for compatibility
            this.initializeLegacySystems();

            // Initialize Advanced AI System
            
            this.aiIntegration = createAIIntegration(this);
            this.aiIntegration.initialize();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Register systems with game loop
            this.registerSystems();
            
            // Initialize game data
            this.buildingTypes = GameData.getBuildingTypes();
            this.unitTypes = GameData.getUnitTypes();
            
            this.init();

            // --- TEST BATTLE SCENE SETUP ---
            // Setup the test battle scene after core initialization
            this.setupTestBattleScene();
            // --- END TEST BATTLE SCENE SETUP ---

        } catch (error) {
            throw error;
        }
    }

    /**
     * Initialize legacy systems for backward compatibility
     */
    initializeLegacySystems() {
        try {
            this.soundManager = new SoundManager();
            this.pathfinder = new AdvancedPathfinder(this);
            this.flowFieldPathfinder = new FlowFieldPathfinder(this);
            this.unitMovementManager = new UnitMovementManager(this);
            this.ui = new UI(this);
            // Create AI controllers for all non-player teams
            this.aiControllers = new Map();
            this.aiControllers.set('enemy', new AIController(this, 'enemy'));
            this.aiControllers.set('faction2', new AIController(this, 'faction2'));
            this.aiControllers.set('faction3', new AIController(this, 'faction3'));
            
            // Keep reference to main AI controller for backward compatibility
            this.aiController = this.aiControllers.get('enemy');
            
            // Phase 2 Advanced Mechanics systems
            this.environmentalSystem = new EnvironmentalSystem(this);
            this.technologySystem = new TechnologySystem(this);
            
            // Phase 3 Strategic Depth systems
            this.diplomacySystem = new DiplomacySystem(this);
            this.dynamicMapSystem = new DynamicMapSystem(this);
            this.victorySystem = new VictorySystem(this);
            
            // Phase 4 Polish & Integration systems
            this.performanceManager = new PerformanceManager(this);
            this.balanceManager = new GameBalanceManager(this);
            this.userExperienceManager = new UserExperienceManager(this);
            this.saveLoadManager = new SaveLoadManager(this);
            this.animationManager = new AnimationManager(this);
            
            // Additional systems
            this.levelingSystem = new LevelingSystem();
            this.lootSystem = new LootSystem(this);
            this.neutralEnemies = new NeutralEnemies(this);
            this.visualEffects = new VisualEffects(this);
        } catch (error) {
            throw error;
        }
    }

    /**
     * Setup legacy compatibility properties
     */
    setupLegacyCompatibility() {
        console.log('🔧 DUNE2 DEBUG: Setting up legacy compatibility...');
        console.log('🔧 DUNE2 DEBUG: GameState exists:', !!this.gameState);
        console.log('🔧 DUNE2 DEBUG: GameState.resources exists:', !!this.gameState?.resources);
        console.log('🔧 DUNE2 DEBUG: GameState.resources value:', this.gameState?.resources);
        
        // Expose gameState properties for backward compatibility
        Object.defineProperty(this, 'resources', {
            get: () => {
                console.log('🔧 DUNE2 DEBUG: Resources getter called, returning:', this.gameState?.resources);
                return this.gameState.resources;
            },
            set: (value) => {
                console.log('🔧 DUNE2 DEBUG: Resources setter called with:', value);
                this.gameState.resources = value;
            }
        });
        
        console.log('🔧 DUNE2 DEBUG: Resources property defined, testing access...');
        console.log('🔧 DUNE2 DEBUG: this.resources =', this.resources);
        
        Object.defineProperty(this, 'buildings', {
            get: () => this.gameState.entities.buildings,
            set: (value) => this.gameState.entities.buildings = value
        });
        
        Object.defineProperty(this, 'units', {
            get: () => this.gameState.entities.units,
            set: (value) => this.gameState.entities.units = value
        });
        
        Object.defineProperty(this, 'projectiles', {
            get: () => this.gameState.entities.projectiles,
            set: (value) => this.gameState.entities.projectiles = value
        });
        
        Object.defineProperty(this, 'spiceFields', {
            get: () => this.gameState.entities.spiceFields,
            set: (value) => this.gameState.entities.spiceFields = value
        });
        
        Object.defineProperty(this, 'terrain', {
            get: () => this.gameState.entities.terrain,
            set: (value) => this.gameState.entities.terrain = value
        });
        
        Object.defineProperty(this, 'selectedUnits', {
            get: () => this.gameState.selection.selectedUnits,
            set: (value) => this.gameState.selection.selectedUnits = value
        });
        
        Object.defineProperty(this, 'selectedBuilding', {
            get: () => this.gameState.selection.selectedBuilding,
            set: (value) => this.gameState.selection.selectedBuilding = value
        });
        
        Object.defineProperty(this, 'teams', {
            get: () => this.gameState.teams,
            set: (value) => this.gameState.teams = value
        });
        
        Object.defineProperty(this, 'paused', {
            get: () => this.gameState.timing.paused,
            set: (value) => this.gameState.timing.paused = value
        });
        
        // Expose world properties
        Object.defineProperty(this, 'width', {
            get: () => this.gameState.world.width,
            set: (value) => this.gameState.world.width = value
        });
        
        Object.defineProperty(this, 'height', {
            get: () => this.gameState.world.height,
            set: (value) => this.gameState.world.height = value
        });
        
        Object.defineProperty(this, 'viewX', {
            get: () => this.gameState.world.viewX,
            set: (value) => this.gameState.world.viewX = value
        });
        
        Object.defineProperty(this, 'viewY', {
            get: () => this.gameState.world.viewY,
            set: (value) => this.gameState.world.viewY = value
        });
        
        Object.defineProperty(this, 'zoom', {
            get: () => this.gameState.world.zoom,
            set: (value) => this.gameState.world.zoom = value
        });
        
        // Resource system compatibility
        Object.defineProperty(this, 'resourceManager', {
            get: () => this.resourceSystem,
            set: (value) => this.resourceSystem = value
        });
        
        // Canvas references for legacy compatibility
        this.gameCanvas = document.getElementById('gameCanvas');
        this.ctx = this.gameCanvas.getContext('2d');
        this.minimapCanvas = document.getElementById('minimapCanvas');
        this.minimapCtx = this.minimapCanvas.getContext('2d');
        this.gameAreaEl = document.getElementById('gameArea');
        
        // MinimapManager compatibility properties
        Object.defineProperty(this, 'canvas', {
            get: () => this.gameCanvas
        });
        
        Object.defineProperty(this, 'camera', {
            get: () => ({
                x: this.gameState.world.viewX,
                y: this.gameState.world.viewY
            }),
            set: (value) => {
                if (value && typeof value === 'object') {
                    if (typeof value.x === 'number') this.gameState.world.viewX = value.x;
                    if (typeof value.y === 'number') this.gameState.world.viewY = value.y;
                }
            }
        });
        
        Object.defineProperty(this, 'mapWidth', {
            get: () => this.gameState.world.width,
            set: (value) => this.gameState.world.width = value
        });
        
        Object.defineProperty(this, 'mapHeight', {
            get: () => this.gameState.world.height,
            set: (value) => this.gameState.world.height = value
        });
    }

    /**
     * Setup event listeners for system communication
     */
    setupEventListeners() {
        // Input system events
        this.eventBus.on('input:right_click', (data) => this.handleRightClick(data.x, data.y));
        this.eventBus.on('input:place_building', (data) => this.placeBuilding(data.x, data.y));
        this.eventBus.on('input:selection_end', (data) => this.endSelection(data.startX, data.startY, data.endX, data.endY));
        this.eventBus.on('input:window_resize', () => this.handleWindowResize());
        
        // Building placement events - DIAGNOSTIC LOGS
        this.eventBus.on('input:update_ghost_building', (data) => {
            console.log('🏗️ DUNE2 DEBUG: input:update_ghost_building event received:', data);
            this.updateGhostBuilding(data.x, data.y);
        });
        
        this.eventBus.on('input:cancel_building_mode', () => {
            console.log('🏗️ DUNE2 DEBUG: input:cancel_building_mode event received');
            this.cancelBuildingMode();
        });
        
        // Additional combat visual effects events
        this.eventBus.on('combat:critical_hit', (event) => {
            console.log(`💥 GAME DEBUG: Critical hit at (${event.target.x}, ${event.target.y})`);
            if (this.visualEffects) {
                // Enhanced hit effect for critical hits
                if (this.visualEffects.createHitSpark) {
                    this.visualEffects.createHitSpark(event.target.x, event.target.y);
                }
                // Screen shake for critical hits
                if (this.visualEffects.createScreenShake) {
                    this.visualEffects.createScreenShake(8, 300);
                }
                // Critical hit text
                if (this.visualEffects.createFloatingText) {
                    this.visualEffects.createFloatingText(event.target.x, event.target.y - 30, 'CRITICAL!', '#FF0000', 18, 2000);
                }
            }
        });
        
        this.eventBus.on('combat:healed', (event) => {
            console.log(`💚 GAME DEBUG: Healing at (${event.x}, ${event.y}), amount: ${event.amount}`);
            if (this.visualEffects && this.visualEffects.createHealingNumber && event.amount) {
                this.visualEffects.createHealingNumber(event.x, event.y, event.amount);
            }
        });
        
        // Combat system events
        this.eventBus.on('combat:projectile_fired', (event) => {
            console.log(`🚀 GAME DEBUG: Received projectile_fired event:`, {
                hasAttacker: !!event.attacker,
                hasTarget: !!event.target,
                startX: event.startX,
                startY: event.startY,
                targetX: event.targetX,
                targetY: event.targetY,
                speed: event.speed,
                damage: event.calculatedDamage,
                type: event.projectileType
            });
            
            // Create muzzle flash effect
            if (this.visualEffects && this.visualEffects.createMuzzleFlash && event.attacker) {
                const direction = Math.atan2(event.targetY - event.startY, event.targetX - event.startX);
                this.visualEffects.createMuzzleFlash(event.startX, event.startY, direction, 1);
            }
            
            // Ensure all parameters from the event payload are correctly passed
            // to the updated createProjectile method.
            // The event payload from CombatSystem.js is assumed to contain all these fields.
            this.createProjectile(
                event.attacker,         // source entity (e.g., a Unit or Building instance)
                event.target,           // target entity (e.g., a Unit or Building instance)
                event.startX,           // projectile's starting X coordinate
                event.startY,           // projectile's starting Y coordinate
                event.targetX,          // projectile's target X coordinate (can be entity's current X)
                event.targetY,          // projectile's target Y coordinate (can be entity's current Y)
                event.speed,            // speed of the projectile
                event.range,            // maximum range of the projectile
                event.calculatedDamage, // calculated damage for this projectile instance
                event.projectileType,   // type of projectile (string or identifier)
                event.visual,           // visual representation data for the projectile
                event.soundFire,        // sound to play on firing
                event.soundHit,         // sound to play on hit
                event.onHitCallback,    // custom callback function on hit
                event.onExpireCallback, // custom callback function on expiration
                event.options           // additional options for the projectile
            );
        });
        
        // Resource system events
        this.eventBus.on('resources:power_updated', () => this.ui.updateResourceDisplay());
        
        // Rendering system events
        this.eventBus.on('rendering:effects_requested', (data) => this.visualEffects.draw(data.ctx));
        this.eventBus.on('rendering:ui_requested', (data) => this.renderUIElements(data.ctx));
        
        // Combat visual effects events
        this.eventBus.on('combat:hit', (event) => {
            console.log(`✨ GAME DEBUG: Handling combat:hit event at (${event.x}, ${event.y}), damage: ${event.damage}`);
            if (this.visualEffects) {
                // Create hit spark effect
                if (this.visualEffects.createHitSpark) {
                    this.visualEffects.createHitSpark(event.x, event.y);
                }
                
                // Create damage number
                if (this.visualEffects.createDamageNumber && event.damage) {
                    // Check if it's a critical hit (you can extend this logic)
                    const isCritical = event.damage > (event.target?.maxHealth * 0.2) || false;
                    this.visualEffects.createDamageNumber(event.x, event.y, event.damage, isCritical);
                }
            }
        });
        
        this.eventBus.on('combat:entity_death', (event) => {
            console.log(`💀 GAME DEBUG: Handling combat:entity_death event at (${event.x}, ${event.y})`);
            if (this.visualEffects) {
                // Create death explosion
                if (this.visualEffects.createExplosion) {
                    const explosionSize = event.entity?.type === 'tank' ? 50 : 30;
                    this.visualEffects.createExplosion(event.x, event.y, explosionSize, 'death');
                }
                
                // Create death text
                if (this.visualEffects.createFloatingText) {
                    this.visualEffects.createFloatingText(event.x, event.y - 20, 'DESTROYED', '#FF4444', 16, 2000);
                }
            }
        });
        
        // Game loop events - Register RenderingSystem as a proper system instead of event-driven
        // Remove the infinite render loop by not calling render on every frame event
        // this.eventBus.on('gameloop:frame', () => this.renderingSystem.render());
    }

    /**
     * Register systems with the game loop
     */
    registerSystems() {
        this.gameLoop.registerSystem(this.inputSystem, 10);
        this.gameLoop.registerSystem(this.selectionManager, 15);
        this.gameLoop.registerSystem(this.resourceSystem, 20);
        this.gameLoop.registerSystem(this.combatSystem, 30);
        this.gameLoop.registerSystem(this.environmentalSystem, 40);
        this.gameLoop.registerSystem(this.technologySystem, 50);
        this.gameLoop.registerSystem(this.diplomacySystem, 60);
        this.gameLoop.registerSystem(this.dynamicMapSystem, 70);
        this.gameLoop.registerSystem(this.victorySystem, 80);
        this.gameLoop.registerSystem(this.lootSystem, 90);
        this.gameLoop.registerSystem(this.neutralEnemies, 100);
        this.gameLoop.registerSystem(this.visualEffects, 110);
        this.gameLoop.registerSystem(this.unitMovementManager, 120);
        this.gameLoop.registerSystem(this.performanceManager, 130);
        this.gameLoop.registerSystem(this.userExperienceManager, 140);
        this.gameLoop.registerSystem(this.animationManager, 150);
        // Register Game instance itself to call its update method (for fog of war, etc.)
        this.gameLoop.registerSystem(this, 155); // Before AIIntegration and RenderingSystem
        // Register AIIntegration system
        if (this.aiIntegration && this.aiIntegration.isEnabled) {
            this.gameLoop.registerSystem(this.aiIntegration, 158); // After Game, before Rendering
            console.log('✅ DUNE2 DEBUG: AIIntegration registered with GameLoop.');
        } else {
            console.warn('⚠️ DUNE2 DEBUG: AIIntegration not registered with GameLoop:', {
                hasAIIntegration: !!this.aiIntegration,
                isEnabled: this.aiIntegration?.isEnabled
            });
        }
        
        // Register all AI controllers
        if (this.aiControllers) {
            this.aiControllers.forEach((aiController, teamId) => {
                console.log(`✅ DUNE2 DEBUG: Registering AIController for team ${teamId}`);
                this.gameLoop.registerSystem(aiController, 157); // Before AIIntegration
            });
        } else if (this.aiController) {
            console.log('✅ DUNE2 DEBUG: Registering basic AIController as fallback');
            this.gameLoop.registerSystem(this.aiController, 157); // Before AIIntegration
        }
        // Register RenderingSystem as the last system to run each frame
        this.gameLoop.registerSystem(this.renderingSystem, 160);
    }

    /**
     * Initialize the game
     */
    init() {
        this.renderingSystem.initializeCanvas();
        this.generateMap();
        this.createInitialBase();
        this.initFogOfWar();
        this.updateFogOfWar();
        this.ui.updateResourceDisplay();
        this.ui.updateBuildPanel();
        
        // Initialize pathfinding systems
        this.pathfinder.updateGridObstacles(); // This now calls AdvancedPathfinder's method
        // this.advancedPathfinder.updateGridObstacles(); // Redundant, as pathfinder is the advanced one
        this.flowFieldPathfinder.updateCostField();
        
        // Center camera on player base (400, 400)
        const canvasWidth = this.gameCanvas.width;
        const canvasHeight = this.gameCanvas.height;
        
        this.gameState.world.viewX = 400 - (canvasWidth / 2);
        this.gameState.world.viewY = 400 - (canvasHeight / 2);
        
        this.ui.drawMinimapStatic();
        this.updateViewport();
        
        // Start the game loop
        this.gameLoop.start();
    }

    /**
     * Legacy method implementations for backward compatibility
     */
    
    addBuilding(config) {
        console.log('🏗️ DUNE2 DEBUG: addBuilding called with config:', config);
        
        const building = new Building(config.x, config.y, config.type, config.team);
        
        console.log('🏗️ DUNE2 DEBUG: Building entity created:', {
            id: building.id,
            type: building.type,
            x: building.x,
            y: building.y,
            team: building.team,
            constructionProgress: building.constructionProgress,
            isUnderConstruction: building.isUnderConstruction,
            maxConstructionTime: building.maxConstructionTime,
            health: building.health,
            maxHealth: building.maxHealth
        });
        
        // Handle instant construction
        if (config.instant) {
            console.log('🏗️ DUNE2 DEBUG: Instant construction requested');
            building.constructionProgress = 100;
            building.isUnderConstruction = false;
            building.health = building.maxHealth;
        }
        
        this.gameState.addBuilding(building);
        console.log('🏗️ DUNE2 DEBUG: Building added to game state. Total buildings:', this.gameState.entities.buildings.length);
        
        if (building.constructionProgress >= 100) {
            console.log('🏗️ DUNE2 DEBUG: Building completed, updating pathfinding');
            this.pathfinder.updateGridObstacles(); // This now calls AdvancedPathfinder's method
            // this.advancedPathfinder.updateGridObstacles(); // Redundant
            this.flowFieldPathfinder.updateCostField();
        }
        
        if (building.constructionProgress === 100 && !config.instant && building.team === 'player') {
            console.log('🏗️ DUNE2 DEBUG: Playing completion sound');
            this.soundManager.play('complete');
        }
        
        return building;
    }

    createUnit(type, x, y, team) {
        const unit = new Unit(x, y, type, team);
        this.levelingSystem.initializeEntityLevel(unit);
        this.gameState.addUnit(unit);
        this.ui.updateResourceDisplay();
        return unit;
    }

    createProjectile(source, targetEntity, startX, startY, targetX, targetY, speed, range, damage, type, visual, soundFire, soundHit, onHitCallback, onExpireCallback, options = {}) {
        console.log(`🎯 GAME DEBUG: Creating projectile:`, {
            sourceType: source?.type || 'unknown',
            targetType: targetEntity?.type || 'unknown',
            startX, startY, targetX, targetY,
            speed, range, damage, type
        });
        
        // The Projectile constructor is assumed to handle all these parameters
        // as per the problem description (no changes needed in Projectile.js).
        const projectile = new Projectile(
            this,                   // game instance
            source,
            targetEntity,
            startX,
            startY,
            targetX,
            targetY,
            speed,
            range,
            damage,
            type,
            visual,
            soundFire,
            soundHit,
            onHitCallback,
            onExpireCallback,
            options
        );
        
        console.log(`✅ GAME DEBUG: Projectile created, adding to gameState. Current projectiles: ${this.gameState.entities.projectiles.length}`);
        this.gameState.addProjectile(projectile);
        console.log(`📊 GAME DEBUG: Projectiles after add: ${this.gameState.entities.projectiles.length}`);
        return projectile;
    }

    handleRightClick(targetGameX, targetGameY) {
        // Get selected units
        const selectedUnits = this.gameState.selection.selectedUnits || [];
        const playerUnits = selectedUnits.filter(unit => unit.team === 'player');
        
        if (playerUnits.length === 0) {
            return;
        }
        
        // Issue movement commands to selected units
        playerUnits.forEach((unit, index) => {
            // Find path using pathfinder
            const path = this.pathfinder.findPath(unit.x, unit.y, targetGameX, targetGameY, unit);
            
            if (path && path.length > 0) {
                // Set unit movement state
                unit.moveTo(targetGameX, targetGameY, path);
                unit.state = 'moving'; // Ensure state compatibility with UnitMovementManager
            } else {
                // Direct movement if no path found
                unit.moveTo(targetGameX, targetGameY);
                unit.state = 'moving';
            }
        });
    }

    placeBuilding(x, y) {
        console.log('🏗️ DUNE2 DEBUG: placeBuilding called with:', { x, y });
        
        // Check if we're in building placement mode
        if (!this.placingBuilding && !this.gameState.selection.buildingMode) {
            console.log('🏗️ DUNE2 DEBUG: Not in building placement mode, aborting');
            return;
        }
        
        const buildingType = this.buildingType || this.gameState.selection.buildingMode;
        if (!buildingType) {
            console.log('🏗️ DUNE2 DEBUG: No building type specified, aborting');
            return;
        }
        
        console.log('🏗️ DUNE2 DEBUG: Placing building of type:', buildingType);
        
        // Get building data
        const buildingData = this.buildingTypes[buildingType];
        if (!buildingData) {
            console.log('🏗️ DUNE2 DEBUG: No building data found for type:', buildingType);
            return;
        }
        
        // Check resources
        if (this.resources.spice < buildingData.cost) {
            console.log('🏗️ DUNE2 DEBUG: Insufficient spice:', { need: buildingData.cost, have: this.resources.spice });
            this.ui?.showNotification(`Insufficient spice! Need ${buildingData.cost}, have ${this.resources.spice}`, 'error', 3000);
            return;
        }
        
        // Validate placement
        if (!this.isValidBuildingPlacement(x, y, buildingData)) {
            console.log('🏗️ DUNE2 DEBUG: Invalid building placement location');
            this.ui?.showNotification('Cannot place building here!', 'error', 2000);
            return;
        }
        
        console.log('🏗️ DUNE2 DEBUG: Creating building...');
        
        // Create the building
        const building = this.addBuilding({
            type: buildingType,
            x: x,
            y: y,
            team: 'player',
            instant: false
        });
        
        console.log('🏗️ DUNE2 DEBUG: Building created:', {
            id: building.id,
            type: building.type,
            x: building.x,
            y: building.y,
            constructionProgress: building.constructionProgress,
            isUnderConstruction: building.isUnderConstruction,
            health: building.health,
            maxHealth: building.maxHealth
        });
        
        // Deduct resources
        this.resources.spice -= buildingData.cost;
        console.log('🏗️ DUNE2 DEBUG: Resources deducted. New spice amount:', this.resources.spice);
        
        // Clear building placement mode
        this.placingBuilding = false;
        this.buildingType = null;
        this.gameState.selection.buildingMode = null;
        this.gameState.selection.ghostBuilding = null;
        
        // Restore cursor
        if (this.canvas) {
            this.canvas.style.cursor = 'default';
        }
        
        console.log('🏗️ DUNE2 DEBUG: Building placement completed successfully');
        
        // Update UI
        this.ui?.updateResourceDisplay();
        if (this.ui?.buildPanel) {
            this.ui.buildPanel.onBuildingPlaced();
        }
    }

    updateGhostBuilding(x, y) {
        console.log('🏗️ DUNE2 DEBUG: updateGhostBuilding called with:', { x, y });
        
        if (!this.gameState.selection.buildingMode) {
            console.log('🏗️ DUNE2 DEBUG: No building mode active, skipping ghost building update');
            return;
        }
        
        const buildingType = this.gameState.selection.buildingMode;
        const buildingData = this.buildingTypes[buildingType];
        
        if (!buildingData) {
            console.log('🏗️ DUNE2 DEBUG: No building data found for type:', buildingType);
            return;
        }
        
        console.log('🏗️ DUNE2 DEBUG: Creating/updating ghost building for:', buildingType);
        
        // Create or update ghost building
        this.gameState.selection.ghostBuilding = {
            type: buildingType,
            x: x,
            y: y,
            width: buildingData.width || 50,
            height: buildingData.height || 50,
            valid: this.isValidBuildingPlacement(x, y, buildingData)
        };
        
        console.log('🏗️ DUNE2 DEBUG: Ghost building updated:', this.gameState.selection.ghostBuilding);
    }
    
    isValidBuildingPlacement(x, y, buildingData) {
        console.log('🏗️ DUNE2 DEBUG: Validating building placement at:', { x, y });
        
        const halfWidth = (buildingData.width || 50) / 2;
        const halfHeight = (buildingData.height || 50) / 2;
        
        // Check map boundaries
        if (x - halfWidth < 0 || x + halfWidth > this.gameState.world.width ||
            y - halfHeight < 0 || y + halfHeight > this.gameState.world.height) {
            console.log('🏗️ DUNE2 DEBUG: Building placement invalid - outside map boundaries');
            return false;
        }
        
        // Check collision with existing buildings
        const buildingBuffer = 10;
        for (const building of this.gameState.entities.buildings) {
            const bHalfWidth = (building.width || 50) / 2;
            const bHalfHeight = (building.height || 50) / 2;
            
            if (Math.abs(building.x - x) < (bHalfWidth + halfWidth + buildingBuffer) &&
                Math.abs(building.y - y) < (bHalfHeight + halfHeight + buildingBuffer)) {
                console.log('🏗️ DUNE2 DEBUG: Building placement invalid - collision with existing building');
                return false;
            }
        }
        
        // Check collision with terrain obstacles
        for (const obstacle of this.gameState.entities.terrain) {
            const distance = Math.sqrt((obstacle.x - x) ** 2 + (obstacle.y - y) ** 2);
            if (distance < obstacle.radius + Math.max(halfWidth, halfHeight)) {
                console.log('🏗️ DUNE2 DEBUG: Building placement invalid - collision with terrain');
                return false;
            }
        }
        
        console.log('🏗️ DUNE2 DEBUG: Building placement valid');
        return true;
    }
    
    cancelBuildingMode() {
        console.log('🏗️ DUNE2 DEBUG: cancelBuildingMode called');
        
        this.placingBuilding = false;
        this.buildingType = null;
        this.gameState.selection.buildingMode = null;
        this.gameState.selection.ghostBuilding = null;
        
        // Restore cursor
        if (this.canvas) {
            this.canvas.style.cursor = 'default';
        }
        
        // Show build panel again
        if (this.ui?.buildPanel) {
            this.ui.buildPanel.show();
        }
        
        console.log('🏗️ DUNE2 DEBUG: Building mode cancelled');
    }

    endSelection(startX, startY, endX, endY) {
        // Delegate to existing implementation
    }

    generateMap() {
        this.dynamicMapSystem.generateProceduralTerrain();
        // Initialize terrain grid and other map setup
    }

    createInitialBase() {
        const playerBase = this.addBuilding({ type: 'base', x: 400, y: 400, team: 'player', instant: true });

        if (this.gameState.config.loadInitialUnits) {
            const harvester = this.createUnit('harvester', 500, 400, 'player');
            const soldier1 = this.createUnit('soldier', 350, 350, 'player');
            const soldier2 = this.createUnit('soldier', 350, 450, 'player');
            const soldier3 = this.createUnit('soldier', 300, 400, 'player');
        }
        
        // Create enemy faction
        const enemyBase = this.addBuilding({ type: 'base', x: 2600, y: 2600, team: 'enemy', instant: true });
        const enemyPower = this.addBuilding({ type: 'powerplant', x: 2500, y: 2600, team: 'enemy', instant: true });

        if (this.gameState.config.loadInitialUnits) {
            const enemySoldier1 = this.createUnit('soldier', 2550, 2550, 'enemy');
            const enemySoldier2 = this.createUnit('soldier', 2650, 2550, 'enemy');
            const enemyTank = this.createUnit('tank', 2600, 2500, 'enemy');
        }
        
        // Create second AI faction for faction-vs-faction combat
        const faction2Base = this.addBuilding({ type: 'base', x: 400, y: 2600, team: 'faction2', instant: true });
        const faction2Power = this.addBuilding({ type: 'powerplant', x: 300, y: 2600, team: 'faction2', instant: true });
        
        if (this.gameState.config.loadInitialUnits) {
            const faction2Soldier1 = this.createUnit('soldier', 350, 2550, 'faction2');
            const faction2Soldier2 = this.createUnit('soldier', 450, 2550, 'faction2');
            const faction2Tank = this.createUnit('tank', 400, 2500, 'faction2');
        }
        
        // Create third AI faction
        const faction3Base = this.addBuilding({ type: 'base', x: 2600, y: 400, team: 'faction3', instant: true });
        const faction3Power = this.addBuilding({ type: 'powerplant', x: 2500, y: 400, team: 'faction3', instant: true });
        
        if (this.gameState.config.loadInitialUnits) {
            const faction3Soldier1 = this.createUnit('soldier', 2550, 350, 'faction3');
            const faction3Soldier2 = this.createUnit('soldier', 2650, 350, 'faction3');
            const faction3Tank = this.createUnit('tank', 2600, 300, 'faction3');
        }
    }

    initFogOfWar() {
        this.gameState.fogOfWar.data = [];
        const numCols = Math.ceil(this.gameState.world.width / this.gameState.fogOfWar.gridSize);
        const numRows = Math.ceil(this.gameState.world.height / this.gameState.fogOfWar.gridSize);
        
        for (let y = 0; y < numRows; y++) {
            this.gameState.fogOfWar.data[y] = [];
            for (let x = 0; x < numCols; x++) {
                this.gameState.fogOfWar.data[y][x] = {
                    explored: false,
                    visible: false,
                    currentOpacity: 0.8
                };
            }
        }
    }

    updateFogOfWar() {
        const fogData = this.gameState.fogOfWar.data;
        const gridSize = this.gameState.fogOfWar.gridSize;
        const defaultVisionRadius = this.gameState.config.defaultVisionRadius || 250; // Assuming a default in gameState.config

        if (!fogData || fogData.length === 0 || !fogData[0] || fogData[0].length === 0) return;

        const numFogRows = fogData.length;
        const numFogCols = fogData[0].length;

        // Reset visibility for the current frame
        for (let y = 0; y < numFogRows; y++) {
            for (let x = 0; x < numFogCols; x++) {
                fogData[y][x].visibleThisFrame = false;
            }
        }

        // Player entities that provide vision
        const playerEntities = [
            ...this.gameState.entities.units.filter(u => u.team === 'player' && u.health > 0),
            ...this.gameState.entities.buildings.filter(b => b.team === 'player' && b.health > 0 && b.constructionProgress >= 100)
        ];

        playerEntities.forEach(entity => {
            let visionRange = entity.visionRadius || defaultVisionRadius;
            // Example: Check for radar building type and apply vision bonus
            if (entity.type && this.buildingTypes[entity.type] && this.buildingTypes[entity.type].isRadar) {
                 visionRange += this.buildingTypes[entity.type].visionBonus || 0;
            }


            const entityFogX = Math.floor(entity.x / gridSize);
            const entityFogY = Math.floor(entity.y / gridSize);
            const rangeInFogCells = Math.ceil(visionRange / gridSize);

            for (let yOff = -rangeInFogCells; yOff <= rangeInFogCells; yOff++) {
                for (let xOff = -rangeInFogCells; xOff <= rangeInFogCells; xOff++) {
                    const fogX = entityFogX + xOff;
                    const fogY = entityFogY + yOff;

                    if (fogX >= 0 && fogX < numFogCols && fogY >= 0 && fogY < numFogRows) {
                        const cell = fogData[fogY][fogX];
                        if (cell.visibleThisFrame) continue; // Already marked visible by another unit this frame

                        const cellCenterX = fogX * gridSize + gridSize / 2;
                        const cellCenterY = fogY * gridSize + gridSize / 2;
                        const distSq = (entity.x - cellCenterX) ** 2 + (entity.y - cellCenterY) ** 2;

                        if (distSq <= visionRange ** 2) {
                            cell.visibleThisFrame = true;
                            cell.explored = true;
                        }
                    }
                }
            }
        });

        // Update opacity based on visibility and explored status
        for (let y = 0; y < numFogRows; y++) {
            for (let x = 0; x < numFogCols; x++) {
                const cell = fogData[y][x];
                let newOpacity;
                if (cell.visibleThisFrame) {
                    newOpacity = 0.0; // Currently visible
                } else if (cell.explored) {
                    newOpacity = 0.5; // Explored but not currently visible
                } else {
                    newOpacity = 0.8; // Unexplored (matches initFogOfWar)
                }

                if (cell.currentOpacity !== newOpacity) {
                    cell.currentOpacity = newOpacity;
                }
                // Update the 'visible' state if your rendering or logic uses it beyond 'visibleThisFrame'
                // For now, rendering system uses currentOpacity, so visibleThisFrame handles the frame's visibility.
                // cell.visible = cell.visibleThisFrame; // If needed elsewhere
            }
        }
    }

    /**
     * Main update method for the Game class, called by GameLoop.
     * @param {number} deltaTime - Time since the last frame.
     */
    update(deltaTime) {
        // Update game-specific logic that isn't part of a dedicated system
        if (!this.gameState.timing.paused) {
            this.updateFogOfWar();
            
            // Update all building entities - CRITICAL FOR CONSTRUCTION PROGRESS
            this.updateBuildings(deltaTime);
            
            // Update all unit entities
            this.updateUnits(deltaTime);
            
            // Update all projectile entities
            this.updateProjectiles(deltaTime);
            
            // Legacy production queue update (consider moving to a system)
            this.updateProductionQueues(deltaTime);

            // Legacy spice regeneration (consider moving to ResourceSystem or EnvironmentalSystem)
            this.updateSpiceRegeneration(this.gameState.timing.lastTimestamp);
        }
    }
    
    /**
     * Update all building entities
     */
    updateBuildings(deltaTime) {
        const buildingsToRemove = [];
        
        this.gameState.entities.buildings.forEach((building, index) => {
            if (building && typeof building.update === 'function') {
                building.update(deltaTime);
                
                // Check if building was just completed
                if (building.constructionProgress >= 100 && building.isUnderConstruction) {
                    console.log('🏗️ DUNE2 DEBUG: Building construction completed:', {
                        id: building.id,
                        type: building.type,
                        x: building.x,
                        y: building.y
                    });
                    
                    // Update pathfinding when building is completed
                    this.pathfinder.updateGridObstacles();
                    this.flowFieldPathfinder.updateCostField();
                    
                    // Play completion sound for player buildings
                    if (building.team === 'player' && this.soundManager) {
                        this.soundManager.play('complete');
                    }
                }
                
                // Remove destroyed buildings
                if (building.health <= 0) {
                    buildingsToRemove.push(building);
                }
            }
        });
        
        // Remove destroyed buildings
        buildingsToRemove.forEach(building => {
            this.gameState.removeBuilding(building);
        });
    }
    
    /**
     * Update all unit entities
     */
    updateUnits(deltaTime) {
        const unitsToRemove = [];
        
        this.gameState.entities.units.forEach((unit, index) => {
            if (unit && typeof unit.update === 'function') {
                unit.update(deltaTime);
                
                // Remove destroyed units
                if (unit.health <= 0) {
                    unitsToRemove.push(unit);
                }
            }
        });
        
        // Remove destroyed units
        unitsToRemove.forEach(unit => {
            this.gameState.removeUnit(unit);
        });
    }
    
    /**
     * Update all projectile entities
     */
    updateProjectiles(deltaTime) {
        const projectilesToRemove = [];
        
        this.gameState.entities.projectiles.forEach((projectile, index) => {
            if (projectile && typeof projectile.update === 'function') {
                const stillActive = projectile.update(deltaTime);
                
                // Remove expired projectiles
                if (!stillActive) {
                    projectilesToRemove.push(projectile);
                }
            }
        });
        
        // Remove expired projectiles
        projectilesToRemove.forEach(projectile => {
            this.gameState.removeProjectile(projectile);
        });
    }
    
    // Placeholder for legacy updateProductionQueues - adapt or move to a system
    updateProductionQueues(deltaTime) {
        if (!this.productionQueues || typeof this.productionQueues.forEach !== 'function') {
            this.productionQueues = new Map(); // Initialize if undefined
        }
        this.productionQueues.forEach((queue, buildingId) => {
            if (queue.length === 0) return;
            const building = this.gameState.entities.buildings.find(b => b.id === buildingId);
            if (!building || building.constructionProgress < 100 ||
                (building.power < 0 && this.gameState.resources.power < 0 && building.team === 'player')) return;

            const item = queue[0];
            item.progress += (100 / (item.buildTime || GameData.getUnitTypes()[item.type].buildTime)) * deltaTime;

            if (item.progress >= 100) {
                queue.shift();
                const spawnOffset = (building.width || 50) / 2 + 20;
                const spawnAngle = Math.random() * Math.PI * 2;
                const spawnX = building.rallyPoint ? building.rallyPoint.x : building.x + Math.cos(spawnAngle) * spawnOffset;
                const spawnY = building.rallyPoint ? building.rallyPoint.y : building.y + Math.sin(spawnAngle) * spawnOffset;
                this.createUnit(item.type, spawnX, spawnY, building.team);
                if (queue.length > 0) queue[0].progress = 0;
                if (building.team === 'player' && this.soundManager) this.soundManager.play('complete');
            }
        });
        if (this.gameState.selection.selectedBuilding && this.productionQueues.has(this.gameState.selection.selectedBuilding.id)) {
            if (this.ui && this.ui.updateUnitInfoPanel) this.ui.updateUnitInfoPanel();
        }
    }

    // Placeholder for legacy updateSpiceRegeneration - adapt or move
    updateSpiceRegeneration(timestamp) {
        if (!this.gameState.timing.lastSpiceRegenTime || timestamp - this.gameState.timing.lastSpiceRegenTime > 5000) {
            this.gameState.timing.lastSpiceRegenTime = timestamp;
            this.gameState.entities.spiceFields.forEach(field => {
                if (field.amount < field.maxAmount) {
                    field.amount = Math.min(field.amount + 25, field.maxAmount);
                }
            });
        }
    }


    updateViewport() {
        this.inputSystem.constrainView();
        this.ui.updateViewportRect(
            this.gameState.world.viewX,
            this.gameState.world.viewY,
            this.gameCanvas.width / this.gameState.world.zoom,
            this.gameCanvas.height / this.gameState.world.zoom
        );
    }

    renderUIElements(ctx) {
        // Render UI overlays, selection indicators, etc.
    }

    /**
     * Handle window resize events
     */
    handleWindowResize() {
        // Resize the rendering system's canvas
        this.renderingSystem.resize();
        
        // Update viewport constraints
        this.updateViewport();
        
        // Update UI elements that depend on canvas size
        if (this.ui && this.ui.drawMinimapStatic) {
            this.ui.drawMinimapStatic();
        }
    }

    // Legacy method stubs - these would delegate to appropriate systems
    togglePause() { this.gameState.togglePause(); }
    showHelp() { this.ui.showNotification("Help: Left Click Select, Right Click Command. P to Pause. Destroy enemy Command Centers!", "info", 5000); }
    restart() { if (confirm('Restart game?')) location.reload(); }
    
    // Expose system methods for backward compatibility
    updatePower() { this.resourceSystem.updatePowerGeneration(this.gameState.timing.gameTime); }
    findNearestSpiceField(unit) { return this.resourceSystem.findNearestSpiceField(unit); }
    applySplashDamage(centerX, centerY, damage, radius, attackerTeam, shooter) {
        return this.combatSystem.applySplashDamage(centerX, centerY, damage, radius, shooter);
    }
    
    // Expose UI methods for HTML onclick handlers
    showBuildTab(category) {
        if (this.ui && this.ui.buildPanel && this.ui.buildPanel.showBuildTab) {
            this.ui.buildPanel.showBuildTab(category);
        }
    }
    
    openTechTree() {
        if (this.ui && this.ui.openTechTree) {
            this.ui.openTechTree();
        }
    }
    
    closeTechTree() {
        if (this.ui && this.ui.closeTechTree) {
            this.ui.closeTechTree();
        }
    }

    /**
     * Sets up a predefined test battle scene.
     * This is a utility method for development and testing.
     * It assumes gameState.config.loadInitialUnits might be false.
     * @param {object} [options={}] - Optional parameters to pass to the scene setup function.
     */
    setupTestBattleScene(options = {}) {
        // Pass 'this' (the game instance) and any options to the external setup function.
        // Also pass gameData for unit type validation within the setup function.
        setupTestBattleSceneExternal(this, { ...options, gameData: GameData });
    }
}