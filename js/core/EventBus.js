/**
 * EventBus - Centralized event management system
 * Provides decoupled communication between game systems
 */
export class EventBus {
    constructor() {
        this.listeners = new Map();
        this.onceListeners = new Map();
        this.eventHistory = [];
        this.maxHistorySize = 100;
    }

    /**
     * Subscribe to an event
     * @param {string} eventType - Type of event to listen for
     * @param {Function} callback - Function to call when event occurs
     * @param {Object} context - Context to bind callback to
     * @returns {Function} Unsubscribe function
     */
    on(eventType, callback, context = null) {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }

        const listener = { callback, context };
        this.listeners.get(eventType).push(listener);

        // Return unsubscribe function
        return () => this.off(eventType, callback, context);
    }

    /**
     * Subscribe to an event once
     * @param {string} eventType - Type of event to listen for
     * @param {Function} callback - Function to call when event occurs
     * @param {Object} context - Context to bind callback to
     */
    once(eventType, callback, context = null) {
        if (!this.onceListeners.has(eventType)) {
            this.onceListeners.set(eventType, []);
        }

        const listener = { callback, context };
        this.onceListeners.get(eventType).push(listener);
    }

    /**
     * Unsubscribe from an event
     * @param {string} eventType - Type of event to stop listening for
     * @param {Function} callback - Callback function to remove
     * @param {Object} context - Context that was bound to callback
     */
    off(eventType, callback, context = null) {
        if (this.listeners.has(eventType)) {
            const listeners = this.listeners.get(eventType);
            const index = listeners.findIndex(l => 
                l.callback === callback && l.context === context
            );
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Emit an event to all listeners
     * @param {string} eventType - Type of event to emit
     * @param {*} data - Data to pass to listeners
     */
    emit(eventType, data = null) {
        // Add to history
        this.eventHistory.push({
            type: eventType,
            data,
            timestamp: Date.now()
        });

        // Trim history if needed
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }

        // Call regular listeners
        if (this.listeners.has(eventType)) {
            const listeners = [...this.listeners.get(eventType)];
            listeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data);
                    } else {
                        listener.callback(data);
                    }
                } catch (error) {
                    console.error(`Error in event listener for ${eventType}:`, error);
                }
            });
        }

        // Call once listeners and remove them
        if (this.onceListeners.has(eventType)) {
            const onceListeners = [...this.onceListeners.get(eventType)];
            this.onceListeners.delete(eventType);
            
            onceListeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data);
                    } else {
                        listener.callback(data);
                    }
                } catch (error) {
                    console.error(`Error in once event listener for ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * Remove all listeners for an event type
     * @param {string} eventType - Event type to clear
     */
    clear(eventType) {
        this.listeners.delete(eventType);
        this.onceListeners.delete(eventType);
    }

    /**
     * Remove all listeners
     */
    clearAll() {
        this.listeners.clear();
        this.onceListeners.clear();
    }

    /**
     * Get event history
     * @param {string} eventType - Optional filter by event type
     * @returns {Array} Array of historical events
     */
    getHistory(eventType = null) {
        if (eventType) {
            return this.eventHistory.filter(event => event.type === eventType);
        }
        return [...this.eventHistory];
    }

    /**
     * Check if there are listeners for an event type
     * @param {string} eventType - Event type to check
     * @returns {boolean} True if there are listeners
     */
    hasListeners(eventType) {
        return (this.listeners.has(eventType) && this.listeners.get(eventType).length > 0) ||
               (this.onceListeners.has(eventType) && this.onceListeners.get(eventType).length > 0);
    }

    /**
     * Get listener count for an event type
     * @param {string} eventType - Event type to count
     * @returns {number} Number of listeners
     */
    getListenerCount(eventType) {
        let count = 0;
        if (this.listeners.has(eventType)) {
            count += this.listeners.get(eventType).length;
        }
        if (this.onceListeners.has(eventType)) {
            count += this.onceListeners.get(eventType).length;
        }
        return count;
    }

    /**
     * Get all registered event types
     * @returns {Array} Array of event type strings
     */
    getEventTypes() {
        const types = new Set();
        this.listeners.forEach((_, type) => types.add(type));
        this.onceListeners.forEach((_, type) => types.add(type));
        return Array.from(types);
    }
}