/**
 * GameLoop - Main game loop and timing management
 * Handles frame timing, delta time calculation, and system updates
 */
export class GameLoop {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        this.systems = [];
        this.isRunning = false;
        this.frameId = null;
        this.maxDeltaTime = 0.1; // Cap delta time to prevent large jumps
        this.targetFPS = 60;
        this.frameTime = 1000 / this.targetFPS;
        this.gameSpeedMultiplier = 1; // Global game speed multiplier
        
        // Performance tracking
        this.performanceStats = {
            fps: 0,
            frameTime: 0,
            lastFrameTime: 0,
            frameCount: 0,
            lastStatsUpdate: 0
        };
    }

    /**
     * Register a system to be updated in the game loop
     * @param {Object} system - System with update method
     * @param {number} priority - Update priority (lower = earlier)
     */
    registerSystem(system, priority = 100) {
        this.systems.push({ system, priority });
        this.systems.sort((a, b) => a.priority - b.priority);
        
        this.eventBus.emit('gameloop:system_registered', { 
            system: system.constructor.name, 
            priority 
        });
    }

    /**
     * Unregister a system from the game loop
     * @param {Object} system - System to remove
     */
    unregisterSystem(system) {
        const index = this.systems.findIndex(s => s.system === system);
        if (index !== -1) {
            this.systems.splice(index, 1);
            this.eventBus.emit('gameloop:system_unregistered', { 
                system: system.constructor.name 
            });
        }
    }

    /**
     * Start the game loop
     */
    start() {
        if (this.isRunning) {
            console.warn('GameLoop is already running');
            return;
        }

        this.isRunning = true;
        this.gameState.timing.lastTimestamp = performance.now();
        this.performanceStats.lastStatsUpdate = performance.now();
        
        this.eventBus.emit('gameloop:started');
        this.loop(this.gameState.timing.lastTimestamp);
    }

    /**
     * Stop the game loop
     */
    stop() {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
            this.frameId = null;
        }
        
        this.eventBus.emit('gameloop:stopped');
    }

    /**
     * Main game loop function
     * @param {number} timestamp - Current timestamp from requestAnimationFrame
     */
    loop(timestamp) {
        if (!this.isRunning) {
            return;
        }

        // Calculate delta time
        const rawDeltaTime = this.gameState.timing.paused ?
            0 :
            Math.min(this.maxDeltaTime, (timestamp - this.gameState.timing.lastTimestamp) / 1000);
        const deltaTime = rawDeltaTime * this.gameSpeedMultiplier;

        // Update game state timing
        this.gameState.updateTiming(timestamp, deltaTime);

        // Update performance stats
        this.updatePerformanceStats(timestamp, deltaTime);

        // Update all registered systems
        if (!this.gameState.timing.paused) {
            this.updateSystems(deltaTime);
        }

        // Emit frame update event
        this.eventBus.emit('gameloop:frame', {
            deltaTime,
            timestamp,
            gameTime: this.gameState.timing.gameTime
        });

        // Schedule next frame
        this.frameId = requestAnimationFrame((ts) => this.loop(ts));
    }

    /**
     * Update all registered systems
     * @param {number} deltaTime - Time since last frame in seconds
     */
    updateSystems(deltaTime) {
        const updateStart = performance.now();

        for (const { system } of this.systems) {
            try {
                if (system.update && typeof system.update === 'function') {
                    const systemStart = performance.now();
                    system.update(deltaTime);
                    const systemTime = performance.now() - systemStart;
                    
                    // Emit system performance data
                    if (systemTime > 5) { // Only track systems taking more than 5ms
                        this.eventBus.emit('gameloop:system_performance', {
                            system: system.constructor.name,
                            time: systemTime
                        });
                    }
                }
            } catch (error) {
                console.error(`Error updating system ${system.constructor.name}:`, error);
                this.eventBus.emit('gameloop:system_error', {
                    system: system.constructor.name,
                    error
                });
            }
        }

        const totalUpdateTime = performance.now() - updateStart;
        
        // Emit overall update performance
        this.eventBus.emit('gameloop:update_performance', {
            totalTime: totalUpdateTime,
            systemCount: this.systems.length
        });
    }

    /**
     * Update performance statistics
     * @param {number} timestamp - Current timestamp
     * @param {number} deltaTime - Time since last frame
     */
    updatePerformanceStats(timestamp, deltaTime) {
        this.performanceStats.frameCount++;
        this.performanceStats.frameTime = timestamp - this.performanceStats.lastFrameTime;
        this.performanceStats.lastFrameTime = timestamp;

        // Update FPS calculation every second
        if (timestamp - this.performanceStats.lastStatsUpdate >= 1000) {
            this.performanceStats.fps = Math.round(
                this.performanceStats.frameCount * 1000 / 
                (timestamp - this.performanceStats.lastStatsUpdate)
            );
            
            this.performanceStats.frameCount = 0;
            this.performanceStats.lastStatsUpdate = timestamp;
            
            // Emit performance stats
            this.eventBus.emit('gameloop:performance_stats', {
                fps: this.performanceStats.fps,
                frameTime: this.performanceStats.frameTime,
                avgFrameTime: 1000 / this.performanceStats.fps
            });
        }
    }

    /**
     * Get current performance statistics
     * @returns {Object} Performance stats object
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            isRunning: this.isRunning,
            systemCount: this.systems.length,
            targetFPS: this.targetFPS
        };
    }

    /**
     * Set target FPS (affects frame timing calculations)
     * @param {number} fps - Target frames per second
     */
    setTargetFPS(fps) {
        this.targetFPS = Math.max(1, Math.min(120, fps));
        this.frameTime = 1000 / this.targetFPS;
        
        this.eventBus.emit('gameloop:target_fps_changed', {
            targetFPS: this.targetFPS
        });
    }

    /**
     * Set maximum delta time to prevent large frame jumps
     * @param {number} maxDelta - Maximum delta time in seconds
     */
    setMaxDeltaTime(maxDelta) {
        this.maxDeltaTime = Math.max(0.001, maxDelta);
        
        this.eventBus.emit('gameloop:max_delta_changed', {
            maxDeltaTime: this.maxDeltaTime
        });
    }

    /**
     * Pause the game loop (stops system updates but continues rendering)
     */
    pause() {
        this.gameState.timing.paused = true;
        this.eventBus.emit('gameloop:paused');
    }

    /**
     * Resume the game loop
     */
    resume() {
        if (this.gameState.timing.paused) {
            this.gameState.timing.paused = false;
            this.gameState.timing.lastTimestamp = performance.now();
            this.eventBus.emit('gameloop:resumed');
        }
    }

    /**
     * Toggle pause state
     */
    togglePause() {
        if (this.gameState.timing.paused) {
            this.resume();
        } else {
            this.pause();
        }
    }

    /**
     * Execute a single frame update (useful for debugging)
     */
    step() {
        if (this.gameState.timing.paused) {
            const timestamp = performance.now();
            const deltaTime = 1 / this.targetFPS; // Use target frame time
            
            this.gameState.updateTiming(timestamp, deltaTime);
            this.updateSystems(deltaTime);
            
            this.eventBus.emit('gameloop:step', {
                deltaTime,
                timestamp
            });
        }
    }

    /**
     * Get list of registered systems
     * @returns {Array} Array of system information
     */
    getRegisteredSystems() {
        return this.systems.map(({ system, priority }) => ({
            name: system.constructor.name,
            priority,
            hasUpdate: typeof system.update === 'function'
        }));
    }

    /**
     * Emergency stop - immediately halt the game loop
     */
    emergencyStop() {
        this.isRunning = false;
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
            this.frameId = null;
        }
        
        this.eventBus.emit('gameloop:emergency_stop');
        console.warn('GameLoop emergency stop executed');
    }

    /**
     * Set the global game speed multiplier
     * @param {number} multiplier - New speed multiplier (e.g., 1 for normal, 0.5 for half speed)
     */
    setGameSpeed(multiplier) {
        this.gameSpeedMultiplier = Math.max(0.01, multiplier); // Prevent speed from being too low or zero
        this.eventBus.emit('gameloop:speed_changed', {
            speed: this.gameSpeedMultiplier
        });
        console.log(`Game speed set to: ${this.gameSpeedMultiplier}`);
    }
}