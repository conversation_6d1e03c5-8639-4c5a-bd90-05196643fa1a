export class PerformanceManager {
    constructor(game) {
        this.game = game;
        this.settings = {
            maxParticles: 500,
            maxEffects: 100,
            cullingEnabled: true,
            lodEnabled: true,
            batchingEnabled: true,
            emergencyMode: false
        };
        
        this.stats = {
            fps: 60,
            frameTime: 16.67,
            entityCount: 0,
            particleCount: 0,
            drawCalls: 0,
            lastFrameTime: 0
        };
        
        this.frameTimeHistory = [];
        this.maxFrameHistory = 60;
        this.emergencyThreshold = 10; // FPS threshold for emergency mode
        this.qualityLevel = 'high'; // high, medium, low, emergency
    }
    
    updateFrameTiming(deltaTime) {
        const frameTime = deltaTime * 1000; // Convert to milliseconds
        this.stats.frameTime = frameTime;
        this.stats.fps = Math.round(1000 / frameTime);
        
        // Track frame time history
        this.frameTimeHistory.push(frameTime);
        if (this.frameTimeHistory.length > this.maxFrameHistory) {
            this.frameTimeHistory.shift();
        }
        
        // Check for performance issues
        if (this.stats.fps < this.emergencyThreshold && !this.settings.emergencyMode) {
            this.enableEmergencyMode();
        }
        
        // Auto-adjust quality based on performance
        this.autoAdjustQuality();
    }
    
    updateEntitiesOptimized(entities, deltaTime) {
        const visibleEntities = [];
        this.stats.entityCount = entities.length;
        
        if (!this.settings.cullingEnabled) {
            // Update all entities if culling is disabled
            entities.forEach(entity => {
                entity.update(deltaTime);
                visibleEntities.push(entity);
            });
            return visibleEntities;
        }
        
        // Frustum culling - only update visible entities
        entities.forEach(entity => {
            if (this.isEntityVisible(entity)) {
                entity.update(deltaTime);
                visibleEntities.push(entity);
            } else if (this.settings.lodEnabled) {
                // Update critical properties for off-screen entities
                this.updateEntityLOD(entity, deltaTime);
            }
        });
        
        return visibleEntities;
    }
    
    isEntityVisible(entity) {
        const margin = 100; // Extra margin for smooth transitions
        const viewLeft = this.game.viewX - margin;
        const viewRight = this.game.viewX + (this.game.gameCanvas.width / this.game.zoom) + margin;
        const viewTop = this.game.viewY - margin;
        const viewBottom = this.game.viewY + (this.game.gameCanvas.height / this.game.zoom) + margin;
        
        const entitySize = entity.size || entity.width || entity.height || 20;
        
        return entity.x + entitySize >= viewLeft &&
               entity.x - entitySize <= viewRight &&
               entity.y + entitySize >= viewTop &&
               entity.y - entitySize <= viewBottom;
    }
    
    updateEntityLOD(entity, deltaTime) {
        // Simplified update for off-screen entities
        if (entity.health <= 0) return;
        
        // Update critical AI behavior
        if (entity.update && typeof entity.update === 'function') {
            // Only update essential properties
            if (entity.isUnit) {
                // Update movement and basic AI
                if (entity.path && entity.path.length > 0) {
                    this.game.moveUnit(entity, deltaTime);
                }
            }
        }
    }
    
    batchRenderEntities(ctx, entities, renderFunction) {
        if (!this.settings.batchingEnabled) {
            entities.forEach(entity => renderFunction(ctx, entity));
            return;
        }
        
        // Group entities by type for batched rendering
        const entityGroups = {};
        entities.forEach(entity => {
            const type = entity.type || 'unknown';
            if (!entityGroups[type]) {
                entityGroups[type] = [];
            }
            entityGroups[type].push(entity);
        });
        
        // Render each group
        Object.values(entityGroups).forEach(group => {
            ctx.save();
            group.forEach(entity => renderFunction(ctx, entity));
            ctx.restore();
            this.stats.drawCalls++;
        });
    }
    
    autoAdjustQuality() {
        const avgFrameTime = this.getAverageFrameTime();
        const targetFrameTime = 16.67; // 60 FPS
        
        if (avgFrameTime > targetFrameTime * 2 && this.qualityLevel !== 'emergency') {
            // Performance is poor, reduce quality
            this.reduceQuality();
        } else if (avgFrameTime < targetFrameTime * 0.8 && this.qualityLevel !== 'high') {
            // Performance is good, increase quality
            this.increaseQuality();
        }
    }
    
    getAverageFrameTime() {
        if (this.frameTimeHistory.length === 0) return 16.67;
        
        const sum = this.frameTimeHistory.reduce((a, b) => a + b, 0);
        return sum / this.frameTimeHistory.length;
    }
    
    reduceQuality() {
        switch (this.qualityLevel) {
            case 'high':
                this.setQualityLevel('medium');
                break;
            case 'medium':
                this.setQualityLevel('low');
                break;
            case 'low':
                this.setQualityLevel('emergency');
                break;
        }
    }
    
    increaseQuality() {
        switch (this.qualityLevel) {
            case 'emergency':
                this.setQualityLevel('low');
                break;
            case 'low':
                this.setQualityLevel('medium');
                break;
            case 'medium':
                this.setQualityLevel('high');
                break;
        }
    }
    
    setQualityLevel(level) {
        this.qualityLevel = level;
        
        switch (level) {
            case 'high':
                this.settings.maxParticles = 500;
                this.settings.maxEffects = 100;
                this.settings.cullingEnabled = true;
                this.settings.lodEnabled = true;
                this.settings.batchingEnabled = true;
                break;
            case 'medium':
                this.settings.maxParticles = 300;
                this.settings.maxEffects = 60;
                this.settings.cullingEnabled = true;
                this.settings.lodEnabled = true;
                this.settings.batchingEnabled = true;
                break;
            case 'low':
                this.settings.maxParticles = 150;
                this.settings.maxEffects = 30;
                this.settings.cullingEnabled = true;
                this.settings.lodEnabled = true;
                this.settings.batchingEnabled = true;
                break;
            case 'emergency':
                this.enableEmergencyMode();
                break;
        }
        
        console.log(`Performance quality set to: ${level}`);
    }
    
    enableEmergencyMode() {
        this.settings.emergencyMode = true;
        this.qualityLevel = 'emergency';
        this.settings.maxParticles = 50;
        this.settings.maxEffects = 10;
        this.settings.cullingEnabled = true;
        this.settings.lodEnabled = true;
        this.settings.batchingEnabled = true;
        
        // Disable non-essential visual effects
        if (this.game.visualEffects) {
            this.game.visualEffects.emergencyMode = true;
        }
        
        // Reduce AI update frequency
        if (this.game.aiController) {
            this.game.aiController.updateFrequency = 0.5; // Half frequency
        }
        
        console.log('Emergency performance mode activated');
    }
    
    resetToHighQuality() {
        this.settings.emergencyMode = false;
        this.setQualityLevel('high');
        
        // Re-enable visual effects
        if (this.game.visualEffects) {
            this.game.visualEffects.emergencyMode = false;
        }
        
        // Restore AI update frequency
        if (this.game.aiController) {
            this.game.aiController.updateFrequency = 1.0;
        }
        
        console.log('High quality mode restored');
    }
    
    getPerformanceStats() {
        return {
            fps: this.stats.fps,
            frameTime: this.stats.frameTime,
            entityCount: this.stats.entityCount,
            particleCount: this.stats.particleCount,
            drawCalls: this.stats.drawCalls,
            qualityLevel: this.qualityLevel,
            emergencyMode: this.settings.emergencyMode,
            averageFrameTime: this.getAverageFrameTime()
        };
    }
    
    optimizeMemory() {
        // Clean up unused objects
        if (this.game.visualEffects) {
            this.game.visualEffects.cleanupDeadParticles();
        }
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
        
        console.log('Memory optimization completed');
    }
    
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
                total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
            };
        }
        return null;
    }
    
    shouldSkipFrame() {
        // Skip frame if performance is critical
        return this.settings.emergencyMode && this.stats.fps < 15;
    }
    
    throttleUpdates(callback, frequency = 1.0) {
        // Throttle non-critical updates based on performance
        const shouldUpdate = Math.random() < frequency * (this.stats.fps / 60);
        if (shouldUpdate) {
            callback();
        }
    }
}