// Game data and configuration
export class GameData {
    static getUnitTypes() {
        return {
            // Infantry Units
            soldier: {
                name: 'Soldier',
                category: 'infantry',
                health: 80,
                damage: 15,
                range: 60,
                speed: 1.0,
                cost: 60,
                buildTime: 8,
                width: 16,
                height: 16,
                color: '#4A90E2'
            },
            
            rocketeer: {
                name: 'Rocketeer',
                category: 'infantry',
                health: 60,
                damage: 35,
                range: 80,
                speed: 0.8,
                cost: 120,
                buildTime: 12,
                width: 16,
                height: 16,
                color: '#E94B3C',
                deployable: true
            },
            
            engineer: {
                name: 'Engineer',
                category: 'infantry',
                health: 50,
                damage: 5,
                range: 20,
                speed: 0.8,
                cost: 80,
                buildTime: 10,
                width: 16,
                height: 16,
                color: '#F39C12',
                canRepair: true
            },
            
            medic: {
                name: 'Medic',
                category: 'infantry',
                health: 60,
                damage: 0,
                range: 30,
                speed: 0.88,
                cost: 100,
                buildTime: 12,
                width: 16,
                height: 16,
                color: '#2ECC71',
                canHeal: true
            },
            
            scout: {
                name: 'Scout',
                category: 'infantry',
                health: 40,
                damage: 8,
                range: 50,
                speed: 1.6,
                cost: 40,
                buildTime: 6,
                width: 14,
                height: 14,
                color: '#95A5A6'
            },
            
            // Vehicle Units
            harvester: {
                name: 'Harvester',
                category: 'vehicle',
                health: 200,
                damage: 0,
                range: 0,
                speed: 0.6,
                cost: 300,
                buildTime: 20,
                width: 32,
                height: 24,
                color: '#E67E22',
                canHarvest: true,
                maxSpice: 100
            },
            
            tank: {
                name: 'Tank',
                category: 'vehicle',
                health: 180,
                damage: 40,
                range: 70,
                speed: 0.8,
                cost: 200,
                buildTime: 15,
                width: 28,
                height: 20,
                color: '#34495E'
            },
            
            artillery: {
                name: 'Artillery',
                category: 'vehicle',
                health: 120,
                damage: 80,
                range: 120,
                speed: 0.48,
                cost: 350,
                buildTime: 25,
                width: 30,
                height: 22,
                color: '#8E44AD',
                deployable: true
            },
            
            apc: {
                name: 'APC',
                category: 'vehicle',
                health: 150,
                damage: 20,
                range: 50,
                speed: 1.12,
                cost: 180,
                buildTime: 14,
                width: 26,
                height: 18,
                color: '#27AE60',
                transport: 4
            },
            
            siege_tank: {
                name: 'Siege Tank',
                category: 'vehicle',
                health: 250,
                damage: 60,
                range: 90,
                speed: 0.6,
                cost: 400,
                buildTime: 30,
                width: 32,
                height: 24,
                color: '#2C3E50',
                deployable: true,
                requiredTech: 'advanced_weapons'
            },
            
            // Air Units
            ornithopter: {
                name: 'Ornithopter',
                category: 'air',
                health: 80,
                damage: 25,
                range: 60,
                speed: 1.8,
                cost: 250,
                buildTime: 18,
                width: 24,
                height: 16,
                color: '#3498DB'
            },
            
            carryall: {
                name: 'Carryall',
                category: 'air',
                health: 120,
                damage: 0,
                range: 0,
                speed: 1.4,
                cost: 400,
                buildTime: 25,
                width: 32,
                height: 20,
                color: '#9B59B6',
                transport: 1
            }
        };
    }

    static getBuildingTypes() {
        return {
            // Core Buildings
            construction_yard: {
                name: 'Construction Yard',
                category: 'core',
                health: 400,
                cost: 0,
                buildTime: 0,
                width: 60,
                height: 60,
                color: '#34495E',
                power: 0,
                produces: []
            },
            
            power_plant: {
                name: 'Power Plant',
                category: 'core',
                health: 200,
                cost: 150,
                buildTime: 20,
                width: 40,
                height: 40,
                color: '#F39C12',
                power: 100
            },
            
            refinery: {
                name: 'Refinery',
                category: 'core',
                health: 300,
                cost: 400,
                buildTime: 30,
                width: 50,
                height: 50,
                color: '#E67E22',
                power: -20,
                storage: 1000
            },
            
            // Production Buildings
            barracks: {
                name: 'Barracks',
                category: 'production',
                health: 250,
                cost: 200,
                buildTime: 25,
                width: 45,
                height: 45,
                color: '#2ECC71',
                power: -15,
                produces: ['soldier', 'rocketeer', 'engineer', 'medic', 'scout']
            },
            
            factory: {
                name: 'Factory',
                category: 'production',
                health: 300,
                cost: 350,
                buildTime: 35,
                width: 55,
                height: 55,
                color: '#95A5A6',
                power: -25,
                produces: ['harvester', 'tank', 'artillery', 'apc'],
                prerequisites: ['refinery']
            },
            
            starport: {
                name: 'Starport',
                category: 'production',
                health: 200,
                cost: 500,
                buildTime: 40,
                width: 50,
                height: 50,
                color: '#3498DB',
                power: -30,
                produces: ['ornithopter', 'carryall'],
                prerequisites: ['factory']
            },
            
            // Defense Buildings
            gun_turret: {
                name: 'Gun Turret',
                category: 'defense',
                health: 150,
                cost: 120,
                buildTime: 15,
                width: 25,
                height: 25,
                color: '#E74C3C',
                power: -10,
                damage: 30,
                range: 80
            },
            
            rocket_turret: {
                name: 'Rocket Turret',
                category: 'defense',
                health: 180,
                cost: 200,
                buildTime: 20,
                width: 30,
                height: 30,
                color: '#C0392B',
                power: -15,
                damage: 50,
                range: 100,
                prerequisites: ['barracks']
            },
            
            shield_generator: {
                name: 'Shield Generator',
                category: 'defense',
                health: 120,
                cost: 300,
                buildTime: 25,
                width: 35,
                height: 35,
                color: '#9B59B6',
                power: -40,
                shields: 200,
                shieldRegen: 5,
                requiredTech: 'shield_technology'
            },
            
            // Advanced Buildings
            research_lab: {
                name: 'Research Lab',
                category: 'advanced',
                health: 200,
                cost: 400,
                buildTime: 30,
                width: 45,
                height: 45,
                color: '#8E44AD',
                power: -20,
                prerequisites: ['factory']
            },
            
            high_tech_factory: {
                name: 'High Tech Factory',
                category: 'advanced',
                health: 350,
                cost: 600,
                buildTime: 45,
                width: 60,
                height: 60,
                color: '#2C3E50',
                power: -35,
                produces: ['siege_tank'],
                prerequisites: ['research_lab'],
                requiredTech: 'advanced_materials'
            },
            
            palace: {
                name: 'Palace',
                category: 'advanced',
                health: 500,
                cost: 1000,
                buildTime: 60,
                width: 70,
                height: 70,
                color: '#F1C40F',
                power: -50,
                prerequisites: ['high_tech_factory']
            }
        };
    }

    static getTechnologies() {
        return {
            // Military Technologies
            advanced_weapons: {
                name: 'Advanced Weapons',
                category: 'military',
                cost: 200,
                time: 60,
                description: 'Unlocks advanced military units and improves weapon damage.',
                prerequisites: []
            },
            
            armor_upgrades: {
                name: 'Armor Upgrades',
                category: 'military',
                cost: 150,
                time: 45,
                description: 'Increases unit and building armor by 25%.',
                prerequisites: []
            },
            
            tactical_systems: {
                name: 'Tactical Systems',
                category: 'military',
                cost: 300,
                time: 75,
                description: 'Improves unit coordination and formation effectiveness.',
                prerequisites: ['advanced_weapons']
            },
            
            // Economic Technologies
            improved_harvesting: {
                name: 'Improved Harvesting',
                category: 'economic',
                cost: 100,
                time: 30,
                description: 'Increases spice harvesting efficiency by 50%.',
                prerequisites: []
            },
            
            spice_processing: {
                name: 'Spice Processing',
                category: 'economic',
                cost: 250,
                time: 60,
                description: 'Improves spice refining and storage capacity.',
                prerequisites: ['improved_harvesting']
            },
            
            resource_management: {
                name: 'Resource Management',
                category: 'economic',
                cost: 180,
                time: 45,
                description: 'Reduces building costs and improves resource efficiency.',
                prerequisites: []
            },
            
            // Defensive Technologies
            shield_technology: {
                name: 'Shield Technology',
                category: 'defensive',
                cost: 220,
                time: 50,
                description: 'Unlocks energy shields for buildings and units.',
                prerequisites: []
            },
            
            fortification: {
                name: 'Fortification',
                category: 'defensive',
                cost: 160,
                time: 40,
                description: 'Increases building health and defensive capabilities.',
                prerequisites: []
            },
            
            early_warning: {
                name: 'Early Warning Systems',
                category: 'defensive',
                cost: 140,
                time: 35,
                description: 'Provides advanced radar and detection capabilities.',
                prerequisites: []
            },
            
            // Advanced Technologies
            artificial_intelligence: {
                name: 'Artificial Intelligence',
                category: 'advanced',
                cost: 500,
                time: 120,
                description: 'Unlocks AI-controlled units and automated systems.',
                prerequisites: ['tactical_systems', 'early_warning']
            },
            
            quantum_computing: {
                name: 'Quantum Computing',
                category: 'advanced',
                cost: 400,
                time: 90,
                description: 'Accelerates research and improves all technology effectiveness.',
                prerequisites: ['artificial_intelligence']
            },
            
            advanced_materials: {
                name: 'Advanced Materials',
                category: 'advanced',
                cost: 350,
                time: 80,
                description: 'Unlocks advanced construction materials and techniques.',
                prerequisites: ['shield_technology', 'fortification']
            }
        };
    }

    static getTerrainTypes() {
        return {
            sand: {
                name: 'Sand',
                color: '#F4D03F',
                movementModifier: 1.0,
                buildable: true,
                harvestable: false
            },
            
            rock: {
                name: 'Rock',
                color: '#85929E',
                movementModifier: 0.7,
                buildable: false,
                harvestable: false
            },
            
            spice: {
                name: 'Spice Field',
                color: '#FF8C00',
                movementModifier: 0.8,
                buildable: false,
                harvestable: true,
                spiceAmount: 1000
            },
            
            concrete: {
                name: 'Concrete',
                color: '#BDC3C7',
                movementModifier: 1.2,
                buildable: true,
                harvestable: false
            }
        };
    }

    static getGameConstants() {
        return {
            // Map settings
            MAP_WIDTH: 2000,
            MAP_HEIGHT: 2000,
            TILE_SIZE: 20,
            
            // Resource settings
            STARTING_SPICE: 1000,
            STARTING_POWER: 100,
            MAX_UNIT_COUNT: 50,
            
            // Combat settings
            DAMAGE_VARIANCE: 0.2,
            ARMOR_EFFECTIVENESS: 0.8,
            EXPERIENCE_PER_KILL: 50,
            
            // Movement settings
            PATHFINDING_GRID_SIZE: 10,
            MAX_PATH_LENGTH: 200,
            FORMATION_SPACING: 30,
            
            // Production settings
            PRODUCTION_SPEED_MULTIPLIER: 1.0,
            COST_MULTIPLIER: 1.0,
            
            // AI settings
            AI_UPDATE_INTERVAL: 1000,
            AI_DIFFICULTY_MODIFIERS: {
                easy: { damage: 0.7, health: 0.8, cost: 1.2 },
                normal: { damage: 1.0, health: 1.0, cost: 1.0 },
                hard: { damage: 1.3, health: 1.2, cost: 0.8 }
            }
        };
    }

    static getFactionData() {
        return {
            atreides: {
                name: 'House Atreides',
                color: '#0066CC',
                bonuses: {
                    unitHealth: 1.1,
                    buildSpeed: 1.2
                },
                specialUnits: ['sonic_trooper'],
                description: 'Noble house known for honor and advanced technology.'
            },
            
            harkonnen: {
                name: 'House Harkonnen',
                color: '#CC0000',
                bonuses: {
                    unitDamage: 1.15,
                    buildCost: 0.9
                },
                specialUnits: ['devastator'],
                description: 'Brutal house focused on overwhelming firepower.'
            },
            
            ordos: {
                name: 'House Ordos',
                color: '#00CC00',
                bonuses: {
                    unitSpeed: 1.2,
                    techCost: 0.8
                },
                specialUnits: ['stealth_raider'],
                description: 'Secretive house specializing in stealth and technology.'
            }
        };
    }
}