export class UserExperienceManager {
    constructor(game) {
        this.game = game;
        this.settings = {
            showTooltips: true,
            showTutorials: true,
            autoSave: true,
            autoSaveInterval: 300, // 5 minutes
            keyboardShortcuts: true,
            contextualHelp: true,
            smartNotifications: true,
            adaptiveUI: true
        };
        
        this.helpSystem = new HelpSystem(game);
        this.tutorialSystem = new TutorialSystem(game);
        this.accessibilityManager = new AccessibilityManager(game);
        
        this.userStats = {
            sessionsPlayed: 0,
            totalPlayTime: 0,
            lastPlayTime: Date.now(),
            preferredDifficulty: 'normal',
            mostUsedUnits: {},
            averageGameLength: 0
        };
        
        this.autoSaveTimer = 0;
        this.loadUserStats();
    }
    
    update(deltaTime) {
        // Update auto-save timer
        if (this.settings.autoSave) {
            this.autoSaveTimer += deltaTime;
            if (this.autoSaveTimer >= this.settings.autoSaveInterval) {
                this.performAutoSave();
                this.autoSaveTimer = 0;
            }
        }
        
        // Update user stats
        this.userStats.totalPlayTime += deltaTime;
        
        // Update help system
        this.helpSystem.update(deltaTime);
        
        // Update tutorial system
        this.tutorialSystem.update(deltaTime);
        
        // Update accessibility features
        this.accessibilityManager.update(deltaTime);
    }
    
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveUserSettings();
        
        // Apply settings immediately
        this.applySettings();
    }
    
    applySettings() {
        // Apply keyboard shortcuts
        if (this.settings.keyboardShortcuts) {
            this.enableKeyboardShortcuts();
        } else {
            this.disableKeyboardShortcuts();
        }
        
        // Apply accessibility settings
        this.accessibilityManager.applySettings(this.settings);
        
        // Apply UI adaptations
        if (this.settings.adaptiveUI) {
            this.enableAdaptiveUI();
        }
    }
    
    enableKeyboardShortcuts() {
        const shortcuts = {
            'KeyA': () => this.game.selectAllPlayerUnitsOnScreen(),
            'KeyS': () => this.game.stopSelectedUnits(),
            'KeyH': () => this.game.showHelp(),
            'KeyP': () => this.game.togglePause(),
            'KeyR': () => this.game.restart(),
            'Escape': () => this.game.deselectAll(),
            'Delete': () => this.game.destroySelectedUnits(),
            'F1': () => this.helpSystem.showContextualHelp('general'),
            'F5': () => this.performQuickSave(),
            'F9': () => this.performQuickLoad()
        };
        
        // Add number key shortcuts for control groups
        for (let i = 1; i <= 9; i++) {
            shortcuts[`Digit${i}`] = (event) => {
                if (event.ctrlKey) {
                    this.game.createControlGroup(i);
                } else {
                    this.game.selectControlGroup(i);
                }
            };
        }
        
        this.keyboardShortcuts = shortcuts;
        document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
    }
    
    disableKeyboardShortcuts() {
        document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
        this.keyboardShortcuts = {};
    }
    
    handleKeyboardShortcut(event) {
        if (!this.settings.keyboardShortcuts) return;
        
        const shortcut = this.keyboardShortcuts[event.code];
        if (shortcut && !event.target.matches('input, textarea')) {
            event.preventDefault();
            shortcut(event);
        }
    }
    
    enableAdaptiveUI() {
        // Adapt UI based on user behavior
        this.analyzeUserBehavior();
        this.optimizeUILayout();
    }
    
    analyzeUserBehavior() {
        // Analyze which units the player uses most
        const unitCounts = {};
        this.game.teams.player.units.forEach(unit => {
            unitCounts[unit.type] = (unitCounts[unit.type] || 0) + 1;
        });
        
        // Update user stats
        Object.keys(unitCounts).forEach(unitType => {
            this.userStats.mostUsedUnits[unitType] = 
                (this.userStats.mostUsedUnits[unitType] || 0) + unitCounts[unitType];
        });
    }
    
    optimizeUILayout() {
        // Reorder build panel based on usage
        const mostUsedUnits = Object.entries(this.userStats.mostUsedUnits)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([unitType]) => unitType);
        
        // Update UI to prioritize most used units
        if (this.game.ui.optimizeBuildPanel) {
            this.game.ui.optimizeBuildPanel(mostUsedUnits);
        }
    }
    
    performAutoSave() {
        if (this.game.saveLoadManager) {
            this.game.saveLoadManager.saveGame(0, 'Auto-save');
            if (this.settings.smartNotifications) {
                this.game.ui.showNotification('Auto-saved', 'info', 2000);
            }
        }
    }
    
    performQuickSave() {
        if (this.game.saveLoadManager) {
            this.game.saveLoadManager.saveGame(9, 'Quick-save');
            this.game.ui.showNotification('Quick-saved', 'success');
        }
    }
    
    performQuickLoad() {
        if (this.game.saveLoadManager) {
            this.game.saveLoadManager.loadGame(9);
        }
    }
    
    showSmartNotification(message, type = 'info', context = null) {
        if (!this.settings.smartNotifications) return;
        
        // Filter out repetitive notifications
        if (this.isRepetitiveNotification(message)) return;
        
        // Enhance notification with context
        if (context) {
            message = this.enhanceNotificationWithContext(message, context);
        }
        
        this.game.ui.showNotification(message, type);
    }
    
    isRepetitiveNotification(message) {
        // Simple repetition detection
        const now = Date.now();
        if (!this.lastNotifications) this.lastNotifications = [];
        
        // Remove old notifications (older than 10 seconds)
        this.lastNotifications = this.lastNotifications.filter(n => now - n.time < 10000);
        
        // Check if this message was shown recently
        const recent = this.lastNotifications.find(n => n.message === message);
        if (recent) return true;
        
        // Add to recent notifications
        this.lastNotifications.push({ message, time: now });
        return false;
    }
    
    enhanceNotificationWithContext(message, context) {
        // Add helpful context to notifications
        switch (context.type) {
            case 'resource_shortage':
                return `${message} (Build more harvesters or refineries)`;
            case 'unit_lost':
                return `${message} (Consider better positioning or upgrades)`;
            case 'building_destroyed':
                return `${message} (Build defensive structures nearby)`;
            default:
                return message;
        }
    }
    
    trackUserAction(action, data = {}) {
        // Track user actions for analytics and improvement
        const actionData = {
            action,
            timestamp: Date.now(),
            gameTime: this.game.gameTime,
            ...data
        };
        
        // Store in user stats or send to analytics
        this.storeActionData(actionData);
    }
    
    storeActionData(actionData) {
        // Simple local storage of action data
        if (!this.actionHistory) this.actionHistory = [];
        this.actionHistory.push(actionData);
        
        // Keep only recent actions (last 1000)
        if (this.actionHistory.length > 1000) {
            this.actionHistory = this.actionHistory.slice(-1000);
        }
    }
    
    getPersonalizedRecommendations() {
        const recommendations = [];
        
        // Analyze user behavior and provide recommendations
        const totalUnits = Object.values(this.userStats.mostUsedUnits)
            .reduce((sum, count) => sum + count, 0);
        
        if (totalUnits > 0) {
            const diversity = Object.keys(this.userStats.mostUsedUnits).length;
            if (diversity < 3) {
                recommendations.push({
                    type: 'strategy',
                    message: 'Try experimenting with different unit types for more tactical options',
                    priority: 'medium'
                });
            }
        }
        
        // Check difficulty progression
        if (this.userStats.sessionsPlayed > 5 && this.userStats.preferredDifficulty === 'easy') {
            recommendations.push({
                type: 'progression',
                message: 'Consider trying normal difficulty for a greater challenge',
                priority: 'low'
            });
        }
        
        return recommendations;
    }
    
    saveUserStats() {
        try {
            localStorage.setItem('dune2_user_stats', JSON.stringify(this.userStats));
        } catch (error) {
            console.error('Failed to save user stats:', error);
        }
    }
    
    loadUserStats() {
        try {
            const saved = localStorage.getItem('dune2_user_stats');
            if (saved) {
                this.userStats = { ...this.userStats, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('Failed to load user stats:', error);
        }
    }
    
    saveUserSettings() {
        try {
            localStorage.setItem('dune2_user_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('Failed to save user settings:', error);
        }
    }
    
    loadUserSettings() {
        try {
            const saved = localStorage.getItem('dune2_user_settings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('Failed to load user settings:', error);
        }
    }
}

class HelpSystem {
    constructor(game) {
        this.game = game;
        this.shownContextualHelpTriggers = new Set(); // Track shown help messages
        this.helpTopics = {
            general: 'Use left click to select units, right click to move or attack. Build structures to expand your base.',
            combat: 'Different units have strengths and weaknesses. Use formations and terrain to your advantage.',
            economy: 'Harvest spice to fund your operations. Build refineries near spice fields for efficiency.',
            technology: 'Research new technologies to unlock advanced units and abilities.',
            diplomacy: 'Negotiate with other factions for mutual benefit or strategic advantage.'
        };
    }
    
    update(deltaTime) {
        // Update contextual help triggers
        this.checkContextualTriggers();
    }
    
    checkContextualTriggers() {
        // Show contextual help based on game state
        const firstUnitTrigger = 'first_unit';
        if (this.game.teams.player.units.length === 0 && this.game.teams.player.buildings.length === 1 && !this.shownContextualHelpTriggers.has(firstUnitTrigger)) {
            this.showContextualHelp(firstUnitTrigger);
            this.shownContextualHelpTriggers.add(firstUnitTrigger);
        }
        
        const needHarvesterTrigger = 'need_harvester';
        if (this.game.resources.spice < 100 && this.game.teams.player.units.filter(u => u.type === 'harvester').length === 0 && !this.shownContextualHelpTriggers.has(needHarvesterTrigger)) {
            this.showContextualHelp(needHarvesterTrigger);
            this.shownContextualHelpTriggers.add(needHarvesterTrigger);
        }
    }
    
    showContextualHelp(trigger) {
        const helpMessages = {
            first_unit: 'Build a barracks and train soldiers to defend your base!',
            need_harvester: 'Build a factory and train harvesters to collect spice!',
            under_attack: 'Your base is under attack! Build defensive structures or train more units!',
            low_power: 'Build more power plants to support your growing base!',
            research_available: 'Visit the research lab to unlock new technologies!'
        };
        
        const message = helpMessages[trigger] || this.helpTopics.general;
        this.game.ui.showNotification(message, 'info', 5000);
    }
}

class TutorialSystem {
    constructor(game) {
        this.game = game;
        this.currentTutorial = null;
        this.tutorialStep = 0;
        this.tutorialCompleted = false;
    }
    
    update(deltaTime) {
        if (this.currentTutorial && !this.tutorialCompleted) {
            this.updateCurrentTutorial();
        }
    }
    
    startTutorial(tutorialName) {
        this.currentTutorial = tutorialName;
        this.tutorialStep = 0;
        this.tutorialCompleted = false;
        
        this.showTutorialStep();
    }
    
    updateCurrentTutorial() {
        // Check if current step is completed
        if (this.isTutorialStepCompleted()) {
            this.nextTutorialStep();
        }
    }
    
    isTutorialStepCompleted() {
        // Check completion conditions based on tutorial and step
        switch (this.currentTutorial) {
            case 'basic':
                return this.checkBasicTutorialStep();
            default:
                return false;
        }
    }
    
    checkBasicTutorialStep() {
        switch (this.tutorialStep) {
            case 0: // Select unit
                return this.game.selectedUnits.length > 0;
            case 1: // Move unit
                return this.game.selectedUnits.some(unit => unit.state === 'moving');
            case 2: // Build structure
                return this.game.teams.player.buildings.length > 1;
            default:
                return true;
        }
    }
    
    nextTutorialStep() {
        this.tutorialStep++;
        this.showTutorialStep();
    }
    
    showTutorialStep() {
        const tutorials = {
            basic: [
                'Welcome to Dune 2! Click on a unit to select it.',
                'Great! Now right-click somewhere to move your unit.',
                'Excellent! Now try building a structure by clicking the build button.',
                'Tutorial complete! You\'re ready to play!'
            ]
        };
        
        const steps = tutorials[this.currentTutorial];
        if (steps && this.tutorialStep < steps.length) {
            this.game.ui.showNotification(steps[this.tutorialStep], 'tutorial', 10000);
        } else {
            this.completeTutorial();
        }
    }
    
    completeTutorial() {
        this.tutorialCompleted = true;
        this.game.ui.showNotification('Tutorial completed!', 'success');
    }
}

class AccessibilityManager {
    constructor(game) {
        this.game = game;
        this.settings = {
            highContrast: false,
            largeText: false,
            colorBlindSupport: false,
            screenReaderSupport: false,
            reducedMotion: false
        };
    }
    
    update(deltaTime) {
        // Update accessibility features
    }
    
    applySettings(settings) {
        this.settings = { ...this.settings, ...settings };
        
        if (this.settings.highContrast) {
            this.enableHighContrast();
        }
        
        if (this.settings.largeText) {
            this.enableLargeText();
        }
        
        if (this.settings.colorBlindSupport) {
            this.enableColorBlindSupport();
        }
    }
    
    enableHighContrast() {
        document.body.classList.add('high-contrast');
    }
    
    enableLargeText() {
        document.body.classList.add('large-text');
    }
    
    enableColorBlindSupport() {
        // Implement color blind friendly palette
        document.body.classList.add('colorblind-support');
    }
}