import { GameData } from '../GameData.js';

export class TechnologyTreeManager {
    constructor(game) {
        this.game = game;
        
        // Technology Tree Elements
        this.techTreeEl = document.getElementById('techTree');
        this.techTreeContentEl = document.getElementById('techTreeContent');
        this.techTreeCloseEl = document.getElementById('techTreeClose');
        this.techCategoriesEl = document.getElementById('techCategories');
        this.techGridEl = document.getElementById('techGrid');
        this.techInfoPanelEl = document.getElementById('techInfoPanel');
        this.techNameEl = document.getElementById('techName');
        this.techDescriptionEl = document.getElementById('techDescription');
        this.techCostEl = document.getElementById('techCost');
        this.techTimeEl = document.getElementById('techTime');
        this.techPrereqEl = document.getElementById('techPrerequisites');
        this.techUnlocksEl = document.getElementById('techUnlocks');
        this.researchButtonEl = document.getElementById('researchButton');
        
        // State
        this.selectedTech = null;
        this.selectedCategory = 'military';
        this.isOpen = false;
        
        this.setupTechCategories();
    }

    setupControls() {
        // Setup tech tree toggle button
        const techTreeButton = document.getElementById('techTreeButton');
        if (techTreeButton) {
            techTreeButton.addEventListener('click', () => {
                this.toggle();
            });
        }
        
        // Setup close button
        if (this.techTreeCloseEl) {
            this.techTreeCloseEl.addEventListener('click', () => {
                this.close();
            });
        }
        
        // Setup research button
        if (this.researchButtonEl) {
            this.researchButtonEl.addEventListener('click', () => {
                this.startResearch();
            });
        }
        
        // Setup escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    setupTechCategories() {
        if (!this.techCategoriesEl) return;
        
        const categories = [
            { key: 'military', name: 'Military', icon: '⚔️' },
            { key: 'economic', name: 'Economic', icon: '💰' },
            { key: 'defensive', name: 'Defensive', icon: '🛡️' },
            { key: 'advanced', name: 'Advanced', icon: '🔬' }
        ];
        
        this.techCategoriesEl.innerHTML = '';
        
        categories.forEach(category => {
            const button = document.createElement('button');
            button.className = 'tech-category-button';
            if (category.key === this.selectedCategory) {
                button.classList.add('active');
            }
            
            button.innerHTML = `
                <span class="category-icon">${category.icon}</span>
                <span class="category-name">${category.name}</span>
            `;
            
            button.onclick = () => {
                this.selectCategory(category.key);
            };
            
            this.techCategoriesEl.appendChild(button);
        });
    }

    selectCategory(categoryKey) {
        this.selectedCategory = categoryKey;
        
        // Update category button states
        const categoryButtons = this.techCategoriesEl?.querySelectorAll('.tech-category-button');
        categoryButtons?.forEach((button, index) => {
            const categories = ['military', 'economic', 'defensive', 'advanced'];
            button.classList.toggle('active', categories[index] === categoryKey);
        });
        
        this.updateTechGrid();
    }

    open() {
        if (this.techTreeEl) {
            this.techTreeEl.style.display = 'flex';
            this.isOpen = true;
            this.updateTechGrid();
        }
    }

    close() {
        if (this.techTreeEl) {
            this.techTreeEl.style.display = 'none';
            this.isOpen = false;
            this.selectedTech = null;
            this.updateTechInfo();
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    updateTechGrid() {
        if (!this.techGridEl) return;
        
        this.techGridEl.innerHTML = '';
        
        const technologies = this.getTechnologiesByCategory(this.selectedCategory);
        
        if (technologies.length === 0) {
            this.techGridEl.innerHTML = '<div class="no-techs">No technologies available in this category</div>';
            return;
        }
        
        technologies.forEach(tech => {
            const techButton = this.createTechButton(tech.key, tech.data);
            this.techGridEl.appendChild(techButton);
        });
    }

    getTechnologiesByCategory(category) {
        const allTechs = GameData.getTechnologies();
        const technologies = [];
        
        Object.keys(allTechs).forEach(techKey => {
            const techData = allTechs[techKey];
            if (techData.category === category) {
                technologies.push({
                    key: techKey,
                    data: techData
                });
            }
        });
        
        return technologies;
    }

    createTechButton(techKey, techData) {
        const button = document.createElement('button');
        button.className = 'tech-button';
        
        const techSystem = this.game.technologySystem;
        const isResearched = techSystem?.isResearched(techKey) || false;
        const isResearching = techSystem?.currentResearch === techKey;
        const canResearch = this.canResearchTech(techKey, techData);
        
        if (isResearched) {
            button.classList.add('researched');
        } else if (isResearching) {
            button.classList.add('researching');
        } else if (!canResearch) {
            button.classList.add('disabled');
        }
        
        if (this.selectedTech === techKey) {
            button.classList.add('selected');
        }
        
        // Get tech icon
        const icon = this.getTechIcon(techKey);
        
        button.innerHTML = `
            <div class="tech-icon">${icon}</div>
            <div class="tech-name">${techData.name}</div>
            <div class="tech-cost">🔬 ${techData.cost}</div>
            ${isResearched ? '<div class="tech-status">✓ Researched</div>' : ''}
            ${isResearching ? '<div class="tech-status">🔄 Researching</div>' : ''}
            ${!canResearch && !isResearched ? '<div class="tech-status">🔒 Locked</div>' : ''}
        `;
        
        button.onclick = () => {
            this.selectTech(techKey, techData);
        };
        
        return button;
    }

    getTechIcon(techKey) {
        const icons = {
            // Military
            advanced_weapons: '🔫',
            armor_upgrades: '🛡️',
            tactical_systems: '🎯',
            
            // Economic
            improved_harvesting: '⛏️',
            spice_processing: '🏭',
            resource_management: '📊',
            
            // Defensive
            shield_technology: '🛡️',
            fortification: '🏰',
            early_warning: '📡',
            
            // Advanced
            artificial_intelligence: '🤖',
            quantum_computing: '💻',
            advanced_materials: '🔬'
        };
        
        return icons[techKey] || '🔬';
    }

    canResearchTech(techKey, techData) {
        const techSystem = this.game.technologySystem;
        if (!techSystem) return false;
        
        // Check if already researched
        if (techSystem.isResearched(techKey)) return false;
        
        // Check if currently researching something else
        if (techSystem.currentResearch && techSystem.currentResearch !== techKey) return false;
        
        // Check prerequisites
        if (techData.prerequisites) {
            const hasPrerequisites = techData.prerequisites.every(prereq => 
                techSystem.isResearched(prereq)
            );
            if (!hasPrerequisites) return false;
        }
        
        // Check cost
        const resourceManager = this.game.resourceManager;
        if (resourceManager && techData.cost > (resourceManager.resources.technology || 0)) {
            return false;
        }
        
        return true;
    }

    selectTech(techKey, techData) {
        this.selectedTech = techKey;
        
        // Update button states
        const techButtons = this.techGridEl?.querySelectorAll('.tech-button');
        techButtons?.forEach(button => {
            button.classList.remove('selected');
        });
        
        // Find and select the clicked button
        const clickedButton = Array.from(techButtons || []).find(button => 
            button.querySelector('.tech-name')?.textContent === techData.name
        );
        if (clickedButton) {
            clickedButton.classList.add('selected');
        }
        
        this.updateTechInfo(techKey, techData);
    }

    updateTechInfo(techKey = null, techData = null) {
        if (!techKey || !techData) {
            if (this.techInfoPanelEl) {
                this.techInfoPanelEl.style.display = 'none';
            }
            return;
        }
        
        if (this.techInfoPanelEl) {
            this.techInfoPanelEl.style.display = 'block';
        }
        
        if (this.techNameEl) this.techNameEl.textContent = techData.name;
        if (this.techDescriptionEl) this.techDescriptionEl.textContent = techData.description || 'No description available.';
        if (this.techCostEl) this.techCostEl.textContent = `${techData.cost} Research Points`;
        if (this.techTimeEl) this.techTimeEl.textContent = `${techData.time || 60} seconds`;
        
        // Update prerequisites
        if (this.techPrereqEl) {
            if (techData.prerequisites && techData.prerequisites.length > 0) {
                this.techPrereqEl.innerHTML = techData.prerequisites.map(prereq => {
                    const prereqData = GameData.getTechnologies()[prereq];
                    const isResearched = this.game.technologySystem?.isResearched(prereq);
                    return `<span class="prereq ${isResearched ? 'completed' : 'pending'}">${prereqData?.name || prereq}</span>`;
                }).join('');
            } else {
                this.techPrereqEl.textContent = 'None';
            }
        }
        
        // Update unlocks
        if (this.techUnlocksEl) {
            const unlocks = this.getTechUnlocks(techKey);
            if (unlocks.length > 0) {
                this.techUnlocksEl.innerHTML = unlocks.join(', ');
            } else {
                this.techUnlocksEl.textContent = 'Various improvements';
            }
        }
        
        // Update research button
        this.updateResearchButton(techKey, techData);
    }

    getTechUnlocks(techKey) {
        const unlocks = [];
        
        // Check what buildings this tech unlocks
        const buildings = GameData.getBuildingTypes();
        Object.keys(buildings).forEach(buildingKey => {
            const building = buildings[buildingKey];
            if (building.requiredTech === techKey) {
                unlocks.push(building.name);
            }
        });
        
        // Check what units this tech unlocks
        const units = GameData.getUnitTypes();
        Object.keys(units).forEach(unitKey => {
            const unit = units[unitKey];
            if (unit.requiredTech === techKey) {
                unlocks.push(unit.name);
            }
        });
        
        return unlocks;
    }

    updateResearchButton(techKey, techData) {
        if (!this.researchButtonEl) return;
        
        const techSystem = this.game.technologySystem;
        const isResearched = techSystem?.isResearched(techKey);
        const isResearching = techSystem?.currentResearch === techKey;
        const canResearch = this.canResearchTech(techKey, techData);
        
        if (isResearched) {
            this.researchButtonEl.textContent = '✓ Researched';
            this.researchButtonEl.disabled = true;
            this.researchButtonEl.className = 'research-button researched';
        } else if (isResearching) {
            const progress = techSystem.researchProgress || 0;
            this.researchButtonEl.textContent = `🔄 Researching (${progress.toFixed(0)}%)`;
            this.researchButtonEl.disabled = true;
            this.researchButtonEl.className = 'research-button researching';
        } else if (canResearch) {
            this.researchButtonEl.textContent = '🔬 Start Research';
            this.researchButtonEl.disabled = false;
            this.researchButtonEl.className = 'research-button available';
        } else {
            this.researchButtonEl.textContent = '🔒 Cannot Research';
            this.researchButtonEl.disabled = true;
            this.researchButtonEl.className = 'research-button disabled';
        }
    }

    startResearch() {
        if (!this.selectedTech) return;
        
        const techSystem = this.game.technologySystem;
        if (techSystem && techSystem.startResearch) {
            const success = techSystem.startResearch(this.selectedTech);
            if (success) {
                this.game.ui?.showNotification(
                    `Started researching ${GameData.getTechnologies()[this.selectedTech].name}`,
                    'success'
                );
                this.updateTechGrid();
                this.updateTechInfo(this.selectedTech, GameData.getTechnologies()[this.selectedTech]);
            } else {
                this.game.ui?.showNotification(
                    'Cannot start research: Requirements not met',
                    'error'
                );
            }
        }
    }
}