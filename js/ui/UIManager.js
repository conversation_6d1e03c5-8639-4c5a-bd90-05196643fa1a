import { BuildPanel } from './BuildPanel.js';
import { MinimapManager } from './MinimapManager.js';
import { NotificationManager } from './NotificationManager.js';
import { PanelManager } from './PanelManager.js';
import { ResourcePanel } from './ResourcePanel.js';
import { TechnologyTreeManager } from './TechnologyTreeManager.js';
import { UnitInfoPanel } from './UnitInfoPanel.js';

export class UIManager {
    constructor(game) {
        this.game = game;
        
        // Check if DOM is ready before initializing UI components
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeUIComponents();
            });
        } else {
            this.initializeUIComponents();
        }
    }

    initializeUIComponents() {
        console.log('🎮 DUNE2 DEBUG: UIManager initializing components');
        
        // Verify critical DOM elements exist before creating UI components
        const requiredElements = [
            'buildPanel', 'resourcePanel', 'unitInfoPanel',
            'minimapCanvas', 'gameCanvas'
        ];
        
        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        if (missingElements.length > 0) {
            console.error('❌ DUNE2 DEBUG: Missing DOM elements:', missingElements);
            // Retry after a short delay
            setTimeout(() => this.initializeUIComponents(), 100);
            return;
        }
        
        // Initialize UI subsystems
        this.resourcePanel = new ResourcePanel(this.game);
        this.unitInfoPanel = new UnitInfoPanel(this.game);
        this.buildPanel = new BuildPanel(this.game);
        this.minimapManager = new MinimapManager(this.game);
        this.technologyTree = new TechnologyTreeManager(this.game);
        this.notifications = new NotificationManager();
        this.panelManager = new PanelManager(this.game);
        
        // UI state
        this.selectedTech = null;
        
        // Initialize enhanced features
        this.initializeEnhancedFeatures();
        
        console.log('✅ DUNE2 DEBUG: UIManager components initialized successfully');
    }

    initializeEnhancedFeatures() {
        // Setup minimap controls
        this.minimapManager.setupControls();
        
        // Setup technology tree
        this.technologyTree.setupControls();
        
        // Setup terrain tooltip
        this.setupTerrainTooltip();
        
        // Setup resource rate tracking
        this.resourcePanel.setupTracking();
        
        // Setup collapsible panels
        this.panelManager.setupCollapsiblePanels();
        
        // Setup music controls
        this.setupMusicControls();
    }

    setupTerrainTooltip() {
        // This will be called by the game when hovering over terrain
        if (this.game.canvas) {
            this.game.canvas.addEventListener('mousemove', (e) => {
                const showTerrainToggle = document.getElementById('showTerrainToggle');
                if (showTerrainToggle && showTerrainToggle.checked) {
                    this.updateTerrainTooltip(e);
                }
            });
        }
    }

    setupMusicControls() {
        // Setup music volume control
        const musicVolumeSlider = document.getElementById('musicVolume');
        if (musicVolumeSlider) {
            musicVolumeSlider.addEventListener('input', (e) => {
                const volume = parseFloat(e.target.value);
                this.game.soundManager.setMusicVolume(volume);
            });
        }
        
        // Setup music toggle
        const musicToggle = document.getElementById('musicToggle');
        if (musicToggle) {
            musicToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.game.soundManager.enableMusic();
                } else {
                    this.game.soundManager.disableMusic();
                }
            });
        }
        
        // Setup faction theme buttons (if they exist in the UI)
        const factionButtons = document.querySelectorAll('.faction-theme-button');
        factionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const factionName = button.dataset.faction;
                this.game.playFactionTheme(factionName);
            });
        });
    }

    updateResourceDisplay() {
        this.resourcePanel.update();
    }

    updateBuildPanel() {
        this.buildPanel.update();
    }

    updateUnitInfoPanel() {
        this.unitInfoPanel.update();
    }

    showNotification(message, type = 'info', duration = 3000) {
        this.notifications.show(message, type, duration);
    }

    togglePauseOverlay(isPaused) {
        const pauseOverlayEl = document.getElementById('pauseOverlay');
        if (pauseOverlayEl) {
            pauseOverlayEl.style.display = isPaused ? 'flex' : 'none';
        }
    }

    updateSelectionBox(isActive, x, y, width, height) {
        const selectionBoxEl = document.getElementById('selectionBox');
        if (!selectionBoxEl) return;
        
        if (isActive) {
            selectionBoxEl.style.left = `${x}px`;
            selectionBoxEl.style.top = `${y}px`;
            selectionBoxEl.style.width = `${width}px`;
            selectionBoxEl.style.height = `${height}px`;
            selectionBoxEl.style.display = 'block';
        } else {
            selectionBoxEl.style.display = 'none';
        }
    }

    // Enhanced Minimap Methods
    drawMinimapStatic() {
        this.minimapManager.drawStatic();
    }

    updateMinimapDynamic() {
        this.minimapManager.updateDynamic();
    }

    updateViewportRect(viewX, viewY, width, height) {
        this.minimapManager.updateViewportRect(viewX, viewY, width, height);
    }

    updateTerrainDisplay() {
        // Update terrain-related display elements
        // This could involve showing/hiding terrain overlays
    }

    updateTerrainTooltip(event) {
        // Calculate world coordinates from mouse position
        // Show terrain information tooltip
        // This would need integration with the game's terrain system
    }

    // Technology Tree Methods
    openTechTree() {
        this.technologyTree.open();
    }

    closeTechTree() {
        this.technologyTree.close();
    }

    formatStats() {
        const stats = this.game.teams.player.stats;
        return `
            <p>Units Built: ${stats.unitsBuilt}</p>
            <p>Units Lost: ${stats.unitsLost}</p>
            <p>Buildings Built: ${stats.buildingsBuilt}</p>
            <p>Buildings Lost: ${stats.buildingsLost}</p>
            <p>Spice Collected: ${Math.floor(stats.spiceCollected)}</p>
            <p>Enemies Destroyed: ${stats.enemiesDestroyed}</p>
        `;
    }

    // Backward compatibility methods
    updateGridLayout() {
        this.panelManager.updateGridLayout();
    }
}