export class PanelManager {
    constructor(game) {
        this.game = game;
        
        // Panel state
        this.collapsedPanels = new Set();
        this.panelSizes = new Map();
        
        // Grid layout state
        this.gridEnabled = false;
        this.snapToGrid = true;
        this.gridSize = 20;
        
        this.initializePanels();
    }

    initializePanels() {
        // Find all collapsible panels
        this.setupCollapsiblePanels();
        
        // Setup grid layout controls
        this.setupGridControls();
        
        // Setup panel resize handlers
        this.setupResizeHandlers();
    }

    setupCollapsiblePanels() {
        const panels = document.querySelectorAll('.collapsible-panel');
        
        panels.forEach(panel => {
            const header = panel.querySelector('.panel-header');
            const content = panel.querySelector('.panel-content');
            const toggleButton = panel.querySelector('.panel-toggle');
            
            if (header && content) {
                // Create toggle button if it doesn't exist
                if (!toggleButton) {
                    const button = document.createElement('button');
                    button.className = 'panel-toggle';
                    button.innerHTML = '−';
                    button.title = 'Collapse panel';
                    header.appendChild(button);
                    
                    button.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.togglePanel(panel);
                    });
                } else {
                    toggleButton.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.togglePanel(panel);
                    });
                }
                
                // Allow header click to toggle
                header.addEventListener('click', () => {
                    this.togglePanel(panel);
                });
                
                // Store original height
                if (!this.panelSizes.has(panel.id)) {
                    this.panelSizes.set(panel.id, {
                        height: content.offsetHeight,
                        width: panel.offsetWidth
                    });
                }
            }
        });
    }

    togglePanel(panel) {
        const content = panel.querySelector('.panel-content');
        const toggleButton = panel.querySelector('.panel-toggle');
        
        if (!content) return;
        
        const isCollapsed = this.collapsedPanels.has(panel.id);
        
        if (isCollapsed) {
            // Expand panel
            this.collapsedPanels.delete(panel.id);
            content.style.display = 'block';
            content.style.height = 'auto';
            panel.classList.remove('collapsed');
            
            if (toggleButton) {
                toggleButton.innerHTML = '−';
                toggleButton.title = 'Collapse panel';
            }
            
            // Animate expansion
            requestAnimationFrame(() => {
                content.style.transition = 'height 0.3s ease-in-out';
                content.style.height = this.panelSizes.get(panel.id)?.height + 'px' || 'auto';
            });
        } else {
            // Collapse panel
            this.collapsedPanels.add(panel.id);
            
            // Store current height before collapsing
            this.panelSizes.set(panel.id, {
                ...this.panelSizes.get(panel.id),
                height: content.offsetHeight
            });
            
            content.style.transition = 'height 0.3s ease-in-out';
            content.style.height = '0px';
            panel.classList.add('collapsed');
            
            if (toggleButton) {
                toggleButton.innerHTML = '+';
                toggleButton.title = 'Expand panel';
            }
            
            // Hide content after animation
            setTimeout(() => {
                content.style.display = 'none';
            }, 300);
        }
    }

    setupGridControls() {
        // Setup grid toggle
        const gridToggle = document.getElementById('gridToggle');
        if (gridToggle) {
            gridToggle.addEventListener('change', (e) => {
                this.gridEnabled = e.target.checked;
                this.updateGridDisplay();
            });
        }
        
        // Setup snap to grid toggle
        const snapToggle = document.getElementById('snapToGrid');
        if (snapToggle) {
            snapToggle.addEventListener('change', (e) => {
                this.snapToGrid = e.target.checked;
            });
        }
        
        // Setup grid size control
        const gridSizeInput = document.getElementById('gridSize');
        if (gridSizeInput) {
            gridSizeInput.addEventListener('input', (e) => {
                this.gridSize = parseInt(e.target.value) || 20;
                this.updateGridDisplay();
            });
        }
    }

    updateGridDisplay() {
        const gameCanvas = this.game.canvas;
        if (!gameCanvas) return;
        
        // Remove existing grid overlay
        const existingGrid = document.getElementById('gridOverlay');
        if (existingGrid) {
            existingGrid.remove();
        }
        
        if (!this.gridEnabled) return;
        
        // Create grid overlay
        const gridOverlay = document.createElement('canvas');
        gridOverlay.id = 'gridOverlay';
        gridOverlay.width = gameCanvas.width;
        gridOverlay.height = gameCanvas.height;
        gridOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1000;
            opacity: 0.3;
        `;
        
        gameCanvas.parentNode.insertBefore(gridOverlay, gameCanvas.nextSibling);
        
        // Draw grid
        this.drawGrid(gridOverlay);
    }

    drawGrid(canvas) {
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 1;
        ctx.setLineDash([2, 2]);
        
        // Draw vertical lines
        for (let x = 0; x <= width; x += this.gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        
        // Draw horizontal lines
        for (let y = 0; y <= height; y += this.gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
    }

    setupResizeHandlers() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.updateGridDisplay();
            this.updatePanelPositions();
        });
        
        // Setup panel drag and resize functionality
        this.setupPanelDragging();
    }

    setupPanelDragging() {
        const draggablePanels = document.querySelectorAll('.draggable-panel');
        
        draggablePanels.forEach(panel => {
            const header = panel.querySelector('.panel-header');
            if (!header) return;
            
            let isDragging = false;
            let startX, startY, startLeft, startTop;
            
            header.style.cursor = 'move';
            
            header.addEventListener('mousedown', (e) => {
                if (e.target.classList.contains('panel-toggle')) return;
                
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                
                const rect = panel.getBoundingClientRect();
                startLeft = rect.left;
                startTop = rect.top;
                
                panel.style.position = 'absolute';
                panel.style.zIndex = '10000';
                
                e.preventDefault();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                
                let newLeft = startLeft + (e.clientX - startX);
                let newTop = startTop + (e.clientY - startY);
                
                // Snap to grid if enabled
                if (this.snapToGrid && this.gridEnabled) {
                    newLeft = Math.round(newLeft / this.gridSize) * this.gridSize;
                    newTop = Math.round(newTop / this.gridSize) * this.gridSize;
                }
                
                // Keep panel within viewport
                const maxLeft = window.innerWidth - panel.offsetWidth;
                const maxTop = window.innerHeight - panel.offsetHeight;
                
                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));
                
                panel.style.left = newLeft + 'px';
                panel.style.top = newTop + 'px';
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    panel.style.zIndex = '';
                }
            });
        });
    }

    updatePanelPositions() {
        // Update panel positions after window resize
        const panels = document.querySelectorAll('.draggable-panel');
        
        panels.forEach(panel => {
            if (panel.style.position === 'absolute') {
                const rect = panel.getBoundingClientRect();
                const maxLeft = window.innerWidth - panel.offsetWidth;
                const maxTop = window.innerHeight - panel.offsetHeight;
                
                if (rect.left > maxLeft) {
                    panel.style.left = maxLeft + 'px';
                }
                if (rect.top > maxTop) {
                    panel.style.top = maxTop + 'px';
                }
            }
        });
    }

    updateGridLayout() {
        // Legacy method for backward compatibility
        this.updateGridDisplay();
    }

    // Panel state management
    saveLayout() {
        const layout = {
            collapsedPanels: Array.from(this.collapsedPanels),
            panelSizes: Object.fromEntries(this.panelSizes),
            gridEnabled: this.gridEnabled,
            snapToGrid: this.snapToGrid,
            gridSize: this.gridSize
        };
        
        localStorage.setItem('dune2_ui_layout', JSON.stringify(layout));
    }

    loadLayout() {
        try {
            const saved = localStorage.getItem('dune2_ui_layout');
            if (!saved) return;
            
            const layout = JSON.parse(saved);
            
            this.collapsedPanels = new Set(layout.collapsedPanels || []);
            this.panelSizes = new Map(Object.entries(layout.panelSizes || {}));
            this.gridEnabled = layout.gridEnabled || false;
            this.snapToGrid = layout.snapToGrid !== false;
            this.gridSize = layout.gridSize || 20;
            
            // Apply loaded state
            this.applyLayout();
        } catch (error) {
            console.warn('Failed to load UI layout:', error);
        }
    }

    applyLayout() {
        // Apply collapsed state
        this.collapsedPanels.forEach(panelId => {
            const panel = document.getElementById(panelId);
            if (panel) {
                this.togglePanel(panel);
            }
        });
        
        // Apply grid settings
        const gridToggle = document.getElementById('gridToggle');
        if (gridToggle) {
            gridToggle.checked = this.gridEnabled;
        }
        
        const snapToggle = document.getElementById('snapToGrid');
        if (snapToggle) {
            snapToggle.checked = this.snapToGrid;
        }
        
        const gridSizeInput = document.getElementById('gridSize');
        if (gridSizeInput) {
            gridSizeInput.value = this.gridSize;
        }
        
        this.updateGridDisplay();
    }

    resetLayout() {
        // Reset all panels to default state
        this.collapsedPanels.clear();
        this.panelSizes.clear();
        this.gridEnabled = false;
        this.snapToGrid = true;
        this.gridSize = 20;
        
        // Remove saved layout
        localStorage.removeItem('dune2_ui_layout');
        
        // Reset panel positions
        const panels = document.querySelectorAll('.draggable-panel');
        panels.forEach(panel => {
            panel.style.position = '';
            panel.style.left = '';
            panel.style.top = '';
            panel.classList.remove('collapsed');
            
            const content = panel.querySelector('.panel-content');
            if (content) {
                content.style.display = 'block';
                content.style.height = 'auto';
            }
            
            const toggleButton = panel.querySelector('.panel-toggle');
            if (toggleButton) {
                toggleButton.innerHTML = '−';
                toggleButton.title = 'Collapse panel';
            }
        });
        
        this.updateGridDisplay();
    }
}