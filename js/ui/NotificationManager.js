export class NotificationManager {
    constructor() {
        this.notifications = [];
        this.notificationContainer = null;
        this.maxNotifications = 5;
        this.defaultDuration = 3000;
        
        this.createContainer();
    }

    createContainer() {
        // Check if container already exists
        this.notificationContainer = document.getElementById('notificationContainer');
        
        if (!this.notificationContainer) {
            // Create notification container if it doesn't exist
            this.notificationContainer = document.createElement('div');
            this.notificationContainer.id = 'notificationContainer';
            this.notificationContainer.className = 'notification-container';
            this.notificationContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 300px;
            `;
            document.body.appendChild(this.notificationContainer);
        }
    }

    show(message, type = 'info', duration = null) {
        const notification = this.createNotification(message, type, duration || this.defaultDuration);
        this.addNotification(notification);
        return notification.id;
    }

    createNotification(message, type, duration) {
        const id = Date.now() + Math.random();
        
        const notification = {
            id,
            message,
            type,
            duration,
            element: null,
            timeoutId: null
        };
        
        // Create DOM element
        const element = document.createElement('div');
        element.className = `notification notification-${type}`;
        element.style.cssText = `
            background: ${this.getBackgroundColor(type)};
            color: ${this.getTextColor(type)};
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            border-left: 4px solid ${this.getBorderColor(type)};
            font-size: 14px;
            line-height: 1.4;
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
            opacity: 0;
            max-width: 100%;
            word-wrap: break-word;
        `;
        
        // Add icon and message
        const icon = this.getIcon(type);
        element.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 8px;">
                <span style="flex-shrink: 0; font-size: 16px;">${icon}</span>
                <span style="flex: 1;">${message}</span>
                <span style="flex-shrink: 0; opacity: 0.7; cursor: pointer; font-weight: bold;">&times;</span>
            </div>
        `;
        
        // Add click to dismiss
        element.addEventListener('click', () => {
            this.dismiss(notification.id);
        });
        
        notification.element = element;
        
        return notification;
    }

    addNotification(notification) {
        // Remove oldest notifications if we exceed the limit
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.removeNotification(oldest);
        }
        
        this.notifications.push(notification);
        this.notificationContainer.appendChild(notification.element);
        
        // Trigger animation
        requestAnimationFrame(() => {
            notification.element.style.transform = 'translateX(0)';
            notification.element.style.opacity = '1';
        });
        
        // Set auto-dismiss timer
        if (notification.duration > 0) {
            notification.timeoutId = setTimeout(() => {
                this.dismiss(notification.id);
            }, notification.duration);
        }
    }

    dismiss(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;
        
        // Clear timeout
        if (notification.timeoutId) {
            clearTimeout(notification.timeoutId);
        }
        
        // Animate out
        notification.element.style.transform = 'translateX(100%)';
        notification.element.style.opacity = '0';
        
        // Remove after animation
        setTimeout(() => {
            this.removeNotification(notification);
        }, 300);
    }

    removeNotification(notification) {
        // Remove from array
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
        
        // Remove from DOM
        if (notification.element && notification.element.parentNode) {
            notification.element.parentNode.removeChild(notification.element);
        }
        
        // Clear timeout
        if (notification.timeoutId) {
            clearTimeout(notification.timeoutId);
        }
    }

    clear() {
        // Dismiss all notifications
        const notificationIds = this.notifications.map(n => n.id);
        notificationIds.forEach(id => this.dismiss(id));
    }

    getBackgroundColor(type) {
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            warning: '#FF9800',
            error: '#F44336',
            achievement: '#9C27B0'
        };
        return colors[type] || colors.info;
    }

    getTextColor(type) {
        return '#FFFFFF';
    }

    getBorderColor(type) {
        const colors = {
            info: '#1976D2',
            success: '#388E3C',
            warning: '#F57C00',
            error: '#D32F2F',
            achievement: '#7B1FA2'
        };
        return colors[type] || colors.info;
    }

    getIcon(type) {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            achievement: '🏆'
        };
        return icons[type] || icons.info;
    }

    // Convenience methods
    info(message, duration) {
        return this.show(message, 'info', duration);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    achievement(message, duration) {
        return this.show(message, 'achievement', duration || 5000);
    }

    // Game-specific notification methods
    unitProduced(unitName) {
        this.success(`${unitName} training complete`);
    }

    buildingComplete(buildingName) {
        this.success(`${buildingName} construction complete`);
    }

    researchComplete(techName) {
        this.achievement(`Research complete: ${techName}`);
    }

    underAttack(location) {
        this.error(`Base under attack${location ? ` at ${location}` : ''}!`);
    }

    resourceLow(resourceName) {
        this.warning(`${resourceName} running low`);
    }

    missionObjective(objective) {
        this.info(objective, 5000);
    }

    levelUp(unitName, level) {
        this.achievement(`${unitName} promoted to level ${level}!`);
    }

    spiceDepletion(location) {
        this.warning(`Spice field depleted${location ? ` at ${location}` : ''}`);
    }

    enemyDetected(enemyType, location) {
        this.warning(`${enemyType} detected${location ? ` at ${location}` : ''}`);
    }

    allianceFormed(factionName) {
        this.success(`Alliance formed with ${factionName}`);
    }

    allianceBroken(factionName) {
        this.error(`Alliance broken with ${factionName}`);
    }
}