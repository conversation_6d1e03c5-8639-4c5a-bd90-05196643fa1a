import { GameData } from '../GameData.js';
import { getUnitSVG } from '../GameSVGs.js';

export class UnitInfoPanel {
    constructor(game) {
        this.game = game;
        
        // Enhanced Unit Info Panel Elements
        this.unitInfoPanelEl = document.getElementById('unitInfoPanel');
        this.unitNameEl = document.getElementById('infoPanelName');
        this.unitTypeEl = document.getElementById('unitType');
        this.unitSpriteEl = document.getElementById('unitSprite');
        this.unitLevelEl = document.getElementById('unitLevel');
        this.unitExperienceEl = document.getElementById('unitExperience');
        this.unitXpFillEl = document.getElementById('unitXpFill');
        this.unitStatusBadgesEl = document.getElementById('unitStatusBadges');
        
        // Enhanced stat elements
        this.unitHealthCurrentEl = document.getElementById('infoPanelHealthCurrent');
        this.unitHealthMaxEl = document.getElementById('infoPanelHealthMax');
        this.unitHealthFillEl = document.getElementById('unitHealthFill');
        this.unitAttackEl = document.getElementById('infoPanelAttack');
        this.unitDefenseEl = document.getElementById('infoPanelDefense');
        this.unitSpeedEl = document.getElementById('infoPanelSpeed');
        this.unitRangeEl = document.getElementById('infoPanelRange');
        
        // Abilities and commands
        this.unitAbilitiesEl = document.getElementById('unitAbilities');
        this.abilityListEl = document.getElementById('abilityList');
        this.unitCommandsEl = document.getElementById('unitCommands');
        
        // Production and build elements
        this.productionQueuePanelEl = document.getElementById('productionQueue');
        this.queueItemsEl = document.getElementById('queueItems');
        
        // Legacy elements for compatibility
        this.unitVisualEl = document.getElementById('unitVisual');
        this.unitSpriteOldEl = document.getElementById('unitSpriteOld');
        this.unitStatsEl = document.getElementById('unitStats');
        this.unitHealthEl = document.getElementById('infoPanelHealth');
        this.unitAttackOldEl = document.getElementById('infoPanelAttackOld');
        this.unitDefenseOldEl = document.getElementById('infoPanelDefenseOld');
        this.unitSpeedOldEl = document.getElementById('infoPanelSpeedOld');
    }

    update() {
        if (this.game.selectedUnits.length === 1) {
            const unit = this.game.selectedUnits[0];
            // Only show info for alive units
            if (unit && unit.isAlive()) {
                this.showEnhancedUnitInfo(unit);
            } else {
                this.hide();
            }
        } else if (this.game.selectedUnits.length > 1) {
            // Filter out dead units
            const aliveUnits = this.game.selectedUnits.filter(unit => unit && unit.isAlive());
            if (aliveUnits.length > 1) {
                this.showMultiUnitInfo();
            } else if (aliveUnits.length === 1) {
                this.showEnhancedUnitInfo(aliveUnits[0]);
            } else {
                this.hide();
            }
        } else if (this.game.selectedBuilding) {
            // Only show info for alive buildings
            if (this.game.selectedBuilding.isAlive()) {
                this.showBuildingInfo(this.game.selectedBuilding);
            } else {
                this.hide();
            }
        } else {
            this.hide();
        }
    }

    hide() {
        if (this.unitInfoPanelEl) {
            this.unitInfoPanelEl.style.display = 'none';
        }
    }

    showEnhancedUnitInfo(unit) {
        if (!this.unitInfoPanelEl) return;
        
        this.unitInfoPanelEl.style.display = 'flex';
        
        // Update unit identity
        if (this.unitNameEl) this.unitNameEl.textContent = unit.name;
        if (this.unitTypeEl) this.unitTypeEl.textContent = this.getUnitCategory(unit.type);
        
        // Update unit portrait
        this.updateUnitPortrait(unit);
        
        // Update level and experience
        this.updateLevelAndExperience(unit);
        
        // Update status badges
        this.updateStatusBadges(unit);
        
        // Update stats
        this.updateUnitStats(unit);
        
        // Update health bar
        this.updateHealthBar(unit);
        
        // Update abilities
        this.updateUnitAbilities(unit);
        
        // Update commands
        this.updateUnitCommands(unit);
        
        // Hide legacy elements
        this.hideLegacyElements();
    }

    updateUnitPortrait(unit) {
        if (!this.unitSpriteEl) return;
        
        const neutralUnitSvgMap = {
            'marauder_scout': 'soldier',
            'marauder_raider': 'tank',
            'marauder_heavy': 'tank',
            'pirate_fighter': 'soldier',
            'pirate_corsair': 'tank',
            'pirate_captain': 'tank'
        };
        
        const svgType = neutralUnitSvgMap[unit.type] || unit.type;
        const svgDataURL = getUnitSVG(svgType);
        
        if (svgDataURL) {
            this.unitSpriteEl.src = svgDataURL;
        }
        this.unitSpriteEl.alt = unit.name;
    }

    updateLevelAndExperience(unit) {
        if (unit.level !== undefined) {
            if (this.unitLevelEl) {
                this.unitLevelEl.textContent = `Lvl ${unit.level}`;
                this.unitLevelEl.style.display = 'block';
            }
            if (this.unitExperienceEl) {
                this.unitExperienceEl.style.display = 'block';
            }
            
            if (this.unitXpFillEl) {
                const xpPercent = ((unit.experience || 0) / (unit.experienceToNext || 100)) * 100;
                this.unitXpFillEl.style.width = `${xpPercent}%`;
            }
        } else {
            if (this.unitLevelEl) this.unitLevelEl.style.display = 'none';
            if (this.unitExperienceEl) this.unitExperienceEl.style.display = 'none';
        }
    }

    updateUnitStats(unit) {
        if (this.unitHealthCurrentEl) this.unitHealthCurrentEl.textContent = Math.floor(unit.health);
        if (this.unitHealthMaxEl) this.unitHealthMaxEl.textContent = unit.maxHealth;
        if (this.unitAttackEl) this.unitAttackEl.textContent = unit.damage || 0;
        if (this.unitDefenseEl) this.unitDefenseEl.textContent = unit.armor || 0;
        if (this.unitSpeedEl) this.unitSpeedEl.textContent = unit.speed.toFixed(1);
        if (this.unitRangeEl) this.unitRangeEl.textContent = unit.range || 0;
    }

    updateHealthBar(unit) {
        if (!this.unitHealthFillEl) return;
        
        const healthPercent = (unit.health / unit.maxHealth) * 100;
        this.unitHealthFillEl.style.width = `${healthPercent}%`;
        this.unitHealthFillEl.classList.remove('high', 'medium', 'low');
        
        if (healthPercent > 66) {
            this.unitHealthFillEl.classList.add('high');
        } else if (healthPercent > 33) {
            this.unitHealthFillEl.classList.add('medium');
        } else {
            this.unitHealthFillEl.classList.add('low');
        }
    }

    getUnitCategory(unitType) {
        const unitData = GameData.getUnitTypes()[unitType];
        if (!unitData) return 'Unknown';
        
        const categoryNames = {
            'infantry': 'Infantry Unit',
            'vehicle': 'Ground Vehicle',
            'air': 'Air Unit',
            'naval': 'Naval Unit'
        };
        
        return categoryNames[unitData.category] || 'Unit';
    }

    updateStatusBadges(unit) {
        if (!this.unitStatusBadgesEl) return;
        
        this.unitStatusBadgesEl.innerHTML = '';
        
        // Add various status badges based on unit properties
        if (unit.veteran) this.addStatusBadge('Veteran', 'veteran');
        if (unit.stealth) this.addStatusBadge('Stealth', 'stealth');
        if (unit.deployed) this.addStatusBadge('Deployed', 'deployed');
        if (unit.formation) this.addStatusBadge('Formation', 'formation');
        if (unit.canHeal) this.addStatusBadge('Medic', 'medic');
        if (unit.canRepair) this.addStatusBadge('Engineer', 'engineer');
    }

    addStatusBadge(text, type) {
        if (!this.unitStatusBadgesEl) return;
        
        const badge = document.createElement('span');
        badge.className = `status-badge ${type}`;
        badge.textContent = text;
        this.unitStatusBadgesEl.appendChild(badge);
    }

    updateUnitAbilities(unit) {
        if (!this.abilityListEl) return;
        
        this.abilityListEl.innerHTML = '';
        
        // Check for unit-specific abilities
        const abilities = this.getUnitAbilities(unit);
        
        if (abilities.length > 0) {
            if (this.unitAbilitiesEl) this.unitAbilitiesEl.style.display = 'block';
            
            abilities.forEach(ability => {
                const button = document.createElement('button');
                button.className = 'ability-button';
                if (ability.cooldown > 0) {
                    button.classList.add('cooldown');
                }
                
                button.innerHTML = `
                    <span class="ability-icon">${ability.icon}</span>
                    <span class="ability-name">${ability.name}</span>
                    <span class="ability-cooldown">${ability.cooldown > 0 ? `${ability.cooldown}s` : 'Ready'}</span>
                `;
                
                if (ability.cooldown === 0) {
                    button.onclick = () => this.game.useUnitAbility(unit, ability.action);
                }
                
                this.abilityListEl.appendChild(button);
            });
        } else {
            if (this.unitAbilitiesEl) this.unitAbilitiesEl.style.display = 'none';
        }
    }

    getUnitAbilities(unit) {
        const abilities = [];
        
        if (unit.deployable) {
            abilities.push({
                icon: '🎯',
                name: 'Deploy',
                cooldown: unit.deployCooldown || 0,
                action: 'deploy'
            });
        }
        
        if (unit.canHeal) {
            abilities.push({
                icon: '⚕️',
                name: 'Heal',
                cooldown: unit.healCooldown || 0,
                action: 'heal'
            });
        }
        
        if (unit.canRepair) {
            abilities.push({
                icon: '🔧',
                name: 'Repair',
                cooldown: unit.repairCooldown || 0,
                action: 'repair'
            });
        }
        
        if (unit.stealth) {
            abilities.push({
                icon: '👤',
                name: 'Stealth',
                cooldown: unit.stealthCooldown || 0,
                action: 'stealth'
            });
        }
        
        return abilities;
    }

    updateUnitCommands(unit) {
        if (!this.unitCommandsEl) return;
        
        this.unitCommandsEl.innerHTML = '';
        
        // Standard commands
        const commands = [
            { name: 'Move', action: 'move', icon: '➡️' },
            { name: 'Stop', action: 'stop', icon: '⏹️' }
        ];
        
        if (unit.damage > 0) {
            commands.splice(1, 0, { name: 'Attack', action: 'attack', icon: '⚔️' });
            commands.push({ name: 'Patrol', action: 'patrol', icon: '🔄' });
        }
        
        if (unit.transport > 0) {
            commands.push({ name: 'Load', action: 'load', icon: '📦' });
            commands.push({ name: 'Unload', action: 'unload', icon: '📤' });
        }
        
        commands.forEach(command => {
            const button = document.createElement('button');
            button.className = 'command-button';
            button.innerHTML = `${command.icon} ${command.name}`;
            button.onclick = () => this.game.setUnitCommand(command.action);
            this.unitCommandsEl.appendChild(button);
        });
    }

    showMultiUnitInfo() {
        if (!this.unitInfoPanelEl) return;
        
        this.unitInfoPanelEl.style.display = 'flex';
        if (this.unitNameEl) this.unitNameEl.textContent = `${this.game.selectedUnits.length} Units Selected`;
        if (this.unitTypeEl) this.unitTypeEl.textContent = 'Multiple Units';
        
        // Hide detailed info for multiple selection
        if (this.unitLevelEl) this.unitLevelEl.style.display = 'none';
        if (this.unitExperienceEl) this.unitExperienceEl.style.display = 'none';
        if (this.unitStatusBadgesEl) this.unitStatusBadgesEl.innerHTML = '';
        if (this.unitAbilitiesEl) this.unitAbilitiesEl.style.display = 'none';
        
        // Clear individual stats
        this.clearIndividualStats();
        
        // Show common commands
        this.updateMultiUnitCommands();
        
        this.hideLegacyElements();
    }

    updateMultiUnitCommands() {
        if (!this.unitCommandsEl) return;
        
        this.unitCommandsEl.innerHTML = '';
        
        const commands = [
            { name: 'Move', action: 'move', icon: '➡️' },
            { name: 'Attack', action: 'attack', icon: '⚔️' },
            { name: 'Patrol', action: 'patrol', icon: '🔄' },
            { name: 'Stop', action: 'stop', icon: '⏹️' }
        ];
        
        commands.forEach(command => {
            const button = document.createElement('button');
            button.className = 'command-button';
            button.innerHTML = `${command.icon} ${command.name}`;
            button.onclick = () => this.game.setUnitCommand(command.action);
            this.unitCommandsEl.appendChild(button);
        });
    }

    showBuildingInfo(building) {
        if (!this.game.buildings.includes(building) || !building.isAlive()) {
            this.game.selectedBuilding = null;
            this.hide();
            return;
        }
        
        if (!this.unitInfoPanelEl) return;
        
        this.unitInfoPanelEl.style.display = 'flex';
        if (this.unitNameEl) this.unitNameEl.textContent = building.name;
        if (this.unitTypeEl) this.unitTypeEl.textContent = this.getBuildingCategory(building.type);
        
        // Hide unit-specific elements
        if (this.unitLevelEl) this.unitLevelEl.style.display = 'none';
        if (this.unitExperienceEl) this.unitExperienceEl.style.display = 'none';
        if (this.unitStatusBadgesEl) this.unitStatusBadgesEl.innerHTML = '';
        if (this.unitAbilitiesEl) this.unitAbilitiesEl.style.display = 'none';
        
        // Show building health
        this.updateBuildingHealth(building);
        
        // Clear other stats
        this.clearOtherStats();
        
        // Show production options if building is complete
        if (this.unitCommandsEl) this.unitCommandsEl.innerHTML = '';
        if (building.constructionProgress >= 100) {
            this.showBuildingProduction(building);
        }
        
        // Show production queue
        this.updateProductionQueue(building);
        
        this.hideLegacyElements();
    }

    updateBuildingHealth(building) {
        if (this.unitHealthCurrentEl) this.unitHealthCurrentEl.textContent = Math.floor(building.health);
        if (this.unitHealthMaxEl) this.unitHealthMaxEl.textContent = building.maxHealth;
        
        if (this.unitHealthFillEl) {
            const healthPercent = (building.health / building.maxHealth) * 100;
            this.unitHealthFillEl.style.width = `${healthPercent}%`;
            this.unitHealthFillEl.classList.remove('high', 'medium', 'low');
            if (healthPercent > 66) {
                this.unitHealthFillEl.classList.add('high');
            } else if (healthPercent > 33) {
                this.unitHealthFillEl.classList.add('medium');
            } else {
                this.unitHealthFillEl.classList.add('low');
            }
        }
    }

    getBuildingCategory(buildingType) {
        const buildingData = GameData.getBuildingTypes()[buildingType];
        if (!buildingData) return 'Building';
        
        const categoryNames = {
            'core': 'Core Building',
            'production': 'Production Facility',
            'defense': 'Defense Structure',
            'advanced': 'Advanced Building',
            'specialized': 'Specialized Facility',
            'economic': 'Economic Building'
        };
        
        return categoryNames[buildingData.category] || 'Building';
    }

    showBuildingProduction(building) {
        const buildingData = GameData.getBuildingTypes()[building.type];
        const produces = buildingData.produces || [];
        
        if (produces.length === 0 || !this.unitCommandsEl) return;
        
        produces.forEach(unitKey => {
            const unitData = GameData.getUnitTypes()[unitKey];
            if (!unitData) return;
            
            const button = document.createElement('button');
            button.className = 'command-button';
            
            const icon = this.getUnitIcon(unitKey);
            button.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; gap: 2px;">
                    <span style="font-size: 1.1em;">${icon}</span>
                    <span style="font-size: 0.8em;">${unitData.name}</span>
                    <span class="button-cost" style="font-size: 0.7em;">💰 ${unitData.cost}</span>
                </div>
            `;
            
            const canAfford = this.game.resources.spice >= unitData.cost;
            const unitCap = parseInt(this.game.ui?.unitMaxEl?.textContent || '50');
            const hasCap = this.game.teams.player.units.length < unitCap;
            
            button.disabled = !canAfford || !hasCap;
            
            if (button.disabled) {
                let tooltipText = `${unitData.name}\nCost: ${unitData.cost} Spice\n`;
                const reasons = [];
                if (!canAfford) {
                    reasons.push(`Need ${unitData.cost - this.game.resources.spice} more spice`);
                }
                if (!hasCap) {
                    reasons.push("Unit cap reached");
                }
                tooltipText += `Cannot train: ${reasons.join(', ')}`;
                button.title = tooltipText;
            } else {
                button.title = `Train ${unitData.name}\nCost: ${unitData.cost} Spice\nBuild Time: ${unitData.buildTime}s`;
            }
            
            button.onclick = () => {
                if (canAfford && hasCap) {
                    this.game.trainUnit(unitKey, building.id);
                } else {
                    this.game.ui?.showNotification("Cannot train unit: Conditions not met.", "error");
                }
            };
            
            this.unitCommandsEl.appendChild(button);
        });
    }

    getUnitIcon(unitType) {
        const icons = {
            // Infantry
            soldier: '🪖', rocketeer: '🚀', engineer: '🔧', medic: '⚕️',
            scout: '👁️', stealth_trooper: '🥷', flamethrower: '🔥',
            // Vehicles
            harvester: '🚜', tank: '🚗', artillery: '💥', apc: '🚐', siege_tank: '🏰',
            // Air units
            ornithopter: '🚁', carryall: '✈️',
            // Naval units
            frigate: '🚢', submarine: '🚤'
        };
        return icons[unitType] || '👤';
    }

    updateProductionQueue(building) {
        const queue = this.game.productionQueues.get(building.id);
        if (queue && queue.length > 0 && this.productionQueuePanelEl && this.queueItemsEl) {
            this.productionQueuePanelEl.style.display = 'block';
            this.queueItemsEl.innerHTML = queue.map((item, idx) => {
                const unitData = GameData.getUnitTypes()[item.type];
                const icon = this.getUnitIcon(item.type);
                return `
                    <div class="queue-item">
                        <span>${icon} ${unitData.name}</span>
                        ${idx === 0 ? `<span>(${(item.progress).toFixed(0)}%)</span>` : ''}
                        ${idx === 0 ? `<div class="queue-progress" style="width:${item.progress.toFixed(1)}%"></div>` : ''}
                    </div>
                `;
            }).join('');
        } else if (this.productionQueuePanelEl) {
            this.productionQueuePanelEl.style.display = 'none';
        }
    }

    clearIndividualStats() {
        if (this.unitHealthCurrentEl) this.unitHealthCurrentEl.textContent = '';
        if (this.unitHealthMaxEl) this.unitHealthMaxEl.textContent = '';
        if (this.unitAttackEl) this.unitAttackEl.textContent = '';
        if (this.unitDefenseEl) this.unitDefenseEl.textContent = '';
        if (this.unitSpeedEl) this.unitSpeedEl.textContent = '';
        if (this.unitRangeEl) this.unitRangeEl.textContent = '';
    }

    clearOtherStats() {
        if (this.unitAttackEl) this.unitAttackEl.textContent = '';
        if (this.unitDefenseEl) this.unitDefenseEl.textContent = '';
        if (this.unitSpeedEl) this.unitSpeedEl.textContent = '';
        if (this.unitRangeEl) this.unitRangeEl.textContent = '';
    }

    hideLegacyElements() {
        if (this.unitVisualEl) this.unitVisualEl.style.display = 'none';
        if (this.unitStatsEl) this.unitStatsEl.style.display = 'none';
        if (this.productionQueuePanelEl) this.productionQueuePanelEl.style.display = 'none';
    }
}