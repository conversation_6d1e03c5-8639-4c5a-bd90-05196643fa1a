/**
 * SelectionManager - Handles unit selection logic and visual feedback
 * Manages single-click selection, rectangle selection, and multi-unit operations
 */
export class SelectionManager {
    constructor(eventBus, gameState) {
        this.eventBus = eventBus;
        this.gameState = gameState;
        
        // Selection state
        this.selectedUnits = new Set();
        this.hoveredUnit = null;
        this.selectionGroups = new Map(); // Control groups 1-9
        
        // Visual feedback
        this.selectionIndicators = [];
        
        this.setupEventListeners();
    }
    
    /**
     * Update method called by GameLoop
     */
    update(deltaTime) {
        // Update hover detection based on mouse position
        // This could be enhanced to track mouse position for hover effects
        
        // Clean up dead units from selection
        this.cleanupDeadUnits();
    }
    
    /**
     * Setup event listeners for selection events
     */
    setupEventListeners() {
        this.eventBus.on('input:selection_start', (data) => this.handleSelectionStart(data));
        this.eventBus.on('input:selection_end', (data) => this.handleSelectionEnd(data));
        this.eventBus.on('input:create_control_group', (data) => this.createControlGroup(data.groupNumber));
        this.eventBus.on('input:select_control_group', (data) => this.selectControlGroup(data.groupNumber));
        this.eventBus.on('input:select_all_units', () => this.selectAllPlayerUnits());
        this.eventBus.on('rendering:world_ui_requested', (data) => this.renderSelectionIndicators(data.ctx));
        
        // Listen for entity death events to automatically deselect dead units
        this.eventBus.on('combat:entity_death', (data) => this.handleEntityDeath(data));
    }
    
    /**
     * Handle selection start (single click or drag start)
     */
    handleSelectionStart(data) {
        const { gameX, gameY, modifiers } = data;
        
        // Find entity (unit or building) at click position
        const clickedEntity = this.findUnitAtPosition(gameX, gameY);
        
        if (clickedEntity && clickedEntity.team === 'player') {
            console.log(`[DEBUG] SelectionManager: Clicked on ${clickedEntity.isUnit ? 'unit' : 'building'} ${clickedEntity.type}`);
            if (!modifiers.shift && !modifiers.ctrl) {
                // Single selection - clear existing selection
                this.clearSelection();
                this.selectEntity(clickedEntity);
            } else if (modifiers.shift) {
                // Add to selection
                this.selectEntity(clickedEntity);
            } else if (modifiers.ctrl) {
                // Toggle selection
                this.toggleEntity(clickedEntity);
            }
        } else if (!modifiers.shift && !modifiers.ctrl) {
            // Clicked on empty space - clear selection
            this.clearSelection();
        }
    }
    
    /**
     * Handle selection end (drag selection or single click completion)
     */
    handleSelectionEnd(data) {
        if (!data.isDragSelection) return;
        
        const { selectionRect } = data;
        if (!selectionRect) return;
        
        // Find all units within selection rectangle
        const unitsInRect = this.findUnitsInRectangle(selectionRect);
        const playerUnits = unitsInRect.filter(unit => unit.team === 'player');
        
        if (playerUnits.length > 0) {
            // Clear existing selection and select all units in rectangle
            this.clearSelection();
            playerUnits.forEach(unit => this.selectUnit(unit));
        }
    }
    
    /**
     * Find unit or building at specific position
     */
    findUnitAtPosition(x, y) {
        console.log(`[DEBUG] SelectionManager: Searching for entity at position (${x.toFixed(1)}, ${y.toFixed(1)})`);
        
        const units = this.gameState.entities.units;
        const buildings = this.gameState.entities.buildings;
        
        console.log(`[DEBUG] SelectionManager: Found ${units.length} units and ${buildings.length} buildings to check`);
        
        // Check units first (they should have priority over buildings)
        for (let i = units.length - 1; i >= 0; i--) {
            const unit = units[i];
            const halfWidth = unit.width / 2;
            const halfHeight = unit.height / 2;
            
            if (x >= unit.x - halfWidth && x <= unit.x + halfWidth &&
                y >= unit.y - halfHeight && y <= unit.y + halfHeight) {
                console.log(`[DEBUG] SelectionManager: Found unit ${unit.type} at (${unit.x}, ${unit.y})`);
                return unit;
            }
        }
        
        // Check buildings if no unit was found
        for (let i = buildings.length - 1; i >= 0; i--) {
            const building = buildings[i];
            const halfWidth = building.width / 2;
            const halfHeight = building.height / 2;
            
            if (x >= building.x - halfWidth && x <= building.x + halfWidth &&
                y >= building.y - halfHeight && y <= building.y + halfHeight) {
                console.log(`[DEBUG] SelectionManager: Found building ${building.type} at (${building.x}, ${building.y})`);
                return building;
            }
        }
        
        console.log(`[DEBUG] SelectionManager: No entity found at position`);
        return null;
    }
    
    /**
     * Find all units within a rectangle
     */
    findUnitsInRectangle(rect) {
        const { left, right, top, bottom } = rect;
        const units = this.gameState.entities.units;
        
        return units.filter(unit => {
            return unit.x >= left && unit.x <= right &&
                   unit.y >= top && unit.y <= bottom;
        });
    }
    
    /**
     * Select an entity (unit or building)
     */
    selectEntity(entity) {
        if (!entity || entity.team !== 'player') return;
        
        this.selectedUnits.add(entity);
        entity.selected = true;
        
        const eventName = entity.isUnit ? 'selection:unit_selected' : 'selection:building_selected';
        this.eventBus.emit(eventName, { entity });
        this.updateGameStateSelection();
        console.log(`[DEBUG] SelectionManager: Selected ${entity.isUnit ? 'unit' : 'building'} ${entity.type}`);
    }
    
    /**
     * Select a unit (backward compatibility)
     */
    selectUnit(unit) {
        this.selectEntity(unit);
    }
    
    /**
     * Deselect an entity (unit or building)
     */
    deselectEntity(entity) {
        this.selectedUnits.delete(entity);
        entity.selected = false;
        
        const eventName = entity.isUnit ? 'selection:unit_deselected' : 'selection:building_deselected';
        this.eventBus.emit(eventName, { entity });
        this.updateGameStateSelection();
    }
    
    /**
     * Deselect a unit (backward compatibility)
     */
    deselectUnit(unit) {
        this.deselectEntity(unit);
    }
    
    /**
     * Toggle entity selection
     */
    toggleEntity(entity) {
        if (this.selectedUnits.has(entity)) {
            this.deselectEntity(entity);
        } else {
            this.selectEntity(entity);
        }
    }
    
    /**
     * Toggle unit selection (backward compatibility)
     */
    toggleUnit(unit) {
        this.toggleEntity(unit);
    }
    
    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedUnits.forEach(unit => {
            unit.selected = false;
        });
        this.selectedUnits.clear();
        
        this.eventBus.emit('selection:cleared');
        this.updateGameStateSelection();
    }
    
    /**
     * Select all player units
     */
    selectAllPlayerUnits() {
        this.clearSelection();
        
        const playerUnits = this.gameState.entities.units.filter(unit => unit.team === 'player');
        playerUnits.forEach(unit => this.selectUnit(unit));
    }
    
    /**
     * Create control group
     */
    createControlGroup(groupNumber) {
        if (groupNumber < 1 || groupNumber > 9) return;
        
        const selectedArray = Array.from(this.selectedUnits);
        if (selectedArray.length > 0) {
            this.selectionGroups.set(groupNumber, selectedArray);
            this.eventBus.emit('selection:control_group_created', { 
                groupNumber, 
                units: selectedArray 
            });
        }
    }
    
    /**
     * Select control group
     */
    selectControlGroup(groupNumber) {
        if (groupNumber < 1 || groupNumber > 9) return;
        
        const group = this.selectionGroups.get(groupNumber);
        if (group && group.length > 0) {
            this.clearSelection();
            
            // Only select units that still exist and are alive
            const validUnits = group.filter(unit => 
                this.gameState.entities.units.includes(unit) && unit.isAlive()
            );
            
            validUnits.forEach(unit => this.selectUnit(unit));
            
            // Update group to remove dead units
            if (validUnits.length !== group.length) {
                if (validUnits.length > 0) {
                    this.selectionGroups.set(groupNumber, validUnits);
                } else {
                    this.selectionGroups.delete(groupNumber);
                }
            }
        }
    }
    
    /**
     * Update game state selection for backward compatibility
     */
    updateGameStateSelection() {
        this.gameState.selection.selectedUnits = Array.from(this.selectedUnits);
    }
    
    /**
     * Render selection indicators in world space
     */
    renderSelectionIndicators(ctx) {
        this.selectedUnits.forEach(entity => {
            // Only render selection indicators for alive entities
            if (entity.isAlive()) {
                if (entity.isUnit) {
                    this.renderUnitSelectionIndicator(ctx, entity);
                } else {
                    this.renderBuildingSelectionIndicator(ctx, entity);
                }
            }
        });
        
        // Render hover indicator (only for alive entities)
        if (this.hoveredUnit && this.hoveredUnit.isAlive()) {
            if (this.hoveredUnit.isUnit) {
                this.renderUnitHoverIndicator(ctx, this.hoveredUnit);
            } else {
                this.renderBuildingHoverIndicator(ctx, this.hoveredUnit);
            }
        }
    }
    
    /**
     * Render selection indicator for a unit in world space
     */
    renderUnitSelectionIndicator(ctx, unit) {
        ctx.save();
        ctx.translate(unit.x, unit.y);
        
        // Selection circle
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 2 / this.gameState.world.zoom; // Scale line width with zoom
        ctx.setLineDash([]);
        ctx.beginPath();
        ctx.arc(0, 0, unit.width / 2 + 5, 0, Math.PI * 2);
        ctx.stroke();
        
        // Health bar
        this.renderHealthBar(ctx, unit);
        
        ctx.restore();
    }
    
    /**
     * Render hover indicator for a unit in world space
     */
    renderUnitHoverIndicator(ctx, unit) {
        ctx.save();
        ctx.translate(unit.x, unit.y);
        
        ctx.strokeStyle = '#FFFF00';
        ctx.lineWidth = 1 / this.gameState.world.zoom; // Scale line width with zoom
        ctx.setLineDash([3 / this.gameState.world.zoom, 3 / this.gameState.world.zoom]); // Scale dash pattern
        ctx.beginPath();
        ctx.arc(0, 0, unit.width / 2 + 3, 0, Math.PI * 2);
        ctx.stroke();
        
        ctx.restore();
    }
    
    /**
     * Render health bar for a unit in world space
     */
    renderHealthBar(ctx, unit) {
        // Don't render health bars for dead units
        if (unit.health >= unit.maxHealth || !unit.isAlive()) return;
        
        const barWidth = unit.width;
        const barHeight = 4;
        const yOffset = -unit.height / 2 - 10;
        
        // Background
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(-barWidth / 2, yOffset, barWidth, barHeight);
        
        // Health
        const healthPercent = unit.health / unit.maxHealth;
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(-barWidth / 2, yOffset, barWidth * healthPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1 / this.gameState.world.zoom; // Scale line width with zoom
        ctx.strokeRect(-barWidth / 2, yOffset, barWidth, barHeight);
    }
    
    /**
     * Render selection indicator for a building in world space
     */
    renderBuildingSelectionIndicator(ctx, building) {
        ctx.save();
        ctx.translate(building.x, building.y);
        
        // Selection rectangle (buildings use rectangles instead of circles)
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 3 / this.gameState.world.zoom; // Thicker line for buildings
        ctx.setLineDash([]);
        ctx.strokeRect(-building.width / 2 - 5, -building.height / 2 - 5, building.width + 10, building.height + 10);
        
        // Health bar
        this.renderHealthBar(ctx, building);
        
        // Construction progress bar if under construction
        if (building.isUnderConstruction) {
            this.renderConstructionProgress(ctx, building);
        }
        
        ctx.restore();
    }
    
    /**
     * Render hover indicator for a building in world space
     */
    renderBuildingHoverIndicator(ctx, building) {
        ctx.save();
        ctx.translate(building.x, building.y);
        
        ctx.strokeStyle = '#FFFF00';
        ctx.lineWidth = 2 / this.gameState.world.zoom;
        ctx.setLineDash([5 / this.gameState.world.zoom, 5 / this.gameState.world.zoom]);
        ctx.strokeRect(-building.width / 2 - 3, -building.height / 2 - 3, building.width + 6, building.height + 6);
        
        ctx.restore();
    }
    
    /**
     * Render construction progress bar for a building
     */
    renderConstructionProgress(ctx, building) {
        const barWidth = building.width;
        const barHeight = 6;
        const yOffset = building.height / 2 + 15;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(-barWidth / 2, yOffset, barWidth, barHeight);
        
        // Progress
        const progressPercent = building.constructionProgress / 100;
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(-barWidth / 2, yOffset, barWidth * progressPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1 / this.gameState.world.zoom;
        ctx.strokeRect(-barWidth / 2, yOffset, barWidth, barHeight);
    }
    
    /**
     * Get selected units
     */
    getSelectedUnits() {
        return Array.from(this.selectedUnits);
    }
    
    /**
     * Get selection count
     */
    getSelectionCount() {
        return this.selectedUnits.size;
    }
    
    /**
     * Handle entity death - automatically deselect dead entities
     */
    handleEntityDeath(data) {
        const deadEntity = data.entity;
        if (!deadEntity) return;
        
        console.log(`[DEBUG] SelectionManager: Entity died, checking if selected: ${deadEntity.type || 'unknown'}`);
        
        // Remove from selection if it was selected
        if (this.selectedUnits.has(deadEntity)) {
            console.log(`[DEBUG] SelectionManager: Removing dead entity from selection: ${deadEntity.type || 'unknown'}`);
            this.deselectEntity(deadEntity);
        }
        
        // Remove from control groups
        this.removeFromControlGroups(deadEntity);
        
        // Clear hover if this entity was hovered
        if (this.hoveredUnit === deadEntity) {
            this.hoveredUnit = null;
        }
    }
    
    /**
     * Remove entity from all control groups
     */
    removeFromControlGroups(entity) {
        this.selectionGroups.forEach((group, groupNumber) => {
            const index = group.indexOf(entity);
            if (index !== -1) {
                group.splice(index, 1);
                console.log(`[DEBUG] SelectionManager: Removed dead entity from control group ${groupNumber}`);
                
                // Remove empty control groups
                if (group.length === 0) {
                    this.selectionGroups.delete(groupNumber);
                    console.log(`[DEBUG] SelectionManager: Removed empty control group ${groupNumber}`);
                }
            }
        });
    }
    
    /**
     * Clean up dead units from selection (called during update)
     */
    cleanupDeadUnits() {
        const deadUnits = [];
        
        // Find dead units in selection
        this.selectedUnits.forEach(unit => {
            if (!unit.isAlive()) {
                deadUnits.push(unit);
            }
        });
        
        // Remove dead units from selection
        deadUnits.forEach(unit => {
            console.log(`[DEBUG] SelectionManager: Cleaning up dead unit from selection: ${unit.type || 'unknown'}`);
            this.deselectEntity(unit);
        });
    }
}