export class ResourcePanel {
    constructor(game) {
        this.game = game;
        
        // Enhanced Resource Panel Elements
        this.spiceCountEl = document.getElementById('spiceAmount');
        this.waterCountEl = document.getElementById('waterAmount');
        this.powerCountEl = document.getElementById('powerAmount');
        this.powerMaxEl = document.getElementById('maxPower');
        this.techCountEl = document.getElementById('techAmount');
        this.unitCountEl = document.getElementById('unitCount');
        this.unitMaxEl = document.getElementById('unitMax');
        this.unitStorageFillEl = document.getElementById('unitStorageFill');
        
        // Resource rate indicators
        this.spiceRateEl = document.getElementById('spiceRate');
        this.waterRateEl = document.getElementById('waterRate');
        this.techRateEl = document.getElementById('techRate');
        
        // Resource tracking state
        this.lastResourceUpdate = 0;
        this.resourceRates = {
            spice: 0,
            water: 0,
            tech: 0
        };
        this.lastResources = null;
    }

    setupTracking() {
        // Track resource changes for rate calculation
        setInterval(() => {
            this.updateResourceRates();
        }, 1000); // Update every second
    }

    update() {
        if (!this.game || !this.game.resources) {
            console.warn('ResourcePanel: Game or resources not available yet');
            return;
        }
        
        const resources = this.game.resources;
        const newResources = this.game.resourceManager ? this.game.resourceManager.getAllResources() : {};
        
        // Update legacy resource amounts
        if (this.spiceCountEl) {
            this.spiceCountEl.textContent = Math.floor(resources.spice);
        }
        if (this.powerCountEl) {
            this.powerCountEl.textContent = resources.power;
        }
        if (this.powerMaxEl) {
            this.powerMaxEl.textContent = resources.maxPower;
        }
        if (this.unitCountEl && this.game.teams && this.game.teams.player) {
            this.unitCountEl.textContent = this.game.teams.player.units.length;
        }
        
        // Update new resources from resource manager
        if (this.waterCountEl) {
            this.waterCountEl.textContent = Math.floor(newResources.water || 0);
        }
        if (this.techCountEl) {
            this.techCountEl.textContent = Math.floor(newResources.technology || 0);
        }
        
        // Update unit storage indicator
        if (this.unitStorageFillEl && this.unitMaxEl && this.game.teams && this.game.teams.player) {
            const unitPercentage = (this.game.teams.player.units.length / parseInt(this.unitMaxEl.textContent)) * 100;
            this.unitStorageFillEl.style.width = `${Math.min(unitPercentage, 100)}%`;
            
            // Update storage fill color based on capacity
            this.unitStorageFillEl.classList.remove('warning', 'critical');
            if (unitPercentage > 90) {
                this.unitStorageFillEl.classList.add('critical');
            } else if (unitPercentage > 75) {
                this.unitStorageFillEl.classList.add('warning');
            }
        }
    }

    updateResourceRates() {
        if (!this.game || !this.game.resources) {
            return;
        }
        
        const now = Date.now();
        if (this.lastResourceUpdate === 0) {
            this.lastResourceUpdate = now;
            return;
        }
        
        const deltaTime = (now - this.lastResourceUpdate) / 1000; // seconds
        const resources = this.game.resources;
        
        // Calculate rates (per minute)
        if (this.lastResources) {
            this.resourceRates.spice = ((resources.spice - this.lastResources.spice) / deltaTime) * 60;
            if (resources.water !== undefined && this.lastResources.water !== undefined) {
                this.resourceRates.water = ((resources.water - this.lastResources.water) / deltaTime) * 60;
            }
            if (resources.technology !== undefined && this.lastResources.technology !== undefined) {
                this.resourceRates.tech = ((resources.technology - this.lastResources.technology) / deltaTime) * 60;
            }
        }
        
        // Update rate displays
        this.updateRateDisplay(this.spiceRateEl, this.resourceRates.spice);
        if (this.waterRateEl) this.updateRateDisplay(this.waterRateEl, this.resourceRates.water);
        if (this.techRateEl) this.updateRateDisplay(this.techRateEl, this.resourceRates.tech);
        
        // Store current resources for next calculation
        this.lastResources = { ...resources };
        this.lastResourceUpdate = now;
    }

    updateRateDisplay(element, rate) {
        if (!element) return;
        
        const sign = rate >= 0 ? '+' : '';
        element.textContent = `${sign}${Math.round(rate)}/min`;
        element.classList.remove('positive', 'negative');
        element.classList.add(rate >= 0 ? 'positive' : 'negative');
    }

    getResourceRates() {
        return this.resourceRates;
    }

    getCurrentResources() {
        if (!this.game || !this.game.resources) {
            return {
                spice: 0,
                water: 0,
                technology: 0,
                power: 0,
                maxPower: 0,
                unitCount: 0
            };
        }
        
        return {
            spice: this.game.resources.spice,
            water: this.game.resourceManager ? (this.game.resourceManager.getResource('water') || 0) : 0,
            technology: this.game.resourceManager ? (this.game.resourceManager.getResource('technology') || 0) : 0,
            power: this.game.resources.power,
            maxPower: this.game.resources.maxPower,
            unitCount: this.game.teams && this.game.teams.player ? this.game.teams.player.units.length : 0
        };
    }
}