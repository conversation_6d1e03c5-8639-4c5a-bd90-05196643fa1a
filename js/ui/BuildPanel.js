import { GameData } from '../GameData.js';

export class BuildPanel {
    constructor(game) {
        this.game = game;
        
        // Build Panel Elements
        this.buildPanelEl = document.getElementById('buildPanel');
        this.buildButtonsEl = document.getElementById('buildButtons');
        this.buildCategoriesEl = document.getElementById('buildCategories');
        this.buildingGridEl = document.getElementById('buildingGrid');
        
        // State
        this.selectedCategory = 'core';
        this.availableBuildings = [];
        
        this.setupBuildCategories();
    }

    setupBuildCategories() {
        if (!this.buildCategoriesEl) return;
        
        const categories = [
            { key: 'core', name: 'Core', icon: '🏛️' },
            { key: 'production', name: 'Production', icon: '🏭' },
            { key: 'defense', name: 'Defense', icon: '🛡️' },
            { key: 'advanced', name: 'Advanced', icon: '🔬' },
            { key: 'economic', name: 'Economic', icon: '💰' }
        ];
        
        this.buildCategoriesEl.innerHTML = '';
        
        categories.forEach(category => {
            const button = document.createElement('button');
            button.className = 'category-button';
            if (category.key === this.selectedCategory) {
                button.classList.add('active');
            }
            
            button.innerHTML = `
                <span class="category-icon">${category.icon}</span>
                <span class="category-name">${category.name}</span>
            `;
            
            button.onclick = () => {
                this.selectCategory(category.key);
            };
            
            this.buildCategoriesEl.appendChild(button);
        });
    }

    selectCategory(categoryKey) {
        this.selectedCategory = categoryKey;
        
        // Update category button states
        const categoryButtons = this.buildCategoriesEl?.querySelectorAll('.category-button');
        categoryButtons?.forEach((button, index) => {
            const categories = ['core', 'production', 'defense', 'advanced', 'economic'];
            button.classList.toggle('active', categories[index] === categoryKey);
        });
        
        this.updateBuildingGrid();
    }

    update() {
        this.updateAvailableBuildings();
        this.updateBuildingGrid();
    }

    updateAvailableBuildings() {
        const buildingTypes = GameData.getBuildingTypes();
        this.availableBuildings = [];
        
        Object.keys(buildingTypes).forEach(buildingKey => {
            const buildingData = buildingTypes[buildingKey];
            
            // Check if building is available (tech requirements, etc.)
            if (this.isBuildingAvailable(buildingKey, buildingData)) {
                this.availableBuildings.push({
                    key: buildingKey,
                    data: buildingData
                });
            }
        });
    }

    isBuildingAvailable(buildingKey, buildingData) {
        // Check technology requirements
        if (buildingData.requiredTech) {
            const techManager = this.game.technologySystem;
            if (!techManager || !techManager.isResearched(buildingData.requiredTech)) {
                return false;
            }
        }
        
        // Check prerequisite buildings
        if (buildingData.prerequisites) {
            const hasPrerequisites = buildingData.prerequisites.every(prereq => {
                return this.game.buildings.some(building => 
                    building.type === prereq && building.constructionProgress >= 100
                );
            });
            if (!hasPrerequisites) {
                return false;
            }
        }
        
        // Check faction restrictions
        if (buildingData.faction && buildingData.faction !== this.game.playerFaction) {
            return false;
        }
        
        return true;
    }

    updateBuildingGrid() {
        if (!this.buildingGridEl) return;
        
        this.buildingGridEl.innerHTML = '';
        
        const categoryBuildings = this.availableBuildings.filter(building => 
            building.data.category === this.selectedCategory
        );
        
        if (categoryBuildings.length === 0) {
            this.buildingGridEl.innerHTML = '<div class="no-buildings">No buildings available in this category</div>';
            return;
        }
        
        categoryBuildings.forEach(building => {
            const buildingButton = this.createBuildingButton(building.key, building.data);
            this.buildingGridEl.appendChild(buildingButton);
        });
    }

    createBuildingButton(buildingKey, buildingData) {
        const button = document.createElement('button');
        button.className = 'building-button';
        
        const canAfford = this.game.resources.spice >= buildingData.cost;
        const hasSpace = this.checkBuildingSpace(buildingData);
        const canBuild = canAfford && hasSpace;
        
        if (!canBuild) {
            button.classList.add('disabled');
        }
        
        // Get building icon
        const icon = this.getBuildingIcon(buildingKey);
        
        button.innerHTML = `
            <div class="building-icon">${icon}</div>
            <div class="building-name">${buildingData.name}</div>
            <div class="building-cost">💰 ${buildingData.cost}</div>
            ${buildingData.power ? `<div class="building-power">⚡ ${buildingData.power}</div>` : ''}
            ${!canAfford ? '<div class="building-warning">Insufficient Spice</div>' : ''}
            ${!hasSpace ? '<div class="building-warning">No Space</div>' : ''}
        `;
        
        // Add tooltip with detailed information
        button.title = this.createBuildingTooltip(buildingKey, buildingData);
        
        button.onclick = () => {
            console.log('🏗️ DUNE2 DEBUG: Building button clicked:', {
                buildingKey,
                buildingName: buildingData.name,
                canBuild,
                canAfford,
                hasSpace,
                cost: buildingData.cost,
                currentSpice: this.game.resources.spice
            });
            
            if (canBuild) {
                console.log('🏗️ DUNE2 DEBUG: Starting building placement for:', buildingKey);
                this.startBuildingPlacement(buildingKey);
            } else {
                console.log('🏗️ DUNE2 DEBUG: Cannot build, showing error for:', buildingKey);
                this.showBuildingError(buildingData, canAfford, hasSpace);
            }
        };
        
        return button;
    }

    getBuildingIcon(buildingKey) {
        const icons = {
            // Core buildings
            construction_yard: '🏗️',
            power_plant: '⚡',
            refinery: '🏭',
            
            // Production buildings
            barracks: '🪖',
            factory: '🏭',
            starport: '🚀',
            
            // Defense buildings
            gun_turret: '🔫',
            rocket_turret: '🚀',
            shield_generator: '🛡️',
            
            // Advanced buildings
            research_lab: '🔬',
            high_tech_factory: '🏭',
            palace: '🏰',
            
            // Economic buildings
            silo: '🏪',
            outpost: '🏕️',
            spice_storage: '📦'
        };
        
        return icons[buildingKey] || '🏢';
    }

    createBuildingTooltip(buildingKey, buildingData) {
        let tooltip = `${buildingData.name}\n`;
        tooltip += `Cost: ${buildingData.cost} Spice\n`;
        tooltip += `Build Time: ${buildingData.buildTime || 30}s\n`;
        
        if (buildingData.power) {
            tooltip += `Power: ${buildingData.power > 0 ? '+' : ''}${buildingData.power}\n`;
        }
        
        if (buildingData.description) {
            tooltip += `\n${buildingData.description}`;
        }
        
        if (buildingData.produces && buildingData.produces.length > 0) {
            tooltip += `\nProduces: ${buildingData.produces.join(', ')}`;
        }
        
        if (buildingData.requiredTech) {
            tooltip += `\nRequires: ${buildingData.requiredTech}`;
        }
        
        if (buildingData.prerequisites && buildingData.prerequisites.length > 0) {
            tooltip += `\nPrerequisites: ${buildingData.prerequisites.join(', ')}`;
        }
        
        return tooltip;
    }

    checkBuildingSpace(buildingData) {
        // Simple space check - in a real implementation, this would check
        // available construction areas on the map
        return true;
    }

    startBuildingPlacement(buildingKey) {
        console.log('🏗️ DUNE2 DEBUG: BuildPanel.startBuildingPlacement called with:', buildingKey);
        
        // Set the game to building placement mode
        this.game.buildingType = buildingKey;
        this.game.placingBuilding = true;
        
        // Also set GameState building mode for consistency
        this.game.gameState.selection.buildingMode = buildingKey;
        
        // Create initial ghost building at center of screen
        const buildingData = this.game.buildingTypes[buildingKey];
        if (buildingData) {
            const centerX = this.game.gameState.world.viewX + (this.game.gameCanvas.width / this.game.gameState.world.zoom) / 2;
            const centerY = this.game.gameState.world.viewY + (this.game.gameCanvas.height / this.game.gameState.world.zoom) / 2;
            
            console.log('🏗️ DUNE2 DEBUG: Creating initial ghost building at center:', { centerX, centerY });
            this.game.updateGhostBuilding(centerX, centerY);
        }
        
        console.log('🏗️ DUNE2 DEBUG: Building placement state set:', {
            'game.buildingType': this.game.buildingType,
            'game.placingBuilding': this.game.placingBuilding,
            'gameState.selection.buildingMode': this.game.gameState.selection.buildingMode,
            'ghostBuilding': this.game.gameState.selection.ghostBuilding
        });
        
        // Update cursor or visual feedback
        if (this.game.canvas) {
            this.game.canvas.style.cursor = 'crosshair';
            console.log('🏗️ DUNE2 DEBUG: Cursor set to crosshair');
        } else {
            console.warn('⚠️ DUNE2 DEBUG: game.canvas not found for cursor update');
        }
        
        // Show placement instructions
        this.game.ui?.showNotification(
            `Click on the map to place ${GameData.getBuildingTypes()[buildingKey].name}`,
            'info',
            5000
        );
        console.log('🏗️ DUNE2 DEBUG: Placement notification shown');
        
        // Hide build panel during placement
        if (this.buildPanelEl) {
            this.buildPanelEl.style.display = 'none';
            console.log('🏗️ DUNE2 DEBUG: Build panel hidden during placement');
        } else {
            console.warn('⚠️ DUNE2 DEBUG: buildPanelEl not found for hiding');
        }
    }

    showBuildingError(buildingData, canAfford, hasSpace) {
        let message = `Cannot build ${buildingData.name}: `;
        const reasons = [];
        
        if (!canAfford) {
            const needed = buildingData.cost - this.game.resources.spice;
            reasons.push(`Need ${needed} more spice`);
        }
        
        if (!hasSpace) {
            reasons.push('No suitable build location');
        }
        
        message += reasons.join(', ');
        
        this.game.ui?.showNotification(message, 'error', 3000);
    }

    show() {
        console.log('🏗️ DUNE2 DEBUG: BuildPanel.show() called');
        if (this.buildPanelEl) {
            console.log('🏗️ DUNE2 DEBUG: Setting buildPanel display to block');
            this.buildPanelEl.style.display = 'block';
            console.log('🏗️ DUNE2 DEBUG: buildPanel display after setting:', this.buildPanelEl.style.display);
            this.update();
        } else {
            console.error('❌ DUNE2 DEBUG: buildPanelEl not found - DOM element missing!');
        }
    }

    hide() {
        if (this.buildPanelEl) {
            this.buildPanelEl.style.display = 'none';
        }
    }

    toggle() {
        if (this.buildPanelEl) {
            const isVisible = this.buildPanelEl.style.display !== 'none';
            if (isVisible) {
                this.hide();
            } else {
                this.show();
            }
        }
    }

    showBuildTab(category = null) {
        // Accept category parameter and switch to that category if provided
        
        // Map HTML category names to internal category keys
        const categoryMapping = {
            'buildings': 'core',
            'defenses': 'defense',
            'upgrades': 'advanced',
            'advanced': 'advanced'
        };
        
        // If a category is specified, switch to it
        if (category && categoryMapping[category]) {
            this.selectCategory(categoryMapping[category]);
        }
        
        this.show();
    }

    cancelPlacement() {
        this.game.placingBuilding = false;
        this.game.buildingType = null;
        
        if (this.game.canvas) {
            this.game.canvas.style.cursor = 'default';
        }
        
        // Show build panel again
        this.show();
    }

    // Handle successful building placement
    onBuildingPlaced() {
        this.cancelPlacement();
        this.update(); // Refresh available buildings
    }
}