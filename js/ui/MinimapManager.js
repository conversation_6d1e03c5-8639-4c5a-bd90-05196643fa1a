export class MinimapManager {
    constructor(game) {
        this.game = game;
        
        // Minimap Elements
        this.minimapEl = document.getElementById('minimap');
        this.minimapCanvasEl = document.getElementById('minimapCanvas');
        this.minimapViewportEl = document.getElementById('minimapViewport');
        
        // Minimap state
        this.minimapScale = 0.1;
        this.lastStaticUpdate = 0;
        this.staticUpdateInterval = 1000; // Update static elements every 1 second for better responsiveness
        this.lastDynamicUpdate = 0;
        this.dynamicUpdateInterval = 100; // Update dynamic elements every 100ms
        
        // Initialize minimap
        this.initializeMinimap();
        this.setupEventListeners();
    }

    initializeMinimap() {
        if (!this.minimapCanvasEl) return;
        
        // Set canvas size
        this.minimapCanvasEl.width = 200;
        this.minimapCanvasEl.height = 200;
        
        // Setup click handling for minimap navigation
        this.setupClickNavigation();
    }

    setupControls() {
        // Setup minimap toggle
        const minimapToggle = document.getElementById('minimapToggle');
        if (minimapToggle) {
            minimapToggle.addEventListener('change', (e) => {
                if (this.minimapEl) {
                    this.minimapEl.style.display = e.target.checked ? 'block' : 'none';
                }
            });
        }
        
        // Setup terrain overlay toggle
        const terrainToggle = document.getElementById('showTerrainToggle');
        if (terrainToggle) {
            terrainToggle.addEventListener('change', () => {
                this.drawStatic(); // Redraw with/without terrain
            });
        }
    }
    
    setupEventListeners() {
        // Listen for zoom changes to update minimap viewport
        if (this.game.eventBus) {
            this.game.eventBus.on('input:zoom_applied', () => {
                this.updateDynamic();
            });
            
            this.game.eventBus.on('input:viewport_moved', () => {
                this.updateDynamic();
            });
        }
    }

    setupClickNavigation() {
        if (!this.minimapCanvasEl) return;
        
        this.minimapCanvasEl.addEventListener('click', (e) => {
            const rect = this.minimapCanvasEl.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // Convert minimap coordinates to world coordinates
            const worldX = (x / this.minimapCanvasEl.width) * this.game.mapWidth;
            const worldY = (y / this.minimapCanvasEl.height) * this.game.mapHeight;
            
            // Calculate viewport size based on current zoom
            const viewportWidth = this.game.canvas.width / this.game.zoom;
            const viewportHeight = this.game.canvas.height / this.game.zoom;
            
            // Center camera on clicked position
            this.game.camera.x = worldX - viewportWidth / 2;
            this.game.camera.y = worldY - viewportHeight / 2;
            
            // Clamp camera to map bounds
            this.game.camera.x = Math.max(0, Math.min(this.game.camera.x, this.game.mapWidth - viewportWidth));
            this.game.camera.y = Math.max(0, Math.min(this.game.camera.y, this.game.mapHeight - viewportHeight));
            
            // Update the viewport to apply the camera changes
            this.game.updateViewport();
        });
    }

    drawStatic() {
        if (!this.minimapCanvasEl) return;
        
        const ctx = this.minimapCanvasEl.getContext('2d');
        const width = this.minimapCanvasEl.width;
        const height = this.minimapCanvasEl.height;
        
        // Clear minimap
        ctx.clearRect(0, 0, width, height);
        
        // Draw terrain if enabled
        const showTerrain = document.getElementById('showTerrainToggle')?.checked;
        if (showTerrain) {
            this.drawTerrain(ctx, width, height);
        }
        
        // Draw map background
        ctx.fillStyle = '#8B4513'; // Desert color
        ctx.fillRect(0, 0, width, height);
        
        // Draw spice fields
        this.drawSpiceFields(ctx, width, height);
        
        // Draw buildings
        this.drawBuildings(ctx, width, height);
        
        this.lastStaticUpdate = Date.now();
    }

    drawTerrain(ctx, width, height) {
        // Simple terrain representation
        // In a real implementation, this would sample the actual terrain data
        
        // Draw some terrain features
        ctx.fillStyle = '#CD853F'; // Sandy areas
        for (let i = 0; i < 10; i++) {
            const x = Math.random() * width;
            const y = Math.random() * height;
            const size = 5 + Math.random() * 15;
            
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // Draw rocky areas
        ctx.fillStyle = '#696969';
        for (let i = 0; i < 5; i++) {
            const x = Math.random() * width;
            const y = Math.random() * height;
            const size = 3 + Math.random() * 8;
            
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    drawSpiceFields(ctx, width, height) {
        if (!this.game.spiceFields) return;
        
        ctx.fillStyle = '#FFA500';
        this.game.spiceFields.forEach(field => {
            const x = (field.x / this.game.mapWidth) * width;
            const y = (field.y / this.game.mapHeight) * height;
            const size = Math.max(2, (field.radius / this.game.mapWidth) * width);
            
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
        });
    }

    drawBuildings(ctx, width, height) {
        if (!this.game.buildings) return;
        
        this.game.buildings.forEach(building => {
            const x = (building.x / this.game.mapWidth) * width;
            const y = (building.y / this.game.mapHeight) * height;
            
            // Color based on team
            if (building.team === 'player') {
                ctx.fillStyle = '#00FF00';
            } else if (building.team === 'enemy') {
                ctx.fillStyle = '#FF0000';
            } else {
                ctx.fillStyle = '#FFFF00'; // Neutral
            }
            
            const size = building.constructionProgress >= 100 ? 3 : 2;
            ctx.fillRect(x - size/2, y - size/2, size, size);
        });
    }

    updateDynamic() {
        if (!this.minimapCanvasEl) return;
        
        const now = Date.now();
        
        // Check if we need to update static elements
        if (now - this.lastStaticUpdate > this.staticUpdateInterval) {
            this.drawStatic();
        }
        
        // Throttle dynamic updates for performance
        if (now - this.lastDynamicUpdate < this.dynamicUpdateInterval) {
            return;
        }
        this.lastDynamicUpdate = now;
        
        const ctx = this.minimapCanvasEl.getContext('2d');
        const width = this.minimapCanvasEl.width;
        const height = this.minimapCanvasEl.height;
        
        // Save the current state
        ctx.save();
        
        // Clear only the dynamic layer (we could use a separate canvas for this)
        // For now, we'll redraw everything
        this.drawStatic();
        
        // Draw units with better scaling
        this.drawUnits(ctx, width, height);
        
        // Draw projectiles
        this.drawProjectiles(ctx, width, height);
        
        // Draw viewport rectangle with zoom consideration
        this.drawViewportRect(ctx, width, height);
        
        ctx.restore();
    }

    drawUnits(ctx, width, height) {
        // Draw player units
        if (this.game.teams?.player?.units) {
            ctx.fillStyle = '#00FFFF';
            this.game.teams.player.units.forEach(unit => {
                const x = (unit.x / this.game.mapWidth) * width;
                const y = (unit.y / this.game.mapHeight) * height;
                ctx.fillRect(x - 1, y - 1, 2, 2);
            });
        }
        
        // Draw enemy units
        if (this.game.teams?.enemy?.units) {
            ctx.fillStyle = '#FF4500';
            this.game.teams.enemy.units.forEach(unit => {
                const x = (unit.x / this.game.mapWidth) * width;
                const y = (unit.y / this.game.mapHeight) * height;
                ctx.fillRect(x - 1, y - 1, 2, 2);
            });
        }
        
        // Draw neutral units
        if (this.game.neutralEnemies?.units) {
            ctx.fillStyle = '#FFFF00';
            this.game.neutralEnemies.units.forEach(unit => {
                const x = (unit.x / this.game.mapWidth) * width;
                const y = (unit.y / this.game.mapHeight) * height;
                ctx.fillRect(x - 1, y - 1, 2, 2);
            });
        }
    }

    drawProjectiles(ctx, width, height) {
        if (!this.game.projectiles) return;
        
        ctx.fillStyle = '#FFFFFF';
        this.game.projectiles.forEach(projectile => {
            const x = (projectile.x / this.game.mapWidth) * width;
            const y = (projectile.y / this.game.mapHeight) * height;
            ctx.fillRect(x, y, 1, 1);
        });
    }

    drawViewportRect(ctx, width, height) {
        if (!this.game.camera || !this.game.canvas) return;
        
        // Calculate viewport size based on current zoom
        const zoom = this.game.zoom || 1;
        const actualViewWidth = this.game.canvas.width / zoom;
        const actualViewHeight = this.game.canvas.height / zoom;
        
        // Convert to minimap coordinates
        const viewX = (this.game.camera.x / this.game.mapWidth) * width;
        const viewY = (this.game.camera.y / this.game.mapHeight) * height;
        const viewWidth = (actualViewWidth / this.game.mapWidth) * width;
        const viewHeight = (actualViewHeight / this.game.mapHeight) * height;
        
        // Draw viewport rectangle with better visibility
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.strokeRect(viewX, viewY, viewWidth, viewHeight);
        
        // Add a subtle fill for better visibility
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(viewX, viewY, viewWidth, viewHeight);
    }

    updateViewportRect(viewX, viewY, width, height) {
        // This method is called by the main UI manager
        // The actual drawing is handled in drawViewportRect
    }

    // Get minimap position for world coordinates
    worldToMinimap(worldX, worldY) {
        if (!this.minimapCanvasEl) return { x: 0, y: 0 };
        
        return {
            x: (worldX / this.game.mapWidth) * this.minimapCanvasEl.width,
            y: (worldY / this.game.mapHeight) * this.minimapCanvasEl.height
        };
    }

    // Get world position for minimap coordinates
    minimapToWorld(minimapX, minimapY) {
        if (!this.minimapCanvasEl) return { x: 0, y: 0 };
        
        return {
            x: (minimapX / this.minimapCanvasEl.width) * this.game.mapWidth,
            y: (minimapY / this.minimapCanvasEl.height) * this.game.mapHeight
        };
    }
}