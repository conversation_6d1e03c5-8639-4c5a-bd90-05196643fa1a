export class LootSystem {
    constructor(game) {
        this.game = game;
        this.lootItems = [];
        this.itemTypes = this.getItemTypes();
    }

    getItemTypes() {
        return {
            // Credits
            credits_small: {
                name: 'Credits',
                type: 'credits',
                value: 50,
                color: '#FFD700',
                size: 8,
                duration: 30000, // 30 seconds before disappearing
                rarity: 'common'
            },
            credits_medium: {
                name: 'Credits',
                type: 'credits',
                value: 150,
                color: '#FFD700',
                size: 12,
                duration: 30000,
                rarity: 'uncommon'
            },
            credits_large: {
                name: 'Credits',
                type: 'credits',
                value: 300,
                color: '#FFD700',
                size: 16,
                duration: 30000,
                rarity: 'rare'
            },

            // Spice
            spice_chunk: {
                name: 'Spice Chunk',
                type: 'spice',
                value: 100,
                color: '#FFA500',
                size: 10,
                duration: 45000,
                rarity: 'common'
            },
            spice_crystal: {
                name: 'Spice Crystal',
                type: 'spice',
                value: 250,
                color: '#FF8C00',
                size: 14,
                duration: 45000,
                rarity: 'uncommon'
            },

            // Power-ups
            health_boost: {
                name: 'Health Boost',
                type: 'boost',
                effect: 'heal',
                value: 0.5, // 50% heal
                color: '#00FF00',
                size: 10,
                duration: 20000,
                rarity: 'uncommon'
            },
            damage_boost: {
                name: 'Damage Amplifier',
                type: 'boost',
                effect: 'damage',
                value: 1.5, // 50% damage increase
                duration_effect: 30000, // Effect lasts 30 seconds
                color: '#FF4500',
                size: 10,
                duration: 25000,
                rarity: 'rare'
            },
            speed_boost: {
                name: 'Speed Enhancer',
                type: 'boost',
                effect: 'speed',
                value: 1.3, // 30% speed increase
                duration_effect: 25000,
                color: '#00BFFF',
                size: 10,
                duration: 25000,
                rarity: 'rare'
            },
            xp_boost: {
                name: 'Experience Orb',
                type: 'boost',
                effect: 'xp',
                value: 100, // Flat XP gain
                color: '#9370DB',
                size: 12,
                duration: 20000,
                rarity: 'uncommon'
            },

            // Special items
            repair_kit: {
                name: 'Repair Kit',
                type: 'consumable',
                effect: 'repair',
                value: 1.0, // Full repair
                color: '#32CD32',
                size: 10,
                duration: 35000,
                rarity: 'uncommon'
            },
            shield_generator: {
                name: 'Shield Generator',
                type: 'boost',
                effect: 'shield',
                value: 0.5, // 50% damage reduction
                duration_effect: 45000,
                color: '#4169E1',
                size: 12,
                duration: 30000,
                rarity: 'rare'
            }
        };
    }

    // Drop loot when an entity dies
    dropLoot(entity) {
        const dropChance = this.calculateDropChance(entity);
        const numDrops = this.calculateNumDrops(entity);

        for (let i = 0; i < numDrops; i++) {
            if (Math.random() < dropChance) {
                const itemType = this.selectRandomItem(entity);
                if (itemType) {
                    this.createLootItem(itemType, entity.x, entity.y);
                }
            }
        }

        // Special drops for harvesters (spice explosion)
        if (entity.type === 'harvester' && entity.carrying > 0) {
            const spiceAmount = entity.carrying;
            const numSpiceChunks = Math.ceil(spiceAmount / 100);
            
            for (let i = 0; i < Math.min(numSpiceChunks, 5); i++) {
                const angle = (Math.PI * 2 * i) / numSpiceChunks;
                const distance = 30 + Math.random() * 40;
                const x = entity.x + Math.cos(angle) * distance;
                const y = entity.y + Math.sin(angle) * distance;
                
                this.createLootItem('spice_chunk', x, y);
            }
        }
    }

    calculateDropChance(entity) {
        let baseChance = 0.3; // 30% base drop chance
        
        // Higher level entities drop more loot
        if (entity.level) {
            baseChance += entity.level * 0.05;
        }
        
        // Different entity types have different drop rates
        if (entity.isBuilding) {
            baseChance += 0.2; // Buildings drop more loot
        }
        
        if (entity.team === 'neutral') {
            baseChance += 0.3; // Neutral enemies drop more loot
        }
        
        return Math.min(baseChance, 0.8); // Cap at 80%
    }

    calculateNumDrops(entity) {
        let numDrops = 1;
        
        if (entity.isBuilding) {
            numDrops += 1;
        }
        
        if (entity.level && entity.level > 5) {
            numDrops += 1;
        }
        
        if (entity.team === 'neutral') {
            numDrops += Math.floor(Math.random() * 2); // 0-1 extra drops
        }
        
        return numDrops;
    }

    selectRandomItem(entity) {
        const rarityWeights = {
            common: 60,
            uncommon: 30,
            rare: 10
        };
        
        // Adjust weights based on entity
        if (entity.level && entity.level > 7) {
            rarityWeights.rare += 15;
            rarityWeights.uncommon += 10;
            rarityWeights.common -= 25;
        }
        
        if (entity.team === 'neutral') {
            rarityWeights.rare += 20;
            rarityWeights.uncommon += 15;
            rarityWeights.common -= 35;
        }
        
        // Select rarity
        const totalWeight = Object.values(rarityWeights).reduce((a, b) => a + b, 0);
        let random = Math.random() * totalWeight;
        let selectedRarity = 'common';
        
        for (const [rarity, weight] of Object.entries(rarityWeights)) {
            random -= weight;
            if (random <= 0) {
                selectedRarity = rarity;
                break;
            }
        }
        
        // Get items of selected rarity
        const itemsOfRarity = Object.entries(this.itemTypes)
            .filter(([key, item]) => item.rarity === selectedRarity);
        
        if (itemsOfRarity.length === 0) return null;
        
        const randomItem = itemsOfRarity[Math.floor(Math.random() * itemsOfRarity.length)];
        return randomItem[0];
    }

    createLootItem(itemTypeKey, x, y) {
        const itemType = this.itemTypes[itemTypeKey];
        if (!itemType) return;
        
        // Add some randomness to position
        const offsetX = (Math.random() - 0.5) * 40;
        const offsetY = (Math.random() - 0.5) * 40;
        
        const lootItem = {
            id: `loot_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
            type: itemTypeKey,
            x: x + offsetX,
            y: y + offsetY,
            ...itemType,
            createdAt: Date.now(),
            collected: false,
            bobOffset: Math.random() * Math.PI * 2, // For floating animation
            glowPhase: Math.random() * Math.PI * 2 // For glow animation
        };
        
        this.lootItems.push(lootItem);
        
        // Create pickup effect
        this.game.createLootSpawnEffect(lootItem.x, lootItem.y, lootItem.color);
    }

    update(deltaTime) {
        const now = Date.now();
        
        // Update loot items
        for (let i = this.lootItems.length - 1; i >= 0; i--) {
            const item = this.lootItems[i];
            
            // Remove expired items
            if (now - item.createdAt > item.duration) {
                this.lootItems.splice(i, 1);
                continue;
            }
            
            // Update animation phases
            item.bobOffset += deltaTime * 3;
            item.glowPhase += deltaTime * 4;
            
            // Check for pickup by player units
            this.checkPickup(item);
        }
    }

    checkPickup(item) {
        if (item.collected) return;
        
        const pickupRange = 25;
        
        for (const unit of this.game.teams.player.units) {
            if (unit.health <= 0) continue;
            
            const distance = Math.hypot(unit.x - item.x, unit.y - item.y);
            if (distance < pickupRange) {
                this.pickupItem(item, unit);
                break;
            }
        }
    }

    pickupItem(item, collector) {
        if (item.collected) return;
        
        item.collected = true;
        
        // Apply item effect
        switch (item.type) {
            case 'credits':
                this.game.resources.spice += item.value;
                this.game.ui.showNotification(`+${item.value} Credits`, 'success', 2000);
                break;
                
            case 'spice':
                this.game.resources.spice += item.value;
                this.game.ui.showNotification(`+${item.value} Spice`, 'success', 2000);
                break;
                
            case 'boost':
                this.applyBoost(item, collector);
                break;
                
            case 'consumable':
                this.applyConsumable(item, collector);
                break;
        }
        
        // Remove from array
        const index = this.lootItems.indexOf(item);
        if (index > -1) {
            this.lootItems.splice(index, 1);
        }
        
        // Play pickup sound and effect
        this.game.soundManager.play('complete');
        this.game.createLootPickupEffect(item.x, item.y, item.color);
        this.game.ui.updateResourceDisplay();
    }

    applyBoost(item, target) {
        switch (item.effect) {
            case 'heal':
                const healAmount = Math.floor(target.maxHealth * item.value);
                target.health = Math.min(target.maxHealth, target.health + healAmount);
                this.game.ui.showNotification(`${target.type} healed for ${healAmount}!`, 'success', 2000);
                break;
                
            case 'damage':
                this.applyTemporaryBoost(target, 'damage', item.value, item.duration_effect);
                this.game.ui.showNotification(`${target.type} damage boosted!`, 'success', 2000);
                break;
                
            case 'speed':
                this.applyTemporaryBoost(target, 'speed', item.value, item.duration_effect);
                this.game.ui.showNotification(`${target.type} speed boosted!`, 'success', 2000);
                break;
                
            case 'shield':
                this.applyTemporaryBoost(target, 'shield', item.value, item.duration_effect);
                this.game.ui.showNotification(`${target.type} shielded!`, 'success', 2000);
                break;
                
            case 'xp':
                this.game.levelingSystem.addXP(target, item.value);
                this.game.ui.showNotification(`${target.type} gained ${item.value} XP!`, 'success', 2000);
                break;
        }
    }

    applyConsumable(item, target) {
        switch (item.effect) {
            case 'repair':
                target.health = target.maxHealth;
                this.game.ui.showNotification(`${target.type} fully repaired!`, 'success', 2000);
                break;
        }
    }

    applyTemporaryBoost(target, boostType, multiplier, duration) {
        if (!target.activeBoosts) {
            target.activeBoosts = {};
        }
        
        // Remove existing boost of same type
        if (target.activeBoosts[boostType]) {
            clearTimeout(target.activeBoosts[boostType].timeout);
        }
        
        // Apply boost
        target.activeBoosts[boostType] = {
            multiplier: multiplier,
            timeout: setTimeout(() => {
                delete target.activeBoosts[boostType];
            }, duration)
        };
    }

    // Get effective stat value considering boosts
    getEffectiveStat(entity, statName, baseValue) {
        if (!entity.activeBoosts || !entity.activeBoosts[statName]) {
            return baseValue;
        }
        
        return baseValue * entity.activeBoosts[statName].multiplier;
    }

    // Check if entity has shield
    hasShield(entity) {
        return entity.activeBoosts && entity.activeBoosts.shield;
    }

    // Apply shield damage reduction
    applyShieldDamage(entity, damage) {
        if (this.hasShield(entity)) {
            const reduction = entity.activeBoosts.shield.multiplier;
            return damage * (1 - reduction);
        }
        return damage;
    }

    draw(ctx) {
        this.lootItems.forEach(item => {
            if (item.collected) return;
            
            ctx.save();
            
            // Floating animation
            const bobHeight = Math.sin(item.bobOffset) * 3;
            const glowIntensity = (Math.sin(item.glowPhase) + 1) * 0.5;
            
            ctx.translate(item.x, item.y + bobHeight);
            
            // Glow effect
            ctx.shadowColor = item.color;
            ctx.shadowBlur = 10 + glowIntensity * 10;
            
            // Draw item
            ctx.fillStyle = item.color;
            ctx.beginPath();
            
            // Different shapes for different item types
            switch (item.type) {
                case 'credits':
                    // Diamond shape
                    ctx.moveTo(0, -item.size);
                    ctx.lineTo(item.size, 0);
                    ctx.lineTo(0, item.size);
                    ctx.lineTo(-item.size, 0);
                    ctx.closePath();
                    break;
                    
                case 'spice':
                    // Hexagon
                    for (let i = 0; i < 6; i++) {
                        const angle = (i * Math.PI) / 3;
                        const x = Math.cos(angle) * item.size;
                        const y = Math.sin(angle) * item.size;
                        if (i === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                    ctx.closePath();
                    break;
                    
                default:
                    // Circle for boosts and consumables
                    ctx.arc(0, 0, item.size, 0, Math.PI * 2);
                    break;
            }
            
            ctx.fill();
            
            // Inner highlight
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(-item.size * 0.3, -item.size * 0.3, item.size * 0.4, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }
}
