import { AchievementSystem } from './AchievementSystem.js';
import { CampaignSystem } from './CampaignSystem.js';
import { ScoringSystem } from './ScoringSystem.js';
import { VictoryConditions } from './VictoryConditions.js';

export class VictorySystem {
    constructor(game) {
        this.game = game;
        
        // Initialize subsystems
        this.victoryConditions = new VictoryConditions();
        this.campaignSystem = new CampaignSystem();
        this.achievementSystem = new AchievementSystem();
        this.scoringSystem = new ScoringSystem();
        
        // Victory state
        this.gameEnded = false;
        this.victoryType = null;
        this.finalScore = 0;
        
        // Set initial difficulty
        this.setDifficulty('normal');
    }

    update(deltaTime) {
        if (this.gameEnded) return;

        // Update victory progress
        this.victoryConditions.updateProgress(this.game);
        
        // Update campaign progress
        const missionResult = this.campaignSystem.updateProgress(this.game, this.victoryConditions.victoryProgress);
        if (missionResult) {
            this.handleMissionComplete(missionResult);
        }
        
        // Update achievements
        const newAchievements = this.achievementSystem.updateAchievements(this.game);
        if (newAchievements.length > 0) {
            this.handleNewAchievements(newAchievements);
        }
        
        // Update scoring
        this.scoringSystem.updateScoring(this.game);
        
        // Check for victory conditions
        this.checkVictoryConditions();
    }

    checkVictoryConditions() {
        const completedVictories = this.victoryConditions.checkVictoryConditions();
        
        if (completedVictories.length > 0) {
            // Use the first completed victory type
            this.achieveVictory(completedVictories[0]);
        }
    }

    achieveVictory(victoryType) {
        if (this.gameEnded) return;

        this.gameEnded = true;
        this.victoryType = victoryType;
        
        // Calculate final score
        const achievements = this.achievementSystem.getAllAchievements();
        const campaignData = this.campaignSystem.getCampaignStatus().campaign;
        this.finalScore = this.scoringSystem.calculateFinalScore(this.game, achievements, campaignData);
        
        // Award victory achievement
        this.achievementSystem.unlockAchievement(`victory_${victoryType.toLowerCase()}`);
        
        // Show victory screen
        this.showVictoryScreen(victoryType, this.finalScore);
        
        // Pause game
        this.game.paused = true;
        
        // Handle campaign victory
        if (victoryType === 'CAMPAIGN') {
            this.handleCampaignVictory();
        }
    }

    handleMissionComplete(missionResult) {
        const { mission, rewards, campaignComplete } = missionResult;
        
        // Show mission complete notification
        this.game.ui?.showNotification(`Mission Complete: ${mission.name}!`, 'success');
        
        // Show rewards
        if (rewards.spice || rewards.water || rewards.techPoints) {
            const rewardText = Object.entries(rewards)
                .map(([type, amount]) => `${amount} ${type}`)
                .join(', ');
            this.game.ui?.showNotification(`Rewards: ${rewardText}`, 'info');
        }
        
        // Check for campaign completion
        if (campaignComplete) {
            this.victoryConditions.victoryProgress.CAMPAIGN.completed = true;
        }
    }

    handleNewAchievements(achievements) {
        achievements.forEach(achievement => {
            this.game.ui?.showNotification(
                `Achievement Unlocked: ${achievement.name}!`, 
                'success'
            );
        });
    }

    handleCampaignVictory() {
        this.game.ui?.showNotification(
            'Campaign Complete! You are the Emperor of Arrakis!', 
            'success'
        );
        this.achievementSystem.unlockAchievement('emperor');
    }

    showVictoryScreen(victoryType, finalScore) {
        const victoryInfo = this.victoryConditions.victoryTypes[victoryType];
        const scoreBreakdown = this.scoringSystem.getScoreBreakdown();
        const performanceRating = this.scoringSystem.getPerformanceRating();
        
        console.log(`Victory Achieved: ${victoryInfo.name}`);
        console.log(`Final Score: ${finalScore}`);
        console.log(`Performance Rating: ${performanceRating.rating}`);
        
        // Update UI elements
        if (this.game.ui) {
            this.game.ui.showVictoryScreen({
                victoryType,
                victoryInfo,
                finalScore,
                scoreBreakdown,
                performanceRating,
                achievements: this.achievementSystem.getUnlockedAchievements(),
                campaign: this.campaignSystem.getCampaignStatus().campaign
            });
        }
    }

    // Configuration methods
    setVictoryConditions(conditions) {
        this.victoryConditions.setVictoryConditions(conditions);
        this.game.ui?.showNotification(
            `Victory conditions set: ${conditions.join(', ')}`, 
            'info'
        );
    }

    setDifficulty(difficulty) {
        this.scoringSystem.setDifficultyMultiplier(difficulty);
        this.campaignSystem.campaign.difficulty = difficulty;
    }

    // Status methods
    getVictoryStatus() {
        return {
            ...this.victoryConditions.getVictoryStatus(),
            ...this.campaignSystem.getCampaignStatus(),
            achievements: this.achievementSystem.getAllAchievements(),
            scoring: this.scoringSystem.getScoreBreakdown(),
            gameEnded: this.gameEnded,
            victoryType: this.victoryType,
            finalScore: this.finalScore
        };
    }

    getProgressSummary() {
        const status = this.getVictoryStatus();
        const summary = {
            victory: {
                activeConditions: status.activeConditions,
                progress: status.progress
            },
            campaign: {
                currentMission: status.campaign.currentMission,
                completedMissions: status.campaign.completedMissions.length,
                totalMissions: status.campaign.totalMissions
            },
            achievements: {
                unlocked: status.achievements.filter(a => a.unlocked).length,
                total: status.achievements.length
            },
            score: {
                current: this.scoringSystem.scoring,
                performance: this.scoringSystem.getPerformanceRating()
            }
        };

        return summary;
    }

    // Save/Load functionality
    exportVictoryData() {
        return {
            victoryConditions: this.victoryConditions.getVictoryStatus(),
            campaign: this.campaignSystem.getCampaignStatus(),
            achievements: this.achievementSystem.exportAchievements(),
            scoring: this.scoringSystem.exportScoringData(),
            gameEnded: this.gameEnded,
            victoryType: this.victoryType,
            finalScore: this.finalScore,
            timestamp: Date.now()
        };
    }

    importVictoryData(data) {
        if (data.victoryConditions) {
            this.victoryConditions.activeVictoryConditions = new Set(data.victoryConditions.activeConditions);
            this.victoryConditions.victoryProgress = data.victoryConditions.progress;
        }
        
        if (data.campaign) {
            this.campaignSystem.campaign = { ...this.campaignSystem.campaign, ...data.campaign.campaign };
        }
        
        if (data.achievements) {
            this.achievementSystem.importAchievements(data.achievements);
        }
        
        if (data.scoring) {
            this.scoringSystem.importScoringData(data.scoring);
        }
        
        this.gameEnded = data.gameEnded || false;
        this.victoryType = data.victoryType || null;
        this.finalScore = data.finalScore || 0;
    }

    // Reset functionality
    resetVictorySystem() {
        this.victoryConditions = new VictoryConditions();
        this.campaignSystem = new CampaignSystem();
        this.achievementSystem = new AchievementSystem();
        this.scoringSystem = new ScoringSystem();
        
        this.gameEnded = false;
        this.victoryType = null;
        this.finalScore = 0;
    }

    // Cheat/Debug methods (for development)
    debugUnlockAchievement(achievementId) {
        return this.achievementSystem.unlockAchievement(achievementId);
    }

    debugCompleteMission(missionId) {
        return this.campaignSystem.completeMission(missionId, this.game);
    }

    debugSetVictoryProgress(victoryType, progress) {
        if (this.victoryConditions.victoryProgress[victoryType]) {
            this.victoryConditions.victoryProgress[victoryType].progress = progress;
        }
    }
}