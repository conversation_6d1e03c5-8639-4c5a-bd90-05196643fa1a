export class AchievementSystem {
    constructor() {
        this.achievements = new Map();
        this.initializeAchievements();
    }

    initializeAchievements() {
        const achievementDefinitions = [
            { 
                id: 'first_blood', 
                name: 'First Blood', 
                description: 'Destroy your first enemy unit', 
                icon: '🩸',
                condition: (game) => game.teams.player.stats.enemiesDestroyed > 0
            },
            { 
                id: 'builder', 
                name: 'Builder', 
                description: 'Construct 50 buildings', 
                icon: '🏗️',
                condition: (game) => game.teams.player.stats.buildingsBuilt >= 50
            },
            { 
                id: 'researcher', 
                name: 'Researcher', 
                description: 'Complete 10 technologies', 
                icon: '🔬',
                condition: (game) => game.technologySystem.researchedTechnologies.size >= 10
            },
            { 
                id: 'diplomat', 
                name: 'Diplomat', 
                description: 'Form an alliance', 
                icon: '🤝',
                condition: (game) => {
                    if (!game.diplomacySystem) return false;
                    const summary = game.diplomacySystem.getDiplomaticSummary();
                    return Object.values(summary).some(faction => faction.state === 'ALLIED');
                }
            },
            { 
                id: 'economist', 
                name: 'Economist', 
                description: 'Accumulate 20,000 spice', 
                icon: '💰',
                condition: (game) => game.resourceManager.getResource('spice') >= 20000
            },
            { 
                id: 'survivor', 
                name: 'Survivor', 
                description: 'Survive a sandworm attack', 
                icon: '🐛',
                condition: (game) => game.teams.player.stats.sandwormsSurvived > 0
            },
            { 
                id: 'conqueror', 
                name: 'Conqueror', 
                description: 'Capture 5 neutral structures', 
                icon: '🏰',
                condition: (game) => game.teams.player.stats.structuresCaptured >= 5
            },
            { 
                id: 'tech_master', 
                name: 'Tech Master', 
                description: 'Research all Tier 3 technologies', 
                icon: '🧠',
                condition: (game) => this.countResearchedTechByTier(game, 3) >= 5
            },
            { 
                id: 'spice_lord', 
                name: 'Spice Lord', 
                description: 'Control 80% of spice fields', 
                icon: '👑',
                condition: (game) => this.getSpiceControlPercentage(game) >= 0.8
            },
            { 
                id: 'speed_runner', 
                name: 'Speed Runner', 
                description: 'Complete a mission in under 15 minutes', 
                icon: '⚡',
                condition: (game) => game.teams.player.stats.fastestMission < 900 // 15 minutes
            },
            { 
                id: 'veteran', 
                name: 'Veteran', 
                description: 'Have a unit reach maximum veterancy', 
                icon: '🎖️',
                condition: (game) => game.teams.player.units.some(unit => unit.veterancy >= 3)
            },
            { 
                id: 'fortress', 
                name: 'Fortress', 
                description: 'Build 20 defensive structures', 
                icon: '🛡️',
                condition: (game) => game.teams.player.buildings.filter(b => b.isDefensive).length >= 20
            },
            { 
                id: 'industrialist', 
                name: 'Industrialist', 
                description: 'Have 10 production buildings operating simultaneously', 
                icon: '🏭',
                condition: (game) => game.teams.player.buildings.filter(b => b.isFactory && !b.isUnderConstruction).length >= 10
            },
            { 
                id: 'power_lord', 
                name: 'Power Lord', 
                description: 'Generate 1000+ power units', 
                icon: '⚡',
                condition: (game) => this.calculateTotalPowerGeneration(game) >= 1000
            },
            { 
                id: 'water_baron', 
                name: 'Water Baron', 
                description: 'Accumulate 50,000 water', 
                icon: '💧',
                condition: (game) => game.resourceManager.getResource('water') >= 50000
            }
        ];

        achievementDefinitions.forEach(achievement => {
            this.achievements.set(achievement.id, {
                ...achievement,
                unlocked: false,
                progress: 0,
                maxProgress: 1,
                unlockedAt: null
            });
        });
    }

    updateAchievements(game) {
        const newlyUnlocked = [];

        this.achievements.forEach((achievement, id) => {
            if (achievement.unlocked) return;

            if (achievement.condition(game)) {
                achievement.unlocked = true;
                achievement.unlockedAt = Date.now();
                achievement.progress = 1;
                newlyUnlocked.push(achievement);
            }
        });

        return newlyUnlocked;
    }

    unlockAchievement(achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement || achievement.unlocked) return false;

        achievement.unlocked = true;
        achievement.unlockedAt = Date.now();
        achievement.progress = 1;

        return achievement;
    }

    getAchievementProgress(achievementId) {
        const achievement = this.achievements.get(achievementId);
        return achievement ? achievement.progress : 0;
    }

    getUnlockedAchievements() {
        return Array.from(this.achievements.values()).filter(a => a.unlocked);
    }

    getAllAchievements() {
        return Array.from(this.achievements.values());
    }

    getAchievementStats() {
        const total = this.achievements.size;
        const unlocked = this.getUnlockedAchievements().length;
        const percentage = total > 0 ? (unlocked / total) * 100 : 0;

        return {
            total,
            unlocked,
            percentage: Math.round(percentage)
        };
    }

    // Helper methods
    countResearchedTechByTier(game, tier) {
        let count = 0;
        game.technologySystem.researchedTechnologies.forEach(techId => {
            const tech = game.technologySystem.technologies[techId];
            if (tech && tech.tier === tier) {
                count++;
            }
        });
        return count;
    }

    getSpiceControlPercentage(game) {
        if (!game.spiceFields || game.spiceFields.length === 0) return 0;

        let controlled = 0;
        game.spiceFields.forEach(field => {
            const nearbyPlayerUnits = game.teams.player.units.filter(unit => {
                const dist = Math.hypot(unit.x - field.x, unit.y - field.y);
                return dist < field.radius + 100;
            });

            const nearbyPlayerBuildings = game.teams.player.buildings.filter(building => {
                const dist = Math.hypot(building.x - field.x, building.y - field.y);
                return dist < field.radius + 150;
            });

            if (nearbyPlayerUnits.length > 0 || nearbyPlayerBuildings.length > 0) {
                controlled++;
            }
        });

        return controlled / game.spiceFields.length;
    }

    calculateTotalPowerGeneration(game) {
        return game.teams.player.buildings
            .filter(b => !b.isUnderConstruction)
            .reduce((total, building) => total + (building.powerGeneration || 0), 0);
    }

    // Achievement categories for UI organization
    getAchievementsByCategory() {
        const categories = {
            combat: [],
            economic: [],
            technological: [],
            diplomatic: [],
            construction: [],
            special: []
        };

        this.achievements.forEach(achievement => {
            switch (achievement.id) {
                case 'first_blood':
                case 'survivor':
                case 'conqueror':
                case 'veteran':
                    categories.combat.push(achievement);
                    break;
                case 'economist':
                case 'spice_lord':
                case 'water_baron':
                    categories.economic.push(achievement);
                    break;
                case 'researcher':
                case 'tech_master':
                case 'power_lord':
                    categories.technological.push(achievement);
                    break;
                case 'diplomat':
                    categories.diplomatic.push(achievement);
                    break;
                case 'builder':
                case 'fortress':
                case 'industrialist':
                    categories.construction.push(achievement);
                    break;
                case 'speed_runner':
                default:
                    categories.special.push(achievement);
                    break;
            }
        });

        return categories;
    }

    // Export/Import for save system
    exportAchievements() {
        const data = {};
        this.achievements.forEach((achievement, id) => {
            data[id] = {
                unlocked: achievement.unlocked,
                unlockedAt: achievement.unlockedAt,
                progress: achievement.progress
            };
        });
        return data;
    }

    importAchievements(data) {
        Object.keys(data).forEach(id => {
            const achievement = this.achievements.get(id);
            if (achievement && data[id]) {
                achievement.unlocked = data[id].unlocked || false;
                achievement.unlockedAt = data[id].unlockedAt || null;
                achievement.progress = data[id].progress || 0;
            }
        });
    }
}