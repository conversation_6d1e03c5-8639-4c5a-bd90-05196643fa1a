export class CampaignSystem {
    constructor() {
        this.campaign = {
            currentMission: 1,
            totalMissions: 10,
            completedMissions: [],
            availableMissions: [1],
            missionObjectives: {},
            campaignScore: 0,
            difficulty: 'normal'
        };

        this.initializeCampaign();
    }

    initializeCampaign() {
        this.campaign.missions = {
            1: {
                name: 'Desert Awakening',
                description: 'Establish your first base and harvest spice',
                objectives: [
                    { type: 'build', target: 'base', count: 1, completed: false },
                    { type: 'harvest', target: 'spice', amount: 2000, completed: false },
                    { type: 'build', target: 'harvester', count: 2, completed: false }
                ],
                rewards: { spice: 1000, techPoints: 50 },
                difficulty: 'easy',
                unlocks: [2]
            },
            2: {
                name: 'First Contact',
                description: 'Encounter enemy forces and defend your territory',
                objectives: [
                    { type: 'destroy', target: 'enemy_unit', count: 5, completed: false },
                    { type: 'build', target: 'soldier', count: 10, completed: false },
                    { type: 'survive', duration: 600, completed: false }
                ],
                rewards: { spice: 2000, water: 500 },
                difficulty: 'easy',
                unlocks: [3]
            },
            3: {
                name: 'Technological Edge',
                description: 'Research your first technologies',
                objectives: [
                    { type: 'build', target: 'research_lab', count: 1, completed: false },
                    { type: 'research', target: 'any', count: 3, completed: false },
                    { type: 'build', target: 'powerplant', count: 2, completed: false }
                ],
                rewards: { techPoints: 200, spice: 3000 },
                difficulty: 'medium',
                unlocks: [4, 5]
            },
            4: {
                name: 'Water Wars',
                description: 'Secure water sources and manage resources',
                objectives: [
                    { type: 'build', target: 'water_extraction', count: 2, completed: false },
                    { type: 'accumulate', target: 'water', amount: 2000, completed: false },
                    { type: 'research', target: 'water_recycling', count: 1, completed: false }
                ],
                rewards: { water: 1000, techPoints: 100 },
                difficulty: 'medium',
                unlocks: [6]
            },
            5: {
                name: 'Diplomatic Overtures',
                description: 'Make contact with other factions',
                objectives: [
                    { type: 'research', target: 'trade_networks', count: 1, completed: false },
                    { type: 'diplomacy', target: 'alliance', count: 1, completed: false },
                    { type: 'trade', target: 'any', count: 3, completed: false }
                ],
                rewards: { spice: 5000, diplomaticBonus: true },
                difficulty: 'medium',
                unlocks: [7]
            },
            6: {
                name: 'Advanced Warfare',
                description: 'Deploy advanced military technologies',
                objectives: [
                    { type: 'research', target: 'enhanced_weaponry', count: 1, completed: false },
                    { type: 'build', target: 'tank', count: 5, completed: false },
                    { type: 'destroy', target: 'enemy_building', count: 5, completed: false }
                ],
                rewards: { spice: 4000, techPoints: 150 },
                difficulty: 'hard',
                unlocks: [8]
            },
            7: {
                name: 'Sandworm Hunt',
                description: 'Survive sandworm attacks and capture neutral structures',
                objectives: [
                    { type: 'survive', target: 'sandworm_attack', count: 3, completed: false },
                    { type: 'capture', target: 'neutral_structure', count: 2, completed: false },
                    { type: 'build', target: 'turret', count: 4, completed: false }
                ],
                rewards: { spice: 6000, water: 1500 },
                difficulty: 'hard',
                unlocks: [9]
            },
            8: {
                name: 'Spice Monopoly',
                description: 'Control the majority of spice fields',
                objectives: [
                    { type: 'control', target: 'spice_field', percentage: 0.7, completed: false },
                    { type: 'accumulate', target: 'spice', amount: 25000, completed: false },
                    { type: 'research', target: 'matter_conversion', count: 1, completed: false }
                ],
                rewards: { spice: 10000, techPoints: 300 },
                difficulty: 'hard',
                unlocks: [10]
            },
            9: {
                name: 'Technological Supremacy',
                description: 'Research advanced technologies',
                objectives: [
                    { type: 'research', target: 'plasma_weaponry', count: 1, completed: false },
                    { type: 'research', target: 'shield_technology', count: 1, completed: false },
                    { type: 'research', target: 'orbital_support', count: 1, completed: false }
                ],
                rewards: { techPoints: 500, spice: 8000 },
                difficulty: 'very_hard',
                unlocks: [10]
            },
            10: {
                name: 'Emperor of Arrakis',
                description: 'Achieve total dominance of the planet',
                objectives: [
                    { type: 'victory', target: 'military', completed: false },
                    { type: 'victory', target: 'economic', completed: false },
                    { type: 'research', target: 'quantum_computing', count: 1, completed: false }
                ],
                rewards: { campaignVictory: true },
                difficulty: 'legendary',
                unlocks: []
            }
        };

        this.campaign.missionObjectives = this.campaign.missions[1].objectives;
    }

    updateProgress(game, victoryProgress) {
        if (!this.campaign.missionObjectives) return;

        this.campaign.missionObjectives.forEach(objective => {
            if (objective.completed) return;

            switch (objective.type) {
                case 'build':
                    objective.current = this.countBuiltEntities(game, objective.target);
                    objective.completed = objective.current >= objective.count;
                    break;
                case 'destroy':
                    objective.current = this.countDestroyedEntities(game, objective.target);
                    objective.completed = objective.current >= objective.count;
                    break;
                case 'harvest':
                    objective.current = game.teams.player.stats.spiceCollected;
                    objective.completed = objective.current >= objective.amount;
                    break;
                case 'research':
                    objective.current = this.countResearchedTech(game, objective.target);
                    objective.completed = objective.current >= objective.count;
                    break;
                case 'accumulate':
                    objective.current = game.resourceManager.getResource(objective.target);
                    objective.completed = objective.current >= objective.amount;
                    break;
                case 'survive':
                    if (!objective.startTime) objective.startTime = game.gameTime;
                    objective.current = game.gameTime - objective.startTime;
                    objective.completed = objective.current >= objective.duration;
                    break;
                case 'diplomacy':
                    objective.current = this.countDiplomaticActions(game, objective.target);
                    objective.completed = objective.current >= objective.count;
                    break;
                case 'control':
                    const controlPercentage = this.getControlPercentage(game, objective.target);
                    objective.current = controlPercentage;
                    objective.completed = controlPercentage >= objective.percentage;
                    break;
                case 'victory':
                    objective.completed = victoryProgress[objective.target.toUpperCase()]?.completed || false;
                    break;
            }
        });

        // Check if mission is complete
        const allObjectivesComplete = this.campaign.missionObjectives.every(obj => obj.completed);
        if (allObjectivesComplete && !this.campaign.completedMissions.includes(this.campaign.currentMission)) {
            return this.completeMission(this.campaign.currentMission, game);
        }

        return null;
    }

    completeMission(missionId, game) {
        const mission = this.campaign.missions[missionId];
        if (!mission) return null;

        this.campaign.completedMissions.push(missionId);

        // Award mission rewards
        const rewards = {};
        if (mission.rewards.spice) {
            game.resourceManager.addResource('spice', mission.rewards.spice);
            rewards.spice = mission.rewards.spice;
        }
        if (mission.rewards.water) {
            game.resourceManager.addResource('water', mission.rewards.water);
            rewards.water = mission.rewards.water;
        }
        if (mission.rewards.techPoints) {
            game.resourceManager.addResource('techPoints', mission.rewards.techPoints);
            rewards.techPoints = mission.rewards.techPoints;
        }

        // Unlock next missions
        mission.unlocks.forEach(nextMissionId => {
            if (!this.campaign.availableMissions.includes(nextMissionId)) {
                this.campaign.availableMissions.push(nextMissionId);
            }
        });

        // Update campaign score
        this.campaign.campaignScore += this.calculateMissionScore(mission);

        return {
            mission,
            rewards,
            campaignComplete: this.campaign.completedMissions.length >= this.campaign.totalMissions
        };
    }

    calculateMissionScore(mission) {
        const baseScore = 1000;
        const difficultyMultipliers = {
            easy: 1.0,
            medium: 1.5,
            hard: 2.0,
            very_hard: 2.5,
            legendary: 3.0
        };

        return Math.floor(baseScore * (difficultyMultipliers[mission.difficulty] || 1.0));
    }

    // Helper methods
    countBuiltEntities(game, entityType) {
        if (entityType === 'any') {
            return game.teams.player.units.length + game.teams.player.buildings.length;
        }

        const units = game.teams.player.units.filter(u => u.type === entityType).length;
        const buildings = game.teams.player.buildings.filter(b => b.type === entityType).length;

        return units + buildings;
    }

    countDestroyedEntities(game, entityType) {
        if (entityType === 'enemy_unit') {
            return game.teams.player.stats.enemiesDestroyed;
        }
        if (entityType === 'enemy_building') {
            return game.teams.player.stats.enemyBuildingsDestroyed || 0;
        }

        return 0;
    }

    countResearchedTech(game, techTarget) {
        if (techTarget === 'any') {
            return game.technologySystem.researchedTechnologies.size;
        }

        return game.technologySystem.isResearched(techTarget) ? 1 : 0;
    }

    countDiplomaticActions(game, actionType) {
        if (!game.diplomacySystem) return 0;

        const summary = game.diplomacySystem.getDiplomaticSummary();

        if (actionType === 'alliance') {
            return Object.values(summary).filter(faction => faction.state === 'ALLIED').length;
        }
        if (actionType === 'trade') {
            return Object.values(summary).filter(faction => faction.tradeAgreement).length;
        }

        return 0;
    }

    getControlPercentage(game, target) {
        if (target === 'spice_field') {
            const controlled = this.countControlledSpiceFields(game);
            return controlled / Math.max(1, game.spiceFields.length);
        }

        return 0;
    }

    countControlledSpiceFields(game) {
        let controlled = 0;

        game.spiceFields.forEach(field => {
            const nearbyPlayerUnits = game.teams.player.units.filter(unit => {
                const dist = Math.hypot(unit.x - field.x, unit.y - field.y);
                return dist < field.radius + 100;
            });

            const nearbyPlayerBuildings = game.teams.player.buildings.filter(building => {
                const dist = Math.hypot(building.x - field.x, building.y - field.y);
                return dist < field.radius + 150;
            });

            if (nearbyPlayerUnits.length > 0 || nearbyPlayerBuildings.length > 0) {
                controlled++;
            }
        });

        return controlled;
    }

    getCampaignStatus() {
        return {
            campaign: this.campaign
        };
    }
}