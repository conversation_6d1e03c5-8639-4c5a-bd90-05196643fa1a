export class VictoryConditions {
    constructor() {
        this.victoryTypes = {
            MILITARY: {
                name: 'Military Domination',
                description: 'Destroy all enemy forces and structures',
                icon: '⚔️',
                difficulty: 'medium'
            },
            ECONOMIC: {
                name: 'Economic Supremacy',
                description: 'Accumulate massive wealth and resources',
                icon: '💰',
                difficulty: 'easy'
            },
            TECHNOLOGICAL: {
                name: 'Technological Ascendancy',
                description: 'Research all advanced technologies',
                icon: '🔬',
                difficulty: 'hard'
            },
            DIPLOMATIC: {
                name: 'Diplomatic Unity',
                description: 'Achieve alliance with all major factions',
                icon: '🤝',
                difficulty: 'medium'
            },
            SPICE_CONTROL: {
                name: 'Spice Monopoly',
                description: 'Control all major spice deposits',
                icon: '🏜️',
                difficulty: 'medium'
            },
            CAMPAIGN: {
                name: 'Campaign Victory',
                description: 'Complete all campaign objectives',
                icon: '🏆',
                difficulty: 'varies'
            }
        };

        this.activeVictoryConditions = new Set(['MILITARY']);
        this.victoryProgress = {};
        this.initializeVictoryConditions();
    }

    initializeVictoryConditions() {
        Object.keys(this.victoryTypes).forEach(type => {
            this.victoryProgress[type] = {
                progress: 0,
                maxProgress: 100,
                completed: false,
                requirements: this.getVictoryRequirements(type)
            };
        });
    }

    getVictoryRequirements(victoryType) {
        switch (victoryType) {
            case 'MILITARY':
                return {
                    enemyUnitsDestroyed: 0,
                    enemyBuildingsDestroyed: 0,
                    requiredEnemyUnitsDestroyed: 50,
                    requiredEnemyBuildingsDestroyed: 10
                };
            case 'ECONOMIC':
                return {
                    spiceAccumulated: 0,
                    waterAccumulated: 0,
                    requiredSpice: 50000,
                    requiredWater: 10000,
                    requiredBuildings: 25
                };
            case 'TECHNOLOGICAL':
                return {
                    technologiesResearched: 0,
                    requiredTechnologies: 18,
                    requiredAdvancedTech: 5,
                    requiredLegendaryTech: 2
                };
            case 'DIPLOMATIC':
                return {
                    alliances: 0,
                    tradeAgreements: 0,
                    requiredAlliances: 3,
                    requiredTradeAgreements: 5,
                    noActiveWars: true
                };
            case 'SPICE_CONTROL':
                return {
                    controlledSpiceFields: 0,
                    totalSpiceFields: 0,
                    requiredControlPercentage: 0.8
                };
            case 'CAMPAIGN':
                return {
                    completedMissions: 0,
                    requiredMissions: 10
                };
            default:
                return {};
        }
    }

    updateProgress(game) {
        // Update military victory progress
        if (this.activeVictoryConditions.has('MILITARY')) {
            this.updateMilitaryProgress(game);
        }

        // Update economic victory progress
        if (this.activeVictoryConditions.has('ECONOMIC')) {
            this.updateEconomicProgress(game);
        }

        // Update technological victory progress
        if (this.activeVictoryConditions.has('TECHNOLOGICAL')) {
            this.updateTechnologicalProgress(game);
        }

        // Update diplomatic victory progress
        if (this.activeVictoryConditions.has('DIPLOMATIC')) {
            this.updateDiplomaticProgress(game);
        }

        // Update spice control victory progress
        if (this.activeVictoryConditions.has('SPICE_CONTROL')) {
            this.updateSpiceControlProgress(game);
        }
    }

    updateMilitaryProgress(game) {
        const militaryReq = this.victoryProgress.MILITARY.requirements;
        militaryReq.enemyUnitsDestroyed = game.teams.player.stats.enemiesDestroyed;
        militaryReq.enemyBuildingsDestroyed = this.countDestroyedEnemyBuildings(game);

        const unitProgress = militaryReq.enemyUnitsDestroyed / militaryReq.requiredEnemyUnitsDestroyed;
        const buildingProgress = militaryReq.enemyBuildingsDestroyed / militaryReq.requiredEnemyBuildingsDestroyed;

        this.victoryProgress.MILITARY.progress = Math.min(100, (unitProgress + buildingProgress) * 50);
    }

    updateEconomicProgress(game) {
        const economicReq = this.victoryProgress.ECONOMIC.requirements;
        economicReq.spiceAccumulated = game.resourceManager.getResource('spice');
        economicReq.waterAccumulated = game.resourceManager.getResource('water');

        const spiceProgress = economicReq.spiceAccumulated / economicReq.requiredSpice;
        const waterProgress = economicReq.waterAccumulated / economicReq.requiredWater;
        const buildingProgress = game.teams.player.buildings.length / economicReq.requiredBuildings;

        this.victoryProgress.ECONOMIC.progress = Math.min(100, (spiceProgress + waterProgress + buildingProgress) * 33.33);
    }

    updateTechnologicalProgress(game) {
        const techReq = this.victoryProgress.TECHNOLOGICAL.requirements;
        techReq.technologiesResearched = game.technologySystem.researchedTechnologies.size;

        const advancedTech = this.countResearchedTechByTier(game, 3);
        const legendaryTech = this.countResearchedTechByTier(game, 4);

        const basicProgress = techReq.technologiesResearched / techReq.requiredTechnologies;
        const advancedProgress = advancedTech / techReq.requiredAdvancedTech;
        const legendaryProgress = legendaryTech / techReq.requiredLegendaryTech;

        this.victoryProgress.TECHNOLOGICAL.progress = Math.min(100, (basicProgress + advancedProgress + legendaryProgress) * 33.33);
    }

    updateDiplomaticProgress(game) {
        if (!game.diplomacySystem) return;

        const diplomacyReq = this.victoryProgress.DIPLOMATIC.requirements;
        const summary = game.diplomacySystem.getDiplomaticSummary();

        diplomacyReq.alliances = Object.values(summary).filter(faction => faction.state === 'ALLIED').length;
        diplomacyReq.tradeAgreements = Object.values(summary).filter(faction => faction.tradeAgreement).length;
        diplomacyReq.noActiveWars = !Object.values(summary).some(faction => faction.state === 'WAR');

        const allianceProgress = diplomacyReq.alliances / diplomacyReq.requiredAlliances;
        const tradeProgress = diplomacyReq.tradeAgreements / diplomacyReq.requiredTradeAgreements;
        const peaceBonus = diplomacyReq.noActiveWars ? 0.2 : 0;

        this.victoryProgress.DIPLOMATIC.progress = Math.min(100, (allianceProgress + tradeProgress) * 40 + peaceBonus * 100);
    }

    updateSpiceControlProgress(game) {
        const spiceReq = this.victoryProgress.SPICE_CONTROL.requirements;
        spiceReq.totalSpiceFields = game.spiceFields.length;
        spiceReq.controlledSpiceFields = this.countControlledSpiceFields(game);

        const controlPercentage = spiceReq.controlledSpiceFields / spiceReq.totalSpiceFields;
        this.victoryProgress.SPICE_CONTROL.progress = Math.min(100, (controlPercentage / spiceReq.requiredControlPercentage) * 100);
    }

    checkVictoryConditions() {
        const completedVictories = [];
        
        this.activeVictoryConditions.forEach(victoryType => {
            const progress = this.victoryProgress[victoryType];
            
            if (!progress.completed && progress.progress >= 100) {
                progress.completed = true;
                completedVictories.push(victoryType);
            }
        });

        return completedVictories;
    }

    setVictoryConditions(conditions) {
        this.activeVictoryConditions = new Set(conditions);
    }

    getVictoryStatus() {
        return {
            activeConditions: Array.from(this.activeVictoryConditions),
            progress: this.victoryProgress,
            victoryTypes: this.victoryTypes
        };
    }

    // Helper methods
    countDestroyedEnemyBuildings(game) {
        return game.teams.player.stats.enemyBuildingsDestroyed || 0;
    }

    countResearchedTechByTier(game, tier) {
        let count = 0;
        game.technologySystem.researchedTechnologies.forEach(techId => {
            const tech = game.technologySystem.technologies[techId];
            if (tech && tech.tier === tier) {
                count++;
            }
        });
        return count;
    }

    countControlledSpiceFields(game) {
        let controlled = 0;

        game.spiceFields.forEach(field => {
            const nearbyPlayerUnits = game.teams.player.units.filter(unit => {
                const dist = Math.hypot(unit.x - field.x, unit.y - field.y);
                return dist < field.radius + 100;
            });

            const nearbyPlayerBuildings = game.teams.player.buildings.filter(building => {
                const dist = Math.hypot(building.x - field.x, building.y - field.y);
                return dist < field.radius + 150;
            });

            if (nearbyPlayerUnits.length > 0 || nearbyPlayerBuildings.length > 0) {
                controlled++;
            }
        });

        return controlled;
    }
}