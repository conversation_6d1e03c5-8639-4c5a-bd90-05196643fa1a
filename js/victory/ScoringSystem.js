export class ScoringSystem {
    constructor() {
        this.scoring = {
            unitsBuilt: 0,
            unitsLost: 0,
            enemiesDestroyed: 0,
            buildingsBuilt: 0,
            buildingsLost: 0,
            spiceCollected: 0,
            technologiesResearched: 0,
            diplomaticAgreements: 0,
            timeBonus: 0,
            difficultyMultiplier: 1.0,
            missionBonuses: 0,
            achievementBonuses: 0
        };

        this.scoreMultipliers = {
            enemiesDestroyed: 100,
            buildingsBuilt: 50,
            technologiesResearched: 200,
            spiceCollected: 0.01, // 1 point per 100 spice
            diplomaticAgreements: 300,
            unitsLost: -50, // penalty
            buildingsLost: -100, // penalty
            achievementBonus: 500,
            missionBonus: 1000
        };

        this.difficultyMultipliers = {
            easy: 0.8,
            normal: 1.0,
            hard: 1.3,
            nightmare: 1.6
        };
    }

    updateScoring(game) {
        // Update basic stats
        this.scoring.unitsBuilt = game.teams.player.stats.unitsBuilt || 0;
        this.scoring.unitsLost = game.teams.player.stats.unitsLost || 0;
        this.scoring.enemiesDestroyed = game.teams.player.stats.enemiesDestroyed || 0;
        this.scoring.buildingsBuilt = game.teams.player.stats.buildingsBuilt || 0;
        this.scoring.buildingsLost = game.teams.player.stats.buildingsLost || 0;
        this.scoring.spiceCollected = game.teams.player.stats.spiceCollected || 0;
        this.scoring.technologiesResearched = game.technologySystem?.researchedTechnologies?.size || 0;

        // Update diplomatic agreements
        if (game.diplomacySystem) {
            const summary = game.diplomacySystem.getDiplomaticSummary();
            this.scoring.diplomaticAgreements = Object.values(summary)
                .filter(f => f.tradeAgreement || f.state === 'ALLIED').length;
        }

        // Calculate time bonus (faster completion = higher score)
        if (game.gameTime) {
            const gameTimeMinutes = game.gameTime / 60;
            this.scoring.timeBonus = Math.max(0, 10000 - (gameTimeMinutes * 10));
        }
    }

    calculateFinalScore(game, achievements, campaignData) {
        this.updateScoring(game);

        let score = 0;

        // Base scoring from game performance
        score += this.scoring.enemiesDestroyed * this.scoreMultipliers.enemiesDestroyed;
        score += this.scoring.buildingsBuilt * this.scoreMultipliers.buildingsBuilt;
        score += this.scoring.technologiesResearched * this.scoreMultipliers.technologiesResearched;
        score += Math.floor(this.scoring.spiceCollected * this.scoreMultipliers.spiceCollected);
        score += this.scoring.diplomaticAgreements * this.scoreMultipliers.diplomaticAgreements;

        // Penalties
        score += this.scoring.unitsLost * this.scoreMultipliers.unitsLost;
        score += this.scoring.buildingsLost * this.scoreMultipliers.buildingsLost;

        // Time bonus
        score += this.scoring.timeBonus;

        // Achievement bonuses
        if (achievements) {
            const unlockedAchievements = achievements.filter(a => a.unlocked);
            this.scoring.achievementBonuses = unlockedAchievements.length * this.scoreMultipliers.achievementBonus;
            score += this.scoring.achievementBonuses;
        }

        // Campaign/mission bonuses
        if (campaignData) {
            this.scoring.missionBonuses = campaignData.campaignScore || 0;
            score += this.scoring.missionBonuses;
        }

        // Apply difficulty multiplier
        score *= this.scoring.difficultyMultiplier;

        return Math.max(0, Math.floor(score));
    }

    setDifficultyMultiplier(difficulty) {
        this.scoring.difficultyMultiplier = this.difficultyMultipliers[difficulty] || 1.0;
    }

    getScoreBreakdown() {
        return {
            baseScores: {
                enemiesDestroyed: this.scoring.enemiesDestroyed * this.scoreMultipliers.enemiesDestroyed,
                buildingsBuilt: this.scoring.buildingsBuilt * this.scoreMultipliers.buildingsBuilt,
                technologiesResearched: this.scoring.technologiesResearched * this.scoreMultipliers.technologiesResearched,
                spiceCollected: Math.floor(this.scoring.spiceCollected * this.scoreMultipliers.spiceCollected),
                diplomaticAgreements: this.scoring.diplomaticAgreements * this.scoreMultipliers.diplomaticAgreements
            },
            penalties: {
                unitsLost: this.scoring.unitsLost * this.scoreMultipliers.unitsLost,
                buildingsLost: this.scoring.buildingsLost * this.scoreMultipliers.buildingsLost
            },
            bonuses: {
                timeBonus: this.scoring.timeBonus,
                achievementBonuses: this.scoring.achievementBonuses,
                missionBonuses: this.scoring.missionBonuses
            },
            multipliers: {
                difficulty: this.scoring.difficultyMultiplier
            },
            rawStats: { ...this.scoring }
        };
    }

    getPerformanceRating() {
        const breakdown = this.getScoreBreakdown();
        const totalBaseScore = Object.values(breakdown.baseScores).reduce((sum, score) => sum + score, 0);
        const totalPenalties = Object.values(breakdown.penalties).reduce((sum, penalty) => sum + penalty, 0);
        const totalBonuses = Object.values(breakdown.bonuses).reduce((sum, bonus) => sum + bonus, 0);

        const netScore = totalBaseScore + totalPenalties + totalBonuses;

        // Performance categories
        if (netScore >= 50000) return { rating: 'S', description: 'Legendary Performance', color: '#FFD700' };
        if (netScore >= 35000) return { rating: 'A+', description: 'Outstanding Performance', color: '#FF6B35' };
        if (netScore >= 25000) return { rating: 'A', description: 'Excellent Performance', color: '#F7931E' };
        if (netScore >= 18000) return { rating: 'B+', description: 'Very Good Performance', color: '#FFE135' };
        if (netScore >= 12000) return { rating: 'B', description: 'Good Performance', color: '#C7E596' };
        if (netScore >= 8000) return { rating: 'C+', description: 'Average Performance', color: '#9ACD32' };
        if (netScore >= 5000) return { rating: 'C', description: 'Below Average Performance', color: '#FFA500' };
        if (netScore >= 2000) return { rating: 'D', description: 'Poor Performance', color: '#FF6347' };
        return { rating: 'F', description: 'Very Poor Performance', color: '#FF0000' };
    }

    getEfficiencyMetrics() {
        const metrics = {};

        // Unit efficiency (enemies destroyed per unit lost)
        metrics.unitEfficiency = this.scoring.unitsLost > 0 
            ? this.scoring.enemiesDestroyed / this.scoring.unitsLost 
            : this.scoring.enemiesDestroyed;

        // Economic efficiency (spice per building)
        metrics.economicEfficiency = this.scoring.buildingsBuilt > 0 
            ? this.scoring.spiceCollected / this.scoring.buildingsBuilt 
            : this.scoring.spiceCollected;

        // Technology efficiency (tech per time)
        metrics.technologyEfficiency = this.scoring.technologiesResearched;

        // Overall efficiency rating
        const efficiencyScore = (metrics.unitEfficiency * 0.4) + 
                               (metrics.economicEfficiency * 0.0001) + // Scale down spice
                               (metrics.technologyEfficiency * 0.6);

        if (efficiencyScore >= 50) metrics.overallRating = 'Excellent';
        else if (efficiencyScore >= 25) metrics.overallRating = 'Good';
        else if (efficiencyScore >= 10) metrics.overallRating = 'Average';
        else if (efficiencyScore >= 5) metrics.overallRating = 'Below Average';
        else metrics.overallRating = 'Poor';

        return metrics;
    }

    compareToAverages(averageStats) {
        const comparison = {};
        const currentStats = this.scoring;

        Object.keys(averageStats).forEach(stat => {
            if (currentStats[stat] !== undefined) {
                const current = currentStats[stat];
                const average = averageStats[stat];
                
                if (average > 0) {
                    const ratio = current / average;
                    comparison[stat] = {
                        current,
                        average,
                        ratio,
                        performance: ratio >= 1.2 ? 'Above Average' : 
                                   ratio >= 0.8 ? 'Average' : 'Below Average'
                    };
                }
            }
        });

        return comparison;
    }

    exportScoringData() {
        return {
            scoring: { ...this.scoring },
            timestamp: Date.now(),
            version: '1.0'
        };
    }

    importScoringData(data) {
        if (data.scoring) {
            this.scoring = { ...this.scoring, ...data.scoring };
        }
    }

    resetScoring() {
        Object.keys(this.scoring).forEach(key => {
            if (typeof this.scoring[key] === 'number') {
                this.scoring[key] = key === 'difficultyMultiplier' ? 1.0 : 0;
            }
        });
    }

    // Static methods for global statistics
    static calculateGlobalAverages(allScores) {
        if (!allScores || allScores.length === 0) return {};

        const averages = {};
        const sampleScore = allScores[0];

        Object.keys(sampleScore).forEach(stat => {
            if (typeof sampleScore[stat] === 'number') {
                const sum = allScores.reduce((total, score) => total + (score[stat] || 0), 0);
                averages[stat] = sum / allScores.length;
            }
        });

        return averages;
    }

    static getLeaderboard(allScores, limit = 10) {
        return allScores
            .sort((a, b) => b.finalScore - a.finalScore)
            .slice(0, limit)
            .map((score, index) => ({
                rank: index + 1,
                ...score
            }));
    }
}