export class AnimationManager {
    constructor(game) {
        this.game = game;
        this.animations = [];
        this.cameraTarget = null;
        this.cameraSpeed = 0.05;
    }
    
    update(deltaTime) {
        // Update all active animations
        this.animations = this.animations.filter(animation => {
            animation.update(deltaTime);
            return !animation.isComplete();
        });
        
        // Update camera smoothing
        if (this.cameraTarget) {
            const dx = this.cameraTarget.x - this.game.viewX;
            const dy = this.cameraTarget.y - this.game.viewY;
            
            if (Math.abs(dx) > 5 || Math.abs(dy) > 5) {
                this.game.viewX += dx * this.cameraSpeed;
                this.game.viewY += dy * this.cameraSpeed;
                this.game.updateViewport();
            } else {
                this.cameraTarget = null;
            }
        }
    }
    
    animateUnitSpawn(unit) {
        const animation = new SpawnAnimation(unit);
        this.animations.push(animation);
    }
    
    animateBuildingPlacement(building) {
        const animation = new BuildingPlacementAnimation(building);
        this.animations.push(animation);
    }
    
    animateResourceCollection(x, y, amount) {
        const animation = new ResourceCollectionAnimation(x, y, amount);
        this.animations.push(animation);
    }
    
    animateConstruction(building) {
        const animation = new ConstructionAnimation(building);
        this.animations.push(animation);
    }
    
    setCameraTarget(x, y, smooth = true) {
        if (smooth) {
            this.cameraTarget = { x, y };
        } else {
            this.game.viewX = x;
            this.game.viewY = y;
            this.game.updateViewport();
        }
    }
}

class SpawnAnimation {
    constructor(unit) {
        this.unit = unit;
        this.duration = 0.5;
        this.elapsed = 0;
        this.originalScale = 1;
        this.unit.animationScale = 0;
    }
    
    update(deltaTime) {
        this.elapsed += deltaTime;
        const progress = Math.min(this.elapsed / this.duration, 1);
        
        // Elastic scale animation
        this.unit.animationScale = this.elasticOut(progress);
        
        if (progress >= 1) {
            this.unit.animationScale = this.originalScale;
        }
    }
    
    isComplete() {
        return this.elapsed >= this.duration;
    }
    
    elasticOut(t) {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    }
}

class BuildingPlacementAnimation {
    constructor(building) {
        this.building = building;
        this.duration = 0.8;
        this.elapsed = 0;
        this.originalScale = 1;
        this.building.animationScale = 0;
    }
    
    update(deltaTime) {
        this.elapsed += deltaTime;
        const progress = Math.min(this.elapsed / this.duration, 1);
        
        // Bounce scale animation
        this.building.animationScale = this.bounceOut(progress);
        
        if (progress >= 1) {
            this.building.animationScale = this.originalScale;
        }
    }
    
    isComplete() {
        return this.elapsed >= this.duration;
    }
    
    bounceOut(t) {
        const n1 = 7.5625;
        const d1 = 2.75;
        
        if (t < 1 / d1) {
            return n1 * t * t;
        } else if (t < 2 / d1) {
            return n1 * (t -= 1.5 / d1) * t + 0.75;
        } else if (t < 2.5 / d1) {
            return n1 * (t -= 2.25 / d1) * t + 0.9375;
        } else {
            return n1 * (t -= 2.625 / d1) * t + 0.984375;
        }
    }
}

class ResourceCollectionAnimation {
    constructor(x, y, amount) {
        this.x = x;
        this.y = y;
        this.amount = amount;
        this.duration = 1.0;
        this.elapsed = 0;
        this.startY = y;
        this.endY = y - 50;
    }
    
    update(deltaTime) {
        this.elapsed += deltaTime;
        const progress = Math.min(this.elapsed / this.duration, 1);
        
        this.y = this.startY + (this.endY - this.startY) * progress;
        this.opacity = 1 - progress;
    }
    
    isComplete() {
        return this.elapsed >= this.duration;
    }
    
    draw(ctx) {
        if (this.opacity > 0) {
            ctx.save();
            ctx.globalAlpha = this.opacity;
            ctx.fillStyle = '#FFD700';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`+${this.amount}`, this.x, this.y);
            ctx.restore();
        }
    }
}

class ConstructionAnimation {
    constructor(building) {
        this.building = building;
        this.duration = 2.0;
        this.elapsed = 0;
    }
    
    update(deltaTime) {
        this.elapsed += deltaTime;
        const progress = Math.min(this.elapsed / this.duration, 1);
        
        // Construction progress animation
        this.building.constructionProgress = progress * 100;
    }
    
    isComplete() {
        return this.elapsed >= this.duration;
    }
}