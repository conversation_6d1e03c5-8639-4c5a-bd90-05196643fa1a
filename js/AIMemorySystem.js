/**
 * Advanced AI Memory System
 * Handles sophisticated memory management, pattern recognition, and learning
 */
export class AIMemorySystem {
    constructor(aiController) {
        this.ai = aiController;
        
        // Memory categories
        this.memories = {
            tactical: new Map(),      // Combat and tactical memories
            strategic: new Map(),     // Long-term strategic memories
            economic: new Map(),      // Resource and economic memories
            diplomatic: new Map(),    // Diplomatic relationship memories
            player: new Map(),        // Player behavior patterns
            environmental: new Map()  // Map and environmental memories
        };
        
        // Memory importance weights
        this.memoryWeights = {
            critical: 1.0,    // Never forgotten
            important: 0.8,   // Slow decay
            normal: 0.5,      // Normal decay
            minor: 0.2        // Fast decay
        };
        
        // Pattern recognition
        this.patterns = {
            playerBehavior: new Map(),
            combatPatterns: new Map(),
            economicPatterns: new Map(),
            buildPatterns: new Map()
        };
        
        // Learning algorithms
        this.learning = {
            reinforcement: new ReinforcementLearning(),
            patternRecognition: new PatternRecognition(),
            behaviorPrediction: new BehaviorPrediction()
        };
        
        // Memory consolidation
        this.consolidation = {
            lastConsolidation: 0,
            consolidationInterval: 300, // 5 minutes
            memoryThreshold: 1000
        };
    }
    
    // Store a new memory
    storeMemory(category, key, data, importance = 'normal') {
        const memory = {
            data: data,
            timestamp: this.ai.game.gameTime,
            importance: importance,
            weight: this.memoryWeights[importance],
            accessCount: 0,
            lastAccessed: this.ai.game.gameTime,
            relevanceScore: this.calculateRelevanceScore(data),
            associations: []
        };
        
        if (!this.memories[category]) {
            this.memories[category] = new Map();
        }
        
        this.memories[category].set(key, memory);
        
        // Create associations with similar memories
        this.createAssociations(category, key, memory);
        
        // Trigger pattern recognition
        this.updatePatterns(category, data);
        
        console.log(`AI stored ${importance} memory: ${category}/${key}`);
    }
    
    // Retrieve a memory
    retrieveMemory(category, key) {
        const categoryMemories = this.memories[category];
        if (!categoryMemories || !categoryMemories.has(key)) {
            return null;
        }
        
        const memory = categoryMemories.get(key);
        memory.accessCount++;
        memory.lastAccessed = this.ai.game.gameTime;
        
        // Strengthen memory through access
        memory.weight = Math.min(1.0, memory.weight + 0.01);
        
        return memory.data;
    }
    
    // Find memories by pattern
    findMemoriesByPattern(category, pattern) {
        const categoryMemories = this.memories[category];
        if (!categoryMemories) return [];
        
        const matches = [];
        
        categoryMemories.forEach((memory, key) => {
            if (this.matchesPattern(memory.data, pattern)) {
                matches.push({
                    key: key,
                    data: memory.data,
                    relevance: memory.relevanceScore,
                    timestamp: memory.timestamp
                });
            }
        });
        
        return matches.sort((a, b) => b.relevance - a.relevance);
    }
    
    // Store player action for pattern analysis
    recordPlayerAction(action) {
        const key = `player_action_${Date.now()}`;
        
        this.storeMemory('player', key, {
            type: action.type,
            location: action.location,
            units: action.units,
            target: action.target,
            context: action.context,
            gameTime: this.ai.game.gameTime,
            gamePhase: this.determineGamePhase()
        }, 'important');
        
        // Update player behavior patterns
        this.updatePlayerBehaviorPatterns(action);
    }
    
    // Record combat outcome for learning
    recordCombatOutcome(combatData) {
        const key = `combat_${Date.now()}`;
        
        this.storeMemory('tactical', key, {
            aiUnits: combatData.aiUnits,
            playerUnits: combatData.playerUnits,
            outcome: combatData.outcome,
            casualties: combatData.casualties,
            tactics: combatData.tactics,
            terrain: combatData.terrain,
            duration: combatData.duration
        }, combatData.outcome === 'victory' ? 'important' : 'critical');
        
        // Learn from combat outcome
        this.learning.reinforcement.updateFromCombat(combatData);
    }
    
    // Record strategic decision and outcome
    recordStrategicDecision(decision, outcome) {
        const key = `strategy_${Date.now()}`;
        
        this.storeMemory('strategic', key, {
            decision: decision,
            context: this.ai.aiLayers.strategic.assessSituation(),
            outcome: outcome,
            effectiveness: outcome.effectiveness,
            gameState: this.captureGameState()
        }, outcome.effectiveness > 0.7 ? 'important' : 'normal');
        
        // Update strategy effectiveness tracking
        this.updateStrategyEffectiveness(decision.type, outcome.effectiveness);
    }
    
    // Analyze player behavior patterns
    analyzePlayerBehavior() {
        const recentActions = this.getRecentPlayerActions(600); // Last 10 minutes
        
        if (recentActions.length < 5) return null;
        
        const analysis = {
            aggressionLevel: this.calculatePlayerAggression(recentActions),
            economicFocus: this.calculateEconomicFocus(recentActions),
            tacticalPatterns: this.identifyTacticalPatterns(recentActions),
            buildPatterns: this.identifyBuildPatterns(recentActions),
            predictedNextAction: this.predictNextPlayerAction(recentActions)
        };
        
        // Store analysis for future reference
        this.storeMemory('player', 'behavior_analysis', analysis, 'important');
        
        return analysis;
    }
    
    // Get strategic insights from memory
    getStrategicInsights() {
        const insights = {
            effectiveStrategies: this.getEffectiveStrategies(),
            playerWeaknesses: this.identifyPlayerWeaknesses(),
            successfulTactics: this.getSuccessfulTactics(),
            economicOpportunities: this.identifyEconomicOpportunities(),
            threatAssessment: this.assessThreats()
        };
        
        return insights;
    }
    
    // Update memory system
    update(deltaTime) {
        // Decay memories over time
        this.decayMemories(deltaTime);
        
        // Consolidate memories periodically
        if (this.shouldConsolidate()) {
            this.consolidateMemories();
        }
        
        // Update learning algorithms
        this.learning.reinforcement.update(deltaTime);
        this.learning.patternRecognition.update(deltaTime);
        this.learning.behaviorPrediction.update(deltaTime);
        
        // Clean up old, irrelevant memories
        this.cleanupMemories();
    }
    
    // Calculate relevance score for a memory
    calculateRelevanceScore(data) {
        let score = 50; // Base score
        
        // Recent memories are more relevant
        const age = this.ai.game.gameTime - (data.timestamp || this.ai.game.gameTime);
        score += Math.max(0, 50 - (age / 60)); // Decay over 1 hour
        
        // Important data types get higher scores
        if (data.type === 'base_attack' || data.type === 'major_loss') {
            score += 30;
        } else if (data.type === 'successful_strategy') {
            score += 20;
        }
        
        // Strategic value
        if (data.strategicValue) {
            score += data.strategicValue * 10;
        }
        
        return Math.min(100, Math.max(0, score));
    }
    
    // Create associations between related memories
    createAssociations(category, key, memory) {
        const categoryMemories = this.memories[category];
        if (!categoryMemories) return;
        
        categoryMemories.forEach((otherMemory, otherKey) => {
            if (otherKey === key) return;
            
            const similarity = this.calculateMemorySimilarity(memory.data, otherMemory.data);
            if (similarity > 0.6) {
                memory.associations.push({
                    key: otherKey,
                    similarity: similarity,
                    category: category
                });
                
                otherMemory.associations.push({
                    key: key,
                    similarity: similarity,
                    category: category
                });
            }
        });
    }
    
    // Calculate similarity between two memories
    calculateMemorySimilarity(data1, data2) {
        let similarity = 0;
        let factors = 0;
        
        // Type similarity
        if (data1.type === data2.type) {
            similarity += 0.4;
        }
        factors++;
        
        // Location similarity
        if (data1.location && data2.location) {
            const distance = Math.sqrt(
                (data1.location.x - data2.location.x) ** 2 +
                (data1.location.y - data2.location.y) ** 2
            );
            similarity += Math.max(0, 0.3 - (distance / 1000));
            factors++;
        }
        
        // Context similarity
        if (data1.context && data2.context) {
            const contextSimilarity = this.calculateContextSimilarity(data1.context, data2.context);
            similarity += contextSimilarity * 0.3;
            factors++;
        }
        
        return factors > 0 ? similarity / factors : 0;
    }
    
    // Update player behavior patterns
    updatePlayerBehaviorPatterns(action) {
        const patternKey = `${action.type}_${this.determineGamePhase()}`;
        
        if (!this.patterns.playerBehavior.has(patternKey)) {
            this.patterns.playerBehavior.set(patternKey, {
                count: 0,
                frequency: 0,
                contexts: [],
                outcomes: []
            });
        }
        
        const pattern = this.patterns.playerBehavior.get(patternKey);
        pattern.count++;
        pattern.frequency = pattern.count / (this.ai.game.gameTime / 60); // Per minute
        pattern.contexts.push(action.context);
        
        // Keep only recent contexts
        if (pattern.contexts.length > 20) {
            pattern.contexts = pattern.contexts.slice(-20);
        }
    }
    
    // Get recent player actions
    getRecentPlayerActions(timeWindow) {
        const cutoffTime = this.ai.game.gameTime - timeWindow;
        const actions = [];
        
        const playerMemories = this.memories.player;
        if (!playerMemories) return actions;
        
        playerMemories.forEach((memory, key) => {
            if (memory.timestamp >= cutoffTime && memory.data.type) {
                actions.push(memory.data);
            }
        });
        
        return actions.sort((a, b) => a.gameTime - b.gameTime);
    }
    
    // Calculate player aggression level
    calculatePlayerAggression(actions) {
        const aggressiveActions = ['attack', 'raid', 'assault', 'destroy'];
        const aggressiveCount = actions.filter(action => 
            aggressiveActions.includes(action.type)
        ).length;
        
        return Math.min(1.0, aggressiveCount / Math.max(1, actions.length));
    }
    
    // Calculate economic focus
    calculateEconomicFocus(actions) {
        const economicActions = ['build_harvester', 'build_refinery', 'expand', 'collect_spice'];
        const economicCount = actions.filter(action => 
            economicActions.includes(action.type)
        ).length;
        
        return Math.min(1.0, economicCount / Math.max(1, actions.length));
    }
    
    // Identify tactical patterns
    identifyTacticalPatterns(actions) {
        const patterns = [];
        
        // Look for common sequences
        for (let i = 0; i < actions.length - 2; i++) {
            const sequence = [actions[i].type, actions[i + 1].type, actions[i + 2].type];
            patterns.push(sequence.join(' -> '));
        }
        
        // Count pattern frequencies
        const patternCounts = {};
        patterns.forEach(pattern => {
            patternCounts[pattern] = (patternCounts[pattern] || 0) + 1;
        });
        
        // Return most common patterns
        return Object.entries(patternCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([pattern, count]) => ({ pattern, count }));
    }
    
    // Predict next player action
    predictNextPlayerAction(actions) {
        if (actions.length < 3) return null;
        
        const lastAction = actions[actions.length - 1];
        const patterns = this.patterns.playerBehavior;
        
        // Find similar historical contexts
        const similarContexts = [];
        patterns.forEach((pattern, key) => {
            pattern.contexts.forEach(context => {
                const similarity = this.calculateContextSimilarity(lastAction.context, context);
                if (similarity > 0.7) {
                    similarContexts.push({ pattern: key, similarity });
                }
            });
        });
        
        if (similarContexts.length === 0) return null;
        
        // Return most likely next action based on patterns
        const mostLikely = similarContexts
            .sort((a, b) => b.similarity - a.similarity)[0];
        
        return {
            predictedAction: mostLikely.pattern.split('_')[0],
            confidence: mostLikely.similarity,
            reasoning: `Based on ${similarContexts.length} similar contexts`
        };
    }
    
    // Decay memories over time
    decayMemories(deltaTime) {
        Object.values(this.memories).forEach(categoryMemories => {
            categoryMemories.forEach((memory, key) => {
                // Calculate decay rate based on importance and access
                let decayRate = 0.001; // Base decay per second
                
                // Important memories decay slower
                decayRate *= (2 - memory.weight);
                
                // Frequently accessed memories decay slower
                const accessFactor = Math.min(2, memory.accessCount / 10);
                decayRate *= (2 - accessFactor);
                
                // Apply decay
                memory.weight = Math.max(0, memory.weight - decayRate * deltaTime);
                memory.relevanceScore = Math.max(0, memory.relevanceScore - decayRate * deltaTime * 10);
                
                // Remove memories that have decayed too much
                if (memory.weight < 0.1 && memory.importance !== 'critical') {
                    categoryMemories.delete(key);
                }
            });
        });
    }
    
    // Consolidate memories
    consolidateMemories() {
        console.log('AI consolidating memories...');
        
        // Merge similar memories
        this.mergeSimilarMemories();
        
        // Strengthen important patterns
        this.strengthenImportantPatterns();
        
        // Update learning models
        this.updateLearningModels();
        
        this.consolidation.lastConsolidation = this.ai.game.gameTime;
    }
    
    // Check if consolidation should occur
    shouldConsolidate() {
        const timeSinceLastConsolidation = this.ai.game.gameTime - this.consolidation.lastConsolidation;
        const totalMemories = Object.values(this.memories).reduce(
            (total, categoryMemories) => total + categoryMemories.size, 0
        );
        
        return timeSinceLastConsolidation >= this.consolidation.consolidationInterval ||
               totalMemories >= this.consolidation.memoryThreshold;
    }
    
    // Merge similar memories to reduce redundancy
    mergeSimilarMemories() {
        Object.entries(this.memories).forEach(([category, categoryMemories]) => {
            const memoriesToMerge = [];
            
            categoryMemories.forEach((memory1, key1) => {
                categoryMemories.forEach((memory2, key2) => {
                    if (key1 !== key2) {
                        const similarity = this.calculateMemorySimilarity(memory1.data, memory2.data);
                        if (similarity > 0.8) {
                            memoriesToMerge.push([key1, key2, similarity]);
                        }
                    }
                });
            });
            
            // Merge highly similar memories
            memoriesToMerge.forEach(([key1, key2, similarity]) => {
                if (categoryMemories.has(key1) && categoryMemories.has(key2)) {
                    const memory1 = categoryMemories.get(key1);
                    const memory2 = categoryMemories.get(key2);
                    
                    // Keep the more important memory, merge data
                    const primaryMemory = memory1.weight > memory2.weight ? memory1 : memory2;
                    const secondaryMemory = memory1.weight > memory2.weight ? memory2 : memory1;
                    const primaryKey = memory1.weight > memory2.weight ? key1 : key2;
                    const secondaryKey = memory1.weight > memory2.weight ? key2 : key1;
                    
                    // Merge data and strengthen primary memory
                    primaryMemory.weight = Math.min(1.0, primaryMemory.weight + secondaryMemory.weight * 0.3);
                    primaryMemory.accessCount += secondaryMemory.accessCount;
                    primaryMemory.associations.push(...secondaryMemory.associations);
                    
                    // Remove secondary memory
                    categoryMemories.delete(secondaryKey);
                }
            });
        });
    }
    
    // Strengthen important patterns
    strengthenImportantPatterns() {
        // Identify and strengthen patterns that led to success
        const successfulOutcomes = this.findMemoriesByPattern('strategic', { effectiveness: { min: 0.7 } });
        
        successfulOutcomes.forEach(outcome => {
            const relatedMemories = this.findRelatedMemories(outcome);
            relatedMemories.forEach(memory => {
                memory.weight = Math.min(1.0, memory.weight + 0.1);
                memory.importance = 'important';
            });
        });
    }
    
    // Update learning models with consolidated data
    updateLearningModels() {
        // Update reinforcement learning with strategic outcomes
        const strategicMemories = Array.from(this.memories.strategic.values());
        this.learning.reinforcement.updateFromMemories(strategicMemories);
        
        // Update pattern recognition with player behavior
        const playerMemories = Array.from(this.memories.player.values());
        this.learning.patternRecognition.updateFromMemories(playerMemories);
        
        // Update behavior prediction with recent patterns
        this.learning.behaviorPrediction.updateFromPatterns(this.patterns);
    }
    
    // Clean up old, irrelevant memories
    cleanupMemories() {
        const maxAge = 3600; // 1 hour
        const currentTime = this.ai.game.gameTime;
        
        Object.values(this.memories).forEach(categoryMemories => {
            categoryMemories.forEach((memory, key) => {
                const age = currentTime - memory.timestamp;
                
                // Remove very old memories with low relevance
                if (age > maxAge && memory.relevanceScore < 20 && memory.importance !== 'critical') {
                    categoryMemories.delete(key);
                }
            });
        });
    }
    
    // Utility methods
    determineGamePhase() {
        const gameTime = this.ai.game.gameTime;
        if (gameTime < 300) return 'early';
        if (gameTime < 900) return 'mid';
        return 'late';
    }
    
    captureGameState() {
        return {
            gameTime: this.ai.game.gameTime,
            aiUnits: this.ai.team.units.length,
            aiBuildings: this.ai.team.buildings.length,
            aiSpice: this.ai.team.spice,
            playerUnits: this.ai.game.teams.player.units.length,
            playerBuildings: this.ai.game.teams.player.buildings.length,
            playerSpice: this.ai.game.resourceManager.getResource('spice')
        };
    }
    
    calculateContextSimilarity(context1, context2) {
        if (!context1 || !context2) return 0;
        
        let similarity = 0;
        let factors = 0;
        
        // Compare numerical values
        ['gameTime', 'unitCount', 'buildingCount', 'spice'].forEach(key => {
            if (context1[key] !== undefined && context2[key] !== undefined) {
                const diff = Math.abs(context1[key] - context2[key]);
                const max = Math.max(context1[key], context2[key]);
                similarity += Math.max(0, 1 - (diff / Math.max(1, max)));
                factors++;
            }
        });
        
        return factors > 0 ? similarity / factors : 0;
    }
    
    matchesPattern(data, pattern) {
        return Object.entries(pattern).every(([key, value]) => {
            if (typeof value === 'object' && value.min !== undefined) {
                return data[key] >= value.min && (value.max === undefined || data[key] <= value.max);
            }
            return data[key] === value;
        });
    }
    
    findRelatedMemories(targetMemory) {
        const related = [];
        
        Object.values(this.memories).forEach(categoryMemories => {
            categoryMemories.forEach(memory => {
                if (memory !== targetMemory) {
                    const similarity = this.calculateMemorySimilarity(memory.data, targetMemory.data);
                    if (similarity > 0.5) {
                        related.push(memory);
                    }
                }
            });
        });
        
        return related;
    }
    
    getEffectiveStrategies() {
        return this.findMemoriesByPattern('strategic', { effectiveness: { min: 0.7 } })
            .map(memory => memory.data.decision.type);
    }
    
    identifyPlayerWeaknesses() {
        const analysis = this.retrieveMemory('player', 'behavior_analysis');
        return analysis ? analysis.weaknesses || [] : [];
    }
    
    getSuccessfulTactics() {
        return this.findMemoriesByPattern('tactical', { outcome: 'victory' })
            .map(memory => memory.data.tactics);
    }
    
    identifyEconomicOpportunities() {
        return this.findMemoriesByPattern('economic', { success: true })
            .map(memory => memory.data.opportunity);
    }
    
    assessThreats() {
        const recentThreats = this.findMemoriesByPattern('tactical', { 
            timestamp: { min: this.ai.game.gameTime - 300 } 
        });
        
        return recentThreats.map(threat => ({
            type: threat.data.type,
            severity: threat.data.severity,
            location: threat.data.location
        }));
    }
    
    updateStrategyEffectiveness(strategyType, effectiveness) {
        if (!this.ai.strategyAdaptation.strategyEffectiveness.has(strategyType)) {
            this.ai.strategyAdaptation.strategyEffectiveness.set(strategyType, []);
        }
        
        const history = this.ai.strategyAdaptation.strategyEffectiveness.get(strategyType);
        history.push(effectiveness);
        
        // Keep only recent history
        if (history.length > 10) {
            history.shift();
        }
    }
    
    updatePatterns(category, data) {
        // Update pattern recognition based on new data
        this.learning.patternRecognition.addDataPoint(category, data);
    }
}

// Supporting learning classes
class ReinforcementLearning {
    constructor() {
        this.qTable = new Map();
        this.learningRate = 0.1;
        this.discountFactor = 0.9;
        this.explorationRate = 0.2;
    }
    
    updateFromCombat(combatData) {
        const state = this.encodeState(combatData);
        const action = combatData.tactics;
        const reward = combatData.outcome === 'victory' ? 1 : -1;
        
        this.updateQValue(state, action, reward);
    }
    
    updateFromMemories(memories) {
        memories.forEach(memory => {
            if (memory.data.decision && memory.data.outcome) {
                const state = this.encodeState(memory.data.context);
                const action = memory.data.decision.type;
                const reward = memory.data.outcome.effectiveness;
                
                this.updateQValue(state, action, reward);
            }
        });
    }
    
    updateQValue(state, action, reward) {
        const key = `${state}_${action}`;
        const currentQ = this.qTable.get(key) || 0;
        const newQ = currentQ + this.learningRate * (reward - currentQ);
        this.qTable.set(key, newQ);
    }
    
    encodeState(context) {
        // Simplified state encoding
        return `${Math.floor(context.gameTime / 300)}_${context.aiUnits}_${context.playerUnits}`;
    }
    
    update(deltaTime) {
        // Decay exploration rate over time
        this.explorationRate = Math.max(0.05, this.explorationRate - 0.0001 * deltaTime);
    }
}

class PatternRecognition {
    constructor() {
        this.patterns = new Map();
        this.sequenceLength = 3;
    }
    
    addDataPoint(category, data) {
        if (!this.patterns.has(category)) {
            this.patterns.set(category, []);
        }
        
        const categoryData = this.patterns.get(category);
        categoryData.push(data);
        
        // Keep only recent data
        if (categoryData.length > 100) {
            categoryData.shift();
        }
        
        // Analyze patterns when we have enough data
        if (categoryData.length >= this.sequenceLength) {
            this.analyzeSequences(category);
        }
    }
    
    analyzeSequences(category) {
        const data = this.patterns.get(category);
        const sequences = new Map();
        
        for (let i = 0; i <= data.length - this.sequenceLength; i++) {
            const sequence = data.slice(i, i + this.sequenceLength);
            const key = this.encodeSequence(sequence);
            
            sequences.set(key, (sequences.get(key) || 0) + 1);
        }
        
        // Store frequent patterns
        this.patterns.set(`${category}_sequences`, sequences);
    }
    
    encodeSequence(sequence) {
        return sequence.map(item => item.type || 'unknown').join('-');
    }
    
    updateFromMemories(memories) {
        memories.forEach(memory => {
            this.addDataPoint('player_behavior', memory.data);
        });
    }
    
    update(deltaTime) {
        // Periodic pattern analysis
    }
}

class BehaviorPrediction {
    constructor() {
        this.predictions = new Map();
        this.accuracy = 0.5;
    }
    
    updateFromPatterns(patterns) {
        // Update prediction models based on recognized patterns
        Object.entries(patterns).forEach(([category, categoryPatterns]) => {
            this.updateCategoryPredictions(category, categoryPatterns);
        });
    }
    
    updateCategoryPredictions(category, patterns) {
        patterns.forEach((pattern, key) => {
            if (pattern.frequency > 0.1) { // Frequent patterns
                this.predictions.set(key, {
                    probability: pattern.frequency,
                    confidence: Math.min(1.0, pattern.count / 10),
                    lastUpdated: Date.now()
                });
            }
        });
    }
    
    predictNextAction(context) {
        // Find matching predictions
        const matches = [];
        
        this.predictions.forEach((prediction, key) => {
            if (this.contextMatches(key, context)) {
                matches.push({
                    action: key,
                    probability: prediction.probability,
                    confidence: prediction.confidence
                });
            }
        });
        
        return matches.sort((a, b) => b.probability - a.probability)[0] || null;
    }
    
    contextMatches(predictionKey, context) {
        // Simplified context matching
        return predictionKey.includes(context.gamePhase) || 
               predictionKey.includes(context.situation);
    }
    
    update(deltaTime) {
        // Update prediction accuracy and decay old predictions
        this.predictions.forEach((prediction, key) => {
            const age = Date.now() - prediction.lastUpdated;
            if (age > 600000) { // 10 minutes
                prediction.confidence *= 0.9;
            }
        });
    }
}