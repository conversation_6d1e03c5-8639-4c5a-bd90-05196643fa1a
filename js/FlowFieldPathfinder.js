export class FlowFieldPathfinder {
    constructor(game) {
        this.game = game;
        this.gridSize = 25;
        
        // Access world dimensions from gameState
        const worldWidth = game.gameState ? game.gameState.world.width : 3000;
        const worldHeight = game.gameState ? game.gameState.world.height : 3000;
        
        this.width = Math.ceil(worldWidth / this.gridSize);
        this.height = Math.ceil(worldHeight / this.gridSize);
        
        // Flow field data structures
        this.costField = []; // Cost to move through each cell
        this.integrationField = []; // Distance from goal
        this.flowField = []; // Direction vectors for movement
        this.goals = []; // Multiple goal positions
        
        // Performance optimization
        this.flowFieldCache = new Map();
        this.lastUpdate = 0;
        this.updateInterval = 100; // Update every 100ms
        this.maxCacheSize = 20;
        
        // Flow field parameters
        this.maxIntegrationValue = 65535; // Max distance value
        this.smoothingPasses = 2; // Number of smoothing iterations
        
        this.initFields();
    }
    
    initFields() {
        // Initialize cost field
        this.costField = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(1));
        
        // Initialize integration field
        this.integrationField = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(this.maxIntegrationValue));
        
        // Initialize flow field
        this.flowField = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(null).map(() => ({ x: 0, y: 0 })));
    }
    
    updateCostField() {
        const now = performance.now();
        if (now - this.lastUpdate < this.updateInterval) return;
        this.lastUpdate = now;
        
        // Reset cost field
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                this.costField[y][x] = 1;
            }
        }
        
        // Add building costs
        this.game.buildings.forEach(building => {
            if (building.constructionProgress < 100 && building.type !== 'wall') return;
            this.addBuildingToCostField(building);
        });
        
        // Add terrain costs
        this.game.terrain.forEach(obstacle => {
            this.addTerrainToCostField(obstacle);
        });
        
        // Add dynamic obstacles (other units)
        this.addUnitsToCostField();
        
        // Clear cache when cost field changes
        this.flowFieldCache.clear();
    }
    
    addBuildingToCostField(building) {
        const margin = building.type === 'wall' ? 0 : this.gridSize / 3;
        const startX = Math.floor((building.x - building.width/2 - margin) / this.gridSize);
        const startY = Math.floor((building.y - building.height/2 - margin) / this.gridSize);
        const endX = Math.ceil((building.x + building.width/2 + margin) / this.gridSize);
        const endY = Math.ceil((building.y + building.height/2 + margin) / this.gridSize);
        
        for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
            for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                this.costField[y][x] = 255; // Impassable
            }
        }
    }
    
    addTerrainToCostField(obstacle) {
        const startX = Math.floor((obstacle.x - obstacle.radius) / this.gridSize);
        const startY = Math.floor((obstacle.y - obstacle.radius) / this.gridSize);
        const endX = Math.ceil((obstacle.x + obstacle.radius) / this.gridSize);
        const endY = Math.ceil((obstacle.y + obstacle.radius) / this.gridSize);
        
        for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
            for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                const cellCenterX = x * this.gridSize + this.gridSize / 2;
                const cellCenterY = y * this.gridSize + this.gridSize / 2;
                const distance = Math.hypot(cellCenterX - obstacle.x, cellCenterY - obstacle.y);
                
                if (distance < obstacle.radius) {
                    let cost = 1;
                    switch (obstacle.type) {
                        case 'rock':
                        case 'cliff':
                            cost = 255; // Impassable
                            break;
                        case 'water':
                            cost = 10; // High cost
                            break;
                        case 'spice':
                            cost = 1; // Normal cost
                            break;
                        default:
                            cost = 5; // Moderate cost
                    }
                    this.costField[y][x] = Math.max(this.costField[y][x], cost);
                }
            }
        }
    }
    
    addUnitsToCostField() {
        // Add other units as dynamic obstacles
        this.game.units.forEach(unit => {
            if (unit.health <= 0) return;
            
            const gridX = Math.floor(unit.x / this.gridSize);
            const gridY = Math.floor(unit.y / this.gridSize);
            
            if (this.isValidPosition(gridX, gridY)) {
                // Add cost based on unit size and type
                const unitCost = this.getUnitCost(unit);
                this.costField[gridY][gridX] = Math.min(255, this.costField[gridY][gridX] + unitCost);
                
                // Add cost to adjacent cells for larger units
                if (unit.size > 15) {
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const nx = gridX + dx;
                            const ny = gridY + dy;
                            if (this.isValidPosition(nx, ny) && (dx !== 0 || dy !== 0)) {
                                this.costField[ny][nx] = Math.min(255, this.costField[ny][nx] + unitCost / 2);
                            }
                        }
                    }
                }
            }
        });
    }
    
    getUnitCost(unit) {
        // Different unit types have different costs
        if (unit.canFly) return 1; // Flying units don't block ground units much
        if (unit.size > 20) return 8; // Large units
        if (unit.size > 15) return 5; // Medium units
        return 3; // Small units
    }
    
    generateFlowField(goalX, goalY, teamFilter = null) {
        // Check cache first
        const cacheKey = `${Math.floor(goalX/this.gridSize)},${Math.floor(goalY/this.gridSize)}`;
        if (this.flowFieldCache.has(cacheKey)) {
            const cached = this.flowFieldCache.get(cacheKey);
            if (performance.now() - cached.timestamp < 2000) { // 2 second cache
                return cached.flowField;
            }
        }
        
        // Update cost field
        this.updateCostField();
        
        // Convert goal to grid coordinates
        const goalGridX = Math.floor(goalX / this.gridSize);
        const goalGridY = Math.floor(goalY / this.gridSize);
        
        if (!this.isValidPosition(goalGridX, goalGridY)) {
            return null;
        }
        
        // Generate integration field using Dijkstra's algorithm
        this.generateIntegrationField(goalGridX, goalGridY);
        
        // Generate flow field from integration field
        this.generateFlowVectors();
        
        // Smooth the flow field
        for (let i = 0; i < this.smoothingPasses; i++) {
            this.smoothFlowField();
        }
        
        // Cache the result
        this.cacheFlowField(cacheKey, this.flowField);
        
        return this.flowField;
    }
    
    generateIntegrationField(goalX, goalY) {
        // Reset integration field
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                this.integrationField[y][x] = this.maxIntegrationValue;
            }
        }
        
        // Priority queue for Dijkstra's algorithm
        const openSet = [];
        const visited = new Set();
        
        // Start from goal
        this.integrationField[goalY][goalX] = 0;
        openSet.push({ x: goalX, y: goalY, cost: 0 });
        
        while (openSet.length > 0) {
            // Sort by cost (simple implementation, could use binary heap)
            openSet.sort((a, b) => a.cost - b.cost);
            const current = openSet.shift();
            
            const key = `${current.x},${current.y}`;
            if (visited.has(key)) continue;
            visited.add(key);
            
            // Check all neighbors
            const neighbors = this.getNeighbors(current.x, current.y);
            for (const neighbor of neighbors) {
                const { x, y, cost } = neighbor;
                const newCost = current.cost + cost;
                
                if (newCost < this.integrationField[y][x]) {
                    this.integrationField[y][x] = newCost;
                    openSet.push({ x, y, cost: newCost });
                }
            }
        }
    }
    
    getNeighbors(x, y) {
        const neighbors = [];
        const directions = [
            { dx: 0, dy: -1, cost: 1 },    // North
            { dx: 1, dy: 0, cost: 1 },     // East
            { dx: 0, dy: 1, cost: 1 },     // South
            { dx: -1, dy: 0, cost: 1 },    // West
            { dx: 1, dy: -1, cost: 1.414 }, // Northeast
            { dx: 1, dy: 1, cost: 1.414 },  // Southeast
            { dx: -1, dy: 1, cost: 1.414 }, // Southwest
            { dx: -1, dy: -1, cost: 1.414 } // Northwest
        ];
        
        for (const dir of directions) {
            const nx = x + dir.dx;
            const ny = y + dir.dy;
            
            if (this.isValidPosition(nx, ny) && this.costField[ny][nx] < 255) {
                const cost = dir.cost * this.costField[ny][nx];
                neighbors.push({ x: nx, y: ny, cost });
            }
        }
        
        return neighbors;
    }
    
    generateFlowVectors() {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.costField[y][x] >= 255) {
                    // Impassable cell
                    this.flowField[y][x] = { x: 0, y: 0 };
                    continue;
                }
                
                // Find the neighbor with the lowest integration value
                let bestDirection = { x: 0, y: 0 };
                let lowestCost = this.integrationField[y][x];
                
                const directions = [
                    { dx: 0, dy: -1 },  // North
                    { dx: 1, dy: 0 },   // East
                    { dx: 0, dy: 1 },   // South
                    { dx: -1, dy: 0 },  // West
                    { dx: 1, dy: -1 },  // Northeast
                    { dx: 1, dy: 1 },   // Southeast
                    { dx: -1, dy: 1 },  // Southwest
                    { dx: -1, dy: -1 }  // Northwest
                ];
                
                for (const dir of directions) {
                    const nx = x + dir.dx;
                    const ny = y + dir.dy;
                    
                    if (this.isValidPosition(nx, ny) && this.costField[ny][nx] < 255) {
                        if (this.integrationField[ny][nx] < lowestCost) {
                            lowestCost = this.integrationField[ny][nx];
                            bestDirection = { x: dir.dx, y: dir.dy };
                        }
                    }
                }
                
                // Normalize direction vector
                const length = Math.hypot(bestDirection.x, bestDirection.y);
                if (length > 0) {
                    this.flowField[y][x] = {
                        x: bestDirection.x / length,
                        y: bestDirection.y / length
                    };
                } else {
                    this.flowField[y][x] = { x: 0, y: 0 };
                }
            }
        }
    }
    
    smoothFlowField() {
        const smoothed = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(null).map(() => ({ x: 0, y: 0 })));
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.costField[y][x] >= 255) {
                    smoothed[y][x] = { x: 0, y: 0 };
                    continue;
                }
                
                let sumX = 0, sumY = 0, count = 0;
                
                // Average with neighbors
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        const nx = x + dx;
                        const ny = y + dy;
                        
                        if (this.isValidPosition(nx, ny) && this.costField[ny][nx] < 255) {
                            sumX += this.flowField[ny][nx].x;
                            sumY += this.flowField[ny][nx].y;
                            count++;
                        }
                    }
                }
                
                if (count > 0) {
                    const avgX = sumX / count;
                    const avgY = sumY / count;
                    const length = Math.hypot(avgX, avgY);
                    
                    if (length > 0) {
                        smoothed[y][x] = { x: avgX / length, y: avgY / length };
                    } else {
                        smoothed[y][x] = { x: 0, y: 0 };
                    }
                } else {
                    smoothed[y][x] = { x: 0, y: 0 };
                }
            }
        }
        
        this.flowField = smoothed;
    }
    
    cacheFlowField(key, flowField) {
        // Deep copy the flow field for caching
        const cached = {
            flowField: flowField.map(row => row.map(cell => ({ ...cell }))),
            timestamp: performance.now()
        };
        
        this.flowFieldCache.set(key, cached);
        
        // Limit cache size
        if (this.flowFieldCache.size > this.maxCacheSize) {
            const oldestKey = this.flowFieldCache.keys().next().value;
            this.flowFieldCache.delete(oldestKey);
        }
    }
    
    getFlowDirection(x, y, flowField = null) {
        const field = flowField || this.flowField;
        const gridX = Math.floor(x / this.gridSize);
        const gridY = Math.floor(y / this.gridSize);
        
        if (!this.isValidPosition(gridX, gridY)) {
            return { x: 0, y: 0 };
        }
        
        return field[gridY][gridX];
    }
    
    // Multi-goal flow field for complex scenarios
    generateMultiGoalFlowField(goals, weights = null) {
        if (!goals || goals.length === 0) return null;
        
        // Generate individual flow fields for each goal
        const individualFields = [];
        for (let i = 0; i < goals.length; i++) {
            const goal = goals[i];
            const field = this.generateFlowField(goal.x, goal.y);
            if (field) {
                individualFields.push({
                    field,
                    weight: weights ? weights[i] : 1.0
                });
            }
        }
        
        if (individualFields.length === 0) return null;
        
        // Combine flow fields with weights
        const combinedField = Array(this.height).fill(null).map(() => 
            Array(this.width).fill(null).map(() => ({ x: 0, y: 0 })));
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                let totalX = 0, totalY = 0, totalWeight = 0;
                
                for (const { field, weight } of individualFields) {
                    const vector = field[y][x];
                    totalX += vector.x * weight;
                    totalY += vector.y * weight;
                    totalWeight += weight;
                }
                
                if (totalWeight > 0) {
                    const length = Math.hypot(totalX, totalY);
                    if (length > 0) {
                        combinedField[y][x] = {
                            x: totalX / length,
                            y: totalY / length
                        };
                    }
                }
            }
        }
        
        return combinedField;
    }
    
    // Group movement using flow fields
    moveUnitsWithFlowField(units, goalX, goalY) {
        const flowField = this.generateFlowField(goalX, goalY);
        if (!flowField) return false;
        
        units.forEach(unit => {
            const direction = this.getFlowDirection(unit.x, unit.y, flowField);
            
            if (direction.x !== 0 || direction.y !== 0) {
                // Set target based on flow direction
                const stepSize = 50; // Distance to move in flow direction
                unit.targetX = unit.x + direction.x * stepSize;
                unit.targetY = unit.y + direction.y * stepSize;
                
                // Ensure target is within bounds
                unit.targetX = Math.max(0, Math.min(this.game.gameState.world.width, unit.targetX));
                unit.targetY = Math.max(0, Math.min(this.game.gameState.world.height, unit.targetY));
                
                unit.state = 'moving';
                
                // Create a simple path for the unit movement system
                unit.path = [{ x: unit.targetX, y: unit.targetY }];
                unit.pathIndex = 0;
            }
        });
        
        return true;
    }
    
    // Utility methods
    isValidPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height;
    }
    
    // Visualization helpers for debugging
    visualizeFlowField(ctx, flowField = null) {
        const field = flowField || this.flowField;
        if (!field) return;
        
        ctx.save();
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 1;
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const vector = field[y][x];
                if (vector.x === 0 && vector.y === 0) continue;
                
                const centerX = x * this.gridSize + this.gridSize / 2;
                const centerY = y * this.gridSize + this.gridSize / 2;
                const endX = centerX + vector.x * this.gridSize * 0.4;
                const endY = centerY + vector.y * this.gridSize * 0.4;
                
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
                
                // Draw arrowhead
                const angle = Math.atan2(vector.y, vector.x);
                const arrowSize = 3;
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(
                    endX - arrowSize * Math.cos(angle - Math.PI / 6),
                    endY - arrowSize * Math.sin(angle - Math.PI / 6)
                );
                ctx.moveTo(endX, endY);
                ctx.lineTo(
                    endX - arrowSize * Math.cos(angle + Math.PI / 6),
                    endY - arrowSize * Math.sin(angle + Math.PI / 6)
                );
                ctx.stroke();
            }
        }
        
        ctx.restore();
    }
    
    visualizeIntegrationField(ctx) {
        if (!this.integrationField) return;
        
        ctx.save();
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const value = this.integrationField[y][x];
                if (value >= this.maxIntegrationValue) continue;
                
                // Color based on integration value
                const normalized = Math.min(value / 100, 1.0);
                const red = Math.floor(255 * normalized);
                const blue = Math.floor(255 * (1 - normalized));
                
                ctx.fillStyle = `rgba(${red}, 0, ${blue}, 0.3)`;
                ctx.fillRect(
                    x * this.gridSize,
                    y * this.gridSize,
                    this.gridSize,
                    this.gridSize
                );
            }
        }
        
        ctx.restore();
    }
    
    // Performance monitoring
    getPerformanceStats() {
        return {
            cacheSize: this.flowFieldCache.size,
            gridSize: `${this.width}x${this.height}`,
            lastUpdate: this.lastUpdate,
            updateInterval: this.updateInterval
        };
    }
    
    // Cleanup
    cleanup() {
        // Clear old cache entries
        const now = performance.now();
        for (const [key, cached] of this.flowFieldCache.entries()) {
            if (now - cached.timestamp > 10000) { // 10 seconds
                this.flowFieldCache.delete(key);
            }
        }
    }
}