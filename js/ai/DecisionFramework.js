import { EmergencyProtocols } from './decision/EmergencyProtocols.js';
import { GoalOrientedBehavior } from './decision/GoalOrientedBehavior.js';
import { UtilitySystem } from './decision/UtilitySystem.js';
import { distance } from '../utils/index.js';

/**
 * Decision Framework
 * Manages high-level AI decision making, utility calculations, and emergency protocols
 */
export class DecisionFramework {
    constructor(engine) {
        this.engine = engine;
        
        // Decision making components
        this.utilitySystem = new UtilitySystem(engine);
        this.goalOrientedBehavior = new GoalOrientedBehavior(engine);
        this.emergencyProtocols = new EmergencyProtocols(engine);
        
        // Decision state
        this.currentDecision = null;
        this.decisionHistory = [];
        this.decisionCooldowns = new Map();
        
        // Decision weights and parameters
        this.decisionWeights = {
            survival: 1.0,
            economic: 0.7,
            military: 0.8,
            strategic: 0.6,
            opportunistic: 0.5
        };
        
        this.lastDecisionTime = 0;
        this.decisionInterval = 5; // Make decisions every 5 seconds
    }
    
    initialize() {
        this.utilitySystem.initialize();
        this.goalOrientedBehavior.initialize();
        this.emergencyProtocols.initialize();
        
        console.log('Decision Framework initialized');
    }
    
    update(deltaTime) {
        // Update decision components
        this.utilitySystem.update(deltaTime);
        this.goalOrientedBehavior.update(deltaTime);
        this.emergencyProtocols.update(deltaTime);
        
        // Make periodic decisions
        if (this.shouldMakeDecision()) {
            this.makeDecision();
        }
        
        // Update decision cooldowns
        this.updateCooldowns(deltaTime);
        
        // Clean up old decision history
        this.cleanupDecisionHistory();
    }
    
    shouldMakeDecision() {
        const timeSinceLastDecision = this.engine.ai.game.gameTime - this.lastDecisionTime;
        return timeSinceLastDecision >= this.decisionInterval;
    }
    
    makeDecision() {
        // Check for emergencies first
        const emergencies = this.emergencyProtocols.checkEmergencies();
        if (emergencies.length > 0) {
            this.handleEmergencyDecision(emergencies[0]);
            return;
        }
        
        // Evaluate current goals
        const currentGoals = this.goalOrientedBehavior.getCurrentGoals();
        
        // Generate decision options
        const options = this.generateDecisionOptions(currentGoals);
        
        // Evaluate options using utility system
        const evaluatedOptions = options.map(option => ({
            ...option,
            utility: this.utilitySystem.calculateUtility(option)
        }));
        
        // Select best option
        const bestOption = this.selectBestOption(evaluatedOptions);
        
        if (bestOption) {
            this.executeDecision(bestOption);
        }
        
        this.lastDecisionTime = this.engine.ai.game.gameTime;
    }
    
    generateDecisionOptions(goals) {
        const options = [];
        
        // Generate options based on current goals
        goals.forEach(goal => {
            options.push(...this.generateOptionsForGoal(goal));
        });
        
        // Add opportunistic options
        options.push(...this.generateOpportunisticOptions());
        
        // Add defensive options if under threat
        if (this.isUnderThreat()) {
            options.push(...this.generateDefensiveOptions());
        }
        
        // Filter out options on cooldown
        return options.filter(option => !this.isOnCooldown(option.type));
    }
    
    generateOptionsForGoal(goal) {
        const options = [];
        
        switch (goal.type) {
            case 'expand_economy':
                options.push(
                    { type: 'build_harvester', priority: 70, category: 'economic' },
                    { type: 'expand_to_spice', priority: 60, category: 'economic' },
                    { type: 'build_refinery', priority: 50, category: 'economic' }
                );
                break;
                
            case 'build_military':
                options.push(
                    { type: 'train_soldiers', priority: 80, category: 'military' },
                    { type: 'build_barracks', priority: 70, category: 'military' },
                    { type: 'train_tanks', priority: 60, category: 'military' }
                );
                break;
                
            case 'defend_base':
                options.push(
                    { type: 'build_turrets', priority: 90, category: 'military' },
                    { type: 'position_defenders', priority: 85, category: 'military' },
                    { type: 'reinforce_defenses', priority: 75, category: 'military' }
                );
                break;
                
            case 'attack_player':
                options.push(
                    { type: 'launch_assault', priority: 85, category: 'military' },
                    { type: 'harass_economy', priority: 70, category: 'military' },
                    { type: 'scout_targets', priority: 60, category: 'strategic' }
                );
                break;
        }
        
        return options;
    }
    
    generateOpportunisticOptions() {
        const options = [];
        
        // Check for vulnerable player units
        const vulnerableUnits = this.findVulnerablePlayerUnits();
        if (vulnerableUnits.length > 0) {
            options.push({
                type: 'attack_vulnerable_units',
                priority: 75,
                category: 'opportunistic',
                targets: vulnerableUnits
            });
        }
        
        // Check for undefended buildings
        const undefendedBuildings = this.findUndefendedPlayerBuildings();
        if (undefendedBuildings.length > 0) {
            options.push({
                type: 'raid_buildings',
                priority: 80,
                category: 'opportunistic',
                targets: undefendedBuildings
            });
        }
        
        // Check for expansion opportunities
        const expansionSites = this.findExpansionOpportunities();
        if (expansionSites.length > 0) {
            options.push({
                type: 'expand_territory',
                priority: 65,
                category: 'strategic',
                targets: expansionSites
            });
        }
        
        return options;
    }
    
    generateDefensiveOptions() {
        return [
            { type: 'emergency_defense', priority: 95, category: 'survival' },
            { type: 'recall_units', priority: 90, category: 'survival' },
            { type: 'build_emergency_turrets', priority: 85, category: 'survival' },
            { type: 'request_reinforcements', priority: 80, category: 'survival' }
        ];
    }
    
    selectBestOption(options) {
        if (options.length === 0) return null;
        
        // Weight options by category
        const weightedOptions = options.map(option => ({
            ...option,
            weightedUtility: option.utility * (this.decisionWeights[option.category] || 0.5)
        }));
        
        // Sort by weighted utility
        weightedOptions.sort((a, b) => b.weightedUtility - a.weightedUtility);
        
        // Add some randomness to prevent predictability
        const topOptions = weightedOptions.slice(0, Math.min(3, weightedOptions.length));
        const randomIndex = Math.floor(Math.random() * topOptions.length);
        
        return topOptions[randomIndex];
    }
    
    executeDecision(decision) {
        console.log(`AI executing decision: ${decision.type} (utility: ${decision.utility.toFixed(2)})`);
        
        this.currentDecision = decision;
        this.recordDecision(decision);
        
        // Execute the decision
        switch (decision.type) {
            case 'build_harvester':
                this.engine.coordination.economicCoordinator.prioritizeHarvesterProduction();
                break;
            case 'expand_to_spice':
                this.engine.coordination.economicCoordinator.expandEconomicBase();
                break;
            case 'build_refinery':
                this.engine.coordination.economicCoordinator.requestBuilding('refinery', 70);
                break;
            case 'train_soldiers':
                this.engine.coordination.economicCoordinator.prioritizeMilitaryProduction();
                break;
            case 'build_barracks':
                this.engine.coordination.economicCoordinator.requestBuilding('barracks', 75);
                break;
            case 'build_turrets':
                this.engine.coordination.defenseCoordinator.strengthenDefenses();
                break;
            case 'position_defenders':
                this.engine.coordination.defenseCoordinator.activateDefenses();
                break;
            case 'launch_assault':
                this.engine.coordination.attackCoordinator.launchMajorOffensive();
                break;
            case 'harass_economy':
                this.engine.coordination.attackCoordinator.executeEconomicWarfare({ intensity: 2 });
                break;
            case 'attack_vulnerable_units':
                this.engine.coordination.attackCoordinator.targetVulnerableUnits(decision.targets);
                break;
            case 'raid_buildings':
                this.engine.coordination.attackCoordinator.raidUndefendedBuildings(decision.targets);
                break;
            case 'emergency_defense':
                this.engine.coordination.defenseCoordinator.coordinateLastStand();
                break;
            case 'scout_targets':
                this.engine.coordination.scoutingCoordinator.gatherIntelligence();
                break;
        }
        
        // Set cooldown for this decision type
        this.setCooldown(decision.type, this.getDecisionCooldown(decision.type));
    }
    
    handleEmergencyDecision(emergency) {
        console.log(`AI handling emergency: ${emergency.type} (severity: ${emergency.severity})`);
        
        const emergencyDecision = {
            type: `emergency_${emergency.type}`,
            priority: 100,
            category: 'survival',
            emergency: emergency,
            utility: 1.0
        };
        
        this.executeDecision(emergencyDecision);
    }
    
    recordDecision(decision) {
        const record = {
            ...decision,
            timestamp: this.engine.ai.game.gameTime,
            gameState: this.captureGameState(),
            outcome: null // Will be filled later
        };
        
        this.decisionHistory.push(record);
        
        // Store in AI memory for learning
        this.engine.ai.memory.storeMemory('strategic', `decision_${Date.now()}`, record, 'important');
    }
    
    captureGameState() {
        return {
            aiPower: this.engine.ai.calculateAIPower(),
            playerPower: this.engine.ai.calculatePlayerPower(),
            aiSpice: this.engine.ai.team.spice,
            gameTime: this.engine.ai.game.gameTime,
            alertLevel: this.engine.coordination.defenseCoordinator.alertLevel,
            activeAttacks: this.engine.coordination.attackCoordinator.activeAttacks.length
        };
    }
    
    // Utility methods
    isUnderThreat() {
        return this.engine.coordination.defenseCoordinator.alertLevel > 2;
    }
    
    findVulnerablePlayerUnits() {
        return this.engine.ai.game.teams.player.units.filter(unit => {
            return unit.health < unit.maxHealth * 0.3 || this.isIsolated(unit);
        });
    }
    
    findUndefendedPlayerBuildings() {
        return this.engine.ai.game.teams.player.buildings.filter(building => {
            const nearbyDefenders = this.engine.ai.game.teams.player.units.filter(unit =>
                unit.damage > 0 && this.distance(unit, building) < 200
            ).length;
            return nearbyDefenders < 2;
        });
    }
    
    findExpansionOpportunities() {
        return this.engine.ai.game.spiceFields.filter(field => {
            return field.amount > field.maxAmount * 0.5 && this.isSafeForExpansion(field);
        });
    }
    
    isIsolated(unit) {
        const nearbyAllies = this.engine.ai.game.teams.player.units.filter(ally =>
            ally !== unit && this.distance(unit, ally) < 100
        ).length;
        return nearbyAllies === 0;
    }
    
    isSafeForExpansion(field) {
        const nearestEnemy = this.findNearestPlayerBuilding(field);
        return !nearestEnemy || this.distance(field, nearestEnemy) > 300;
    }
    
    findNearestPlayerBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.engine.ai.game.teams.player.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    // Cooldown management
    setCooldown(decisionType, duration) {
        this.decisionCooldowns.set(decisionType, this.engine.ai.game.gameTime + duration);
    }
    
    isOnCooldown(decisionType) {
        const cooldownEnd = this.decisionCooldowns.get(decisionType);
        return cooldownEnd && this.engine.ai.game.gameTime < cooldownEnd;
    }
    
    getDecisionCooldown(decisionType) {
        const cooldowns = {
            'build_harvester': 30,
            'expand_to_spice': 120,
            'build_refinery': 60,
            'train_soldiers': 45,
            'build_barracks': 90,
            'build_turrets': 60,
            'launch_assault': 180,
            'harass_economy': 90,
            'emergency_defense': 300
        };
        
        return cooldowns[decisionType] || 60;
    }
    
    updateCooldowns(deltaTime) {
        const currentTime = this.engine.ai.game.gameTime;
        
        this.decisionCooldowns.forEach((endTime, decisionType) => {
            if (currentTime >= endTime) {
                this.decisionCooldowns.delete(decisionType);
            }
        });
    }
    
    cleanupDecisionHistory() {
        const maxAge = 1800; // 30 minutes
        const cutoffTime = this.engine.ai.game.gameTime - maxAge;
        
        this.decisionHistory = this.decisionHistory.filter(decision =>
            decision.timestamp >= cutoffTime
        );
    }
    
    // Decision analysis and learning
    analyzeDecisionEffectiveness() {
        const recentDecisions = this.decisionHistory.filter(decision =>
            this.engine.ai.game.gameTime - decision.timestamp < 300 // Last 5 minutes
        );
        
        recentDecisions.forEach(decision => {
            if (!decision.outcome) {
                decision.outcome = this.evaluateDecisionOutcome(decision);
            }
        });
        
        return this.calculateOverallEffectiveness(recentDecisions);
    }
    
    evaluateDecisionOutcome(decision) {
        const currentState = this.captureGameState();
        const stateChange = this.compareGameStates(decision.gameState, currentState);
        
        let effectiveness = 0.5; // Neutral baseline
        
        // Evaluate based on decision category
        switch (decision.category) {
            case 'economic':
                effectiveness += stateChange.spiceChange > 0 ? 0.3 : -0.2;
                break;
            case 'military':
                effectiveness += stateChange.powerChange > 0 ? 0.3 : -0.2;
                break;
            case 'survival':
                effectiveness += stateChange.survived ? 0.4 : -0.4;
                break;
        }
        
        return Math.max(0, Math.min(1, effectiveness));
    }
    
    compareGameStates(oldState, newState) {
        return {
            spiceChange: newState.aiSpice - oldState.aiSpice,
            powerChange: newState.aiPower - oldState.aiPower,
            survived: newState.aiPower > 0,
            timeElapsed: newState.gameTime - oldState.gameTime
        };
    }
    
    calculateOverallEffectiveness(decisions) {
        if (decisions.length === 0) return 0.5;
        
        const totalEffectiveness = decisions.reduce((sum, decision) => 
            sum + (decision.outcome || 0.5), 0
        );
        
        return totalEffectiveness / decisions.length;
    }
    
    // Adaptation based on performance
    adaptDecisionWeights() {
        const effectiveness = this.analyzeDecisionEffectiveness();
        
        if (effectiveness < 0.4) {
            // Poor performance, increase survival weight
            this.decisionWeights.survival = Math.min(1.2, this.decisionWeights.survival + 0.1);
            this.decisionWeights.opportunistic = Math.max(0.3, this.decisionWeights.opportunistic - 0.1);
        } else if (effectiveness > 0.7) {
            // Good performance, can be more aggressive
            this.decisionWeights.opportunistic = Math.min(0.8, this.decisionWeights.opportunistic + 0.1);
            this.decisionWeights.survival = Math.max(0.8, this.decisionWeights.survival - 0.05);
        }
    }
    
    // Public interface for other systems
    requestDecision(context) {
        // Allow other systems to request specific decisions
        const options = this.generateDecisionOptions([]);
        const contextualOptions = options.filter(option => 
            this.matchesContext(option, context)
        );
        
        if (contextualOptions.length > 0) {
            const bestOption = this.selectBestOption(contextualOptions);
            this.executeDecision(bestOption);
            return bestOption;
        }
        
        return null;
    }
    
    matchesContext(option, context) {
        if (context.category && option.category !== context.category) {
            return false;
        }
        
        if (context.minPriority && option.priority < context.minPriority) {
            return false;
        }
        
        return true;
    }
    
    getDecisionSummary() {
        return {
            currentDecision: this.currentDecision,
            recentDecisions: this.decisionHistory.slice(-5),
            effectiveness: this.analyzeDecisionEffectiveness(),
            activeCooldowns: Array.from(this.decisionCooldowns.keys()),
            decisionWeights: { ...this.decisionWeights }
        };
    }
}