import { AIController } from '../AIController.js';
import { AIDecisionSystem } from './systems/AIDecisionSystem.js';
import { AIExecutionSystem } from './systems/AIExecutionSystem.js';
import { AIPerformanceMonitor } from './systems/AIPerformanceMonitor.js';
import { AIStateManager } from './systems/AIStateManager.js';
import { EventBus } from '../core/EventBus.js';

/**
 * Advanced AI Controller (Refactored)
 * Main coordinator for all AI systems, integrating decision-making, execution, state management, and performance monitoring
 */
export class AdvancedAIController extends AIController {
    constructor(game, teamId = 'enemy') {
        super(game, teamId);
        
        // AI Systems
        this.aiSystems = {
            decision: null,
            execution: null,
            state: null,
            performance: null
        };
        
        // AI Layers (legacy compatibility)
        this.aiLayers = {
            strategic: new StrategicAI(this),
            tactical: new TacticalAI(this),
            operational: new OperationalAI(this)
        };
        
        // System integration state
        this.integration = {
            initialized: false,
            systemsOnline: new Set(),
            lastHealthCheck: 0,
            healthCheckInterval: 30
        };
        
        // Performance optimization
        this.optimization = {
            enabled: true,
            updateThrottling: false,
            adaptiveFrequency: true,
            targetFPS: 60
        };
        
        this.initializeAdvancedAI();
    }
    
    initializeAdvancedAI() {
        console.log(`Initializing Advanced AI Controller for ${this.teamId}`);
        
        // Initialize AI systems in order
        this.initializeAISystems();
        
        // Set up system integration
        this.setupSystemIntegration();
        
        // Subscribe to system events
        this.subscribeToSystemEvents();
        
        // Mark as initialized
        this.integration.initialized = true;
        
        console.log(`Advanced AI Controller initialized for ${this.teamId} with ${this.integration.systemsOnline.size} systems online`);
    }
    
    initializeAISystems() {
        try {
            // Initialize State Manager first (provides personality and memory)
            this.aiSystems.state = new AIStateManager(this);
            this.integration.systemsOnline.add('state');
            
            // Initialize Decision System (depends on state for personality)
            this.aiSystems.decision = new AIDecisionSystem(this);
            this.integration.systemsOnline.add('decision');
            
            // Initialize Execution System (depends on decisions)
            this.aiSystems.execution = new AIExecutionSystem(this);
            this.integration.systemsOnline.add('execution');
            
            // Initialize Performance Monitor last (monitors all systems)
            this.aiSystems.performance = new AIPerformanceMonitor(this);
            this.integration.systemsOnline.add('performance');
            
            // Set up cross-system references for easy access
            this.personalityTraits = this.aiSystems.state.personalityTraits;
            this.memory = this.aiSystems.state.memory;
            this.revengeSystem = this.aiSystems.state.revengeSystem;
            this.strategyAdaptation = this.aiSystems.state.strategyAdaptation;
            this.performanceMetrics = this.aiSystems.performance.metrics;
            
        } catch (error) {
            console.error('Failed to initialize AI systems:', error);
            throw error;
        }
    }
    
    setupSystemIntegration() {
        // Set up event-driven communication between systems
        this.game.eventBus.on('ai.system.error', this.handleSystemError, this);
        this.game.eventBus.on('ai.performance.critical', this.handleCriticalPerformance, this);
        this.game.eventBus.on('ai.optimization.required', this.handleOptimizationRequired, this);
    }
    
    subscribeToSystemEvents() {
        // Subscribe to game events that AI systems need to know about
        this.game.eventBus.on('game.unit.destroyed', this.handleUnitDestroyed, this);
        this.game.eventBus.on('game.building.destroyed', this.handleBuildingDestroyed, this);
        this.game.eventBus.on('game.player.action', this.handlePlayerAction, this);
        this.game.eventBus.on('game.resource.changed', this.handleResourceChanged, this);
    }
    
    // Override parent update method with advanced AI logic
    update(deltaTime) {
        if (!this.integration.initialized) return;
        
        // Performance monitoring and optimization
        this.updateWithPerformanceMonitoring(deltaTime);
        
        // Health check systems periodically
        this.performSystemHealthCheck(deltaTime);
        
        // Call parent update for basic functionality
        super.update(deltaTime);
    }
    
    updateWithPerformanceMonitoring(deltaTime) {
        const startTime = performance.now();
        
        try {
            // Update all AI systems
            this.updateAISystems(deltaTime);
            
            // Update legacy AI layers for compatibility
            this.updateAILayers(deltaTime);
            
        } catch (error) {
            console.error('Error updating AI systems:', error);
            this.handleSystemError({ error, system: 'controller' });
        }
        
        const updateTime = performance.now() - startTime;
        
        // Emit performance data
        this.game.eventBus.emit('ai.performance.update', {
            updateTime,
            deltaTime,
            systemsOnline: this.integration.systemsOnline.size
        });
    }
    
    updateAISystems(deltaTime) {
        // Update systems in order of dependency
        if (this.aiSystems.state) {
            this.aiSystems.state.update(deltaTime);
        }
        
        if (this.aiSystems.decision) {
            this.aiSystems.decision.update(deltaTime);
        }
        
        if (this.aiSystems.execution) {
            this.aiSystems.execution.update(deltaTime);
        }
        
        if (this.aiSystems.performance) {
            this.aiSystems.performance.update(deltaTime);
        }
    }
    
    updateAILayers(deltaTime) {
        // Update legacy AI layers for backward compatibility
        try {
            this.aiLayers.strategic.update(deltaTime);
            this.aiLayers.tactical.update(deltaTime);
            this.aiLayers.operational.update(deltaTime);
        } catch (error) {
            console.warn('Error updating legacy AI layers:', error);
        }
    }
    
    performSystemHealthCheck(deltaTime) {
        const currentTime = this.game.gameTime;
        
        if (currentTime - this.integration.lastHealthCheck >= this.integration.healthCheckInterval) {
            this.checkSystemHealth();
            this.integration.lastHealthCheck = currentTime;
        }
    }
    
    checkSystemHealth() {
        const expectedSystems = ['state', 'decision', 'execution', 'performance'];
        const onlineSystems = Array.from(this.integration.systemsOnline);
        
        expectedSystems.forEach(systemName => {
            if (!this.integration.systemsOnline.has(systemName)) {
                console.warn(`AI System offline: ${systemName}`);
                this.attemptSystemRecovery(systemName);
            }
        });
        
        // Emit health status
        this.game.eventBus.emit('ai.health.check', {
            expectedSystems: expectedSystems.length,
            onlineSystems: onlineSystems.length,
            systems: onlineSystems
        });
    }
    
    attemptSystemRecovery(systemName) {
        try {
            console.log(`Attempting to recover AI system: ${systemName}`);
            
            switch (systemName) {
                case 'state':
                    if (!this.aiSystems.state) {
                        this.aiSystems.state = new AIStateManager(this);
                        this.integration.systemsOnline.add('state');
                    }
                    break;
                case 'decision':
                    if (!this.aiSystems.decision) {
                        this.aiSystems.decision = new AIDecisionSystem(this);
                        this.integration.systemsOnline.add('decision');
                    }
                    break;
                case 'execution':
                    if (!this.aiSystems.execution) {
                        this.aiSystems.execution = new AIExecutionSystem(this);
                        this.integration.systemsOnline.add('execution');
                    }
                    break;
                case 'performance':
                    if (!this.aiSystems.performance) {
                        this.aiSystems.performance = new AIPerformanceMonitor(this);
                        this.integration.systemsOnline.add('performance');
                    }
                    break;
            }
            
            console.log(`Successfully recovered AI system: ${systemName}`);
        } catch (error) {
            console.error(`Failed to recover AI system ${systemName}:`, error);
        }
    }
    
    // Event handlers
    handleSystemError(event) {
        console.error(`AI System error in ${event.system}:`, event.error);
        
        // Remove failed system from online list
        this.integration.systemsOnline.delete(event.system);
        
        // Attempt recovery
        this.attemptSystemRecovery(event.system);
    }
    
    handleCriticalPerformance(event) {
        console.warn('Critical AI performance detected:', event);
        
        // Enable aggressive optimization
        if (this.optimization.enabled) {
            this.enableAggressiveOptimization();
        }
    }
    
    handleOptimizationRequired(event) {
        console.log('AI optimization required:', event);
        
        // Apply requested optimizations
        this.applyOptimizations(event.optimizations);
    }
    
    handleUnitDestroyed(event) {
        // Record aggression if it's our unit
        if (event.unit.teamId === this.teamId && event.attacker) {
            this.recordPlayerAggression({
                type: 'unit_killed',
                location: { x: event.unit.x, y: event.unit.y },
                unitsLost: 1,
                strategicValue: this.calculateUnitValue(event.unit) / 100,
                playerUnitsInvolved: [event.attacker],
                witnessed: true
            });
        }
    }
    
    handleBuildingDestroyed(event) {
        // Record aggression if it's our building
        if (event.building.teamId === this.teamId && event.attacker) {
            this.recordPlayerAggression({
                type: 'building_destroyed',
                location: { x: event.building.x, y: event.building.y },
                buildingsLost: 1,
                strategicValue: this.calculateBuildingThreatValue(event.building) / 100,
                playerUnitsInvolved: [event.attacker],
                witnessed: true
            });
        }
    }
    
    handlePlayerAction(event) {
        // Let state manager handle player action recording
        if (this.aiSystems.state) {
            this.aiSystems.state.recordPlayerAction(event);
        }
    }
    
    handleResourceChanged(event) {
        // Track resource changes for economic analysis
        if (event.teamId === this.teamId) {
            this.game.eventBus.emit('ai.resource.gained', {
                resource: event.resource,
                amount: event.amount,
                timestamp: this.game.gameTime
            });
        }
    }
    
    // Public interface methods
    recordPlayerAggression(aggressionData) {
        if (this.aiSystems.state) {
            this.aiSystems.state.recordPlayerAggression(aggressionData);
        }
    }
    
    getAIStatus() {
        return {
            teamId: this.teamId,
            initialized: this.integration.initialized,
            systemsOnline: Array.from(this.integration.systemsOnline),
            personality: this.personalityTraits,
            performance: this.aiSystems.performance?.getPerformanceReport(),
            currentStrategy: this.strategyAdaptation?.currentStrategy,
            revengeLevel: this.revengeSystem?.escalationLevel
        };
    }
    
    getSystemHealth() {
        const expectedSystems = 4;
        const onlineSystems = this.integration.systemsOnline.size;
        const healthPercentage = (onlineSystems / expectedSystems) * 100;
        
        return {
            health: healthPercentage,
            status: healthPercentage >= 100 ? 'excellent' : 
                   healthPercentage >= 75 ? 'good' : 
                   healthPercentage >= 50 ? 'degraded' : 'critical',
            systemsOnline: onlineSystems,
            expectedSystems,
            details: this.aiSystems.performance?.getPerformanceReport()
        };
    }
    
    // Optimization methods
    enableAggressiveOptimization() {
        this.optimization.updateThrottling = true;
        this.optimization.adaptiveFrequency = true;
        
        // Reduce update frequency for non-critical systems
        this.game.eventBus.emit('ai.optimize.frequency', { 
            reduce: true, 
            aggressive: true 
        });
        
        console.log('Aggressive AI optimization enabled');
    }
    
    applyOptimizations(optimizations) {
        optimizations.forEach(optimization => {
            switch (optimization) {
                case 'reduce_decision_complexity':
                    if (this.aiSystems.decision) {
                        this.aiSystems.decision.strategicInterval = 45; // Slower strategic decisions
                        this.aiSystems.decision.tacticalInterval = 15; // Slower tactical decisions
                    }
                    break;
                case 'cleanup_memory':
                    if (this.aiSystems.state) {
                        this.aiSystems.state.decayMemories(10); // Force memory cleanup
                    }
                    break;
                case 'reduce_update_frequency':
                    this.optimization.updateThrottling = true;
                    break;
            }
        });
    }
    
    // Utility methods
    calculateUnitValue(unit) {
        let value = unit.maxHealth + (unit.damage || 0) * 5;
        
        // Special units have higher value
        if (['tank', 'rocketeer'].includes(unit.type)) {
            value *= 2;
        }
        
        // Veteran units are more valuable
        if (unit.combatXP > 100) {
            value *= 1.5;
        }
        
        return value;
    }
    
    calculateBuildingThreatValue(building) {
        const threatValues = {
            'base': 100,
            'factory': 80,
            'barracks': 60,
            'refinery': 70,
            'powerplant': 50,
            'turret': 40,
            'radar': 30
        };
        
        return threatValues[building.type] || 20;
    }
    
    // Legacy compatibility methods
    calculateAIPower() {
        const aiUnits = this.team.units.length;
        const aiBuildings = this.team.buildings.length;
        const aiResources = this.team.spice;
        
        return (aiUnits * 10) + (aiBuildings * 50) + (aiResources / 100);
    }
    
    calculatePlayerPower() {
        const playerUnits = this.game.teams.player.units.length;
        const playerBuildings = this.game.teams.player.buildings.length;
        const playerResources = this.game.resourceManager?.getResource('spice') || 0;
        
        return (playerUnits * 10) + (playerBuildings * 50) + (playerResources / 100);
    }
    
    // Export/Import state for save/load
    exportState() {
        const baseState = super.exportState();
        
        return {
            ...baseState,
            aiSystems: {
                state: this.aiSystems.state?.exportState(),
                performance: this.aiSystems.performance?.exportPerformanceData()
            },
            integration: this.integration,
            optimization: this.optimization
        };
    }
    
    importState(state) {
        super.importState(state);
        
        if (state.aiSystems) {
            if (state.aiSystems.state && this.aiSystems.state) {
                this.aiSystems.state.importState(state.aiSystems.state);
            }
            if (state.aiSystems.performance && this.aiSystems.performance) {
                this.aiSystems.performance.importPerformanceData(state.aiSystems.performance);
            }
        }
        
        if (state.integration) {
            this.integration = { ...this.integration, ...state.integration };
        }
        
        if (state.optimization) {
            this.optimization = { ...this.optimization, ...state.optimization };
        }
    }
}

// Legacy AI Layer Classes (simplified for compatibility)
class StrategicAI {
    constructor(controller) {
        this.controller = controller;
        this.lastAssessment = 0;
        this.assessmentInterval = 30;
    }
    
    update(deltaTime) {
        if (this.controller.game.gameTime - this.lastAssessment >= this.assessmentInterval) {
            this.lastAssessment = this.controller.game.gameTime;
            // Delegate to new decision system
            if (this.controller.aiSystems.decision) {
                // Strategic decisions are handled by the new system
            }
        }
    }
}

class TacticalAI {
    constructor(controller) {
        this.controller = controller;
        this.lastAssessment = 0;
        this.assessmentInterval = 10;
    }
    
    update(deltaTime) {
        if (this.controller.game.gameTime - this.lastAssessment >= this.assessmentInterval) {
            this.lastAssessment = this.controller.game.gameTime;
            // Delegate to new decision system
            if (this.controller.aiSystems.decision) {
                // Tactical decisions are handled by the new system
            }
        }
    }
}

class OperationalAI {
    constructor(controller) {
        this.controller = controller;
        this.lastAssessment = 0;
        this.assessmentInterval = 3;
    }
    
    update(deltaTime) {
        if (this.controller.game.gameTime - this.lastAssessment >= this.assessmentInterval) {
            this.lastAssessment = this.controller.game.gameTime;
            // Delegate to new execution system
            if (this.controller.aiSystems.execution) {
                // Operational decisions are handled by the new system
            }
        }
    }
}