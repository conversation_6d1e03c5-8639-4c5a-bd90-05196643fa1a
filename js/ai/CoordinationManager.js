import { AttackCoordinator } from './coordinators/AttackCoordinator.js';
import { DefenseCoordinator } from './coordinators/DefenseCoordinator.js';
import { EconomicCoordinator } from './coordinators/EconomicCoordinator.js';
import { ScoutingCoordinator } from './coordinators/ScoutingCoordinator.js';

/**
 * Coordination Manager
 * Manages all AI coordination systems for unified strategic execution
 */
export class CoordinationManager {
    constructor(engine) {
        this.engine = engine;
        
        // Initialize all coordinators
        this.attackCoordinator = new AttackCoordinator(engine);
        this.defenseCoordinator = new DefenseCoordinator(engine);
        this.economicCoordinator = new EconomicCoordinator(engine);
        this.scoutingCoordinator = new ScoutingCoordinator(engine);
        
        // Coordination state
        this.activeOperations = new Map();
        this.coordinationPriorities = {
            defense: 0,
            attack: 0,
            economy: 0,
            scouting: 0
        };
        
        // Inter-coordinator communication
        this.messageQueue = [];
        this.sharedIntelligence = new Map();
    }
    
    initialize() {
        // Initialize all coordinators
        this.attackCoordinator.initialize();
        this.defenseCoordinator.initialize();
        this.economicCoordinator.initialize();
        this.scoutingCoordinator.initialize();
        
        // Set up inter-coordinator communication
        this.setupCoordinatorCommunication();
        
        console.log('Coordination Manager initialized');
    }
    
    update(deltaTime) {
        // Update coordination priorities
        this.updateCoordinationPriorities();
        
        // Process inter-coordinator messages
        this.processMessages();
        
        // Update all coordinators
        this.attackCoordinator.update(deltaTime);
        this.defenseCoordinator.update(deltaTime);
        this.economicCoordinator.update(deltaTime);
        this.scoutingCoordinator.update(deltaTime);
        
        // Coordinate between systems
        this.coordinateSystems(deltaTime);
        
        // Update active operations
        this.updateActiveOperations(deltaTime);
    }
    
    setupCoordinatorCommunication() {
        // Set up message passing between coordinators
        this.attackCoordinator.setMessageCallback((message) => this.handleMessage(message));
        this.defenseCoordinator.setMessageCallback((message) => this.handleMessage(message));
        this.economicCoordinator.setMessageCallback((message) => this.handleMessage(message));
        this.scoutingCoordinator.setMessageCallback((message) => this.handleMessage(message));
    }
    
    updateCoordinationPriorities() {
        // Calculate priority levels based on current situation
        const gameState = this.assessGameState();
        
        // Defense priority
        this.coordinationPriorities.defense = this.calculateDefensePriority(gameState);
        
        // Attack priority
        this.coordinationPriorities.attack = this.calculateAttackPriority(gameState);
        
        // Economy priority
        this.coordinationPriorities.economy = this.calculateEconomyPriority(gameState);
        
        // Scouting priority
        this.coordinationPriorities.scouting = this.calculateScoutingPriority(gameState);
        
        // Normalize priorities
        this.normalizePriorities();
    }
    
    assessGameState() {
        return {
            aiPower: this.engine.ai.calculateAIPower(),
            playerPower: this.engine.ai.calculatePlayerPower(),
            aiUnits: this.engine.ai.team.units.length,
            playerUnits: this.engine.ai.game.teams.player.units.length,
            aiBuildings: this.engine.ai.team.buildings.length,
            playerBuildings: this.engine.ai.game.teams.player.buildings.length,
            aiSpice: this.engine.ai.team.spice,
            gameTime: this.engine.ai.game.gameTime,
            underAttack: this.isUnderAttack(),
            hasAdvantage: this.hasAdvantage(),
            resourceSituation: this.assessResourceSituation()
        };
    }
    
    calculateDefensePriority(gameState) {
        let priority = 30; // Base priority
        
        // Increase if under attack
        if (gameState.underAttack) {
            priority += 50;
        }
        
        // Increase if player is stronger
        const powerRatio = gameState.aiPower / Math.max(1, gameState.playerPower);
        if (powerRatio < 0.8) {
            priority += 30;
        }
        
        // Increase based on AI personality
        priority += (1 - this.engine.ai.personalityTraits.aggression) * 20;
        
        return Math.min(100, priority);
    }
    
    calculateAttackPriority(gameState) {
        let priority = 20; // Base priority
        
        // Increase if we have advantage
        if (gameState.hasAdvantage) {
            priority += 40;
        }
        
        // Increase based on AI personality
        priority += this.engine.ai.personalityTraits.aggression * 30;
        
        // Increase if revenge is pending
        if (this.engine.ai.revengeSystem.revengeQueue.length > 0) {
            priority += 25;
        }
        
        // Decrease if under attack
        if (gameState.underAttack) {
            priority -= 20;
        }
        
        return Math.max(0, Math.min(100, priority));
    }
    
    calculateEconomyPriority(gameState) {
        let priority = 40; // Base priority
        
        // Increase if low on resources
        if (gameState.aiSpice < 500) {
            priority += 30;
        }
        
        // Increase in early game
        if (gameState.gameTime < 600) {
            priority += 20;
        }
        
        // Increase based on AI personality
        priority += this.engine.ai.personalityTraits.opportunism * 15;
        
        // Decrease if under heavy attack
        if (gameState.underAttack && gameState.aiPower < gameState.playerPower) {
            priority -= 25;
        }
        
        return Math.max(0, Math.min(100, priority));
    }
    
    calculateScoutingPriority(gameState) {
        let priority = 25; // Base priority
        
        // Increase if lacking intelligence
        const intelligenceAge = this.getAverageIntelligenceAge();
        if (intelligenceAge > 120) { // 2 minutes old
            priority += 20;
        }
        
        // Increase based on AI personality
        priority += this.engine.ai.personalityTraits.cunning * 15;
        
        // Increase in early game
        if (gameState.gameTime < 300) {
            priority += 15;
        }
        
        return Math.min(100, priority);
    }
    
    normalizePriorities() {
        const total = Object.values(this.coordinationPriorities).reduce((sum, p) => sum + p, 0);
        
        if (total > 0) {
            Object.keys(this.coordinationPriorities).forEach(key => {
                this.coordinationPriorities[key] = (this.coordinationPriorities[key] / total) * 100;
            });
        }
    }
    
    processMessages() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.routeMessage(message);
        }
    }
    
    handleMessage(message) {
        this.messageQueue.push(message);
    }
    
    routeMessage(message) {
        switch (message.type) {
            case 'request_units':
                this.handleUnitRequest(message);
                break;
            case 'share_intelligence':
                this.handleIntelligenceShare(message);
                break;
            case 'coordinate_attack':
                this.handleAttackCoordination(message);
                break;
            case 'emergency_defense':
                this.handleEmergencyDefense(message);
                break;
            case 'resource_request':
                this.handleResourceRequest(message);
                break;
        }
    }
    
    handleUnitRequest(message) {
        // Coordinate unit allocation between systems
        const availableUnits = this.getAvailableUnits(message.unitType, message.count);
        
        if (availableUnits.length >= message.count) {
            // Allocate units to requesting coordinator
            this.allocateUnits(availableUnits.slice(0, message.count), message.requesterId);
            
            // Send confirmation
            this.sendMessage(message.requesterId, {
                type: 'unit_allocation',
                units: availableUnits.slice(0, message.count),
                success: true
            });
        } else {
            // Send rejection
            this.sendMessage(message.requesterId, {
                type: 'unit_allocation',
                units: [],
                success: false,
                reason: 'insufficient_units'
            });
        }
    }
    
    handleIntelligenceShare(message) {
        // Store shared intelligence
        this.sharedIntelligence.set(message.key, {
            data: message.data,
            source: message.source,
            timestamp: this.engine.ai.game.gameTime,
            reliability: message.reliability || 0.8
        });
        
        // Distribute to relevant coordinators
        this.distributeIntelligence(message);
    }
    
    handleAttackCoordination(message) {
        // Coordinate multi-system attacks
        this.coordinateMultiSystemAttack(message);
    }
    
    handleEmergencyDefense(message) {
        // Coordinate emergency defense response
        this.coordinateEmergencyResponse(message);
    }
    
    handleResourceRequest(message) {
        // Handle resource allocation requests
        this.handleResourceAllocation(message);
    }
    
    coordinateSystems(deltaTime) {
        // Coordinate between different systems based on priorities
        
        // Defense-Economy coordination
        if (this.coordinationPriorities.defense > 60) {
            this.economicCoordinator.prioritizeDefensiveProduction();
        }
        
        // Attack-Scouting coordination
        if (this.coordinationPriorities.attack > 50) {
            this.scoutingCoordinator.prioritizeTargetIntelligence();
        }
        
        // Economy-Attack coordination
        if (this.coordinationPriorities.economy > 70) {
            this.attackCoordinator.reduceAggressiveness();
        }
        
        // Cross-system unit sharing
        this.coordinateUnitSharing();
    }
    
    coordinateUnitSharing() {
        // Share units between systems based on priorities
        const idleUnits = this.getIdleUnits();
        
        if (idleUnits.length > 0) {
            // Allocate based on priorities
            const highestPriority = Math.max(...Object.values(this.coordinationPriorities));
            const prioritySystem = Object.keys(this.coordinationPriorities).find(
                key => this.coordinationPriorities[key] === highestPriority
            );
            
            this.allocateIdleUnits(idleUnits, prioritySystem);
        }
    }
    
    coordinateMultiSystemAttack(message) {
        // Coordinate attack involving multiple systems
        const operation = {
            id: `multi_attack_${Date.now()}`,
            type: 'coordinated_attack',
            target: message.target,
            participants: ['attack', 'scouting'],
            phase: 'planning',
            startTime: this.engine.ai.game.gameTime
        };
        
        this.activeOperations.set(operation.id, operation);
        
        // Notify participating coordinators
        this.attackCoordinator.joinOperation(operation);
        this.scoutingCoordinator.provideIntelligence(operation);
    }
    
    coordinateEmergencyResponse(message) {
        // Coordinate emergency response across all systems
        const operation = {
            id: `emergency_${Date.now()}`,
            type: 'emergency_response',
            threat: message.threat,
            participants: ['defense', 'attack', 'economy'],
            phase: 'active',
            startTime: this.engine.ai.game.gameTime
        };
        
        this.activeOperations.set(operation.id, operation);
        
        // All systems respond to emergency
        this.defenseCoordinator.handleEmergency(operation);
        this.attackCoordinator.supportDefense(operation);
        this.economicCoordinator.emergencyMode(operation);
    }
    
    updateActiveOperations(deltaTime) {
        // Update and clean up active operations
        this.activeOperations.forEach((operation, id) => {
            const age = this.engine.ai.game.gameTime - operation.startTime;
            
            // Update operation status
            this.updateOperationStatus(operation);
            
            // Clean up completed or expired operations
            if (operation.phase === 'completed' || age > 600) { // 10 minutes max
                this.activeOperations.delete(id);
                this.notifyOperationEnd(operation);
            }
        });
    }
    
    updateOperationStatus(operation) {
        switch (operation.type) {
            case 'coordinated_attack':
                this.updateAttackOperation(operation);
                break;
            case 'emergency_response':
                this.updateEmergencyOperation(operation);
                break;
        }
    }
    
    updateAttackOperation(operation) {
        // Check if attack target still exists
        if (!operation.target || operation.target.health <= 0) {
            operation.phase = 'completed';
            operation.result = 'success';
        }
    }
    
    updateEmergencyOperation(operation) {
        // Check if emergency is resolved
        if (!this.isUnderAttack()) {
            operation.phase = 'completed';
            operation.result = 'resolved';
        }
    }
    
    // Utility methods
    getAvailableUnits(unitType, count) {
        return this.engine.ai.team.units
            .filter(unit => {
                if (unitType && unit.type !== unitType) return false;
                return unit.state === 'idle' || unit.priority === 'low';
            })
            .slice(0, count);
    }
    
    getIdleUnits() {
        return this.engine.ai.team.units.filter(unit => unit.state === 'idle');
    }
    
    allocateUnits(units, requesterId) {
        units.forEach(unit => {
            unit.allocatedTo = requesterId;
            unit.allocationTime = this.engine.ai.game.gameTime;
        });
    }
    
    allocateIdleUnits(units, systemType) {
        switch (systemType) {
            case 'defense':
                this.defenseCoordinator.assignUnits(units);
                break;
            case 'attack':
                this.attackCoordinator.assignUnits(units);
                break;
            case 'scouting':
                this.scoutingCoordinator.assignUnits(units);
                break;
        }
    }
    
    sendMessage(recipientId, message) {
        message.timestamp = this.engine.ai.game.gameTime;
        message.senderId = 'coordination_manager';
        
        // Route to appropriate coordinator
        switch (recipientId) {
            case 'attack':
                this.attackCoordinator.receiveMessage(message);
                break;
            case 'defense':
                this.defenseCoordinator.receiveMessage(message);
                break;
            case 'economy':
                this.economicCoordinator.receiveMessage(message);
                break;
            case 'scouting':
                this.scoutingCoordinator.receiveMessage(message);
                break;
        }
    }
    
    distributeIntelligence(message) {
        // Distribute intelligence to relevant coordinators
        if (message.data.type === 'enemy_movement') {
            this.attackCoordinator.receiveIntelligence(message.data);
            this.defenseCoordinator.receiveIntelligence(message.data);
        } else if (message.data.type === 'resource_location') {
            this.economicCoordinator.receiveIntelligence(message.data);
        }
    }
    
    notifyOperationEnd(operation) {
        // Notify all participants that operation has ended
        operation.participants.forEach(participant => {
            this.sendMessage(participant, {
                type: 'operation_ended',
                operationId: operation.id,
                result: operation.result
            });
        });
    }
    
    isUnderAttack() {
        return this.engine.ai.team.buildings.some(building => {
            return this.engine.ai.game.teams.player.units.some(unit =>
                unit.attackTarget === building
            );
        });
    }
    
    hasAdvantage() {
        const aiPower = this.engine.ai.calculateAIPower();
        const playerPower = this.engine.ai.calculatePlayerPower();
        return aiPower > playerPower * 1.2;
    }
    
    assessResourceSituation() {
        const spice = this.engine.ai.team.spice;
        const income = this.calculateSpiceIncome();
        
        if (spice < 200) return 'critical';
        if (spice < 500) return 'low';
        if (spice > 2000) return 'abundant';
        return 'adequate';
    }
    
    calculateSpiceIncome() {
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
        return harvesters * 2; // Approximate income per harvester
    }
    
    getAverageIntelligenceAge() {
        const intel = Array.from(this.sharedIntelligence.values());
        if (intel.length === 0) return 300; // Default to old if no intel
        
        const currentTime = this.engine.ai.game.gameTime;
        const totalAge = intel.reduce((sum, item) => sum + (currentTime - item.timestamp), 0);
        
        return totalAge / intel.length;
    }
    
    handleResourceAllocation(message) {
        // Handle resource allocation between systems
        const available = this.engine.ai.team.spice;
        const requested = message.amount;
        
        if (available >= requested) {
            this.sendMessage(message.requesterId, {
                type: 'resource_allocated',
                amount: requested,
                success: true
            });
        } else {
            this.sendMessage(message.requesterId, {
                type: 'resource_allocated',
                amount: 0,
                success: false,
                reason: 'insufficient_resources'
            });
        }
    }
}