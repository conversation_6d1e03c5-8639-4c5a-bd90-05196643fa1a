import { AIBehaviorEngine } from './AIBehaviorEngine.js';
import { AIController } from '../AIController.js';
import { AIMemorySystem } from '../AIMemorySystem.js'; // Corrected path
import { AdvancedAIController } from './AdvancedAIController.js';

/**
 * AI Integration
 * Integrates the advanced AI system with the existing game architecture
 */
export class AIIntegration {
    constructor(game, eventBus) {
        this.game = game;
        this.eventBus = eventBus; // Store the event bus
        this.advancedAI = null;
        this.isEnabled = true;
        this.fallbackToBasic = false;
        
        // Integration settings
        this.settings = {
            enableAdvancedAI: true,
            enableMemorySystem: true,
            enableBehaviorEngine: true,
            enableRevengeSystem: true,
            enableCoordination: true,
            debugMode: false
        };
        
        // Performance monitoring
        this.performanceMetrics = {
            updateTime: 0,
            memoryUsage: 0,
            decisionCount: 0,
            lastUpdate: 0
        };
    }
    
    // Initialize the advanced AI system
    initialize() {
        try {
            console.log('Initializing Advanced AI System...');
            
            // Check if we should use advanced AI
            if (!this.settings.enableAdvancedAI) {
                console.log('Advanced AI disabled, using basic AI');
                return false;
            }
            
            // Initialize advanced AI controller
            this.initializeAdvancedAI();
            
            // Integrate with existing systems
            this.integrateWithExistingSystems();
            
            // Set up event listeners
            this.setupEventListeners();
            
            return true;

        } catch (error) {
            this.fallbackToBasic = true;
            return false;
        }
    }
    
    initializeAdvancedAI() {
        console.log('🤖 AI DEBUG: Starting initializeAdvancedAI...');
        console.log('🤖 AI DEBUG: Available teams:', Object.keys(this.game.teams));
        
        // Create advanced AI controller for each AI team
        Object.keys(this.game.teams).forEach(teamId => {
            console.log(`🤖 AI DEBUG: Processing team ${teamId}`);
            if (teamId !== 'player') {
                const team = this.game.teams[teamId];
                console.log(`🤖 AI DEBUG: Team ${teamId} structure:`, {
                    hasAIController: !!team.aiController,
                    teamObject: !!team,
                    unitsLength: team.units?.length || 0,
                    buildingsLength: team.buildings?.length || 0
                });
                
                // Create AI controller if it doesn't exist
                if (!team.aiController) {
                    console.log(`🤖 AI DEBUG: Creating basic AI controller for team ${teamId}`);
                    team.aiController = new AIController(this.game, teamId);
                }
                
                // Replace basic AI with advanced AI
                if (team.aiController) {
                    console.log(`🤖 AI DEBUG: Upgrading AI controller for team ${teamId}`);
                    this.upgradeAIController(team, teamId);
                } else {
                    console.warn(`🤖 AI DEBUG: No AI controller found for team ${teamId} after creation attempt`);
                }
            }
        });
        
        console.log('🤖 AI DEBUG: initializeAdvancedAI completed');
    }
    
    upgradeAIController(team, teamId) {
        // Store reference to old AI controller
        const oldAI = team.aiController;
        
        // Create new advanced AI controller
        const advancedAI = new AdvancedAIController(this.game, teamId);
        
        // Transfer state from old AI if needed
        if (oldAI) {
            this.transferAIState(oldAI, advancedAI);
        }
        
        // Replace the AI controller
        team.aiController = advancedAI;
        
        // Store reference for main AI (usually first enemy team)
        if (!this.advancedAI) {
            this.advancedAI = advancedAI;
        }
        
    }
    
    transferAIState(oldAI, newAI) {
        // Transfer basic state from old AI to new AI
        if (oldAI.team) {
            newAI.team = oldAI.team;
        }
        
        if (oldAI.teamId) {
            newAI.teamId = oldAI.teamId;
        }
        
        // Transfer any other relevant state
        if (oldAI.gameTime) {
            newAI.gameTime = oldAI.gameTime;
        }
    }
    
    integrateWithExistingSystems() {
        // Integrate with combat system
        this.integrateWithCombatSystem();
        
        // Integrate with diplomacy system
        this.integrateWithDiplomacySystem();
        
        // Integrate with resource management
        this.integrateWithResourceSystem();
        
        // Integrate with UI for debugging
        if (this.settings.debugMode) {
            this.integrateWithUI();
        }
    }
    
    integrateWithCombatSystem() {
        if (this.game.combatSystem) {
            // Hook into combat events for revenge system
            const originalResolveCombat = this.game.combatSystem.resolveCombat;
            
            this.game.combatSystem.resolveCombat = (attacker, defender) => {
                const result = originalResolveCombat.call(this.game.combatSystem, attacker, defender);
                
                // Record combat for AI memory and revenge system
                this.recordCombatEvent(attacker, defender, result);
                
                return result;
            };
        }
    }
    
    integrateWithDiplomacySystem() {
        if (this.game.diplomacySystem) {
            // Hook into diplomatic events
            const originalChangeDiplomaticStatus = this.game.diplomacySystem.changeDiplomaticStatus;
            
            this.game.diplomacySystem.changeDiplomaticStatus = (faction1, faction2, status) => {
                const result = originalChangeDiplomaticStatus.call(this.game.diplomacySystem, faction1, faction2, status);
                
                // Record diplomatic changes for AI memory
                this.recordDiplomaticEvent(faction1, faction2, status);
                
                return result;
            };
        }
    }
    
    integrateWithResourceSystem() {
        if (this.game.resourceManager) {
            // Monitor resource changes for AI economic intelligence
            const originalUpdateResources = this.game.resourceManager.update;
            
            this.game.resourceManager.update = (deltaTime) => {
                const result = originalUpdateResources.call(this.game.resourceManager, deltaTime);
                
                // Record resource changes for AI analysis
                this.recordResourceChanges();
                
                return result;
            };
        }
    }
    
    integrateWithUI() {
        // Add AI debug information to UI
        if (this.game.ui) {
            this.addAIDebugPanel();
        }
    }
    
    setupEventListeners() {
        // Listen for game events that AI should respond to
        this.eventBus.on('unitDestroyed', this.handleUnitDestroyed, this);
        this.eventBus.on('buildingDestroyed', this.handleBuildingDestroyed, this);
        this.eventBus.on('playerAction', this.handlePlayerAction, this);
        this.eventBus.on('gamePhaseChange', this.handleGamePhaseChange, this);
    }
    
    // Event handlers
    recordCombatEvent(attacker, defender, result) {
        if (!this.advancedAI) return;
        
        // Determine if this involves the player
        const playerInvolved = attacker.team === 'player' || defender.team === 'player';
        
        if (playerInvolved) {
            const combatData = {
                attacker: attacker,
                defender: defender,
                result: result,
                timestamp: this.game.gameTime,
                location: { x: defender.x, y: defender.y }
            };
            
            // Record for memory system
            this.advancedAI.memory.recordCombatOutcome(combatData);
            
            // Check for revenge triggers
            if (defender.team !== 'player' && result.defenderDestroyed) {
                this.advancedAI.recordPlayerAggression({
                    type: 'unit_killed',
                    location: combatData.location,
                    unitsLost: 1,
                    strategicValue: this.calculateStrategicValue(defender),
                    playerUnitsInvolved: [attacker]
                });
            }
        }
    }
    
    recordDiplomaticEvent(faction1, faction2, status) {
        if (!this.advancedAI) return;
        
        // Record diplomatic changes in AI memory
        this.advancedAI.memory.storeMemory('diplomatic', `diplomacy_${Date.now()}`, {
            faction1: faction1,
            faction2: faction2,
            newStatus: status,
            timestamp: this.game.gameTime
        }, 'important');
    }
    
    recordResourceChanges() {
        if (!this.advancedAI || !this.advancedAI.memory) return;
        
        // Record significant resource changes for economic analysis
        try {
            const playerResources = this.game.resourceManager?.getResource('spice') || 0;
            
            if (this.advancedAI.memory.storeMemory) {
                this.advancedAI.memory.storeMemory('economic', 'player_resources', {
                    spice: playerResources,
                    timestamp: this.game.gameTime
                }, 'normal');
            }
        } catch (error) {
            // Error recording resource changes - continue silently
        }
    }
    
    handleUnitDestroyed(event) {
        if (!this.advancedAI) return;
        
        const unit = event.unit;
        
        if (unit.team === 'player') {
            // Player lost a unit - potential revenge opportunity
            this.advancedAI.recordPlayerAggression({
                type: 'unit_killed',
                location: { x: unit.x, y: unit.y },
                unitsLost: 1,
                strategicValue: this.calculateStrategicValue(unit)
            });
        } else if (unit.team === this.advancedAI.teamId) {
            // AI lost a unit - record for learning
            this.advancedAI.memory.storeMemory('tactical', `loss_${Date.now()}`, {
                type: 'unit_lost',
                unitType: unit.type,
                location: { x: unit.x, y: unit.y },
                cause: 'combat'
            }, 'important');
        }
    }
    
    handleBuildingDestroyed(event) {
        if (!this.advancedAI) return;
        
        const building = event.building;
        
        if (building.team === 'player') {
            // Player lost a building - major revenge trigger
            this.advancedAI.recordPlayerAggression({
                type: 'building_destroyed',
                location: { x: building.x, y: building.y },
                buildingsLost: 1,
                strategicValue: this.calculateBuildingStrategicValue(building)
            });
        }
    }
    
    handlePlayerAction(event) {
        if (!this.advancedAI) return;
        
        // Record player actions for behavior analysis
        this.advancedAI.memory.recordPlayerAction({
            type: event.actionType,
            location: event.location,
            units: event.units,
            target: event.target,
            context: this.captureActionContext()
        });
    }
    
    handleGamePhaseChange(event) {
        if (!this.advancedAI) return;
        
        // Notify AI of game phase changes
        this.advancedAI.behaviorEngine.updateGamePhase();
    }
    
    // Update method called by game loop
    update(deltaTime) {
        console.log('🤖 AI DEBUG: AIIntegration.update called', {
            isEnabled: this.isEnabled,
            fallbackToBasic: this.fallbackToBasic,
            deltaTime: deltaTime
        });
        
        if (!this.isEnabled || this.fallbackToBasic) {
            console.log('🤖 AI DEBUG: AI Integration disabled or in fallback mode, skipping update');
            return;
        }
        
        const startTime = performance.now();
        
        try {
            // Update performance metrics
            this.updatePerformanceMetrics(deltaTime);
            
            // Update all advanced AI controllers
            let updatedControllers = 0;
            Object.entries(this.game.teams).forEach(([teamId, team]) => {
                if (team.aiController && team.aiController instanceof AdvancedAIController) {
                    console.log(`🤖 AI DEBUG: Updating AdvancedAIController for team ${teamId}`);
                    team.aiController.update(deltaTime);
                    updatedControllers++;
                } else if (team.aiController) {
                    console.log(`🤖 AI DEBUG: Team ${teamId} has basic AI controller, not AdvancedAIController`);
                } else {
                    console.log(`🤖 AI DEBUG: Team ${teamId} has no AI controller`);
                }
            });
            
            console.log(`🤖 AI DEBUG: Updated ${updatedControllers} AI controllers`);
            
            // Update integration systems
            this.updateIntegrationSystems(deltaTime);
            
        } catch (error) {
            console.error('Error in AI Integration update:', error);
            
            // Consider fallback to basic AI on repeated errors
            this.handleUpdateError(error);
        }
        
        // Record update time
        this.performanceMetrics.updateTime = performance.now() - startTime;
    }
    
    updatePerformanceMetrics(deltaTime) {
        this.performanceMetrics.lastUpdate = this.game.gameTime;
        
        // Monitor memory usage (simplified)
        if (this.advancedAI && this.advancedAI.memory) {
            this.performanceMetrics.memoryUsage = this.calculateMemoryUsage();
        }
        
        // Count decisions made
        if (this.advancedAI && this.advancedAI.behaviorEngine) {
            this.performanceMetrics.decisionCount = this.advancedAI.behaviorEngine.decisionFramework.decisionHistory.length;
        }
    }
    
    updateIntegrationSystems(deltaTime) {
        // Update any integration-specific systems
        if (this.settings.debugMode) {
            this.updateDebugSystems(deltaTime);
        }
    }
    
    updateDebugSystems(deltaTime) {
        // Update debug information
        if (this.debugPanel) {
            this.updateDebugPanel();
        }
    }
    
    // Utility methods
    calculateStrategicValue(unit) {
        const values = {
            'harvester': 3,
            'tank': 2,
            'soldier': 1,
            'rocketeer': 2
        };
        
        return values[unit.type] || 1;
    }
    
    calculateBuildingStrategicValue(building) {
        const values = {
            'base': 5,
            'factory': 4,
            'refinery': 3,
            'barracks': 3,
            'turret': 2,
            'powerplant': 2
        };
        
        return values[building.type] || 1;
    }
    
    captureActionContext() {
        return {
            gameTime: this.game.gameTime,
            playerUnits: this.game.teams.player.units.length,
            playerBuildings: this.game.teams.player.buildings.length,
            playerSpice: this.game.resourceManager.getResource('spice'),
            gamePhase: this.determineGamePhase()
        };
    }
    
    determineGamePhase() {
        const gameTime = this.game.gameTime;
        if (gameTime < 300) return 'early';
        if (gameTime < 900) return 'mid';
        return 'late';
    }
    
    calculateMemoryUsage() {
        if (!this.advancedAI || !this.advancedAI.memory || !this.advancedAI.memory.memories) return 0;
        
        let totalMemories = 0;
        try {
            Object.values(this.advancedAI.memory.memories).forEach(categoryMemories => {
                if (categoryMemories && typeof categoryMemories.size === 'number') {
                    totalMemories += categoryMemories.size;
                } else if (categoryMemories && Array.isArray(categoryMemories)) {
                    totalMemories += categoryMemories.length;
                } else if (categoryMemories && typeof categoryMemories === 'object') {
                    totalMemories += Object.keys(categoryMemories).length;
                }
            });
        } catch (error) {
            return 0;
        }
        
        return totalMemories;
    }
    
    handleUpdateError(error) {
        console.error('AI Integration error:', error);
        
        // Could implement error recovery or fallback logic here
        // For now, just log the error
    }
    
    // Debug methods
    addAIDebugPanel() {
        // Add debug panel to UI (simplified)
        this.debugPanel = {
            visible: false,
            data: {}
        };
    }
    
    updateDebugPanel() {
        if (!this.debugPanel || !this.advancedAI) return;
        
        this.debugPanel.data = {
            personality: this.advancedAI.personalityTraits,
            memoryCount: this.calculateMemoryUsage(),
            revengeQueue: this.advancedAI.revengeSystem.revengeQueue.length,
            alertLevel: this.advancedAI.behaviorEngine.coordination.defenseCoordinator.alertLevel,
            currentStrategy: this.advancedAI.strategyAdaptation.currentStrategy,
            performanceMetrics: this.performanceMetrics
        };
    }
    
    // Public interface
    getAIStatus() {
        if (!this.advancedAI) {
            return { enabled: false, fallback: this.fallbackToBasic };
        }
        
        return {
            enabled: this.isEnabled,
            fallback: this.fallbackToBasic,
            personality: this.advancedAI.personalityTraits || {},
            memoryUsage: this.calculateMemoryUsage(),
            performanceMetrics: this.performanceMetrics,
            revengeQueue: this.advancedAI.revengeSystem?.revengeQueue?.length || 0,
            currentStrategy: this.advancedAI.strategyAdaptation?.currentStrategy || 'unknown'
        };
    }
    
    toggleAI(enabled) {
        this.isEnabled = enabled;
    }
    
    toggleDebugMode(enabled) {
        this.settings.debugMode = enabled;
        
        if (enabled && !this.debugPanel) {
            this.addAIDebugPanel();
        }
    }
    
    getDebugInfo() {
        if (!this.advancedAI) return null;
        
        return {
            aiStatus: this.getAIStatus(),
            memorySystem: this.advancedAI.memory.getIntelligenceSummary(),
            behaviorEngine: this.advancedAI.behaviorEngine.decisionFramework.getDecisionSummary(),
            coordination: {
                attackCoordinator: this.advancedAI.behaviorEngine.coordination.attackCoordinator.activeAttacks.length,
                defenseCoordinator: this.advancedAI.behaviorEngine.coordination.defenseCoordinator.alertLevel,
                economicCoordinator: this.advancedAI.behaviorEngine.coordination.economicCoordinator.buildQueue.length,
                scoutingCoordinator: this.advancedAI.behaviorEngine.coordination.scoutingCoordinator.getIntelligenceSummary()
            }
        };
    }
    
    // Cleanup
    destroy() {
        // Clean up event listeners and references
        if (this.eventBus) {
            this.eventBus.off('unitDestroyed', this.handleUnitDestroyed, this);
            this.eventBus.off('buildingDestroyed', this.handleBuildingDestroyed, this);
            this.eventBus.off('playerAction', this.handlePlayerAction, this);
            this.eventBus.off('gamePhaseChange', this.handleGamePhaseChange, this);
        }
        
        this.advancedAI = null;
        this.game = null;
        this.eventBus = null; // Null out eventBus as well
    }
}

// Export factory function for easy integration
export function createAIIntegration(game) {
    return new AIIntegration(game, game.eventBus);
}

// Export configuration helper
export function configureAdvancedAI(settings = {}) {
    return {
        enableAdvancedAI: settings.enableAdvancedAI !== false,
        enableMemorySystem: settings.enableMemorySystem !== false,
        enableBehaviorEngine: settings.enableBehaviorEngine !== false,
        enableRevengeSystem: settings.enableRevengeSystem !== false,
        enableCoordination: settings.enableCoordination !== false,
        debugMode: settings.debugMode === true
    };
}