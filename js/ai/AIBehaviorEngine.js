import { AIStateManager } from './systems/AIStateManager.js'; // Corrected import
import { BehaviorTreeManager } from './BehaviorTreeManager.js'; // Corrected import, assuming AdvancedBehaviorManager was a typo for BehaviorTreeManager
// import { BehaviorTreeManager } from './BehaviorTreeManager.js'; // Original line, now redundant
import { CoordinationManager } from './CoordinationManager.js';
import { DecisionFramework } from './DecisionFramework.js';

// Missing classes - create simple implementations
class StateMachineManager {
    constructor(engine) {
        this.engine = engine;
        this.stateMachines = new Map();
        this.initializeStateMachines();
    }
    
    initializeStateMachines() {
        this.stateMachines.set('early', {
            name: 'early',
            enter: () => {},
            exit: () => {},
            update: () => {}
        });
        this.stateMachines.set('mid', {
            name: 'mid',
            enter: () => {},
            exit: () => {},
            update: () => {}
        });
        this.stateMachines.set('late', {
            name: 'late',
            enter: () => {},
            exit: () => {},
            update: () => {}
        });
    }
    
    getStateMachine(phase) {
        return this.stateMachines.get(phase) || this.stateMachines.get('early');
    }
}

class AdvancedBehaviorManager {
    constructor(engine) {
        this.engine = engine;
        this.behaviors = new Map();
    }
    
    initialize() {
        console.log('AdvancedBehaviorManager initialized');
    }
    
    update(deltaTime) {
        // Update advanced behaviors
    }
}

/**
 * AI Behavior Engine - Main coordinator for advanced AI behaviors
 * Handles complex decision-making, coordination, and advanced AI behaviors
 */
export class AIBehaviorEngine {
    constructor(aiController) {
        this.ai = aiController;
        
        // Core AI systems
        this.behaviorTrees = new BehaviorTreeManager(this);
        this.stateMachines = new StateMachineManager(this);
        this.coordination = new CoordinationManager(this);
        this.decisionFramework = new DecisionFramework(this);
        this.advancedBehaviors = new AdvancedBehaviorManager(this);
        
        // Current active systems
        this.activeBehaviorTree = null;
        this.activeStateMachine = null;
        this.currentGamePhase = 'early';
        
        this.initialize();
    }
    
    initialize() {
        // Set initial behavior tree based on personality
        this.activeBehaviorTree = this.behaviorTrees.getBehaviorTree(this.ai.personality);
        
        // Set initial state machine
        this.activeStateMachine = this.stateMachines.getStateMachine('early');
        
        // Initialize all systems
        this.coordination.initialize();
        this.decisionFramework.initialize();
        this.advancedBehaviors.initialize();
        
        console.log(`AI Behavior Engine initialized with ${this.ai.personality} personality`);
    }
    
    // Main update loop
    update(deltaTime) {
        // Update game phase detection
        this.updateGamePhase();
        
        // Update active behavior tree
        if (this.activeBehaviorTree) {
            this.activeBehaviorTree.update(deltaTime);
        }
        
        // Update active state machine
        if (this.activeStateMachine) {
            this.activeStateMachine.update(deltaTime);
        }
        
        // Update all systems
        this.coordination.update(deltaTime);
        this.decisionFramework.update(deltaTime);
        this.advancedBehaviors.update(deltaTime);
        
        // Process high-priority decisions
        this.processHighPriorityDecisions(deltaTime);
    }
    
    // Update game phase and switch state machines if needed
    updateGamePhase() {
        const gameTime = this.ai.game.gameTime;
        let newPhase = 'early';
        
        if (gameTime > 1200) { // 20 minutes
            newPhase = 'late';
        } else if (gameTime > 600) { // 10 minutes
            newPhase = 'mid';
        }
        
        if (newPhase !== this.currentGamePhase) {
            this.currentGamePhase = newPhase;
            this.switchStateMachine(newPhase);
        }
    }
    
    // Switch to different state machine
    switchStateMachine(phase) {
        if (this.activeStateMachine) {
            this.activeStateMachine.exit();
        }
        
        this.activeStateMachine = this.stateMachines.getStateMachine(phase);
        this.activeStateMachine.enter();
        
        console.log(`AI switched to ${phase} game state machine`);
    }
    
    // Switch behavior tree based on situation
    switchBehaviorTree(personalityType) {
        const newTree = this.behaviorTrees.getBehaviorTree(personalityType);
        if (newTree) {
            this.activeBehaviorTree = newTree;
            console.log(`AI switched to ${personalityType} behavior tree`);
        }
    }
    
    // Process high-priority decisions that override normal behavior
    processHighPriorityDecisions(deltaTime) {
        // Check for emergency situations
        const emergencies = this.decisionFramework.emergencyProtocols.checkEmergencies();
        
        if (emergencies.length > 0) {
            this.handleEmergencies(emergencies);
            return; // Emergency takes precedence
        }
        
        // Check for high-value opportunities
        const opportunities = this.identifyOpportunities();
        
        if (opportunities.length > 0) {
            this.evaluateOpportunities(opportunities);
        }
        
        // Check for revenge opportunities
        this.processRevengeOpportunities();
    }
    
    // Handle emergency situations
    handleEmergencies(emergencies) {
        emergencies.forEach(emergency => {
            switch (emergency.type) {
                case 'base_under_attack':
                    this.coordination.defenseCoordinator.emergencyDefense(emergency.target);
                    break;
                case 'critical_resource_shortage':
                    this.coordination.economicCoordinator.emergencyResourceGathering();
                    break;
                case 'massive_player_assault':
                    this.coordination.defenseCoordinator.coordinateLastStand();
                    break;
                case 'no_combat_units':
                    this.coordination.economicCoordinator.rushMilitaryProduction();
                    break;
            }
        });
    }
    
    // Identify strategic opportunities
    identifyOpportunities() {
        const opportunities = [];
        
        // Vulnerable player units
        const vulnerableUnits = this.findVulnerablePlayerUnits();
        if (vulnerableUnits.length > 0) {
            opportunities.push({
                type: 'vulnerable_units',
                targets: vulnerableUnits,
                priority: 70,
                timeWindow: 60
            });
        }
        
        // Undefended player buildings
        const undefendedBuildings = this.findUndefendedPlayerBuildings();
        if (undefendedBuildings.length > 0) {
            opportunities.push({
                type: 'undefended_buildings',
                targets: undefendedBuildings,
                priority: 80,
                timeWindow: 90
            });
        }
        
        // Economic expansion opportunities
        const expansionSites = this.findExpansionOpportunities();
        if (expansionSites.length > 0) {
            opportunities.push({
                type: 'expansion',
                targets: expansionSites,
                priority: 60,
                timeWindow: 300
            });
        }
        
        return opportunities.sort((a, b) => b.priority - a.priority);
    }
    
    // Evaluate and act on opportunities
    evaluateOpportunities(opportunities) {
        opportunities.forEach(opportunity => {
            const utility = this.decisionFramework.utilitySystem.calculateUtility(opportunity);
            
            if (utility > 0.7) {
                this.executeOpportunity(opportunity);
            }
        });
    }
    
    // Execute an identified opportunity
    executeOpportunity(opportunity) {
        switch (opportunity.type) {
            case 'vulnerable_units':
                this.coordination.attackCoordinator.targetVulnerableUnits(opportunity.targets);
                break;
            case 'undefended_buildings':
                this.coordination.attackCoordinator.raidUndefendedBuildings(opportunity.targets);
                break;
            case 'expansion':
                this.coordination.economicCoordinator.expandToLocation(opportunity.targets[0]);
                break;
        }
    }
    
    // Process revenge opportunities from the revenge system
    processRevengeOpportunities() {
        if (this.ai.revengeSystem.revengeQueue.length > 0) {
            const revengeAction = this.ai.revengeSystem.revengeQueue[0];
            
            if (this.ai.game.gameTime >= revengeAction.plannedTime) {
                this.executeRevengeWithCoordination(revengeAction);
            }
        }
    }
    
    // Execute revenge with proper coordination
    executeRevengeWithCoordination(revengeAction) {
        console.log(`AI executing coordinated revenge: ${revengeAction.revengeType}`);
        
        switch (revengeAction.revengeType) {
            case 'targeted_assassination':
                this.coordination.attackCoordinator.executeAssassination(revengeAction);
                break;
            case 'building_raid':
                this.coordination.attackCoordinator.executeRaid(revengeAction);
                break;
            case 'full_assault':
                this.coordination.attackCoordinator.executeFullAssault(revengeAction);
                break;
            case 'economic_warfare':
                this.coordination.attackCoordinator.executeEconomicWarfare(revengeAction);
                break;
            case 'coordinated_strike':
                this.coordination.attackCoordinator.executeCoordinatedStrike(revengeAction);
                break;
        }
    }
    
    // Utility methods for opportunity identification
    findVulnerablePlayerUnits() {
        return this.ai.game.teams.player.units.filter(unit => {
            // Low health units
            if (unit.health < unit.maxHealth * 0.3) return true;
            
            // Isolated units
            const nearbyAllies = this.ai.game.teams.player.units.filter(ally =>
                ally !== unit && this.distance(unit, ally) < 100
            ).length;
            
            return nearbyAllies === 0;
        });
    }
    
    findUndefendedPlayerBuildings() {
        return this.ai.game.teams.player.buildings.filter(building => {
            const nearbyDefenders = this.ai.game.teams.player.units.filter(unit =>
                unit.damage > 0 && this.distance(unit, building) < 200
            ).length;
            
            return nearbyDefenders < 2;
        });
    }
    
    findExpansionOpportunities() {
        return this.ai.game.spiceFields.filter(field => {
            const nearestAIBuilding = this.findNearestAIBuilding(field);
            const nearestPlayerBuilding = this.findNearestPlayerBuilding(field);
            
            return field.amount > field.maxAmount * 0.5 && 
                   (!nearestPlayerBuilding || 
                    (nearestAIBuilding && 
                     this.distance(field, nearestAIBuilding) < this.distance(field, nearestPlayerBuilding)));
        });
    }
    
    distance(a, b) {
        return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
    }
    
    findNearestAIBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.ai.team.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    findNearestPlayerBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.ai.game.teams.player.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
}