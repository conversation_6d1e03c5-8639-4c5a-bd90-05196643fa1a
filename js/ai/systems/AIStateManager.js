import { EventBus } from '../../core/EventBus.js';

/**
 * AI State Manager
 * Handles AI state tracking, memory management, and personality-driven behavior adaptation
 */
export class AIStateManager {
    constructor(controller) {
        this.controller = controller;
        this.eventBus = controller.game.eventBus;
        
        // AI Personality System
        this.personalityTraits = {
            aggression: 0.5,
            patience: 0.5,
            cunning: 0.5,
            adaptability: 0.5,
            vengefulness: 0.5,
            opportunism: 0.5
        };
        
        // Advanced Memory System
        this.memory = {
            playerActions: [],
            attackHistory: [],
            lossHistory: [],
            strategicEvents: [],
            relationshipHistory: [],
            territoryControl: new Map(),
            resourceDenialAttempts: [],
            successfulStrategies: [],
            failedStrategies: []
        };
        
        // Sophisticated Revenge System
        this.revengeSystem = {
            grievances: [],
            revengeQueue: [],
            escalationLevel: 0,
            lastRevengeTime: 0,
            grudgeDecayRate: 0.1,
            revengeThreshold: 50,
            maxEscalation: 5
        };
        
        // Dynamic Strategy Adaptation
        this.strategyAdaptation = {
            currentStrategy: 'balanced',
            strategyEffectiveness: new Map(),
            playerBehaviorPattern: new Map(),
            counterStrategies: new Map(),
            adaptationCooldown: 0,
            learningRate: 0.1
        };
        
        // Performance Metrics
        this.performanceMetrics = {
            decisionAccuracy: 0.5,
            strategySuccess: 0.5,
            adaptationSpeed: 0.5,
            resourceEfficiency: 0.5,
            combatEffectiveness: 0.5,
            lastEvaluation: 0
        };
        
        // Learning System
        this.learningSystem = {
            playerBehaviorModel: new Map(),
            strategyEffectivenessModel: new Map(),
            adaptationHistory: [],
            learningRate: 0.1,
            explorationRate: 0.2
        };
        
        // Memory decay settings
        this.memoryDecayRate = 0.01;
        this.criticalMemoryThreshold = 0.8;
        
        this.initialize();
    }
    
    initialize() {
        // Generate personality based on faction
        this.generatePersonality();
        
        // Initialize memory systems
        this.initializeMemory();
        
        // Initialize learning systems
        this.initializeLearning();
        
        // Subscribe to events
        this.eventBus.on('game.unit.destroyed', this.recordUnitLoss, this);
        this.eventBus.on('game.building.destroyed', this.recordBuildingLoss, this);
        this.eventBus.on('game.player.action', this.recordPlayerAction, this);
        this.eventBus.on('ai.strategy.changed', this.recordStrategyChange, this);
        this.eventBus.on('ai.revenge.triggered', this.recordRevengeAction, this);
        
        console.log(`AI State Manager initialized with personality:`, this.personalityTraits);
    }
    
    update(deltaTime) {
        // Update memory and learning
        this.updateMemoryAndLearning(deltaTime);
        
        // Handle revenge system
        this.updateRevengeSystem(deltaTime);
        
        // Adapt strategies based on performance
        this.updateStrategyAdaptation(deltaTime);
        
        // Update performance metrics
        this.updatePerformanceMetrics(deltaTime);
        
        // Decay old memories
        this.decayMemories(deltaTime);
    }
    
    generatePersonality() {
        // Generate personality based on faction type and add some randomness
        const basePersonality = this.getBasePersonalityForFaction();
        
        // Add randomness while maintaining faction characteristics
        Object.keys(this.personalityTraits).forEach(trait => {
            const base = basePersonality[trait] || 0.5;
            const variance = 0.2; // ±20% variance
            this.personalityTraits[trait] = Math.max(0, Math.min(1, 
                base + (Math.random() - 0.5) * variance * 2
            ));
        });
        
        // Adjust revenge system based on personality
        this.revengeSystem.revengeThreshold = 100 - (this.personalityTraits.vengefulness * 80);
        this.revengeSystem.escalationLevel = this.personalityTraits.aggression * 2;
        
        // Update controller's personality reference
        this.controller.personalityTraits = this.personalityTraits;
    }
    
    getBasePersonalityForFaction() {
        const factionPersonalities = {
            'harkonnen': {
                aggression: 0.8,
                patience: 0.3,
                cunning: 0.7,
                adaptability: 0.4,
                vengefulness: 0.9,
                opportunism: 0.8
            },
            'atreides': {
                aggression: 0.5,
                patience: 0.7,
                cunning: 0.6,
                adaptability: 0.7,
                vengefulness: 0.4,
                opportunism: 0.5
            },
            'ordos': {
                aggression: 0.6,
                patience: 0.5,
                cunning: 0.9,
                adaptability: 0.8,
                vengefulness: 0.6,
                opportunism: 0.9
            },
            'fremen': {
                aggression: 0.7,
                patience: 0.8,
                cunning: 0.8,
                adaptability: 0.9,
                vengefulness: 0.7,
                opportunism: 0.6
            }
        };
        
        return factionPersonalities[this.controller.teamId] || {
            aggression: 0.5,
            patience: 0.5,
            cunning: 0.5,
            adaptability: 0.5,
            vengefulness: 0.5,
            opportunism: 0.5
        };
    }
    
    initializeMemory() {
        // Initialize memory with current game state
        this.recordInitialGameState();
        
        // Set up memory categories with different retention rates
        this.memoryCategories = {
            critical: { retention: 0.99, maxAge: 3600 }, // 1 hour
            important: { retention: 0.95, maxAge: 1800 }, // 30 minutes
            normal: { retention: 0.90, maxAge: 900 }, // 15 minutes
            trivial: { retention: 0.80, maxAge: 300 } // 5 minutes
        };
    }
    
    initializeLearning() {
        // Initialize learning algorithms
        this.learningSystem.playerBehaviorModel.set('build_order', []);
        this.learningSystem.playerBehaviorModel.set('unit_preferences', new Map());
        this.learningSystem.playerBehaviorModel.set('tactical_patterns', []);
        this.learningSystem.playerBehaviorModel.set('economic_patterns', []);
        
        // Initialize strategy effectiveness tracking
        const strategies = ['aggressive', 'defensive', 'economic', 'balanced', 'cunning'];
        strategies.forEach(strategy => {
            this.learningSystem.strategyEffectivenessModel.set(strategy, {
                successes: 0,
                failures: 0,
                effectiveness: 0.5
            });
        });
    }
    
    recordInitialGameState() {
        const initialState = {
            timestamp: this.controller.game.gameTime,
            playerUnits: this.controller.game.teams.player.units.length,
            playerBuildings: this.controller.game.teams.player.buildings.length,
            aiUnits: this.controller.team.units.length,
            aiBuildings: this.controller.team.buildings.length,
            spiceFields: this.controller.game.spiceFields.length
        };
        
        this.storeMemory('strategic', 'initial_state', initialState, 'critical');
    }
    
    updateMemoryAndLearning(deltaTime) {
        // Record current events
        this.recordCurrentEvents();
        
        // Analyze player behavior patterns
        this.analyzePlayerBehavior();
        
        // Update learning models
        this.updateLearningModels(deltaTime);
    }
    
    recordCurrentEvents() {
        const currentTime = this.controller.game.gameTime;
        
        // Record significant game state changes
        const currentState = this.captureCurrentGameState();
        
        // Compare with last recorded state
        const lastState = this.getLastMemory('strategic', 'game_state');
        if (lastState && this.isSignificantStateChange(lastState.data, currentState)) {
            this.storeMemory('strategic', 'game_state', currentState, 'important');
        }
    }
    
    captureCurrentGameState() {
        return {
            timestamp: this.controller.game.gameTime,
            playerUnits: this.controller.game.teams.player.units.length,
            playerBuildings: this.controller.game.teams.player.buildings.length,
            aiUnits: this.controller.team.units.length,
            aiBuildings: this.controller.team.buildings.length,
            aiSpice: this.controller.team.spice,
            playerSpice: this.controller.game.resourceManager?.getResource('spice') || 0
        };
    }
    
    isSignificantStateChange(oldState, newState) {
        const unitChange = Math.abs(newState.playerUnits - oldState.playerUnits) > 2;
        const buildingChange = Math.abs(newState.playerBuildings - oldState.playerBuildings) > 0;
        const resourceChange = Math.abs(newState.playerSpice - oldState.playerSpice) > 500;
        
        return unitChange || buildingChange || resourceChange;
    }
    
    analyzePlayerBehavior() {
        // Track player patterns
        this.trackPlayerPatterns();
        
        // Identify player weaknesses
        this.identifyPlayerWeaknesses();
        
        // Predict player actions
        this.predictPlayerActions();
        
        // Update counter-strategies
        this.updateCounterStrategies();
    }
    
    trackPlayerPatterns() {
        const playerUnits = this.controller.game.teams.player.units;
        const playerBuildings = this.controller.game.teams.player.buildings;
        
        // Analyze build order patterns
        this.analyzeBuildOrderPatterns(playerBuildings);
        
        // Analyze unit composition preferences
        this.analyzeUnitCompositionPatterns(playerUnits);
        
        // Analyze tactical patterns
        this.analyzeTacticalPatterns(playerUnits);
        
        // Analyze economic patterns
        this.analyzeEconomicPatterns();
    }
    
    analyzeBuildOrderPatterns(buildings) {
        const buildOrder = buildings
            .filter(building => building.constructionProgress >= 100)
            .sort((a, b) => a.constructionStartTime - b.constructionStartTime)
            .map(building => building.type);
        
        this.learningSystem.playerBehaviorModel.get('build_order').push({
            timestamp: this.controller.game.gameTime,
            order: buildOrder
        });
        
        // Keep only recent build orders
        const recentOrders = this.learningSystem.playerBehaviorModel.get('build_order')
            .filter(order => this.controller.game.gameTime - order.timestamp < 1800);
        
        this.learningSystem.playerBehaviorModel.set('build_order', recentOrders);
    }
    
    analyzeUnitCompositionPatterns(units) {
        const composition = new Map();
        units.forEach(unit => {
            composition.set(unit.type, (composition.get(unit.type) || 0) + 1);
        });
        
        this.learningSystem.playerBehaviorModel.set('unit_preferences', composition);
    }
    
    analyzeTacticalPatterns(units) {
        // Analyze how player uses units tactically
        const tacticalData = {
            timestamp: this.controller.game.gameTime,
            averageGroupSize: this.calculateAverageGroupSize(units),
            formationTightness: this.calculateFormationTightness(units),
            aggressionLevel: this.calculatePlayerAggression(units)
        };
        
        this.learningSystem.playerBehaviorModel.get('tactical_patterns').push(tacticalData);
        
        // Keep only recent patterns
        const recentPatterns = this.learningSystem.playerBehaviorModel.get('tactical_patterns')
            .filter(pattern => this.controller.game.gameTime - pattern.timestamp < 600);
        
        this.learningSystem.playerBehaviorModel.set('tactical_patterns', recentPatterns);
    }
    
    analyzeEconomicPatterns() {
        const economicData = {
            timestamp: this.controller.game.gameTime,
            harvesterCount: this.controller.game.teams.player.units.filter(u => u.type === 'harvester').length,
            refineryCount: this.controller.game.teams.player.buildings.filter(b => b.type === 'refinery').length,
            spiceIncome: this.estimatePlayerSpiceIncome(),
            expansionRate: this.calculatePlayerExpansionRate()
        };
        
        this.learningSystem.playerBehaviorModel.get('economic_patterns').push(economicData);
        
        // Keep only recent patterns
        const recentPatterns = this.learningSystem.playerBehaviorModel.get('economic_patterns')
            .filter(pattern => this.controller.game.gameTime - pattern.timestamp < 900);
        
        this.learningSystem.playerBehaviorModel.set('economic_patterns', recentPatterns);
    }
    
    updateLearningModels(deltaTime) {
        // Update strategy effectiveness based on recent performance
        this.updateStrategyEffectiveness();
        
        // Adapt learning rate based on performance
        this.adaptLearningRate();
        
        // Update player behavior predictions
        this.updatePlayerBehaviorPredictions();
    }
    
    updateStrategyEffectiveness() {
        const currentStrategy = this.strategyAdaptation.currentStrategy;
        const recentPerformance = this.calculateRecentPerformance();
        
        const strategyData = this.learningSystem.strategyEffectivenessModel.get(currentStrategy);
        if (strategyData) {
            if (recentPerformance > 0.6) {
                strategyData.successes++;
            } else if (recentPerformance < 0.4) {
                strategyData.failures++;
            }
            
            // Update effectiveness using exponential moving average
            const alpha = this.learningSystem.learningRate;
            strategyData.effectiveness = (1 - alpha) * strategyData.effectiveness + alpha * recentPerformance;
        }
    }
    
    calculateRecentPerformance() {
        // Calculate performance based on recent metrics
        const metrics = this.performanceMetrics;
        return (metrics.decisionAccuracy + metrics.strategySuccess + metrics.combatEffectiveness) / 3;
    }
    
    // Revenge System Methods
    updateRevengeSystem(deltaTime) {
        // Process grievances
        this.processGrievances(deltaTime);
        
        // Execute revenge if conditions are met
        this.executeRevenge(deltaTime);
        
        // Decay grudges over time
        this.decayGrudges(deltaTime);
    }
    
    recordPlayerAggression(aggressionData) {
        const grievance = {
            type: aggressionData.type,
            severity: this.calculateGrievanceSeverity(aggressionData),
            timestamp: this.controller.game.gameTime,
            location: aggressionData.location,
            unitsLost: aggressionData.unitsLost || 0,
            buildingsLost: aggressionData.buildingsLost || 0,
            strategicValue: aggressionData.strategicValue || 0,
            playerUnitsInvolved: aggressionData.playerUnitsInvolved || [],
            witnessed: aggressionData.witnessed || false
        };
        
        this.revengeSystem.grievances.push(grievance);
        this.memory.attackHistory.push(grievance);
        
        // Immediate emotional response based on personality
        if (this.personalityTraits.vengefulness > 0.6) {
            this.revengeSystem.escalationLevel += grievance.severity * 0.1;
        }
        
        // Emit revenge trigger event
        this.eventBus.emit('ai.revenge.triggered', grievance);
        
        console.log(`AI recorded grievance: ${grievance.type} with severity ${grievance.severity}`);
    }
    
    calculateGrievanceSeverity(aggressionData) {
        let severity = 0;
        
        // Base severity by type
        const baseSeverity = {
            'unit_killed': 10,
            'building_destroyed': 25,
            'base_attacked': 50,
            'resource_denied': 15,
            'territory_invaded': 20,
            'ally_betrayed': 75,
            'civilian_killed': 30
        };
        
        severity += baseSeverity[aggressionData.type] || 10;
        
        // Multiply by strategic value
        severity *= (1 + (aggressionData.strategicValue || 0));
        
        // Personality modifiers
        severity *= (1 + this.personalityTraits.vengefulness * 0.5);
        
        // Recent aggression multiplier (escalation)
        const recentGrievances = this.revengeSystem.grievances.filter(g => 
            this.controller.game.gameTime - g.timestamp < 300 // Last 5 minutes
        ).length;
        severity *= (1 + recentGrievances * 0.1);
        
        return Math.floor(severity);
    }
    
    processGrievances(deltaTime) {
        this.revengeSystem.grievances.forEach(grievance => {
            const priority = this.calculateRevengePriority(grievance);
            
            if (priority > this.revengeSystem.revengeThreshold) {
                this.planSpecificRevenge(grievance);
            }
        });
    }
    
    calculateRevengePriority(grievance) {
        let priority = grievance.severity;
        
        // Time factor - recent grievances have higher priority
        const timeSince = this.controller.game.gameTime - grievance.timestamp;
        const timeFactor = Math.max(0.1, 1 - (timeSince / 1800)); // Decay over 30 minutes
        priority *= timeFactor;
        
        // Escalation factor
        priority *= (1 + this.revengeSystem.escalationLevel * 0.2);
        
        // Strategic importance
        if (grievance.type === 'base_attacked' || grievance.type === 'ally_betrayed') {
            priority *= 2;
        }
        
        // Personality influence
        priority *= (0.5 + this.personalityTraits.vengefulness);
        
        return priority;
    }
    
    planSpecificRevenge(grievance) {
        const revengeAction = {
            id: `revenge_${Date.now()}`,
            targetGrievance: grievance,
            revengeType: this.selectRevengeType(grievance),
            intensity: this.calculateRevengeIntensity(grievance),
            plannedTime: this.controller.game.gameTime + this.calculateRevengeDelay(),
            executed: false
        };
        
        this.revengeSystem.revengeQueue.push(revengeAction);
        
        console.log(`AI planned revenge: ${revengeAction.revengeType} for ${grievance.type}`);
    }
    
    selectRevengeType(grievance) {
        const revengeTypes = {
            'unit_killed': ['targeted_assassination', 'unit_harassment', 'ambush'],
            'building_destroyed': ['building_raid', 'infrastructure_attack', 'economic_warfare'],
            'base_attacked': ['full_assault', 'siege', 'coordinated_strike'],
            'resource_denied': ['resource_raid', 'harvester_hunt', 'economic_sabotage'],
            'territory_invaded': ['counter_invasion', 'territorial_reclaim', 'border_skirmish'],
            'ally_betrayed': ['total_war', 'alliance_destruction', 'diplomatic_isolation']
        };
        
        const availableTypes = revengeTypes[grievance.type] || ['general_attack'];
        
        // Select based on personality and current capabilities
        if (this.personalityTraits.cunning > 0.7) {
            return availableTypes.find(type => type.includes('sabotage') || type.includes('assassination')) || availableTypes[0];
        } else if (this.personalityTraits.aggression > 0.7) {
            return availableTypes.find(type => type.includes('assault') || type.includes('war')) || availableTypes[0];
        }
        
        return availableTypes[Math.floor(Math.random() * availableTypes.length)];
    }
    
    calculateRevengeIntensity(grievance) {
        let intensity = grievance.severity / 20; // Base intensity 0-5
        
        // Escalation increases intensity
        intensity += this.revengeSystem.escalationLevel * 0.3;
        
        // Personality modifiers
        intensity *= (0.5 + this.personalityTraits.vengefulness * 0.5);
        intensity *= (0.8 + this.personalityTraits.aggression * 0.4);
        
        // Cap at maximum
        return Math.min(5, Math.max(1, intensity));
    }
    
    calculateRevengeDelay() {
        // Immediate revenge for high vengefulness, delayed for high patience
        const baseDelay = 60; // 1 minute base
        const personalityDelay = baseDelay * (2 - this.personalityTraits.vengefulness) * this.personalityTraits.patience;
        
        // Add some randomness
        return personalityDelay + (Math.random() * 120);
    }
    
    executeRevenge(deltaTime) {
        const currentTime = this.controller.game.gameTime;
        
        this.revengeSystem.revengeQueue.forEach((revengeAction, index) => {
            if (!revengeAction.executed && currentTime >= revengeAction.plannedTime) {
                this.eventBus.emit('ai.revenge.execute', revengeAction);
                revengeAction.executed = true;
                this.revengeSystem.lastRevengeTime = currentTime;
                
                // Increase escalation
                this.revengeSystem.escalationLevel = Math.min(
                    this.revengeSystem.maxEscalation,
                    this.revengeSystem.escalationLevel + 0.5
                );
            }
        });
        
        // Clean up executed revenge actions
        this.revengeSystem.revengeQueue = this.revengeSystem.revengeQueue.filter(action => !action.executed);
    }
    
    decayGrudges(deltaTime) {
        // Gradually reduce escalation level and remove old grievances
        this.revengeSystem.escalationLevel = Math.max(0, 
            this.revengeSystem.escalationLevel - this.revengeSystem.grudgeDecayRate * deltaTime
        );
        
        // Remove old grievances based on personality
        const maxGrudgeAge = 1800 * (1 + this.personalityTraits.vengefulness); // 30 minutes base
        this.revengeSystem.grievances = this.revengeSystem.grievances.filter(grievance =>
            this.controller.game.gameTime - grievance.timestamp < maxGrudgeAge
        );
    }
    
    // Strategy Adaptation Methods
    updateStrategyAdaptation(deltaTime) {
        // Evaluate current strategy effectiveness
        this.evaluateStrategyEffectiveness();
        
        // Adapt to player behavior changes
        this.adaptToPlayerBehavior();
        
        // Switch strategies if needed
        this.considerStrategySwitch(deltaTime);
    }
    
    evaluateStrategyEffectiveness() {
        const currentStrategy = this.strategyAdaptation.currentStrategy;
        const effectiveness = this.calculateStrategyEffectiveness(currentStrategy);
        
        this.strategyAdaptation.strategyEffectiveness.set(currentStrategy, effectiveness);
    }
    
    calculateStrategyEffectiveness(strategy) {
        // Calculate effectiveness based on recent performance and outcomes
        const recentPerformance = this.calculateRecentPerformance();
        const strategyData = this.learningSystem.strategyEffectivenessModel.get(strategy);
        
        if (strategyData) {
            const totalAttempts = strategyData.successes + strategyData.failures;
            if (totalAttempts > 0) {
                return strategyData.successes / totalAttempts;
            }
        }
        
        return recentPerformance;
    }
    
    adaptToPlayerBehavior() {
        // Analyze recent player behavior and adapt accordingly
        const recentPatterns = this.getRecentPlayerPatterns();
        
        // Update counter-strategies based on patterns
        this.updateCounterStrategiesFromPatterns(recentPatterns);
    }
    
    getRecentPlayerPatterns() {
        return {
            tactical: this.learningSystem.playerBehaviorModel.get('tactical_patterns').slice(-3),
            economic: this.learningSystem.playerBehaviorModel.get('economic_patterns').slice(-3),
            unitPreferences: this.learningSystem.playerBehaviorModel.get('unit_preferences')
        };
    }
    
    updateCounterStrategiesFromPatterns(patterns) {
        // Update counter-strategies based on observed patterns
        if (patterns.tactical.length > 0) {
            const avgAggression = patterns.tactical.reduce((sum, p) => sum + p.aggressionLevel, 0) / patterns.tactical.length;
            
            if (avgAggression > 0.7) {
                this.strategyAdaptation.counterStrategies.set('player_aggressive', 'defensive_counter');
            } else if (avgAggression < 0.3) {
                this.strategyAdaptation.counterStrategies.set('player_passive', 'aggressive_pressure');
            }
        }
    }
    
    considerStrategySwitch(deltaTime) {
        if (this.strategyAdaptation.adaptationCooldown > 0) {
            this.strategyAdaptation.adaptationCooldown -= deltaTime;
            return;
        }
        
        const currentEffectiveness = this.strategyAdaptation.strategyEffectiveness.get(this.strategyAdaptation.currentStrategy) || 0.5;
        
        if (currentEffectiveness < 0.3) {
            // Current strategy is failing, consider switching
            const bestStrategy = this.findBestAlternativeStrategy();
            if (bestStrategy && bestStrategy !== this.strategyAdaptation.currentStrategy) {
                this.switchStrategy(bestStrategy);
            }
        }
    }
    
    findBestAlternativeStrategy() {
        let bestStrategy = null;
        let bestEffectiveness = 0;
        
        this.strategyAdaptation.strategyEffectiveness.forEach((effectiveness, strategy) => {
            if (strategy !== this.strategyAdaptation.currentStrategy && effectiveness > bestEffectiveness) {
                bestEffectiveness = effectiveness;
                bestStrategy = strategy;
            }
        });
        
        return bestStrategy;
    }
    
    switchStrategy(newStrategy) {
        const oldStrategy = this.strategyAdaptation.currentStrategy;
        this.strategyAdaptation.currentStrategy = newStrategy;
        this.strategyAdaptation.adaptationCooldown = 300; // 5 minute cooldown
        
        // Record strategy change
        this.storeMemory('strategic', 'strategy_change', {
            from: oldStrategy,
            to: newStrategy,
            reason: 'poor_performance'
        }, 'important');
        
        // Emit strategy change event
        this.eventBus.emit('ai.strategy.changed', {
            oldStrategy,
            newStrategy,
            effectiveness: this.strategyAdaptation.strategyEffectiveness.get(newStrategy)
        });
        
        console.log(`AI switched strategy from ${oldStrategy} to ${newStrategy}`);
    }
    
    // Performance Metrics
    updatePerformanceMetrics(deltaTime) {
        if (this.controller.game.gameTime - this.performanceMetrics.lastEvaluation >= 60) {
            this.evaluatePerformanceMetrics();
            this.performanceMetrics.lastEvaluation = this.controller.game.gameTime;
        }
    }
    
    evaluatePerformanceMetrics() {
        // Calculate various performance metrics
        this.performanceMetrics.decisionAccuracy = this.calculateDecisionAccuracy();
        this.performanceMetrics.strategySuccess = this.calculateStrategySuccess();
        this.performanceMetrics.adaptationSpeed = this.calculateAdaptationSpeed();
        this.performanceMetrics.resourceEfficiency = this.calculateResourceEfficiency();
        this.performanceMetrics.combatEffectiveness = this.calculateCombatEffectiveness();
    }
    
    calculateDecisionAccuracy() {
        // Placeholder implementation
        return 0.7;
    }
    
    calculateStrategySuccess() {
        const currentStrategy = this.strategyAdaptation.currentStrategy;
        return this.strategyAdaptation.strategyEffectiveness.get(currentStrategy) || 0.5;
    }
    
    calculateAdaptationSpeed() {
        // Measure how quickly AI adapts to changes
        return Math.min(1, this.learningSystem.learningRate * 10);
    }
    
    calculateResourceEfficiency() {
        // Measure resource usage efficiency
        const spiceIncome = this.controller.team.units.filter(u => u.type === 'harvester').length * 2;
        const spiceUsage = this.controller.team.units.length * 0.5; // Approximate usage
        
        return spiceIncome > 0 ? Math.min(1, spiceIncome / Math.max(1, spiceUsage)) : 0.5;
    }
    
    calculateCombatEffectiveness() {
        // Measure combat performance
        const recentCombatEvents = this.memory.attackHistory.filter(event =>
            this.controller.game.gameTime - event.timestamp < 300
        );
        
        if (recentCombatEvents.length === 0) return 0.5;
        
        const successfulAttacks = recentCombatEvents.filter(event => event.success).length;
        return successfulAttacks / recentCombatEvents.length;
    }
    
    // Memory Management
    storeMemory(category, key, data, importance = 'normal') {
        const memory = {
            category,
            key,
            data,
            importance,
            timestamp: this.controller.game.gameTime,
            accessCount: 0
        };
        
        if (!this.memory[category]) {
            this.memory[category] = [];
        }
        
        this.memory[category].push(memory);
    }
    
    getMemory(category, key) {
        if (!this.memory[category]) return null;
        
        const memory = this.memory[category].find(m => m.key === key);
        if (memory) {
            memory.accessCount++;
            return memory;
        }
        
        return null;
    }
    
    getLastMemory(category, keyPrefix) {
        if (!this.memory[category]) return null;
        
        const memories = this.memory[category]
            .filter(m => m.key.startsWith(keyPrefix))
            .sort((a, b) => b.timestamp - a.timestamp);
        
        return memories[0] || null;
    }
    
    decayMemories(deltaTime) {
        Object.keys(this.memory).forEach(category => {
            if (Array.isArray(this.memory[category])) {
                this.memory[category] = this.memory[category].filter(memory => {
                    const age = this.controller.game.gameTime - memory.timestamp;
                    const categorySettings = this.memoryCategories[memory.importance] || this.memoryCategories.normal;
                    
                    // Keep memory if it's not too old or if it's been accessed frequently
                    return age < categorySettings.maxAge || memory.accessCount > 3;
                });
            }
        });
    }
    
    // Event handlers
    recordUnitLoss(event) {
        if (event.unit.teamId === this.controller.teamId) {
            this.memory.lossHistory.push({
                type: 'unit_lost',
                unitType: event.unit.type,
                timestamp: this.controller.game.gameTime,
                location: { x: event.unit.x, y: event.unit.y },
                cause: event.cause
            });
        }
    }
    
    recordBuildingLoss(event) {
        if (event.building.teamId === this.controller.teamId) {
            this.memory.lossHistory.push({
                type: 'building_lost',
                buildingType: event.building.type,
                timestamp: this.controller.game.gameTime,
                location: { x: event.building.x, y: event.building.y },
                cause: event.cause
            });
        }
    }
    
    recordPlayerAction(event) {
        this.memory.playerActions.push({
            action: event.action,
            timestamp: this.controller.game.gameTime,
            data: event.data
        });
        
        // Keep only recent actions
        this.memory.playerActions = this.memory.playerActions.filter(action =>
            this.controller.game.gameTime - action.timestamp < 600
        );
    }
    
    recordStrategyChange(event) {
        this.memory.strategicEvents.push({
            type: 'strategy_change',
            from: event.oldStrategy,
            to: event.newStrategy,
            timestamp: this.controller.game.gameTime,
            effectiveness: event.effectiveness
        });
    }
    
    recordRevengeAction(event) {
        this.memory.strategicEvents.push({
            type: 'revenge_action',
            revengeType: event.revengeType,
            targetGrievance: event.targetGrievance,
            timestamp: this.controller.game.gameTime
        });
    }
    
    // Utility methods
    calculateAverageGroupSize(units) {
        // Placeholder implementation
        return units.length > 0 ? Math.max(1, units.length / 3) : 1;
    }
    
    calculateFormationTightness(units) {
        // Placeholder implementation
        return 0.5;
    }
    
    calculatePlayerAggression(units) {
        const attackingUnits = units.filter(unit => unit.state === 'attacking').length;
        return units.length > 0 ? attackingUnits / units.length : 0;
    }
    
    estimatePlayerSpiceIncome() {
        const harvesters = this.controller.game.teams.player.units.filter(u => u.type === 'harvester').length;
        return harvesters * 2; // Approximate income per harvester
    }
    
    calculatePlayerExpansionRate() {
        // Placeholder implementation
        return 0.1;
    }
    
    adaptLearningRate() {
        // Adjust learning rate based on performance
        const performance = this.calculateRecentPerformance();
        
        if (performance < 0.4) {
            // Poor performance, increase learning rate
            this.learningSystem.learningRate = Math.min(0.3, this.learningSystem.learningRate * 1.1);
        } else if (performance > 0.7) {
            // Good performance, decrease learning rate
            this.learningSystem.learningRate = Math.max(0.05, this.learningSystem.learningRate * 0.95);
        }
    }
    
    updatePlayerBehaviorPredictions() {
        // Update predictions based on learned patterns
        // Implementation would analyze patterns and make predictions
    }
    
    identifyPlayerWeaknesses() {
        const weaknesses = [];
        
        // Check for economic vulnerabilities
        if (this.isPlayerEconomyVulnerable()) {
            weaknesses.push('economy');
        }
        
        // Check for military weaknesses
        if (this.isPlayerMilitaryWeak()) {
            weaknesses.push('military');
        }
        
        // Check for defensive gaps
        if (this.hasPlayerDefensiveGaps()) {
            weaknesses.push('defense');
        }
        
        this.memory.playerWeaknesses = weaknesses;
        return weaknesses;
    }
    
    predictPlayerActions() {
        // Predict likely player actions based on patterns
        // Implementation would use learned patterns to make predictions
    }
    
    updateCounterStrategies() {
        // Update counter-strategies based on player behavior
        // Implementation would adapt strategies to counter player patterns
    }
    
    isPlayerEconomyVulnerable() {
        const harvesters = this.controller.game.teams.player.units.filter(u => u.type === 'harvester').length;
        const refineries = this.controller.game.teams.player.buildings.filter(b => b.type === 'refinery').length;
        
        return harvesters < 3 || refineries < 2;
    }
    
    isPlayerMilitaryWeak() {
        const combatUnits = this.controller.game.teams.player.units.filter(u => u.damage > 0).length;
        const aiCombatUnits = this.controller.team.units.filter(u => u.damage > 0).length;
        
        return combatUnits < aiCombatUnits * 0.8;
    }
    
    hasPlayerDefensiveGaps() {
        const playerBuildings = this.controller.game.teams.player.buildings;
        const turrets = playerBuildings.filter(b => b.type === 'turret').length;
        
        return turrets < playerBuildings.length * 0.2;
    }
    
    // Export/Import for save/load
    exportState() {
        return {
            personalityTraits: this.personalityTraits,
            memory: this.memory,
            revengeSystem: this.revengeSystem,
            strategyAdaptation: this.strategyAdaptation,
            performanceMetrics: this.performanceMetrics,
            learningSystem: {
                playerBehaviorModel: Object.fromEntries(this.learningSystem.playerBehaviorModel),
                strategyEffectivenessModel: Object.fromEntries(this.learningSystem.strategyEffectivenessModel),
                adaptationHistory: this.learningSystem.adaptationHistory,
                learningRate: this.learningSystem.learningRate,
                explorationRate: this.learningSystem.explorationRate
            }
        };
    }
    
    importState(state) {
        if (state.personalityTraits) {
            this.personalityTraits = state.personalityTraits;
        }
        if (state.memory) {
            this.memory = state.memory;
        }
        if (state.revengeSystem) {
            this.revengeSystem = state.revengeSystem;
        }
        if (state.strategyAdaptation) {
            this.strategyAdaptation = state.strategyAdaptation;
        }
        if (state.performanceMetrics) {
            this.performanceMetrics = state.performanceMetrics;
        }
        if (state.learningSystem) {
            this.learningSystem.playerBehaviorModel = new Map(Object.entries(state.learningSystem.playerBehaviorModel || {}));
            this.learningSystem.strategyEffectivenessModel = new Map(Object.entries(state.learningSystem.strategyEffectivenessModel || {}));
            this.learningSystem.adaptationHistory = state.learningSystem.adaptationHistory || [];
            this.learningSystem.learningRate = state.learningSystem.learningRate || 0.1;
            this.learningSystem.explorationRate = state.learningSystem.explorationRate || 0.2;
        }
    }
}