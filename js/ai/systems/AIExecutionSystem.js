import { EventBus } from '../../core/EventBus.js';
import { distance } from '../../utils/index.js';

/**
 * AI Execution System
 * Handles behavior execution, action management, and coordinated AI operations
 */
export class AIExecutionSystem {
    constructor(controller) {
        this.controller = controller;
        this.eventBus = controller.game.eventBus;
        
        // Execution state
        this.activeActions = new Map();
        this.actionQueue = [];
        this.coordinatedAttacks = [];
        this.combatFormations = new Map();
        
        // Combat coordination
        this.attackGroups = new Map();
        this.defensivePositions = [];
        this.retreatRoutes = [];
        this.rallyPoints = [];
        
        // Deception and tactics
        this.activeDeceptions = new Map();
        this.feintAttacks = [];
        this.fakeRetreats = [];
        
        this.initialize();
    }
    
    initialize() {
        // Subscribe to decision events
        this.eventBus.on('ai.decision.made', this.handleDecision, this);
        this.eventBus.on('ai.attack.execute', this.executeAttack, this);
        this.eventBus.on('ai.defense.strengthen', this.strengthenDefenses, this);
        this.eventBus.on('ai.economy.expand', this.expandEconomy, this);
        this.eventBus.on('ai.military.buildup', this.buildupMilitary, this);
        
        // Subscribe to coordination events
        this.eventBus.on('ai.coordinate.attack', this.coordinateAttack, this);
        this.eventBus.on('ai.coordinate.defense', this.coordinateDefense, this);
        this.eventBus.on('ai.execute.deception', this.executeDeception, this);
        
        console.log('AI Execution System initialized');
    }
    
    update(deltaTime) {
        // Process action queue
        this.processActionQueue(deltaTime);
        
        // Update active actions
        this.updateActiveActions(deltaTime);
        
        // Update coordinated attacks
        this.updateCoordinatedAttacks(deltaTime);
        
        // Update deception tactics
        this.updateDeceptionTactics(deltaTime);
        
        // Execute periodic coordination
        this.executePeriodicCoordination(deltaTime);
    }
    
    handleDecision(decision) {
        // Convert decisions into executable actions
        const action = this.createActionFromDecision(decision);
        if (action) {
            this.queueAction(action);
        }
    }
    
    createActionFromDecision(decision) {
        switch (decision.type) {
            case 'tactical_attack':
                return {
                    type: 'execute_attack',
                    priority: decision.priority,
                    data: decision.data,
                    duration: 60
                };
            case 'strengthen_defenses':
                return {
                    type: 'strengthen_defenses',
                    priority: decision.priority,
                    data: decision.data,
                    duration: 30
                };
            case 'expand_economy':
                return {
                    type: 'expand_economy',
                    priority: decision.priority,
                    data: decision.data,
                    duration: 120
                };
            case 'military_buildup':
                return {
                    type: 'buildup_military',
                    priority: decision.priority,
                    data: decision.data,
                    duration: 90
                };
            default:
                return null;
        }
    }
    
    queueAction(action) {
        action.id = `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        action.timestamp = this.controller.game.gameTime;
        action.status = 'queued';
        
        this.actionQueue.push(action);
        
        // Sort by priority
        this.actionQueue.sort((a, b) => b.priority - a.priority);
    }
    
    processActionQueue(deltaTime) {
        if (this.actionQueue.length === 0) return;
        
        // Process highest priority action
        const action = this.actionQueue.shift();
        this.executeAction(action);
    }
    
    executeAction(action) {
        console.log(`AI executing action: ${action.type}`);
        
        action.status = 'executing';
        action.startTime = this.controller.game.gameTime;
        this.activeActions.set(action.id, action);
        
        // Execute based on action type
        switch (action.type) {
            case 'execute_attack':
                this.performAttack(action);
                break;
            case 'strengthen_defenses':
                this.performDefenseStrengthening(action);
                break;
            case 'expand_economy':
                this.performEconomicExpansion(action);
                break;
            case 'buildup_military':
                this.performMilitaryBuildup(action);
                break;
            case 'coordinate_assault':
                this.performCoordinatedAssault(action);
                break;
            case 'execute_deception':
                this.performDeception(action);
                break;
        }
        
        // Emit action started event
        this.eventBus.emit('ai.action.started', action);
    }
    
    updateActiveActions(deltaTime) {
        const currentTime = this.controller.game.gameTime;
        
        this.activeActions.forEach((action, actionId) => {
            const elapsed = currentTime - action.startTime;
            
            // Update action progress
            action.progress = Math.min(1, elapsed / action.duration);
            
            // Check if action is complete
            if (elapsed >= action.duration) {
                this.completeAction(action);
            } else {
                // Update ongoing action
                this.updateOngoingAction(action, deltaTime);
            }
        });
    }
    
    completeAction(action) {
        action.status = 'completed';
        action.endTime = this.controller.game.gameTime;
        
        console.log(`AI completed action: ${action.type}`);
        
        // Remove from active actions
        this.activeActions.delete(action.id);
        
        // Emit completion event
        this.eventBus.emit('ai.action.completed', action);
        
        // Handle action-specific completion logic
        this.handleActionCompletion(action);
    }
    
    handleActionCompletion(action) {
        switch (action.type) {
            case 'execute_attack':
                this.finalizeAttack(action);
                break;
            case 'coordinate_assault':
                this.finalizeCoordinatedAssault(action);
                break;
            case 'execute_deception':
                this.finalizeDeception(action);
                break;
        }
    }
    
    // Attack execution methods
    performAttack(action) {
        const attackData = action.data;
        
        if (attackData.type === 'vulnerable_units') {
            this.executeTargetedAssault(attackData.targets);
        } else if (attackData.type === 'undefended_buildings') {
            this.executeBuildingRaid(attackData.targets);
        } else if (attackData.type === 'economic_targets') {
            this.executeEconomicWarfare(attackData.targets);
        }
    }
    
    executeTargetedAssault(targets) {
        const availableUnits = this.getAvailableCombatUnits();
        const unitsPerTarget = Math.max(1, Math.floor(availableUnits.length / targets.length));
        
        targets.forEach((target, index) => {
            const assignedUnits = availableUnits.slice(index * unitsPerTarget, (index + 1) * unitsPerTarget);
            
            assignedUnits.forEach(unit => {
                unit.attackTarget = target;
                unit.state = 'attacking';
                unit.priority = 'assault';
            });
        });
    }
    
    executeBuildingRaid(targets) {
        const raiders = this.selectRaidUnits();
        
        if (raiders.length > 0) {
            this.coordinateMultiTargetAttack(raiders, targets);
            
            // Notify UI
            this.controller.game.ui?.showNotification('Enemy raid incoming!', 'warning');
        }
    }
    
    executeEconomicWarfare(targets) {
        const economicRaiders = this.selectFastAttackUnits();
        
        if (economicRaiders.length > 0) {
            this.executeHitAndRunTactics(economicRaiders, targets);
            
            // Notify UI
            this.controller.game.ui?.showNotification('Your economy is under attack!', 'warning');
        }
    }
    
    // Defense execution methods
    performDefenseStrengthening(action) {
        const defenseData = action.data;
        
        // Position defensive units
        this.positionDefensiveUnits(defenseData.vulnerableBuildings);
        
        // Request defensive buildings
        this.requestDefensiveBuildings(defenseData.defenseGaps);
        
        // Establish rally points
        this.establishRallyPoints();
    }
    
    positionDefensiveUnits(vulnerableBuildings) {
        const availableDefenders = this.getAvailableDefensiveUnits();
        
        vulnerableBuildings.forEach(building => {
            const nearbyDefenders = availableDefenders.filter(unit =>
                distance(unit, building) < 300
            ).slice(0, 2);
            
            nearbyDefenders.forEach(defender => {
                const defensivePosition = this.calculateDefensivePosition(building, defender);
                defender.targetX = defensivePosition.x;
                defender.targetY = defensivePosition.y;
                defender.state = 'defending';
                defender.defendTarget = building;
            });
        });
    }
    
    // Economic expansion execution
    performEconomicExpansion(action) {
        const expansionData = action.data;
        
        // Identify best expansion sites
        const expansionSites = this.selectExpansionSites(expansionData);
        
        // Deploy harvesters to new sites
        this.deployHarvestersToSites(expansionSites);
        
        // Request supporting infrastructure
        this.requestSupportingInfrastructure(expansionSites);
    }
    
    // Military buildup execution
    performMilitaryBuildup(action) {
        const buildupData = action.data;
        
        // Prioritize unit production
        this.prioritizeUnitProduction(buildupData.threatLevel);
        
        // Request military buildings
        this.requestMilitaryBuildings();
        
        // Organize military formations
        this.organizeMilitaryFormations();
    }
    
    // Coordinated assault execution
    performCoordinatedAssault(action) {
        const assaultData = action.data;
        
        // Create attack groups
        const attackGroups = this.createAttackGroups(assaultData.intensity);
        
        // Assign targets to groups
        this.assignTargetsToGroups(attackGroups, assaultData.targets);
        
        // Execute timed attacks
        this.executeTimedAttacks(attackGroups);
    }
    
    createAttackGroups(intensity) {
        const allCombatUnits = this.getAvailableCombatUnits();
        const groupSize = Math.max(2, Math.floor(allCombatUnits.length / intensity));
        const groups = [];
        
        for (let i = 0; i < intensity && i * groupSize < allCombatUnits.length; i++) {
            const group = {
                id: `group_${i}`,
                units: allCombatUnits.slice(i * groupSize, (i + 1) * groupSize),
                target: null,
                attackTime: this.controller.game.gameTime + (i * 30) // Stagger attacks
            };
            groups.push(group);
        }
        
        return groups;
    }
    
    executeTimedAttacks(attackGroups) {
        attackGroups.forEach(group => {
            this.coordinatedAttacks.push(group);
        });
    }
    
    updateCoordinatedAttacks(deltaTime) {
        const currentTime = this.controller.game.gameTime;
        
        this.coordinatedAttacks = this.coordinatedAttacks.filter(attack => {
            if (currentTime >= attack.attackTime && !attack.executed) {
                this.executeGroupAttack(attack);
                attack.executed = true;
                return false; // Remove from array
            }
            return true;
        });
    }
    
    executeGroupAttack(group) {
        if (!group.target) return;
        
        group.units.forEach(unit => {
            unit.attackTarget = group.target;
            unit.state = 'attacking';
            unit.groupId = group.id;
        });
        
        console.log(`AI executing coordinated attack with group ${group.id}`);
    }
    
    // Deception execution
    performDeception(action) {
        const deceptionData = action.data;
        
        switch (deceptionData.type) {
            case 'fake_retreat':
                this.executeFakeRetreat(deceptionData);
                break;
            case 'feint_attack':
                this.executeFeintAttack(deceptionData);
                break;
            case 'false_buildup':
                this.executeFalseBuildup(deceptionData);
                break;
        }
    }
    
    executeFakeRetreat(deceptionData) {
        const retreatingUnits = this.controller.team.units
            .filter(unit => unit.damage > 0 && unit.health > unit.maxHealth * 0.3)
            .slice(0, 3);
        
        retreatingUnits.forEach(unit => {
            const retreatDirection = this.calculateRetreatDirection(unit);
            unit.fakeRetreat = {
                active: true,
                phase: 'retreating',
                originalTarget: unit.attackTarget,
                returnTime: this.controller.game.gameTime + 60 + Math.random() * 60
            };
            
            unit.targetX = unit.x + retreatDirection.x * 200;
            unit.targetY = unit.y + retreatDirection.y * 200;
            unit.state = 'moving';
            unit.attackTarget = null;
        });
        
        this.fakeRetreats.push({
            units: retreatingUnits,
            startTime: this.controller.game.gameTime
        });
    }
    
    executeFeintAttack(deceptionData) {
        const feintUnits = this.controller.team.units
            .filter(unit => unit.damage > 0)
            .slice(0, 2);
        
        if (feintUnits.length > 0) {
            const feintTarget = this.selectFeintTarget();
            
            feintUnits.forEach(unit => {
                unit.attackTarget = feintTarget;
                unit.state = 'attacking';
                unit.feintAttack = {
                    active: true,
                    duration: 45 + Math.random() * 30
                };
            });
            
            this.feintAttacks.push({
                units: feintUnits,
                target: feintTarget,
                startTime: this.controller.game.gameTime
            });
            
            // Prepare main attack
            this.prepareMainAttack();
        }
    }
    
    updateDeceptionTactics(deltaTime) {
        // Update fake retreats
        this.updateFakeRetreats();
        
        // Update feint attacks
        this.updateFeintAttacks();
    }
    
    updateFakeRetreats() {
        const currentTime = this.controller.game.gameTime;
        
        this.fakeRetreats = this.fakeRetreats.filter(retreat => {
            retreat.units.forEach(unit => {
                if (unit.fakeRetreat && currentTime >= unit.fakeRetreat.returnTime) {
                    // Return to original target
                    unit.attackTarget = unit.fakeRetreat.originalTarget;
                    unit.state = 'attacking';
                    unit.fakeRetreat = null;
                }
            });
            
            return retreat.units.some(unit => unit.fakeRetreat);
        });
    }
    
    updateFeintAttacks() {
        const currentTime = this.controller.game.gameTime;
        
        this.feintAttacks = this.feintAttacks.filter(feint => {
            const elapsed = currentTime - feint.startTime;
            
            if (elapsed > 60) { // End feint after 1 minute
                feint.units.forEach(unit => {
                    unit.feintAttack = null;
                });
                return false;
            }
            
            return true;
        });
    }
    
    executePeriodicCoordination(deltaTime) {
        // Execute deception tactics occasionally
        if (this.controller.personalityTraits.cunning > 0.6 && Math.random() < 0.1 * deltaTime) {
            this.considerDeceptionTactic();
        }
    }
    
    considerDeceptionTactic() {
        const tactics = ['fake_retreat', 'feint_attack', 'false_buildup'];
        const selectedTactic = tactics[Math.floor(Math.random() * tactics.length)];
        
        const deceptionAction = {
            type: 'execute_deception',
            priority: 40,
            data: { type: selectedTactic },
            duration: 120
        };
        
        this.queueAction(deceptionAction);
    }
    
    // Utility methods
    getAvailableCombatUnits() {
        return this.controller.team.units.filter(unit => 
            unit.damage > 0 && unit.health > 0 && !unit.attackTarget
        );
    }
    
    getAvailableDefensiveUnits() {
        return this.controller.team.units.filter(unit => 
            unit.damage > 0 && unit.health > 0 && unit.state !== 'attacking'
        );
    }
    
    selectRaidUnits() {
        return this.controller.team.units
            .filter(unit => unit.damage > 0 && unit.speed > 2)
            .slice(0, 4);
    }
    
    selectFastAttackUnits() {
        return this.controller.team.units
            .filter(unit => unit.damage > 0 && unit.speed > 2.5)
            .slice(0, 3);
    }
    
    calculateDefensivePosition(building, defender) {
        const angle = Math.random() * Math.PI * 2;
        const radius = 100;
        
        return {
            x: building.x + Math.cos(angle) * radius,
            y: building.y + Math.sin(angle) * radius
        };
    }
    
    calculateRetreatDirection(unit) {
        // Calculate direction away from nearest enemy
        const nearestEnemy = this.controller.game.findNearestPlayerEntity(unit, 300);
        
        if (nearestEnemy) {
            const dx = unit.x - nearestEnemy.x;
            const dy = unit.y - nearestEnemy.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            
            return {
                x: dx / length,
                y: dy / length
            };
        }
        
        return { x: 1, y: 0 }; // Default retreat direction
    }
    
    selectFeintTarget() {
        // Select a visible but less important target
        const targets = this.controller.game.teams.player.buildings
            .filter(building => building.type !== 'base')
            .sort((a, b) => this.controller.calculateBuildingThreatValue(a) - this.controller.calculateBuildingThreatValue(b));
        
        return targets[0] || null;
    }
    
    prepareMainAttack() {
        // Prepare the real attack while feint is ongoing
        const mainForce = this.getAvailableCombatUnits();
        const primaryTarget = this.selectPrimaryAssaultTarget();
        
        if (primaryTarget && mainForce.length > 0) {
            setTimeout(() => {
                mainForce.forEach(unit => {
                    unit.attackTarget = primaryTarget;
                    unit.state = 'attacking';
                    unit.priority = 'main_assault';
                });
            }, 30000); // Attack 30 seconds after feint starts
        }
    }
    
    selectPrimaryAssaultTarget() {
        // Select the most valuable target for main assault
        const targets = this.controller.game.teams.player.buildings
            .sort((a, b) => this.controller.calculateBuildingThreatValue(b) - this.controller.calculateBuildingThreatValue(a));
        
        return targets[0] || null;
    }
    
    // Additional utility methods would be implemented as needed...
    coordinateMultiTargetAttack(units, targets) {
        // Implementation for coordinating attacks on multiple targets
    }
    
    executeHitAndRunTactics(units, targets) {
        // Implementation for hit and run tactics
    }
    
    requestDefensiveBuildings(defenseGaps) {
        // Implementation for requesting defensive buildings
    }
    
    establishRallyPoints() {
        // Implementation for establishing rally points
    }
    
    selectExpansionSites(expansionData) {
        return []; // Placeholder
    }
    
    deployHarvestersToSites(sites) {
        // Implementation for deploying harvesters
    }
    
    requestSupportingInfrastructure(sites) {
        // Implementation for requesting infrastructure
    }
    
    prioritizeUnitProduction(threatLevel) {
        // Implementation for prioritizing unit production
    }
    
    requestMilitaryBuildings() {
        // Implementation for requesting military buildings
    }
    
    organizeMilitaryFormations() {
        // Implementation for organizing formations
    }
    
    assignTargetsToGroups(groups, targets) {
        // Implementation for assigning targets to attack groups
    }
    
    updateOngoingAction(action, deltaTime) {
        // Implementation for updating ongoing actions
    }
    
    finalizeAttack(action) {
        // Implementation for finalizing attacks
    }
    
    finalizeCoordinatedAssault(action) {
        // Implementation for finalizing coordinated assaults
    }
    
    finalizeDeception(action) {
        // Implementation for finalizing deception
    }
    
    executeFalseBuildup(deceptionData) {
        // Implementation for false buildup deception
    }
}