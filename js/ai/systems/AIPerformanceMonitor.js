import { EventBus } from '../../core/EventBus.js';

/**
 * AI Performance Monitor
 * Handles performance tracking, optimization, and AI system health monitoring
 */
export class AIPerformanceMonitor {
    constructor(controller) {
        this.controller = controller;
        this.eventBus = controller.game.eventBus;
        
        // Performance metrics
        this.metrics = {
            // Decision making performance
            decisionLatency: [],
            decisionAccuracy: 0.5,
            decisionsPerSecond: 0,
            
            // Execution performance
            actionExecutionTime: [],
            actionSuccessRate: 0.5,
            coordinationEfficiency: 0.5,
            
            // Combat performance
            combatEffectiveness: 0.5,
            unitLossRate: 0,
            killDeathRatio: 1.0,
            
            // Economic performance
            resourceEfficiency: 0.5,
            economicGrowthRate: 0,
            spicePerMinute: 0,
            
            // Strategic performance
            territoryControl: 0.5,
            strategicGoalCompletion: 0.5,
            adaptationSpeed: 0.5,
            
            // System performance
            cpuUsage: 0,
            memoryUsage: 0,
            updateFrequency: 60
        };
        
        // Performance history for trend analysis
        this.performanceHistory = {
            snapshots: [],
            maxSnapshots: 100,
            snapshotInterval: 30 // seconds
        };
        
        // Optimization settings
        this.optimization = {
            enabled: true,
            aggressiveMode: false,
            adaptiveThresholds: true,
            performanceTargets: {
                decisionLatency: 50, // ms
                actionExecutionTime: 100, // ms
                updateFrequency: 60, // fps
                memoryUsage: 50 // MB
            }
        };
        
        // Monitoring state
        this.monitoring = {
            lastSnapshot: 0,
            lastOptimization: 0,
            optimizationInterval: 60, // seconds
            alertThresholds: {
                criticalLatency: 200, // ms
                criticalMemory: 100, // MB
                criticalCPU: 80 // %
            }
        };
        
        // Performance alerts
        this.alerts = [];
        this.maxAlerts = 20;
        
        this.initialize();
    }
    
    initialize() {
        // Subscribe to performance-related events
        this.eventBus.on('ai.decision.started', this.trackDecisionStart, this);
        this.eventBus.on('ai.decision.completed', this.trackDecisionComplete, this);
        this.eventBus.on('ai.action.started', this.trackActionStart, this);
        this.eventBus.on('ai.action.completed', this.trackActionComplete, this);
        this.eventBus.on('game.unit.destroyed', this.trackUnitDestroyed, this);
        this.eventBus.on('game.building.destroyed', this.trackBuildingDestroyed, this);
        this.eventBus.on('ai.resource.gained', this.trackResourceGained, this);
        
        // Initialize performance tracking
        this.startPerformanceTracking();
        
        console.log('AI Performance Monitor initialized');
    }
    
    update(deltaTime) {
        // Update real-time metrics
        this.updateRealtimeMetrics(deltaTime);
        
        // Take performance snapshots
        this.takePerformanceSnapshot(deltaTime);
        
        // Run optimization checks
        this.runOptimizationChecks(deltaTime);
        
        // Check for performance alerts
        this.checkPerformanceAlerts();
        
        // Update system metrics
        this.updateSystemMetrics(deltaTime);
    }
    
    startPerformanceTracking() {
        // Initialize tracking timers and counters
        this.trackingData = {
            decisionsStarted: new Map(),
            actionsStarted: new Map(),
            frameStartTime: performance.now(),
            lastFrameTime: performance.now(),
            frameCount: 0,
            decisionCount: 0,
            actionCount: 0
        };
    }
    
    updateRealtimeMetrics(deltaTime) {
        const currentTime = performance.now();
        
        // Update frame rate
        this.trackingData.frameCount++;
        const timeSinceLastFrame = currentTime - this.trackingData.lastFrameTime;
        this.trackingData.lastFrameTime = currentTime;
        
        // Calculate FPS
        if (timeSinceLastFrame > 0) {
            this.metrics.updateFrequency = 1000 / timeSinceLastFrame;
        }
        
        // Update decisions per second
        const timeSinceStart = (currentTime - this.trackingData.frameStartTime) / 1000;
        if (timeSinceStart > 0) {
            this.metrics.decisionsPerSecond = this.trackingData.decisionCount / timeSinceStart;
        }
        
        // Update economic metrics
        this.updateEconomicMetrics();
        
        // Update combat metrics
        this.updateCombatMetrics();
        
        // Update strategic metrics
        this.updateStrategicMetrics();
    }
    
    updateEconomicMetrics() {
        // Calculate resource efficiency
        const harvesters = this.controller.team.units.filter(u => u.type === 'harvester').length;
        const spiceIncome = harvesters * 2; // Approximate income per harvester
        const unitCost = this.controller.team.units.length * 0.5; // Approximate maintenance cost
        
        if (unitCost > 0) {
            this.metrics.resourceEfficiency = Math.min(2, spiceIncome / unitCost);
        }
        
        // Calculate spice per minute
        const currentSpice = this.controller.team.spice;
        const gameTimeMinutes = this.controller.game.gameTime / 60;
        
        if (gameTimeMinutes > 0) {
            this.metrics.spicePerMinute = currentSpice / gameTimeMinutes;
        }
    }
    
    updateCombatMetrics() {
        // Calculate combat effectiveness based on recent engagements
        const recentCombatEvents = this.getRecentCombatEvents();
        
        if (recentCombatEvents.length > 0) {
            const wins = recentCombatEvents.filter(event => event.victory).length;
            this.metrics.combatEffectiveness = wins / recentCombatEvents.length;
        }
        
        // Update kill/death ratio
        const recentKills = this.getRecentKills();
        const recentDeaths = this.getRecentDeaths();
        
        if (recentDeaths > 0) {
            this.metrics.killDeathRatio = recentKills / recentDeaths;
        } else if (recentKills > 0) {
            this.metrics.killDeathRatio = recentKills; // No deaths, all kills
        }
    }
    
    updateStrategicMetrics() {
        // Calculate territory control
        const aiBuildings = this.controller.team.buildings.length;
        const playerBuildings = this.controller.game.teams.player.buildings.length;
        const totalBuildings = aiBuildings + playerBuildings;
        
        if (totalBuildings > 0) {
            this.metrics.territoryControl = aiBuildings / totalBuildings;
        }
        
        // Calculate strategic goal completion
        this.metrics.strategicGoalCompletion = this.calculateGoalCompletion();
        
        // Calculate adaptation speed
        this.metrics.adaptationSpeed = this.calculateAdaptationSpeed();
    }
    
    updateSystemMetrics(deltaTime) {
        // Estimate CPU usage (simplified)
        const frameTime = performance.now() - this.trackingData.lastFrameTime;
        const targetFrameTime = 1000 / 60; // 60 FPS target
        this.metrics.cpuUsage = Math.min(100, (frameTime / targetFrameTime) * 100);
        
        // Estimate memory usage (simplified)
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize / (1024 * 1024); // MB
        }
    }
    
    takePerformanceSnapshot(deltaTime) {
        const currentTime = this.controller.game.gameTime;
        
        if (currentTime - this.monitoring.lastSnapshot >= this.performanceHistory.snapshotInterval) {
            const snapshot = this.createPerformanceSnapshot();
            this.performanceHistory.snapshots.push(snapshot);
            
            // Trim old snapshots
            if (this.performanceHistory.snapshots.length > this.performanceHistory.maxSnapshots) {
                this.performanceHistory.snapshots.shift();
            }
            
            this.monitoring.lastSnapshot = currentTime;
            
            // Emit snapshot event
            this.eventBus.emit('ai.performance.snapshot', snapshot);
        }
    }
    
    createPerformanceSnapshot() {
        return {
            timestamp: this.controller.game.gameTime,
            metrics: { ...this.metrics },
            gameState: {
                aiUnits: this.controller.team.units.length,
                aiBuildings: this.controller.team.buildings.length,
                aiSpice: this.controller.team.spice,
                playerUnits: this.controller.game.teams.player.units.length,
                playerBuildings: this.controller.game.teams.player.buildings.length
            },
            systemHealth: this.calculateSystemHealth()
        };
    }
    
    calculateSystemHealth() {
        let health = 100;
        
        // Penalize for high latency
        const avgLatency = this.calculateAverageLatency();
        if (avgLatency > this.optimization.performanceTargets.decisionLatency) {
            health -= (avgLatency - this.optimization.performanceTargets.decisionLatency) / 10;
        }
        
        // Penalize for high memory usage
        if (this.metrics.memoryUsage > this.optimization.performanceTargets.memoryUsage) {
            health -= (this.metrics.memoryUsage - this.optimization.performanceTargets.memoryUsage) / 2;
        }
        
        // Penalize for low FPS
        if (this.metrics.updateFrequency < this.optimization.performanceTargets.updateFrequency) {
            health -= (this.optimization.performanceTargets.updateFrequency - this.metrics.updateFrequency);
        }
        
        return Math.max(0, Math.min(100, health));
    }
    
    runOptimizationChecks(deltaTime) {
        const currentTime = this.controller.game.gameTime;
        
        if (currentTime - this.monitoring.lastOptimization >= this.monitoring.optimizationInterval) {
            this.performOptimization();
            this.monitoring.lastOptimization = currentTime;
        }
    }
    
    performOptimization() {
        if (!this.optimization.enabled) return;
        
        const systemHealth = this.calculateSystemHealth();
        
        // Trigger optimization if health is low
        if (systemHealth < 70) {
            this.triggerOptimization(systemHealth);
        }
        
        // Adaptive threshold adjustment
        if (this.optimization.adaptiveThresholds) {
            this.adjustPerformanceThresholds();
        }
    }
    
    triggerOptimization(systemHealth) {
        console.log(`AI Performance optimization triggered (health: ${systemHealth.toFixed(1)}%)`);
        
        const optimizations = [];
        
        // Check decision latency
        const avgLatency = this.calculateAverageLatency();
        if (avgLatency > this.optimization.performanceTargets.decisionLatency) {
            optimizations.push('reduce_decision_complexity');
        }
        
        // Check memory usage
        if (this.metrics.memoryUsage > this.optimization.performanceTargets.memoryUsage) {
            optimizations.push('cleanup_memory');
        }
        
        // Check update frequency
        if (this.metrics.updateFrequency < this.optimization.performanceTargets.updateFrequency) {
            optimizations.push('reduce_update_frequency');
        }
        
        // Apply optimizations
        optimizations.forEach(optimization => {
            this.applyOptimization(optimization);
        });
        
        // Emit optimization event
        this.eventBus.emit('ai.performance.optimized', {
            systemHealth,
            optimizations,
            timestamp: this.controller.game.gameTime
        });
    }
    
    applyOptimization(optimization) {
        switch (optimization) {
            case 'reduce_decision_complexity':
                this.eventBus.emit('ai.optimize.decisions', { complexity: 'low' });
                break;
            case 'cleanup_memory':
                this.eventBus.emit('ai.optimize.memory', { cleanup: true });
                break;
            case 'reduce_update_frequency':
                this.eventBus.emit('ai.optimize.frequency', { reduce: true });
                break;
        }
    }
    
    adjustPerformanceThresholds() {
        // Adjust thresholds based on recent performance trends
        const recentSnapshots = this.performanceHistory.snapshots.slice(-10);
        
        if (recentSnapshots.length >= 5) {
            const avgLatency = recentSnapshots.reduce((sum, s) => 
                sum + this.calculateSnapshotLatency(s), 0) / recentSnapshots.length;
            
            // Adjust latency threshold
            if (avgLatency < this.optimization.performanceTargets.decisionLatency * 0.8) {
                this.optimization.performanceTargets.decisionLatency *= 0.95; // Tighten threshold
            } else if (avgLatency > this.optimization.performanceTargets.decisionLatency * 1.2) {
                this.optimization.performanceTargets.decisionLatency *= 1.05; // Loosen threshold
            }
        }
    }
    
    checkPerformanceAlerts() {
        // Check for critical performance issues
        const avgLatency = this.calculateAverageLatency();
        
        if (avgLatency > this.monitoring.alertThresholds.criticalLatency) {
            this.createAlert('critical_latency', `Decision latency: ${avgLatency.toFixed(1)}ms`, 'critical');
        }
        
        if (this.metrics.memoryUsage > this.monitoring.alertThresholds.criticalMemory) {
            this.createAlert('critical_memory', `Memory usage: ${this.metrics.memoryUsage.toFixed(1)}MB`, 'critical');
        }
        
        if (this.metrics.cpuUsage > this.monitoring.alertThresholds.criticalCPU) {
            this.createAlert('critical_cpu', `CPU usage: ${this.metrics.cpuUsage.toFixed(1)}%`, 'critical');
        }
        
        // Check for performance degradation trends
        this.checkPerformanceTrends();
    }
    
    checkPerformanceTrends() {
        const recentSnapshots = this.performanceHistory.snapshots.slice(-5);
        
        if (recentSnapshots.length >= 3) {
            // Check for declining performance
            const firstHealth = recentSnapshots[0].systemHealth;
            const lastHealth = recentSnapshots[recentSnapshots.length - 1].systemHealth;
            
            if (lastHealth < firstHealth - 20) {
                this.createAlert('performance_decline', 
                    `System health declined from ${firstHealth.toFixed(1)}% to ${lastHealth.toFixed(1)}%`, 
                    'warning');
            }
        }
    }
    
    createAlert(type, message, severity = 'info') {
        const alert = {
            id: `alert_${Date.now()}`,
            type,
            message,
            severity,
            timestamp: this.controller.game.gameTime,
            acknowledged: false
        };
        
        this.alerts.push(alert);
        
        // Trim old alerts
        if (this.alerts.length > this.maxAlerts) {
            this.alerts.shift();
        }
        
        // Emit alert event
        this.eventBus.emit('ai.performance.alert', alert);
        
        console.log(`AI Performance Alert [${severity}]: ${message}`);
    }
    
    // Event tracking methods
    trackDecisionStart(decision) {
        this.trackingData.decisionsStarted.set(decision.id, performance.now());
        this.trackingData.decisionCount++;
    }
    
    trackDecisionComplete(decision) {
        const startTime = this.trackingData.decisionsStarted.get(decision.id);
        if (startTime) {
            const latency = performance.now() - startTime;
            this.metrics.decisionLatency.push(latency);
            
            // Keep only recent latency measurements
            if (this.metrics.decisionLatency.length > 100) {
                this.metrics.decisionLatency.shift();
            }
            
            this.trackingData.decisionsStarted.delete(decision.id);
        }
    }
    
    trackActionStart(action) {
        this.trackingData.actionsStarted.set(action.id, performance.now());
        this.trackingData.actionCount++;
    }
    
    trackActionComplete(action) {
        const startTime = this.trackingData.actionsStarted.get(action.id);
        if (startTime) {
            const executionTime = performance.now() - startTime;
            this.metrics.actionExecutionTime.push(executionTime);
            
            // Keep only recent execution times
            if (this.metrics.actionExecutionTime.length > 100) {
                this.metrics.actionExecutionTime.shift();
            }
            
            this.trackingData.actionsStarted.delete(action.id);
        }
    }
    
    trackUnitDestroyed(event) {
        // Track unit losses for performance analysis
        if (event.unit.teamId === this.controller.teamId) {
            this.recordCombatEvent({
                type: 'unit_lost',
                unit: event.unit,
                timestamp: this.controller.game.gameTime,
                victory: false
            });
        } else {
            this.recordCombatEvent({
                type: 'enemy_unit_destroyed',
                unit: event.unit,
                timestamp: this.controller.game.gameTime,
                victory: true
            });
        }
    }
    
    trackBuildingDestroyed(event) {
        // Track building losses for performance analysis
        if (event.building.teamId === this.controller.teamId) {
            this.recordCombatEvent({
                type: 'building_lost',
                building: event.building,
                timestamp: this.controller.game.gameTime,
                victory: false
            });
        } else {
            this.recordCombatEvent({
                type: 'enemy_building_destroyed',
                building: event.building,
                timestamp: this.controller.game.gameTime,
                victory: true
            });
        }
    }
    
    trackResourceGained(event) {
        // Track resource efficiency
        this.recordResourceEvent({
            type: 'resource_gained',
            amount: event.amount,
            timestamp: this.controller.game.gameTime
        });
    }
    
    recordCombatEvent(event) {
        if (!this.combatEvents) {
            this.combatEvents = [];
        }
        
        this.combatEvents.push(event);
        
        // Keep only recent events
        const cutoffTime = this.controller.game.gameTime - 300; // Last 5 minutes
        this.combatEvents = this.combatEvents.filter(e => e.timestamp >= cutoffTime);
    }
    
    recordResourceEvent(event) {
        if (!this.resourceEvents) {
            this.resourceEvents = [];
        }
        
        this.resourceEvents.push(event);
        
        // Keep only recent events
        const cutoffTime = this.controller.game.gameTime - 600; // Last 10 minutes
        this.resourceEvents = this.resourceEvents.filter(e => e.timestamp >= cutoffTime);
    }
    
    // Analysis methods
    calculateAverageLatency() {
        if (this.metrics.decisionLatency.length === 0) return 0;
        
        const sum = this.metrics.decisionLatency.reduce((a, b) => a + b, 0);
        return sum / this.metrics.decisionLatency.length;
    }
    
    calculateAverageExecutionTime() {
        if (this.metrics.actionExecutionTime.length === 0) return 0;
        
        const sum = this.metrics.actionExecutionTime.reduce((a, b) => a + b, 0);
        return sum / this.metrics.actionExecutionTime.length;
    }
    
    getRecentCombatEvents() {
        return this.combatEvents || [];
    }
    
    getRecentKills() {
        const events = this.getRecentCombatEvents();
        return events.filter(e => e.victory && (e.type === 'enemy_unit_destroyed' || e.type === 'enemy_building_destroyed')).length;
    }
    
    getRecentDeaths() {
        const events = this.getRecentCombatEvents();
        return events.filter(e => !e.victory && (e.type === 'unit_lost' || e.type === 'building_lost')).length;
    }
    
    calculateGoalCompletion() {
        // Simplified goal completion calculation
        const aiPower = this.controller.team.units.length + this.controller.team.buildings.length;
        const playerPower = this.controller.game.teams.player.units.length + this.controller.game.teams.player.buildings.length;
        const totalPower = aiPower + playerPower;
        
        return totalPower > 0 ? aiPower / totalPower : 0.5;
    }
    
    calculateAdaptationSpeed() {
        // Measure how quickly AI adapts to changes
        return Math.min(1, this.metrics.decisionsPerSecond / 2);
    }
    
    calculateSnapshotLatency(snapshot) {
        // Extract latency information from snapshot
        return snapshot.metrics.decisionLatency.length > 0 ? 
            snapshot.metrics.decisionLatency.reduce((a, b) => a + b, 0) / snapshot.metrics.decisionLatency.length : 0;
    }
    
    // Public interface methods
    getPerformanceReport() {
        return {
            metrics: { ...this.metrics },
            systemHealth: this.calculateSystemHealth(),
            averageLatency: this.calculateAverageLatency(),
            averageExecutionTime: this.calculateAverageExecutionTime(),
            recentAlerts: this.alerts.slice(-5),
            optimizationStatus: {
                enabled: this.optimization.enabled,
                aggressiveMode: this.optimization.aggressiveMode,
                targets: { ...this.optimization.performanceTargets }
            }
        };
    }
    
    getPerformanceTrends() {
        const snapshots = this.performanceHistory.snapshots.slice(-20);
        
        return {
            systemHealthTrend: snapshots.map(s => ({
                timestamp: s.timestamp,
                health: s.systemHealth
            })),
            latencyTrend: snapshots.map(s => ({
                timestamp: s.timestamp,
                latency: this.calculateSnapshotLatency(s)
            })),
            memoryTrend: snapshots.map(s => ({
                timestamp: s.timestamp,
                memory: s.metrics.memoryUsage
            }))
        };
    }
    
    acknowledgeAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.acknowledged = true;
            this.eventBus.emit('ai.performance.alert.acknowledged', alert);
        }
    }
    
    setOptimizationMode(enabled, aggressive = false) {
        this.optimization.enabled = enabled;
        this.optimization.aggressiveMode = aggressive;
        
        console.log(`AI Performance optimization ${enabled ? 'enabled' : 'disabled'}${aggressive ? ' (aggressive mode)' : ''}`);
    }
    
    resetMetrics() {
        // Reset all metrics to initial state
        this.metrics.decisionLatency = [];
        this.metrics.actionExecutionTime = [];
        this.trackingData.frameStartTime = performance.now();
        this.trackingData.decisionCount = 0;
        this.trackingData.actionCount = 0;
        this.combatEvents = [];
        this.resourceEvents = [];
        
        console.log('AI Performance metrics reset');
    }
    
    exportPerformanceData() {
        return {
            metrics: this.metrics,
            history: this.performanceHistory,
            alerts: this.alerts,
            optimization: this.optimization
        };
    }
    
    importPerformanceData(data) {
        if (data.metrics) {
            this.metrics = { ...this.metrics, ...data.metrics };
        }
        if (data.history) {
            this.performanceHistory = data.history;
        }
        if (data.alerts) {
            this.alerts = data.alerts;
        }
        if (data.optimization) {
            this.optimization = { ...this.optimization, ...data.optimization };
        }
    }
}