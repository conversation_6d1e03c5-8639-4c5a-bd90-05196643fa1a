import { EventBus } from '../../core/EventBus.js';
import { distance } from '../../utils/index.js';

/**
 * AI Decision System
 * Handles core AI decision-making logic, personality-driven choices, and strategic planning
 */
export class AIDecisionSystem {
    constructor(controller) {
        this.controller = controller;
        this.eventBus = controller.game.eventBus;
        
        // Decision state
        this.currentDecision = null;
        this.decisionQueue = [];
        this.emergencyOverrides = [];
        this.strategicGoals = new Set();
        this.tacticalObjectives = [];
        this.operationalTasks = [];
        
        // Decision timing
        this.lastStrategicDecision = 0;
        this.lastTacticalDecision = 0;
        this.lastOperationalDecision = 0;
        
        // Decision intervals (seconds)
        this.strategicInterval = 30;
        this.tacticalInterval = 10;
        this.operationalInterval = 3;
        
        this.initialize();
    }
    
    initialize() {
        // Set initial strategic goals based on personality
        this.setInitialStrategicGoals();
        
        // Subscribe to relevant events
        this.eventBus.on('ai.threat.detected', this.handleThreatDetected, this);
        this.eventBus.on('ai.opportunity.found', this.handleOpportunityFound, this);
        this.eventBus.on('ai.emergency.declared', this.handleEmergency, this);
        
        console.log('AI Decision System initialized');
    }
    
    update(deltaTime) {
        const gameTime = this.controller.game.gameTime;
        
        // Handle emergency overrides first
        if (this.emergencyOverrides.length > 0) {
            this.processEmergencyOverrides();
        }
        
        // Make hierarchical decisions at different intervals
        if (gameTime - this.lastStrategicDecision >= this.strategicInterval) {
            this.makeStrategicDecisions();
            this.lastStrategicDecision = gameTime;
        }
        
        if (gameTime - this.lastTacticalDecision >= this.tacticalInterval) {
            this.makeTacticalDecisions();
            this.lastTacticalDecision = gameTime;
        }
        
        if (gameTime - this.lastOperationalDecision >= this.operationalInterval) {
            this.makeOperationalDecisions();
            this.lastOperationalDecision = gameTime;
        }
        
        // Process decision queue
        this.processDecisionQueue();
    }
    
    setInitialStrategicGoals() {
        const personality = this.controller.personalityTraits;
        const goals = ['survive', 'expand', 'develop'];
        
        if (personality.aggression > 0.6) {
            goals.push('dominate', 'eliminate_player');
        }
        
        if (personality.cunning > 0.7) {
            goals.push('deceive', 'manipulate');
        }
        
        if (personality.opportunism > 0.6) {
            goals.push('exploit_weaknesses', 'resource_control');
        }
        
        this.strategicGoals = new Set(goals);
    }
    
    makeStrategicDecisions() {
        // Evaluate current strategic situation
        const assessment = this.assessStrategicSituation();
        
        // Update long-term goals based on assessment
        this.updateStrategicGoals(assessment);
        
        // Generate strategic options
        const options = this.generateStrategicOptions(assessment);
        
        // Select and queue best strategic decision
        const bestOption = this.selectBestOption(options, 'strategic');
        if (bestOption) {
            this.queueDecision(bestOption);
        }
    }
    
    makeTacticalDecisions() {
        // Analyze tactical situation
        const assessment = this.assessTacticalSituation();
        
        // Generate tactical options
        const options = this.generateTacticalOptions(assessment);
        
        // Select and queue best tactical decision
        const bestOption = this.selectBestOption(options, 'tactical');
        if (bestOption) {
            this.queueDecision(bestOption);
        }
    }
    
    makeOperationalDecisions() {
        // Handle immediate operational needs
        const assessment = this.assessOperationalSituation();
        
        // Generate operational options
        const options = this.generateOperationalOptions(assessment);
        
        // Select and execute immediate operational decision
        const bestOption = this.selectBestOption(options, 'operational');
        if (bestOption) {
            this.executeDecision(bestOption);
        }
    }
    
    assessStrategicSituation() {
        return {
            playerPower: this.calculatePlayerPower(),
            aiPower: this.calculateAIPower(),
            resourceSituation: this.assessResourceSituation(),
            territorialControl: this.assessTerritorialControl(),
            threatLevel: this.assessThreatLevel(),
            opportunities: this.identifyOpportunities()
        };
    }
    
    assessTacticalSituation() {
        return {
            combatSituation: this.assessCombatSituation(),
            unitPositioning: this.assessUnitPositioning(),
            defensiveNeeds: this.assessDefensiveNeeds(),
            attackOpportunities: this.assessAttackOpportunities()
        };
    }
    
    assessOperationalSituation() {
        return {
            immediateThreats: this.identifyImmediateThreats(),
            unitStatus: this.assessUnitStatus(),
            resourceNeeds: this.assessImmediateResourceNeeds(),
            emergencies: this.identifyEmergencies()
        };
    }
    
    generateStrategicOptions(assessment) {
        const options = [];
        
        // Economic expansion options
        if (assessment.resourceSituation.expansionOpportunities.length > 0) {
            options.push({
                type: 'expand_economy',
                priority: 70,
                category: 'strategic',
                data: assessment.resourceSituation.expansionOpportunities
            });
        }
        
        // Military buildup options
        if (assessment.threatLevel > 50) {
            options.push({
                type: 'military_buildup',
                priority: 80,
                category: 'strategic',
                data: { threatLevel: assessment.threatLevel }
            });
        }
        
        // Territorial expansion options
        if (assessment.opportunities.includes('territorial_expansion')) {
            options.push({
                type: 'territorial_expansion',
                priority: 60,
                category: 'strategic',
                data: assessment.territorialControl
            });
        }
        
        return options;
    }
    
    generateTacticalOptions(assessment) {
        const options = [];
        
        // Attack opportunities
        assessment.attackOpportunities.forEach(opportunity => {
            options.push({
                type: 'tactical_attack',
                priority: opportunity.priority,
                category: 'tactical',
                data: opportunity
            });
        });
        
        // Defensive maneuvers
        if (assessment.defensiveNeeds.vulnerableBuildings.length > 0) {
            options.push({
                type: 'strengthen_defenses',
                priority: 75,
                category: 'tactical',
                data: assessment.defensiveNeeds
            });
        }
        
        // Unit repositioning
        if (assessment.unitPositioning.positioning < 60) {
            options.push({
                type: 'reposition_units',
                priority: 50,
                category: 'tactical',
                data: assessment.unitPositioning
            });
        }
        
        return options;
    }
    
    generateOperationalOptions(assessment) {
        const options = [];
        
        // Handle immediate threats
        assessment.immediateThreats.forEach(threat => {
            options.push({
                type: 'respond_to_threat',
                priority: threat.severity,
                category: 'operational',
                data: threat
            });
        });
        
        // Handle emergencies
        assessment.emergencies.forEach(emergency => {
            options.push({
                type: 'handle_emergency',
                priority: emergency.severity,
                category: 'operational',
                data: emergency
            });
        });
        
        return options;
    }
    
    selectBestOption(options, category) {
        if (options.length === 0) return null;
        
        // Apply personality-based weighting
        const weightedOptions = options.map(option => ({
            ...option,
            weightedPriority: this.applyPersonalityWeighting(option)
        }));
        
        // Sort by weighted priority
        weightedOptions.sort((a, b) => b.weightedPriority - a.weightedPriority);
        
        // Add some randomness for unpredictability
        const topOptions = weightedOptions.slice(0, Math.min(3, weightedOptions.length));
        const randomIndex = Math.floor(Math.random() * topOptions.length);
        
        return topOptions[randomIndex];
    }
    
    applyPersonalityWeighting(option) {
        let weight = option.priority;
        const personality = this.controller.personalityTraits;
        
        // Apply personality modifiers based on option type
        switch (option.type) {
            case 'tactical_attack':
            case 'military_buildup':
                weight *= (0.5 + personality.aggression * 0.5);
                break;
            case 'expand_economy':
                weight *= (0.5 + personality.patience * 0.5);
                break;
            case 'strengthen_defenses':
                weight *= (0.5 + (1 - personality.aggression) * 0.5);
                break;
            case 'handle_emergency':
                weight *= (0.8 + personality.adaptability * 0.2);
                break;
        }
        
        return weight;
    }
    
    queueDecision(decision) {
        decision.timestamp = this.controller.game.gameTime;
        this.decisionQueue.push(decision);
        
        // Sort queue by priority
        this.decisionQueue.sort((a, b) => b.priority - a.priority);
    }
    
    processDecisionQueue() {
        if (this.decisionQueue.length === 0) return;
        
        const decision = this.decisionQueue.shift();
        this.executeDecision(decision);
    }
    
    executeDecision(decision) {
        console.log(`AI executing decision: ${decision.type} (priority: ${decision.priority})`);
        
        this.currentDecision = decision;
        
        // Emit decision event for other systems
        this.eventBus.emit('ai.decision.made', decision);
        
        // Execute based on decision type
        switch (decision.type) {
            case 'expand_economy':
                this.eventBus.emit('ai.economy.expand', decision.data);
                break;
            case 'military_buildup':
                this.eventBus.emit('ai.military.buildup', decision.data);
                break;
            case 'tactical_attack':
                this.eventBus.emit('ai.attack.execute', decision.data);
                break;
            case 'strengthen_defenses':
                this.eventBus.emit('ai.defense.strengthen', decision.data);
                break;
            case 'respond_to_threat':
                this.eventBus.emit('ai.threat.respond', decision.data);
                break;
            case 'handle_emergency':
                this.eventBus.emit('ai.emergency.handle', decision.data);
                break;
        }
    }
    
    processEmergencyOverrides() {
        const emergency = this.emergencyOverrides.shift();
        
        const emergencyDecision = {
            type: `emergency_${emergency.type}`,
            priority: 100,
            category: 'emergency',
            data: emergency
        };
        
        this.executeDecision(emergencyDecision);
    }
    
    // Event handlers
    handleThreatDetected(threat) {
        if (threat.severity > 80) {
            this.emergencyOverrides.push({
                type: 'threat_response',
                severity: threat.severity,
                threat: threat
            });
        }
    }
    
    handleOpportunityFound(opportunity) {
        const opportunityDecision = {
            type: 'exploit_opportunity',
            priority: opportunity.value,
            category: 'opportunistic',
            data: opportunity
        };
        
        this.queueDecision(opportunityDecision);
    }
    
    handleEmergency(emergency) {
        this.emergencyOverrides.push(emergency);
    }
    
    // Utility methods
    calculatePlayerPower() {
        const playerUnits = this.controller.game.teams.player.units.length;
        const playerBuildings = this.controller.game.teams.player.buildings.length;
        const playerResources = this.controller.game.resourceManager?.getResource('spice') || 0;
        
        return (playerUnits * 10) + (playerBuildings * 50) + (playerResources / 100);
    }
    
    calculateAIPower() {
        const aiUnits = this.controller.team.units.length;
        const aiBuildings = this.controller.team.buildings.length;
        const aiResources = this.controller.team.spice;
        
        return (aiUnits * 10) + (aiBuildings * 50) + (aiResources / 100);
    }
    
    assessResourceSituation() {
        return {
            spiceReserves: this.controller.team.spice,
            expansionOpportunities: this.identifyExpansionOpportunities(),
            resourceSecurity: this.assessResourceSecurity()
        };
    }
    
    assessTerritorialControl() {
        const aiBuildings = this.controller.team.buildings;
        const controlRadius = 200;
        
        let controlledArea = 0;
        aiBuildings.forEach(building => {
            controlledArea += Math.PI * controlRadius * controlRadius;
        });
        
        return {
            controlledArea: controlledArea / (this.controller.game.width * this.controller.game.height),
            strategicPositions: this.identifyStrategicPositions(),
            vulnerableAreas: this.identifyVulnerableAreas()
        };
    }
    
    assessThreatLevel() {
        let threatLevel = 0;
        
        // Player military threat
        const playerCombatUnits = this.controller.game.teams.player.units.filter(u => u.damage > 0).length;
        threatLevel += playerCombatUnits * 2;
        
        // Player economic threat
        const playerEconomicPower = (this.controller.game.resourceManager?.getResource('spice') || 0) / 1000;
        threatLevel += playerEconomicPower * 10;
        
        // Recent aggressive actions
        const recentGrievances = this.controller.revengeSystem.grievances.filter(g =>
            this.controller.game.gameTime - g.timestamp < 300
        ).length;
        threatLevel += recentGrievances * 5;
        
        return Math.min(100, threatLevel);
    }
    
    identifyOpportunities() {
        const opportunities = [];
        
        if (this.hasEconomicOpportunities()) {
            opportunities.push('economic_expansion');
        }
        
        if (this.hasMilitaryOpportunities()) {
            opportunities.push('military_advantage');
        }
        
        if (this.hasTerritorialOpportunities()) {
            opportunities.push('territorial_expansion');
        }
        
        return opportunities;
    }
    
    // Additional utility methods would be implemented here...
    hasEconomicOpportunities() {
        return this.identifyExpansionOpportunities().length > 0;
    }
    
    hasMilitaryOpportunities() {
        const aiCombatUnits = this.controller.team.units.filter(u => u.damage > 0).length;
        const playerCombatUnits = this.controller.game.teams.player.units.filter(u => u.damage > 0).length;
        
        return aiCombatUnits > playerCombatUnits * 1.2;
    }
    
    hasTerritorialOpportunities() {
        return this.controller.game.spiceFields.some(field => 
            field.amount > field.maxAmount * 0.5 && this.isSafeForExpansion(field)
        );
    }
    
    identifyExpansionOpportunities() {
        return this.controller.game.spiceFields.filter(field => {
            return field.amount > 0 && this.isSafeForExpansion(field);
        });
    }
    
    isSafeForExpansion(field) {
        const nearestEnemy = this.findNearestPlayerBuilding(field);
        return !nearestEnemy || distance(field, nearestEnemy) > 300;
    }
    
    findNearestPlayerBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.controller.game.teams.player.buildings.forEach(building => {
            const dist = distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    // More utility methods would be implemented as needed...
    assessResourceSecurity() {
        return 0.8; // Placeholder implementation
    }
    
    identifyStrategicPositions() {
        return []; // Placeholder implementation
    }
    
    identifyVulnerableAreas() {
        return []; // Placeholder implementation
    }
    
    assessCombatSituation() {
        return {}; // Placeholder implementation
    }
    
    assessUnitPositioning() {
        return { positioning: 50 }; // Placeholder implementation
    }
    
    assessDefensiveNeeds() {
        return { vulnerableBuildings: [] }; // Placeholder implementation
    }
    
    assessAttackOpportunities() {
        return []; // Placeholder implementation
    }
    
    identifyImmediateThreats() {
        return []; // Placeholder implementation
    }
    
    assessUnitStatus() {
        return {}; // Placeholder implementation
    }
    
    assessImmediateResourceNeeds() {
        return []; // Placeholder implementation
    }
    
    identifyEmergencies() {
        return []; // Placeholder implementation
    }
    
    updateStrategicGoals(assessment) {
        // Update goals based on current situation
        // Implementation would go here
    }
}