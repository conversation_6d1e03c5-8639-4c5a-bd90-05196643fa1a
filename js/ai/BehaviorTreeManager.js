import { BehaviorTreeNodes } from './BehaviorTreeNodes.js';

/**
 * Behavior Tree Manager
 * Manages different AI behavior trees for various personalities
 */
export class BehaviorTreeManager {
    constructor(engine) {
        this.engine = engine;
        
        // Behavior trees for different AI personalities
        this.behaviorTrees = {
            aggressive: new AggressiveBehaviorTree(engine),
            defensive: new DefensiveBehaviorTree(engine),
            cunning: new CunningBehaviorTree(engine),
            economic: new EconomicBehaviorTree(engine),
            balanced: new BalancedBehaviorTree(engine)
        };
    }
    
    getBehaviorTree(personalityType) {
        return this.behaviorTrees[personalityType] || this.behaviorTrees.balanced;
    }
    
    switchBehaviorTree(personalityType) {
        return this.getBehaviorTree(personalityType);
    }
}

/**
 * Base Behavior Tree class
 */
class BehaviorTree {
    constructor(engine) {
        this.engine = engine;
        this.rootNode = null;
        this.lastUpdate = 0;
        this.updateInterval = 1; // Update every second
    }
    
    update(deltaTime) {
        if (this.engine.ai.game.gameTime - this.lastUpdate >= this.updateInterval) {
            this.lastUpdate = this.engine.ai.game.gameTime;
            
            if (this.rootNode) {
                this.rootNode.execute();
            }
        }
    }
}

/**
 * Aggressive AI Behavior Tree
 * Prioritizes combat and expansion
 */
class AggressiveBehaviorTree extends BehaviorTree {
    constructor(engine) {
        super(engine);
        this.buildTree();
    }
    
    buildTree() {
        const { SelectorNode, SequenceNode, ConditionNode, ActionNode } = BehaviorTreeNodes;
        
        this.rootNode = new SelectorNode([
            new SequenceNode([
                new ConditionNode(() => this.hasEnemyInRange()),
                new ActionNode(() => this.attackNearestEnemy())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.hasEnoughUnits()),
                new ActionNode(() => this.launchOffensive())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.needsMoreUnits()),
                new ActionNode(() => this.buildCombatUnits())
            ]),
            new ActionNode(() => this.defaultBehavior())
        ]);
    }
    
    hasEnemyInRange() {
        return this.engine.ai.game.teams.player.units.some(unit => {
            const nearestAI = this.engine.ai.game.findNearestEnemy(unit, 300);
            return nearestAI && this.engine.ai.team.units.includes(nearestAI);
        });
    }
    
    attackNearestEnemy() {
        const combatUnits = this.engine.ai.team.units.filter(u => u.damage > 0);
        combatUnits.forEach(unit => {
            if (!unit.attackTarget) {
                const target = this.engine.ai.game.findNearestPlayerEntity(unit, 400);
                if (target) {
                    unit.attackTarget = target;
                    unit.state = 'attacking';
                }
            }
        });
        return 'success';
    }
    
    hasEnoughUnits() {
        const combatUnits = this.engine.ai.team.units.filter(u => u.damage > 0).length;
        const playerUnits = this.engine.ai.game.teams.player.units.filter(u => u.damage > 0).length;
        return combatUnits >= playerUnits * 1.2;
    }
    
    launchOffensive() {
        this.engine.coordination.attackCoordinator.launchMajorOffensive();
        return 'success';
    }
    
    needsMoreUnits() {
        const combatUnits = this.engine.ai.team.units.filter(u => u.damage > 0).length;
        return combatUnits < 8;
    }
    
    buildCombatUnits() {
        this.engine.coordination.economicCoordinator.prioritizeMilitaryProduction();
        return 'success';
    }
    
    defaultBehavior() {
        return 'success';
    }
}

/**
 * Defensive AI Behavior Tree
 * Prioritizes defense and fortification
 */
class DefensiveBehaviorTree extends BehaviorTree {
    constructor(engine) {
        super(engine);
        this.buildTree();
    }
    
    buildTree() {
        const { SelectorNode, SequenceNode, ConditionNode, ActionNode } = BehaviorTreeNodes;
        
        this.rootNode = new SelectorNode([
            new SequenceNode([
                new ConditionNode(() => this.isUnderAttack()),
                new ActionNode(() => this.defendBase())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.needsDefenses()),
                new ActionNode(() => this.buildDefenses())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.canExpand()),
                new ActionNode(() => this.expandCarefully())
            ]),
            new ActionNode(() => this.fortifyPosition())
        ]);
    }
    
    isUnderAttack() {
        return this.engine.ai.team.buildings.some(building => {
            return this.engine.ai.game.teams.player.units.some(unit =>
                unit.attackTarget === building
            );
        });
    }
    
    defendBase() {
        this.engine.coordination.defenseCoordinator.activateDefenses();
        return 'success';
    }
    
    needsDefenses() {
        const turrets = this.engine.ai.team.buildings.filter(b => b.type === 'turret').length;
        return turrets < 3;
    }
    
    buildDefenses() {
        this.engine.coordination.economicCoordinator.prioritizeDefensiveBuildings();
        return 'success';
    }
    
    canExpand() {
        const defensiveStrength = this.calculateDefensiveStrength();
        return defensiveStrength > 50;
    }
    
    expandCarefully() {
        this.engine.coordination.economicCoordinator.expandWithEscort();
        return 'success';
    }
    
    fortifyPosition() {
        return 'success';
    }
    
    calculateDefensiveStrength() {
        const turrets = this.engine.ai.team.buildings.filter(b => b.type === 'turret').length;
        const defenders = this.engine.ai.team.units.filter(u => u.damage > 0).length;
        return turrets * 25 + defenders * 10;
    }
}

/**
 * Cunning AI Behavior Tree
 * Prioritizes deception and exploitation
 */
class CunningBehaviorTree extends BehaviorTree {
    constructor(engine) {
        super(engine);
        this.buildTree();
    }
    
    buildTree() {
        const { SelectorNode, SequenceNode, ConditionNode, ActionNode } = BehaviorTreeNodes;
        
        this.rootNode = new SelectorNode([
            new SequenceNode([
                new ConditionNode(() => this.canDeceive()),
                new ActionNode(() => this.executeDeception())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.canExploitWeakness()),
                new ActionNode(() => this.exploitWeakness())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.shouldManipulate()),
                new ActionNode(() => this.manipulatePlayer())
            ]),
            new ActionNode(() => this.gatherIntelligence())
        ]);
    }
    
    canDeceive() {
        return Math.random() < 0.3 && this.engine.ai.personalityTraits.cunning > 0.7;
    }
    
    executeDeception() {
        this.engine.advancedBehaviors.deceptionEngine.executeRandomDeception();
        return 'success';
    }
    
    canExploitWeakness() {
        const weaknesses = this.engine.ai.memory.identifyPlayerWeaknesses();
        return weaknesses.length > 0;
    }
    
    exploitWeakness() {
        this.engine.coordination.attackCoordinator.exploitPlayerWeaknesses();
        return 'success';
    }
    
    shouldManipulate() {
        return this.engine.ai.game.diplomacySystem && Math.random() < 0.2;
    }
    
    manipulatePlayer() {
        return 'success';
    }
    
    gatherIntelligence() {
        this.engine.coordination.scoutingCoordinator.gatherIntelligence();
        return 'success';
    }
}

/**
 * Economic AI Behavior Tree
 * Prioritizes economic development
 */
class EconomicBehaviorTree extends BehaviorTree {
    constructor(engine) {
        super(engine);
        this.buildTree();
    }
    
    buildTree() {
        const { SelectorNode, SequenceNode, ConditionNode, ActionNode } = BehaviorTreeNodes;
        
        this.rootNode = new SelectorNode([
            new SequenceNode([
                new ConditionNode(() => this.needsMoreHarvesters()),
                new ActionNode(() => this.buildHarvesters())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.canExpand()),
                new ActionNode(() => this.expandEconomy())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.hasExcessResources()),
                new ActionNode(() => this.investInTechnology())
            ]),
            new ActionNode(() => this.optimizeEconomy())
        ]);
    }
    
    needsMoreHarvesters() {
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
        const spiceFields = this.engine.ai.game.spiceFields.filter(f => f.amount > 0).length;
        return harvesters < Math.min(spiceFields, 5);
    }
    
    buildHarvesters() {
        this.engine.coordination.economicCoordinator.prioritizeHarvesterProduction();
        return 'success';
    }
    
    canExpand() {
        return this.engine.ai.team.spice > 1000;
    }
    
    expandEconomy() {
        this.engine.coordination.economicCoordinator.expandEconomicBase();
        return 'success';
    }
    
    hasExcessResources() {
        return this.engine.ai.team.spice > 2000;
    }
    
    investInTechnology() {
        this.engine.coordination.economicCoordinator.investInResearch();
        return 'success';
    }
    
    optimizeEconomy() {
        return 'success';
    }
}

/**
 * Balanced AI Behavior Tree
 * Balances all aspects of gameplay
 */
class BalancedBehaviorTree extends BehaviorTree {
    constructor(engine) {
        super(engine);
        this.buildTree();
    }
    
    buildTree() {
        const { SelectorNode, SequenceNode, ConditionNode, ActionNode } = BehaviorTreeNodes;
        
        this.rootNode = new SelectorNode([
            new SequenceNode([
                new ConditionNode(() => this.isInDanger()),
                new ActionNode(() => this.prioritizeDefense())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.hasAdvantage()),
                new ActionNode(() => this.pressAdvantage())
            ]),
            new SequenceNode([
                new ConditionNode(() => this.needsEconomy()),
                new ActionNode(() => this.buildEconomy())
            ]),
            new ActionNode(() => this.balancedDevelopment())
        ]);
    }
    
    isInDanger() {
        const aiPower = this.engine.ai.calculateAIPower();
        const playerPower = this.engine.ai.calculatePlayerPower();
        return aiPower < playerPower * 0.7;
    }
    
    prioritizeDefense() {
        this.engine.coordination.defenseCoordinator.strengthenDefenses();
        return 'success';
    }
    
    hasAdvantage() {
        const aiPower = this.engine.ai.calculateAIPower();
        const playerPower = this.engine.ai.calculatePlayerPower();
        return aiPower > playerPower * 1.3;
    }
    
    pressAdvantage() {
        this.engine.coordination.attackCoordinator.pressAdvantage();
        return 'success';
    }
    
    needsEconomy() {
        return this.engine.ai.team.spice < 1000;
    }
    
    buildEconomy() {
        this.engine.coordination.economicCoordinator.focusOnEconomy();
        return 'success';
    }
    
    balancedDevelopment() {
        return 'success';
    }
}