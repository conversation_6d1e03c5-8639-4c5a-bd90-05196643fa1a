import { distance } from '../../utils/index.js';

/**
 * Scouting Coordinator
 * Manages intelligence gathering, reconnaissance, and map awareness
 */
export class ScoutingCoordinator {
    constructor(engine) {
        this.engine = engine;
        this.scouts = [];
        this.scoutingRoutes = [];
        this.intelligenceData = new Map();
        this.messageCallback = null;
        this.surveillanceTargets = [];
        
        // Scouting strategies
        this.strategies = {
            patrol: { coverage: 'wide', frequency: 'regular', risk: 'low' },
            surveillance: { coverage: 'focused', frequency: 'high', risk: 'medium' },
            reconnaissance: { coverage: 'deep', frequency: 'low', risk: 'high' },
            harassment: { coverage: 'tactical', frequency: 'opportunistic', risk: 'high' }
        };
        
        this.currentStrategy = 'patrol';
        this.intelligencePriorities = new Map();
    }
    
    initialize() {
        this.setupScoutingRoutes();
        this.initializeIntelligencePriorities();
        console.log('Scouting Coordinator initialized');
    }
    
    update(deltaTime) {
        this.manageScouts(deltaTime);
        this.updateIntelligence(deltaTime);
        this.analyzeIntelligence(deltaTime);
        this.updateSurveillance(deltaTime);
        this.processScoutingMissions(deltaTime);
    }
    
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    receiveMessage(message) {
        switch (message.type) {
            case 'unit_allocation':
                this.handleScoutAllocation(message);
                break;
            case 'intelligence_request':
                this.handleIntelligenceRequest(message);
                break;
            case 'coordinate_attack':
                this.supportAttackOperation(message);
                break;
        }
    }
    
    // Main scouting methods
    gatherIntelligence() {
        this.assignScoutingMissions();
        this.processIntelligence();
        this.updateIntelligencePriorities();
    }
    
    prioritizeTargetIntelligence() {
        this.switchStrategy('surveillance');
        this.focusOnCombatIntelligence();
    }
    
    provideIntelligence(operation) {
        // Provide intelligence support for operations
        const relevantIntel = this.getRelevantIntelligence(operation.target);
        this.shareIntelligence('attack', relevantIntel);
        
        // Assign scouts to monitor the operation
        this.assignOperationSurveillance(operation);
    }
    
    assignUnits(units) {
        units.forEach(unit => {
            if (this.isSuitableForScouting(unit)) {
                this.recruitScout(unit);
            }
        });
    }
    
    // Scout management
    assignScoutingMissions() {
        const availableScouts = this.getAvailableScouts();
        const missions = this.generateScoutingMissions();
        
        availableScouts.forEach((scout, index) => {
            if (missions[index]) {
                this.assignMission(scout, missions[index]);
            }
        });
    }
    
    manageScouts(deltaTime) {
        this.scouts.forEach(scout => {
            if (scout.health <= 0) return;
            
            this.updateScoutMission(scout, deltaTime);
            this.gatherLocalIntelligence(scout);
            this.checkScoutSafety(scout);
        });
        
        // Remove dead or reassigned scouts
        this.scouts = this.scouts.filter(scout => 
            scout.health > 0 && scout.state === 'scouting'
        );
        
        // Recruit new scouts if needed
        this.maintainScoutCount();
    }
    
    updateScoutMission(scout, deltaTime) {
        if (!scout.mission) return;
        
        switch (scout.mission.type) {
            case 'patrol':
                this.updatePatrolMission(scout);
                break;
            case 'surveillance':
                this.updateSurveillanceMission(scout);
                break;
            case 'reconnaissance':
                this.updateReconMission(scout);
                break;
            case 'harassment':
                this.updateHarassmentMission(scout);
                break;
        }
    }
    
    updatePatrolMission(scout) {
        if (this.hasReachedWaypoint(scout)) {
            this.advancePatrolRoute(scout);
        }
    }
    
    updateSurveillanceMission(scout) {
        if (scout.mission.target && scout.mission.target.health > 0) {
            const distance = this.distance(scout, scout.mission.target);
            
            if (distance > 200) {
                // Move closer to target
                scout.targetX = scout.mission.target.x + (Math.random() - 0.5) * 150;
                scout.targetY = scout.mission.target.y + (Math.random() - 0.5) * 150;
            }
        } else {
            // Target lost, switch to patrol
            this.reassignScout(scout, 'patrol');
        }
    }
    
    updateReconMission(scout) {
        if (this.hasReachedObjective(scout)) {
            this.completeReconMission(scout);
        }
    }
    
    updateHarassmentMission(scout) {
        const target = this.findHarassmentTarget(scout);
        if (target) {
            this.executeHarassment(scout, target);
        } else {
            this.reassignScout(scout, 'patrol');
        }
    }
    
    // Intelligence gathering and analysis
    gatherLocalIntelligence(scout) {
        const currentTime = this.engine.ai.game.gameTime;
        
        if (currentTime - scout.lastIntelligenceUpdate < 10) return;
        
        scout.lastIntelligenceUpdate = currentTime;
        
        const scanRadius = 200;
        const playerEntities = [
            ...this.engine.ai.game.teams.player.units,
            ...this.engine.ai.game.teams.player.buildings
        ].filter(entity => 
            this.distance(scout, entity) < scanRadius
        );
        
        playerEntities.forEach(entity => {
            this.recordIntelligence(entity, scout);
        });
    }
    
    recordIntelligence(entity, scout) {
        const key = `${entity.type}_${Math.floor(entity.x / 100)}_${Math.floor(entity.y / 100)}`;
        
        const intelligence = {
            entityType: entity.type,
            position: { x: entity.x, y: entity.y },
            health: entity.health,
            maxHealth: entity.maxHealth,
            timestamp: this.engine.ai.game.gameTime,
            scoutId: scout.id,
            threat: this.calculateThreatLevel(entity),
            confidence: this.calculateConfidence(scout, entity),
            movement: this.detectMovement(entity, key)
        };
        
        this.intelligenceData.set(key, intelligence);
        
        // Share critical intelligence immediately
        if (intelligence.threat > 70) {
            this.shareIntelligence('defense', intelligence);
        }
    }
    
    analyzeIntelligence(deltaTime) {
        this.identifyPlayerPatterns();
        this.assessThreatLevels();
        this.identifyOpportunities();
        this.updatePlayerBehaviorModel();
    }
    
    identifyPlayerPatterns() {
        const recentIntel = this.getRecentIntelligence(180); // Last 3 minutes
        
        // Group by entity type
        const patterns = {};
        recentIntel.forEach(intel => {
            if (!patterns[intel.entityType]) {
                patterns[intel.entityType] = [];
            }
            patterns[intel.entityType].push(intel);
        });
        
        // Analyze movement patterns
        Object.entries(patterns).forEach(([entityType, instances]) => {
            if (instances.length > 2) {
                const pattern = this.analyzeMovementPattern(instances);
                this.shareIntelligence('coordination', {
                    type: 'movement_pattern',
                    entityType: entityType,
                    pattern: pattern,
                    confidence: this.calculatePatternConfidence(instances)
                });
            }
        });
    }
    
    assessThreatLevels() {
        const threats = Array.from(this.intelligenceData.values())
            .filter(intel => intel.threat > 50)
            .sort((a, b) => b.threat - a.threat);
        
        threats.slice(0, 5).forEach(threat => {
            this.shareIntelligence('defense', {
                type: 'threat_detected',
                position: threat.position,
                entityType: threat.entityType,
                threatLevel: threat.threat,
                confidence: threat.confidence
            });
        });
    }
    
    identifyOpportunities() {
        const opportunities = Array.from(this.intelligenceData.values())
            .filter(intel => this.isOpportunity(intel));
        
        opportunities.forEach(opportunity => {
            this.shareIntelligence('attack', {
                type: 'opportunity_detected',
                position: opportunity.position,
                entityType: opportunity.entityType,
                opportunityType: this.classifyOpportunity(opportunity),
                confidence: opportunity.confidence
            });
        });
    }
    
    // Scouting routes and missions
    setupScoutingRoutes() {
        const mapWidth = this.engine.ai.game.width;
        const mapHeight = this.engine.ai.game.height;
        
        this.scoutingRoutes = [
            {
                name: 'perimeter_patrol',
                type: 'patrol',
                points: this.generatePerimeterPoints(mapWidth, mapHeight),
                priority: 70
            },
            {
                name: 'center_sweep',
                type: 'reconnaissance',
                points: this.generateCenterPoints(mapWidth, mapHeight),
                priority: 60
            },
            {
                name: 'resource_survey',
                type: 'surveillance',
                points: this.generateResourcePoints(),
                priority: 80
            }
        ];
    }
    
    generateScoutingMissions() {
        const missions = [];
        
        // High priority: surveillance of player base
        const playerBase = this.engine.ai.game.teams.player.buildings.find(b => b.type === 'base');
        if (playerBase) {
            missions.push({
                type: 'surveillance',
                target: playerBase,
                priority: 90,
                duration: 300
            });
        }
        
        // Medium priority: patrol routes
        this.scoutingRoutes.forEach(route => {
            if (route.type === 'patrol') {
                missions.push({
                    type: 'patrol',
                    route: route,
                    priority: route.priority,
                    duration: 600
                });
            }
        });
        
        // Low priority: general reconnaissance
        missions.push({
            type: 'reconnaissance',
            objective: 'map_exploration',
            priority: 40,
            duration: 900
        });
        
        return missions.sort((a, b) => b.priority - a.priority);
    }
    
    assignMission(scout, mission) {
        scout.mission = mission;
        scout.missionStartTime = this.engine.ai.game.gameTime;
        scout.state = 'scouting';
        
        switch (mission.type) {
            case 'patrol':
                this.initializePatrolMission(scout, mission);
                break;
            case 'surveillance':
                this.initializeSurveillanceMission(scout, mission);
                break;
            case 'reconnaissance':
                this.initializeReconMission(scout, mission);
                break;
        }
    }
    
    initializePatrolMission(scout, mission) {
        scout.patrolRoute = mission.route;
        scout.routeIndex = 0;
        
        const firstPoint = mission.route.points[0];
        scout.targetX = firstPoint.x;
        scout.targetY = firstPoint.y;
    }
    
    initializeSurveillanceMission(scout, mission) {
        scout.surveillanceTarget = mission.target;
        
        // Position scout for optimal surveillance
        const angle = Math.random() * Math.PI * 2;
        const distance = 150 + Math.random() * 50;
        
        scout.targetX = mission.target.x + Math.cos(angle) * distance;
        scout.targetY = mission.target.y + Math.sin(angle) * distance;
    }
    
    initializeReconMission(scout, mission) {
        scout.reconObjective = mission.objective;
        
        // Set initial reconnaissance target
        const unexploredArea = this.findUnexploredArea();
        if (unexploredArea) {
            scout.targetX = unexploredArea.x;
            scout.targetY = unexploredArea.y;
        }
    }
    
    // Safety and survival
    checkScoutSafety(scout) {
        const nearbyEnemies = this.engine.ai.game.teams.player.units.filter(enemy =>
            enemy.damage > 0 && this.distance(scout, enemy) < 150
        );
        
        if (nearbyEnemies.length > 0) {
            this.executeEvasiveManeuvers(scout, nearbyEnemies);
        }
    }
    
    executeEvasiveManeuvers(scout, enemies) {
        // Calculate safe retreat direction
        const retreatDirection = this.calculateRetreatDirection(scout, enemies);
        
        scout.targetX = scout.x + retreatDirection.x * 200;
        scout.targetY = scout.y + retreatDirection.y * 200;
        scout.state = 'evading';
        scout.evasionStartTime = this.engine.ai.game.gameTime;
        
        // Record enemy positions for intelligence
        enemies.forEach(enemy => {
            this.recordIntelligence(enemy, scout);
        });
    }
    
    calculateRetreatDirection(scout, enemies) {
        // Calculate direction away from enemies
        let avgEnemyX = 0, avgEnemyY = 0;
        
        enemies.forEach(enemy => {
            avgEnemyX += enemy.x;
            avgEnemyY += enemy.y;
        });
        
        avgEnemyX /= enemies.length;
        avgEnemyY /= enemies.length;
        
        const dx = scout.x - avgEnemyX;
        const dy = scout.y - avgEnemyY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        return {
            x: length > 0 ? dx / length : Math.random() - 0.5,
            y: length > 0 ? dy / length : Math.random() - 0.5
        };
    }
    
    // Utility methods
    getAvailableScouts() {
        return this.engine.ai.team.units.filter(unit =>
            this.isSuitableForScouting(unit) && 
            (unit.state === 'idle' || unit.priority === 'low')
        );
    }
    
    isSuitableForScouting(unit) {
        return unit.speed > 25 && unit.health > unit.maxHealth * 0.5;
    }
    
    recruitScout(unit) {
        unit.state = 'scouting';
        unit.scoutRole = 'active';
        unit.lastIntelligenceUpdate = 0;
        
        this.scouts.push(unit);
    }
    
    maintainScoutCount() {
        const targetScoutCount = Math.min(4, Math.floor(this.engine.ai.team.units.length * 0.2));
        
        if (this.scouts.length < targetScoutCount) {
            const availableUnits = this.getAvailableScouts();
            const needed = targetScoutCount - this.scouts.length;
            
            availableUnits.slice(0, needed).forEach(unit => {
                this.recruitScout(unit);
            });
        }
    }
    
    hasReachedWaypoint(scout) {
        return this.distance(scout, { x: scout.targetX, y: scout.targetY }) < 50;
    }
    
    advancePatrolRoute(scout) {
        if (!scout.patrolRoute) return;
        
        scout.routeIndex = (scout.routeIndex + 1) % scout.patrolRoute.points.length;
        const nextPoint = scout.patrolRoute.points[scout.routeIndex];
        
        scout.targetX = nextPoint.x;
        scout.targetY = nextPoint.y;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    calculateThreatLevel(entity) {
        if (entity.isBuilding) {
            const threatValues = {
                'base': 100,
                'turret': 80,
                'factory': 60,
                'barracks': 60,
                'refinery': 40
            };
            return threatValues[entity.type] || 20;
        } else {
            return (entity.damage || 0) + (entity.health || 0) / 10;
        }
    }
    
    calculateConfidence(scout, entity) {
        const distance = this.distance(scout, entity);
        const baseConfidence = Math.max(0.3, 1 - (distance / 300));
        
        // Adjust for scout quality
        const scoutQuality = (scout.health / scout.maxHealth) * (scout.speed / 50);
        
        return Math.min(1, baseConfidence * scoutQuality);
    }
    
    detectMovement(entity, key) {
        const previousIntel = this.intelligenceData.get(key);
        if (!previousIntel) return null;
        
        const dx = entity.x - previousIntel.position.x;
        const dy = entity.y - previousIntel.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 10) {
            return {
                direction: { x: dx, y: dy },
                speed: distance / (this.engine.ai.game.gameTime - previousIntel.timestamp),
                distance: distance
            };
        }
        
        return null;
    }
    
    getRecentIntelligence(timeWindow) {
        const cutoffTime = this.engine.ai.game.gameTime - timeWindow;
        
        return Array.from(this.intelligenceData.values())
            .filter(intel => intel.timestamp >= cutoffTime);
    }
    
    shareIntelligence(recipient, intelligence) {
        this.sendMessage(recipient, {
            type: 'share_intelligence',
            data: intelligence,
            source: 'scouting',
            reliability: intelligence.confidence || 0.8
        });
    }
    
    sendMessage(recipient, message) {
        if (this.messageCallback) {
            message.recipient = recipient;
            message.sender = 'scouting';
            this.messageCallback(message);
        }
    }
    
    // Route generation helpers
    generatePerimeterPoints(width, height) {
        const margin = 100;
        return [
            { x: margin, y: margin },
            { x: width - margin, y: margin },
            { x: width - margin, y: height - margin },
            { x: margin, y: height - margin }
        ];
    }
    
    generateCenterPoints(width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 4;
        
        const points = [];
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            points.push({
                x: centerX + Math.cos(angle) * radius,
                y: centerY + Math.sin(angle) * radius
            });
        }
        
        return points;
    }
    
    generateResourcePoints() {
        return this.engine.ai.game.spiceFields.map(field => ({
            x: field.x,
            y: field.y
        }));
    }
    
    initializeIntelligencePriorities() {
        this.intelligencePriorities.set('player_base', 100);
        this.intelligencePriorities.set('military_units', 80);
        this.intelligencePriorities.set('economic_targets', 70);
        this.intelligencePriorities.set('defensive_structures', 60);
        this.intelligencePriorities.set('expansion_sites', 50);
    }
    
    updateIntelligence(deltaTime) {
        const maxAge = 300; // 5 minutes
        const currentTime = this.engine.ai.game.gameTime;
        
        this.intelligenceData.forEach((intel, key) => {
            if (currentTime - intel.timestamp > maxAge) {
                this.intelligenceData.delete(key);
            }
        });
    }
    
    processIntelligence() {
        const recentIntel = this.getRecentIntelligence(120);
        
        if (recentIntel.length > 0) {
            const summary = this.generateIntelligenceSummary(recentIntel);
            this.shareIntelligence('coordination', summary);
        }
    }
    
    generateIntelligenceSummary(intelligence) {
        return {
            type: 'intelligence_summary',
            totalContacts: intelligence.length,
            threatLevel: this.calculateOverallThreatLevel(intelligence),
            playerActivity: this.assessPlayerActivity(intelligence),
            recommendations: this.generateRecommendations(intelligence)
        };
    }
    
    calculateOverallThreatLevel(intelligence) {
        const totalThreat = intelligence.reduce((sum, intel) => sum + intel.threat, 0);
        return totalThreat / Math.max(1, intelligence.length);
    }
    
    assessPlayerActivity(intelligence) {
        const movingUnits = intelligence.filter(intel => intel.movement).length;
        return movingUnits / Math.max(1, intelligence.length);
    }
    
    generateRecommendations(intelligence) {
        const recommendations = [];
        
        const highThreats = intelligence.filter(intel => intel.threat > 70);
        if (highThreats.length > 2) {
            recommendations.push('increase_defense');
        }
        
        const vulnerableTargets = intelligence.filter(intel => 
            intel.health < intel.maxHealth * 0.3
        );
        if (vulnerableTargets.length > 0) {
            recommendations.push('attack_opportunity');
        }
        
        return recommendations;
    }
}