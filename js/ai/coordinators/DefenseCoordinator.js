import { distance } from '../../utils/index.js';

/**
 * Defense Coordinator
 * Manages defensive operations, base protection, and emergency responses
 */
export class DefenseCoordinator {
    constructor(engine) {
        this.engine = engine;
        this.defensePoints = [];
        this.alertLevel = 0;
        this.messageCallback = null;
        this.emergencyProtocols = [];
        
        // Defense strategies
        this.strategies = {
            perimeter: { range: 'wide', depth: 'shallow', mobility: 'high' },
            fortress: { range: 'narrow', depth: 'deep', mobility: 'low' },
            mobile: { range: 'medium', depth: 'medium', mobility: 'high' },
            layered: { range: 'wide', depth: 'deep', mobility: 'medium' }
        };
        
        this.currentStrategy = 'perimeter';
        this.defenseFormations = new Map();
    }
    
    initialize() {
        this.setupDefensePoints();
        this.initializeDefenseFormations();
        console.log('Defense Coordinator initialized');
    }
    
    update(deltaTime) {
        this.updateAlertLevel();
        this.coordinateDefenses(deltaTime);
        this.manageDefensePoints(deltaTime);
        this.updateDefenseFormations(deltaTime);
        this.processEmergencyProtocols(deltaTime);
    }
    
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    receiveMessage(message) {
        switch (message.type) {
            case 'unit_allocation':
                this.handleUnitAllocation(message);
                break;
            case 'support_request':
                this.provideSupportDefense(message);
                break;
            case 'threat_detected':
                this.handleThreatDetection(message);
                break;
        }
    }
    
    receiveIntelligence(intelligence) {
        if (intelligence.type === 'enemy_movement') {
            this.adjustDefensePositions(intelligence);
        } else if (intelligence.type === 'incoming_attack') {
            this.prepareDefenses(intelligence);
        }
    }
    
    // Main defense methods
    emergencyDefense(target) {
        const nearbyDefenders = this.engine.ai.team.units.filter(unit =>
            unit.damage > 0 && this.distance(unit, target) < 400
        );
        
        nearbyDefenders.forEach(unit => {
            unit.attackTarget = null;
            unit.targetX = target.x;
            unit.targetY = target.y;
            unit.state = 'emergency_defense';
            unit.priority = 'defend_base';
            unit.emergencyTarget = target;
        });
        
        this.alertLevel = Math.min(5, this.alertLevel + 2);
        this.activateEmergencyProtocol('base_defense', target);
        
        console.log(`Emergency defense activated for ${target.type}`);
    }
    
    coordinateLastStand() {
        const mainBase = this.engine.ai.team.buildings.find(b => b.type === 'base');
        if (!mainBase) return;
        
        this.engine.ai.team.units.forEach(unit => {
            if (unit.damage > 0) {
                unit.attackTarget = null;
                unit.targetX = mainBase.x + (Math.random() - 0.5) * 200;
                unit.targetY = mainBase.y + (Math.random() - 0.5) * 200;
                unit.state = 'last_stand';
                unit.priority = 'desperate_defense';
                unit.lastStandPosition = { x: unit.targetX, y: unit.targetY };
            }
        });
        
        this.alertLevel = 5;
        this.activateEmergencyProtocol('last_stand', mainBase);
        
        console.log('Last stand defense activated!');
    }
    
    activateDefenses() {
        this.defensePoints.forEach(point => {
            this.assignDefenders(point);
            this.activateDefensePoint(point);
        });
        
        this.alertLevel = Math.min(5, this.alertLevel + 1);
        this.switchDefenseStrategy('fortress');
    }
    
    strengthenDefenses() {
        this.requestDefensiveBuildings();
        this.repositionDefenders();
        this.upgradeDefensePoints();
        
        this.sendMessage('economy', {
            type: 'resource_request',
            amount: 500,
            purpose: 'defensive_structures'
        });
    }
    
    // Defense point management
    setupDefensePoints() {
        this.engine.ai.team.buildings.forEach(building => {
            if (['base', 'factory', 'refinery'].includes(building.type)) {
                this.defensePoints.push({
                    id: `defense_${building.id}`,
                    building: building,
                    position: { x: building.x, y: building.y },
                    priority: this.calculateDefensePriority(building),
                    defenders: [],
                    threatLevel: 0,
                    defenseRadius: this.calculateDefenseRadius(building),
                    strategy: 'perimeter'
                });
            }
        });
    }
    
    manageDefensePoints(deltaTime) {
        // Add new defense points for new buildings
        this.engine.ai.team.buildings.forEach(building => {
            if (['base', 'factory', 'refinery'].includes(building.type)) {
                const exists = this.defensePoints.some(point => point.building === building);
                if (!exists) {
                    this.addDefensePoint(building);
                }
            }
        });
        
        // Remove defense points for destroyed buildings
        this.defensePoints = this.defensePoints.filter(point => 
            point.building.health > 0
        );
        
        // Update defense points
        this.defensePoints.forEach(point => {
            this.updateDefensePoint(point);
        });
    }
    
    addDefensePoint(building) {
        const defensePoint = {
            id: `defense_${building.id}`,
            building: building,
            position: { x: building.x, y: building.y },
            priority: this.calculateDefensePriority(building),
            defenders: [],
            threatLevel: 0,
            defenseRadius: this.calculateDefenseRadius(building),
            strategy: this.currentStrategy
        };
        
        this.defensePoints.push(defensePoint);
        this.assignDefenders(defensePoint);
    }
    
    updateDefensePoint(point) {
        // Update threat level
        const nearbyEnemies = this.engine.ai.game.teams.player.units.filter(enemy =>
            this.distance(enemy, point.position) < point.defenseRadius
        );
        
        point.threatLevel = nearbyEnemies.reduce((total, enemy) => 
            total + this.calculateUnitThreat(enemy), 0
        );
        
        // Clean up dead defenders
        point.defenders = point.defenders.filter(defender => defender.health > 0);
        
        // Reassign defenders if needed
        if (point.defenders.length < this.getRequiredDefenders(point)) {
            this.assignDefenders(point);
        }
    }
    
    assignDefenders(defensePoint) {
        const requiredDefenders = this.getRequiredDefenders(defensePoint);
        const currentDefenders = defensePoint.defenders.filter(d => d.health > 0).length;
        
        if (currentDefenders < requiredDefenders) {
            const availableUnits = this.engine.ai.team.units.filter(unit =>
                unit.damage > 0 && 
                (unit.state === 'idle' || unit.priority === 'low') && 
                this.distance(unit, defensePoint.position) < 500
            );
            
            const needed = requiredDefenders - currentDefenders;
            const newDefenders = availableUnits.slice(0, needed);
            
            newDefenders.forEach(unit => {
                this.assignUnitToDefense(unit, defensePoint);
            });
        }
    }
    
    assignUnitToDefense(unit, defensePoint) {
        unit.state = 'defending';
        unit.defensePoint = defensePoint;
        unit.defensePosition = this.calculateDefensePosition(unit, defensePoint);
        unit.targetX = unit.defensePosition.x;
        unit.targetY = unit.defensePosition.y;
        unit.priority = 'defense';
        
        defensePoint.defenders.push(unit);
    }
    
    // Defense formations and strategies
    initializeDefenseFormations() {
        this.defenseFormations.set('circle', {
            positions: this.generateCircleFormation,
            spacing: 80,
            depth: 1
        });
        
        this.defenseFormations.set('line', {
            positions: this.generateLineFormation,
            spacing: 60,
            depth: 2
        });
        
        this.defenseFormations.set('wedge', {
            positions: this.generateWedgeFormation,
            spacing: 70,
            depth: 3
        });
    }
    
    switchDefenseStrategy(strategy) {
        if (this.strategies[strategy]) {
            this.currentStrategy = strategy;
            
            this.defensePoints.forEach(point => {
                point.strategy = strategy;
                this.repositionDefensePoint(point);
            });
            
            console.log(`Switched to ${strategy} defense strategy`);
        }
    }
    
    repositionDefensePoint(point) {
        const formation = this.selectFormation(point);
        const positions = formation.positions(point, point.defenders.length);
        
        point.defenders.forEach((defender, index) => {
            if (positions[index]) {
                defender.defensePosition = positions[index];
                defender.targetX = positions[index].x;
                defender.targetY = positions[index].y;
            }
        });
    }
    
    selectFormation(defensePoint) {
        switch (defensePoint.strategy) {
            case 'perimeter':
                return this.defenseFormations.get('circle');
            case 'fortress':
                return this.defenseFormations.get('line');
            case 'mobile':
                return this.defenseFormations.get('wedge');
            default:
                return this.defenseFormations.get('circle');
        }
    }
    
    // Alert and threat management
    updateAlertLevel() {
        let totalThreat = 0;
        
        this.engine.ai.game.teams.player.units.forEach(enemy => {
            const nearestBuilding = this.findNearestAIBuilding(enemy);
            if (nearestBuilding && this.distance(enemy, nearestBuilding) < 300) {
                totalThreat += this.calculateUnitThreat(enemy);
            }
        });
        
        this.alertLevel = Math.min(5, totalThreat / 100);
        
        // Decay alert level over time
        if (totalThreat === 0) {
            this.alertLevel = Math.max(0, this.alertLevel - 0.1);
        }
        
        // Adjust defense strategy based on alert level
        this.adjustDefenseStrategy();
    }
    
    adjustDefenseStrategy() {
        if (this.alertLevel > 4) {
            this.switchDefenseStrategy('fortress');
        } else if (this.alertLevel > 2) {
            this.switchDefenseStrategy('layered');
        } else if (this.alertLevel < 1) {
            this.switchDefenseStrategy('perimeter');
        }
    }
    
    // Emergency protocols
    activateEmergencyProtocol(type, target) {
        const protocol = {
            id: `emergency_${Date.now()}`,
            type: type,
            target: target,
            startTime: this.engine.ai.game.gameTime,
            active: true,
            participants: []
        };
        
        this.emergencyProtocols.push(protocol);
        
        switch (type) {
            case 'base_defense':
                this.executeBaseDefenseProtocol(protocol);
                break;
            case 'last_stand':
                this.executeLastStandProtocol(protocol);
                break;
            case 'evacuation':
                this.executeEvacuationProtocol(protocol);
                break;
        }
    }
    
    executeBaseDefenseProtocol(protocol) {
        // Recall all units to defend the base
        const defenders = this.engine.ai.team.units.filter(u => u.damage > 0);
        
        defenders.forEach(unit => {
            if (this.distance(unit, protocol.target) > 200) {
                unit.state = 'emergency_recall';
                unit.targetX = protocol.target.x + (Math.random() - 0.5) * 150;
                unit.targetY = protocol.target.y + (Math.random() - 0.5) * 150;
                unit.emergencyProtocol = protocol.id;
                protocol.participants.push(unit);
            }
        });
    }
    
    executeLastStandProtocol(protocol) {
        // All units form defensive perimeter around main base
        const defenders = this.engine.ai.team.units.filter(u => u.damage > 0);
        const positions = this.generateCircleFormation(protocol.target, defenders.length, 100);
        
        defenders.forEach((unit, index) => {
            if (positions[index]) {
                unit.targetX = positions[index].x;
                unit.targetY = positions[index].y;
                unit.state = 'last_stand';
                unit.emergencyProtocol = protocol.id;
                protocol.participants.push(unit);
            }
        });
    }
    
    processEmergencyProtocols(deltaTime) {
        this.emergencyProtocols = this.emergencyProtocols.filter(protocol => {
            const age = this.engine.ai.game.gameTime - protocol.startTime;
            
            // Check if protocol should end
            if (this.shouldEndProtocol(protocol) || age > 300) { // 5 minutes max
                this.endEmergencyProtocol(protocol);
                return false;
            }
            
            return true;
        });
    }
    
    shouldEndProtocol(protocol) {
        switch (protocol.type) {
            case 'base_defense':
                return protocol.target.health <= 0 || this.alertLevel < 2;
            case 'last_stand':
                return this.alertLevel < 3;
            default:
                return false;
        }
    }
    
    endEmergencyProtocol(protocol) {
        protocol.participants.forEach(unit => {
            if (unit.health > 0) {
                unit.emergencyProtocol = null;
                unit.state = 'idle';
            }
        });
        
        console.log(`Emergency protocol ${protocol.type} ended`);
    }
    
    // Coordination methods
    coordinateDefenses(deltaTime) {
        this.defensePoints.forEach(point => {
            this.coordinateDefensePoint(point);
        });
        
        this.coordinateDefenseResponse();
    }
    
    coordinateDefensePoint(point) {
        if (point.threatLevel > 0) {
            const nearbyEnemies = this.engine.ai.game.teams.player.units.filter(enemy =>
                this.distance(enemy, point.position) < point.defenseRadius
            );
            
            this.assignTargets(point.defenders, nearbyEnemies);
        } else {
            this.returnToDefensePositions(point.defenders);
        }
    }
    
    assignTargets(defenders, enemies) {
        // Prioritize targets and assign defenders
        const prioritizedEnemies = enemies.sort((a, b) => 
            this.calculateUnitThreat(b) - this.calculateUnitThreat(a)
        );
        
        defenders.forEach((defender, index) => {
            const target = prioritizedEnemies[index % prioritizedEnemies.length];
            if (target && (!defender.attackTarget || defender.attackTarget.health <= 0)) {
                defender.attackTarget = target;
                defender.state = 'attacking';
            }
        });
    }
    
    returnToDefensePositions(defenders) {
        defenders.forEach(defender => {
            if (defender.state === 'attacking' && !defender.attackTarget) {
                defender.state = 'defending';
                defender.targetX = defender.defensePosition.x;
                defender.targetY = defender.defensePosition.y;
            }
        });
    }
    
    coordinateDefenseResponse() {
        // Coordinate response to multiple threats
        const highThreatPoints = this.defensePoints.filter(p => p.threatLevel > 50);
        
        if (highThreatPoints.length > 1) {
            this.requestReinforcements(highThreatPoints);
        }
    }
    
    requestReinforcements(threatPoints) {
        this.sendMessage('coordination', {
            type: 'request_units',
            unitType: 'combat',
            count: threatPoints.length * 2,
            purpose: 'defense_reinforcement'
        });
    }
    
    // Formation generators
    generateCircleFormation(center, count, radius = 80) {
        const positions = [];
        const angleStep = (Math.PI * 2) / count;
        
        for (let i = 0; i < count; i++) {
            const angle = i * angleStep;
            positions.push({
                x: center.x + Math.cos(angle) * radius,
                y: center.y + Math.sin(angle) * radius
            });
        }
        
        return positions;
    }
    
    generateLineFormation(center, count, spacing = 60) {
        const positions = [];
        const startX = center.x - ((count - 1) * spacing) / 2;
        
        for (let i = 0; i < count; i++) {
            positions.push({
                x: startX + i * spacing,
                y: center.y
            });
        }
        
        return positions;
    }
    
    generateWedgeFormation(center, count, spacing = 70) {
        const positions = [];
        let currentRow = 0;
        let unitsInRow = 1;
        let unitsPlaced = 0;
        
        while (unitsPlaced < count) {
            const rowStartX = center.x - ((unitsInRow - 1) * spacing) / 2;
            const rowY = center.y + currentRow * spacing;
            
            for (let i = 0; i < unitsInRow && unitsPlaced < count; i++) {
                positions.push({
                    x: rowStartX + i * spacing,
                    y: rowY
                });
                unitsPlaced++;
            }
            
            currentRow++;
            unitsInRow += 2;
        }
        
        return positions;
    }
    
    // Utility methods
    calculateDefensePriority(building) {
        const priorities = {
            'base': 100,
            'factory': 80,
            'refinery': 70,
            'barracks': 60,
            'powerplant': 50
        };
        
        return priorities[building.type] || 30;
    }
    
    calculateDefenseRadius(building) {
        const radii = {
            'base': 200,
            'factory': 150,
            'refinery': 120,
            'barracks': 100
        };
        
        return radii[building.type] || 100;
    }
    
    calculateUnitThreat(unit) {
        return (unit.damage || 0) + (unit.health || 0) / 10;
    }
    
    getRequiredDefenders(defensePoint) {
        const baseDef = Math.ceil(defensePoint.priority / 30);
        const threatMod = Math.ceil(defensePoint.threatLevel / 50);
        return Math.min(6, baseDef + threatMod);
    }
    
    calculateDefensePosition(unit, defensePoint) {
        const angle = Math.random() * Math.PI * 2;
        const radius = 80 + Math.random() * 40;
        
        return {
            x: defensePoint.position.x + Math.cos(angle) * radius,
            y: defensePoint.position.y + Math.sin(angle) * radius
        };
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    findNearestAIBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.engine.ai.team.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    sendMessage(recipient, message) {
        if (this.messageCallback) {
            message.recipient = recipient;
            message.sender = 'defense';
            this.messageCallback(message);
        }
    }
    
    requestDefensiveBuildings() {
        const turrets = this.engine.ai.team.buildings.filter(b => b.type === 'turret').length;
        const bases = this.engine.ai.team.buildings.filter(b => b.type === 'base').length;
        
        if (turrets < bases * 2) {
            this.sendMessage('economy', {
                type: 'building_request',
                buildingType: 'turret',
                priority: 75
            });
        }
    }
    
    repositionDefenders() {
        this.defensePoints.forEach(point => {
            this.repositionDefensePoint(point);
        });
    }
    
    upgradeDefensePoints() {
        this.defensePoints.forEach(point => {
            if (point.threatLevel > 75) {
                point.priority = Math.min(100, point.priority + 10);
                point.defenseRadius = Math.min(300, point.defenseRadius + 20);
            }
        });
    }
}