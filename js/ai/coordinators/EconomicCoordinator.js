import { distance } from '../../utils/index.js';

/**
 * Economic Coordinator
 * Manages resource gathering, building construction, and economic strategy
 */
export class EconomicCoordinator {
    constructor(engine) {
        this.engine = engine;
        this.buildQueue = [];
        this.expansionPlans = [];
        this.messageCallback = null;
        this.productionPriorities = new Map();
        
        // Economic strategies
        this.strategies = {
            rush: { speed: 'high', efficiency: 'low', risk: 'high' },
            boom: { speed: 'low', efficiency: 'high', risk: 'low' },
            balanced: { speed: 'medium', efficiency: 'medium', risk: 'medium' },
            turtle: { speed: 'low', efficiency: 'medium', risk: 'very_low' }
        };
        
        this.currentStrategy = 'balanced';
        this.resourceTargets = {
            spice: 1000,
            power: 0,
            harvesters: 3
        };
    }
    
    initialize() {
        this.setupInitialProduction();
        this.assessResourceSituation();
        console.log('Economic Coordinator initialized');
    }
    
    update(deltaTime) {
        this.processBuildQueue(deltaTime);
        this.manageExpansion(deltaTime);
        this.optimizeEconomy(deltaTime);
        this.updateProductionPriorities();
        this.monitorResourceFlow(deltaTime);
    }
    
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    receiveMessage(message) {
        switch (message.type) {
            case 'resource_allocated':
                this.handleResourceAllocation(message);
                break;
            case 'building_request':
                this.handleBuildingRequest(message);
                break;
            case 'production_request':
                this.handleProductionRequest(message);
                break;
        }
    }
    
    receiveIntelligence(intelligence) {
        if (intelligence.type === 'resource_location') {
            this.planExpansion(intelligence);
        } else if (intelligence.type === 'economic_threat') {
            this.adjustEconomicStrategy(intelligence);
        }
    }
    
    // Emergency economic methods
    emergencyResourceGathering() {
        this.prioritizeHarvesterProduction();
        this.sendIdleUnitsToSpice();
        this.switchStrategy('rush');
        
        console.log('Emergency resource gathering activated');
    }
    
    rushMilitaryProduction() {
        this.prioritizeMilitaryProduction();
        this.requestBuilding('barracks', 95);
        this.requestBuilding('factory', 90);
        this.allocateResourcesForMilitary();
        
        console.log('Rush military production activated');
    }
    
    prioritizeDefensiveProduction() {
        this.requestBuilding('turret', 85);
        this.requestBuilding('powerplant', 80);
        this.allocateResourcesForDefense();
    }
    
    emergencyMode(operation) {
        // Adjust all economic activities for emergency
        this.pauseNonEssentialConstruction();
        this.reallocateWorkers();
        this.maximizeResourceIncome();
        
        console.log('Economic emergency mode activated');
    }
    
    // Production management
    prioritizeHarvesterProduction() {
        this.addToBuildQueue({
            type: 'unit',
            unitType: 'harvester',
            priority: 100,
            urgent: true
        });
    }
    
    prioritizeMilitaryProduction() {
        const militaryUnits = ['soldier', 'tank', 'rocketeer'];
        militaryUnits.forEach((unitType, index) => {
            this.addToBuildQueue({
                type: 'unit',
                unitType: unitType,
                priority: 90 - index * 5,
                urgent: true
            });
        });
    }
    
    focusOnEconomy() {
        this.requestBuilding('refinery', 70);
        this.requestBuilding('powerplant', 65);
        this.prioritizeHarvesterProduction();
        this.switchStrategy('boom');
    }
    
    expandEconomicBase() {
        const expansionSite = this.findBestExpansionSite();
        if (expansionSite) {
            this.planExpansionToSite(expansionSite);
        }
    }
    
    investInResearch() {
        if (this.engine.ai.game.technologySystem) {
            const hasResearchLab = this.engine.ai.team.buildings.some(b => 
                b.type === 'research_lab' && b.constructionProgress >= 100
            );
            
            if (!hasResearchLab) {
                this.requestBuilding('research_lab', 60);
            }
        }
    }
    
    expandWithEscort() {
        const expansionSite = this.findBestExpansionSite();
        if (expansionSite) {
            this.expansionPlans.push({
                site: expansionSite,
                phase: 'planning',
                escort: this.requestEscort(),
                timestamp: this.engine.ai.game.gameTime,
                escorted: true
            });
        }
    }
    
    expandToLocation(location) {
        this.expansionPlans.push({
            site: location,
            phase: 'execution',
            escort: null,
            timestamp: this.engine.ai.game.gameTime,
            escorted: false
        });
    }
    
    // Build queue management
    addToBuildQueue(item) {
        item.timestamp = this.engine.ai.game.gameTime;
        item.id = `build_${Date.now()}`;
        
        this.buildQueue.push(item);
        this.sortBuildQueue();
    }
    
    requestBuilding(buildingType, priority = 50, location = null) {
        this.addToBuildQueue({
            type: 'building',
            buildingType: buildingType,
            priority: priority,
            location: location
        });
    }
    
    processBuildQueue(deltaTime) {
        if (this.buildQueue.length === 0) return;
        
        const nextItem = this.buildQueue[0];
        
        if (this.canAffordItem(nextItem)) {
            if (this.executeItem(nextItem)) {
                this.buildQueue.shift();
                console.log(`Completed build item: ${nextItem.type} ${nextItem.buildingType || nextItem.unitType}`);
            }
        } else if (this.shouldSkipItem(nextItem)) {
            this.buildQueue.shift();
            console.log(`Skipped build item: ${nextItem.type} ${nextItem.buildingType || nextItem.unitType}`);
        }
    }
    
    sortBuildQueue() {
        this.buildQueue.sort((a, b) => {
            if (a.urgent && !b.urgent) return -1;
            if (!a.urgent && b.urgent) return 1;
            return b.priority - a.priority;
        });
    }
    
    canAffordItem(item) {
        const cost = this.getItemCost(item);
        return cost && this.engine.ai.team.spice >= cost;
    }
    
    shouldSkipItem(item) {
        const age = this.engine.ai.game.gameTime - item.timestamp;
        return age > 300 && !item.urgent; // Skip items older than 5 minutes
    }
    
    executeItem(item) {
        if (item.type === 'building') {
            return this.buildBuilding(item);
        } else if (item.type === 'unit') {
            return this.buildUnit(item);
        }
        return false;
    }
    
    buildBuilding(item) {
        const buildingData = this.engine.ai.game.buildingTypes[item.buildingType];
        if (!buildingData) return false;
        
        const location = item.location || this.findBuildingLocation(item.buildingType);
        if (!location) return false;
        
        if (this.engine.ai.game.isValidBuildingPlacement(location.x, location.y, buildingData)) {
            this.engine.ai.game.addBuilding({
                type: item.buildingType,
                x: location.x,
                y: location.y,
                team: this.engine.ai.teamId
            });
            
            this.engine.ai.team.spice -= buildingData.cost;
            return true;
        }
        
        return false;
    }
    
    buildUnit(item) {
        const unitData = this.engine.ai.game.unitTypes[item.unitType];
        if (!unitData) return false;
        
        const productionBuilding = this.findProductionBuilding(item.unitType);
        if (!productionBuilding) {
            // Request appropriate building
            const requiredBuilding = this.getRequiredBuilding(item.unitType);
            if (requiredBuilding) {
                this.requestBuilding(requiredBuilding, 80);
            }
            return false;
        }
        
        this.engine.ai.trainUnit(item.unitType, productionBuilding.id);
        return true;
    }
    
    // Resource management
    assessResourceSituation() {
        const situation = {
            spice: this.engine.ai.team.spice,
            spiceIncome: this.calculateSpiceIncome(),
            powerBalance: this.calculatePowerBalance(),
            harvesterCount: this.getHarvesterCount(),
            spiceFieldsAvailable: this.getAvailableSpiceFields().length
        };
        
        this.adjustTargetsBasedOnSituation(situation);
        return situation;
    }
    
    optimizeEconomy(deltaTime) {
        this.optimizeHarvesters();
        this.optimizePowerProduction();
        this.optimizeResourceAllocation();
        this.optimizeExpansion();
    }
    
    optimizeHarvesters() {
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester');
        const availableFields = this.getAvailableSpiceFields();
        
        // Ensure we have enough harvesters
        const optimalHarvesters = Math.min(availableFields.length, this.resourceTargets.harvesters);
        if (harvesters.length < optimalHarvesters) {
            this.prioritizeHarvesterProduction();
        }
        
        // Reassign idle harvesters
        harvesters.forEach(harvester => {
            if (harvester.state === 'idle') {
                const nearestField = this.findNearestSpiceField(harvester);
                if (nearestField) {
                    this.assignHarvesterToField(harvester, nearestField);
                }
            }
        });
    }
    
    optimizePowerProduction() {
        const powerBalance = this.calculatePowerBalance();
        
        if (powerBalance < -10) {
            this.requestBuilding('powerplant', 75);
        }
    }
    
    optimizeResourceAllocation() {
        const situation = this.assessResourceSituation();
        
        if (situation.spiceIncome < 5 && situation.spice > 1000) {
            this.prioritizeHarvesterProduction();
            this.requestBuilding('refinery', 70);
        } else if (situation.spiceIncome > 10 && situation.spice > 2000) {
            this.prioritizeMilitaryProduction();
        }
    }
    
    optimizeExpansion() {
        const availableFields = this.getAvailableSpiceFields();
        const currentHarvesters = this.getHarvesterCount();
        
        if (availableFields.length > currentHarvesters && this.engine.ai.team.spice > 1500) {
            this.planNewExpansion();
        }
    }
    
    // Expansion management
    manageExpansion(deltaTime) {
        this.expansionPlans.forEach((plan, index) => {
            switch (plan.phase) {
                case 'planning':
                    if (this.engine.ai.game.gameTime - plan.timestamp > 60) {
                        plan.phase = 'execution';
                    }
                    break;
                case 'execution':
                    this.executeExpansion(plan);
                    this.expansionPlans.splice(index, 1);
                    break;
            }
        });
    }
    
    executeExpansion(plan) {
        // Build refinery near the expansion site
        this.requestBuilding('refinery', 80, plan.site);
        
        // Send escort if planned
        if (plan.escort && plan.escorted) {
            this.deployEscort(plan.escort, plan.site);
        }
        
        // Build harvester for the new site
        this.addToBuildQueue({
            type: 'unit',
            unitType: 'harvester',
            priority: 75,
            assignedField: plan.site
        });
    }
    
    planNewExpansion() {
        const expansionSite = this.findBestExpansionSite();
        if (expansionSite) {
            this.expandToLocation(expansionSite);
        }
    }
    
    findBestExpansionSite() {
        const availableFields = this.getAvailableSpiceFields();
        
        return availableFields
            .filter(field => this.isExpansionViable(field))
            .sort((a, b) => this.calculateExpansionValue(b) - this.calculateExpansionValue(a))[0];
    }
    
    isExpansionViable(field) {
        const nearestPlayerBuilding = this.findNearestPlayerBuilding(field);
        const nearestAIBuilding = this.findNearestAIBuilding(field);
        
        return field.amount > field.maxAmount * 0.3 &&
               (!nearestPlayerBuilding || this.distance(field, nearestPlayerBuilding) > 300) &&
               (!nearestAIBuilding || this.distance(field, nearestAIBuilding) > 200);
    }
    
    calculateExpansionValue(field) {
        let value = field.amount;
        
        // Distance penalty
        const nearestBase = this.findNearestAIBuilding(field);
        if (nearestBase) {
            const distance = this.distance(field, nearestBase);
            value -= distance / 10;
        }
        
        // Safety bonus
        const nearestEnemy = this.findNearestPlayerBuilding(field);
        if (nearestEnemy) {
            const distance = this.distance(field, nearestEnemy);
            value += Math.min(100, distance / 5);
        }
        
        return value;
    }
    
    // Strategy management
    switchStrategy(strategy) {
        if (this.strategies[strategy]) {
            this.currentStrategy = strategy;
            this.adjustTargetsForStrategy(strategy);
            console.log(`Economic strategy switched to: ${strategy}`);
        }
    }
    
    adjustTargetsForStrategy(strategy) {
        switch (strategy) {
            case 'rush':
                this.resourceTargets.spice = 500;
                this.resourceTargets.harvesters = 2;
                break;
            case 'boom':
                this.resourceTargets.spice = 2000;
                this.resourceTargets.harvesters = 5;
                break;
            case 'turtle':
                this.resourceTargets.spice = 1500;
                this.resourceTargets.harvesters = 3;
                break;
            default:
                this.resourceTargets.spice = 1000;
                this.resourceTargets.harvesters = 3;
        }
    }
    
    adjustTargetsBasedOnSituation(situation) {
        if (situation.spice < 200) {
            this.resourceTargets.harvesters = Math.max(2, this.resourceTargets.harvesters);
        }
        
        if (situation.spiceFieldsAvailable > 4) {
            this.resourceTargets.harvesters = Math.min(6, this.resourceTargets.harvesters + 1);
        }
    }
    
    // Production priorities
    updateProductionPriorities() {
        this.productionPriorities.clear();
        
        const situation = this.assessResourceSituation();
        
        // Set priorities based on current needs
        if (situation.harvesterCount < this.resourceTargets.harvesters) {
            this.productionPriorities.set('harvester', 90);
        }
        
        if (situation.powerBalance < 0) {
            this.productionPriorities.set('powerplant', 85);
        }
        
        if (situation.spice > this.resourceTargets.spice) {
            this.productionPriorities.set('military', 70);
        }
        
        if (this.engine.ai.team.buildings.filter(b => b.type === 'turret').length < 2) {
            this.productionPriorities.set('turret', 60);
        }
    }
    
    // Utility methods
    calculateSpiceIncome() {
        const harvesters = this.getHarvesterCount();
        return harvesters * 2; // Approximate income per harvester
    }
    
    calculatePowerBalance() {
        const production = this.engine.ai.team.buildings
            .filter(b => b.type === 'powerplant' && b.constructionProgress >= 100)
            .reduce((total, building) => total + (building.provides || 0), 0);
        
        const consumption = this.engine.ai.team.buildings
            .filter(b => b.constructionProgress >= 100)
            .reduce((total, building) => total + (building.power || 0), 0);
        
        return production - consumption;
    }
    
    getHarvesterCount() {
        return this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
    }
    
    getAvailableSpiceFields() {
        return this.engine.ai.game.spiceFields.filter(f => f.amount > 0);
    }
    
    findNearestSpiceField(unit) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.getAvailableSpiceFields().forEach(field => {
            const dist = this.distance(unit, field);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = field;
            }
        });
        
        return nearest;
    }
    
    assignHarvesterToField(harvester, field) {
        harvester.targetSpiceField = field;
        harvester.state = 'harvesting';
        harvester.targetX = field.x;
        harvester.targetY = field.y;
    }
    
    findBuildingLocation(buildingType) {
        const bases = this.engine.ai.team.buildings.filter(b => b.type === 'base');
        if (bases.length === 0) return null;
        
        const nearBase = bases[0];
        
        for (let attempts = 0; attempts < 20; attempts++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 100 + Math.random() * 200;
            
            const x = nearBase.x + Math.cos(angle) * distance;
            const y = nearBase.y + Math.sin(angle) * distance;
            
            const buildingData = this.engine.ai.game.buildingTypes[buildingType];
            if (this.engine.ai.game.isValidBuildingPlacement(x, y, buildingData)) {
                return { x, y };
            }
        }
        
        return null;
    }
    
    findProductionBuilding(unitType) {
        const productionMap = {
            'soldier': 'barracks',
            'rocketeer': 'barracks',
            'engineer': 'barracks',
            'harvester': 'factory',
            'tank': 'factory'
        };
        
        const requiredBuilding = productionMap[unitType];
        if (!requiredBuilding) return null;
        
        return this.engine.ai.team.buildings.find(b =>
            b.type === requiredBuilding && 
            b.constructionProgress >= 100 && 
            !b.ai_isProducing
        );
    }
    
    getRequiredBuilding(unitType) {
        const productionMap = {
            'soldier': 'barracks',
            'rocketeer': 'barracks',
            'engineer': 'barracks',
            'harvester': 'factory',
            'tank': 'factory'
        };
        
        return productionMap[unitType];
    }
    
    getItemCost(item) {
        if (item.type === 'building') {
            const buildingData = this.engine.ai.game.buildingTypes[item.buildingType];
            return buildingData ? buildingData.cost : null;
        } else if (item.type === 'unit') {
            const unitData = this.engine.ai.game.unitTypes[item.unitType];
            return unitData ? unitData.cost : null;
        }
        return null;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    findNearestPlayerBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.engine.ai.game.teams.player.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    findNearestAIBuilding(target) {
        let nearest = null;
        let minDistance = Infinity;
        
        this.engine.ai.team.buildings.forEach(building => {
            const dist = this.distance(target, building);
            if (dist < minDistance) {
                minDistance = dist;
                nearest = building;
            }
        });
        
        return nearest;
    }
    
    sendMessage(recipient, message) {
        if (this.messageCallback) {
            message.recipient = recipient;
            message.sender = 'economy';
            this.messageCallback(message);
        }
    }
    
    // Helper methods for emergency operations
    sendIdleUnitsToSpice() {
        const idleUnits = this.engine.ai.team.units.filter(u => 
            u.state === 'idle' && u.type !== 'harvester'
        );
        
        // Convert some idle units to temporary harvesters if possible
        idleUnits.slice(0, 2).forEach(unit => {
            const nearestField = this.findNearestSpiceField(unit);
            if (nearestField) {
                unit.temporaryRole = 'harvester';
                unit.targetX = nearestField.x;
                unit.targetY = nearestField.y;
                unit.state = 'moving';
            }
        });
    }
    
    pauseNonEssentialConstruction() {
        this.buildQueue = this.buildQueue.filter(item => 
            item.urgent || item.priority > 70
        );
    }
    
    reallocateWorkers() {
        // Focus all production on essential items
        this.buildQueue.forEach(item => {
            if (['harvester', 'soldier', 'powerplant'].includes(item.buildingType || item.unitType)) {
                item.priority += 20;
            }
        });
        
        this.sortBuildQueue();
    }
    
    maximizeResourceIncome() {
        // Optimize all harvesters for maximum efficiency
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester');
        
        harvesters.forEach(harvester => {
            if (harvester.state !== 'harvesting') {
                const bestField = this.findBestFieldForHarvester(harvester);
                if (bestField) {
                    this.assignHarvesterToField(harvester, bestField);
                }
            }
        });
    }
    
    findBestFieldForHarvester(harvester) {
        return this.getAvailableSpiceFields()
            .sort((a, b) => {
                const distA = this.distance(harvester, a);
                const distB = this.distance(harvester, b);
                const valueA = a.amount / (distA + 1);
                const valueB = b.amount / (distB + 1);
                return valueB - valueA;
            })[0];
    }
    
    requestEscort() {
        this.sendMessage('coordination', {
            type: 'request_units',
            unitType: 'combat',
            count: 3,
            purpose: 'expansion_escort'
        });
        
        return [];
    }
    
    deployEscort(escort, location) {
        escort.forEach(unit => {
            unit.targetX = location.x + (Math.random() - 0.5) * 200;
            unit.targetY = location.y + (Math.random() - 0.5) * 200;
            unit.state = 'escorting';
            unit.escortLocation = location;
        });
    }
    
    allocateResourcesForMilitary() {
        // Reserve resources for military production
        this.resourceTargets.spice = Math.max(500, this.resourceTargets.spice * 0.7);
    }
    
    allocateResourcesForDefense() {
        // Reserve resources for defensive structures
        this.resourceTargets.spice = Math.max(800, this.resourceTargets.spice * 0.8);
    }
    
    setupInitialProduction() {
        // Set up initial production priorities
        this.prioritizeHarvesterProduction();
        this.requestBuilding('powerplant', 60);
        
        if (this.engine.ai.team.buildings.filter(b => b.type === 'barracks').length === 0) {
            this.requestBuilding('barracks', 50);
        }
    }
    
    monitorResourceFlow(deltaTime) {
        // Monitor and log resource flow for optimization
        const currentSpice = this.engine.ai.team.spice;
        const income = this.calculateSpiceIncome();
        
        // Adjust strategy if resource flow is problematic
        if (income < 3 && currentSpice < 500) {
            this.switchStrategy('rush');
        } else if (income > 8 && currentSpice > 2000) {
            this.switchStrategy('boom');
        }
    }
}