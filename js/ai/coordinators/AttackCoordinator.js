import { distance } from '../../utils/index.js';

/**
 * Attack Coordinator
 * Manages all offensive operations, coordinated attacks, and revenge execution
 */
export class AttackCoordinator {
    constructor(engine) {
        this.engine = engine;
        this.activeAttacks = [];
        this.attackGroups = new Map();
        this.messageCallback = null;
        this.pendingOperations = [];
        
        // Attack strategies
        this.strategies = {
            blitz: { speed: 'high', coordination: 'low', casualties: 'acceptable' },
            siege: { speed: 'low', coordination: 'high', casualties: 'minimal' },
            harassment: { speed: 'medium', coordination: 'medium', casualties: 'low' },
            overwhelming: { speed: 'medium', coordination: 'high', casualties: 'high' }
        };
        
        this.currentStrategy = 'blitz';
    }
    
    initialize() {
        console.log('Attack Coordinator initialized');
    }
    
    update(deltaTime) {
        this.updateActiveAttacks(deltaTime);
        this.coordinateAttackGroups(deltaTime);
        this.processPendingOperations(deltaTime);
        this.evaluateAttackOpportunities(deltaTime);
    }
    
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    receiveMessage(message) {
        switch (message.type) {
            case 'unit_allocation':
                this.handleUnitAllocation(message);
                break;
            case 'operation_ended':
                this.handleOperationEnd(message);
                break;
            case 'support_request':
                this.handleSupportRequest(message);
                break;
        }
    }
    
    receiveIntelligence(intelligence) {
        // Process intelligence for attack planning
        if (intelligence.type === 'enemy_movement') {
            this.planCounterAttack(intelligence);
        } else if (intelligence.type === 'vulnerable_target') {
            this.planOpportunisticAttack(intelligence);
        }
    }
    
    // Main attack methods
    launchMajorOffensive() {
        const combatUnits = this.engine.ai.team.units.filter(u => u.damage > 0);
        if (combatUnits.length >= 5) {
            const target = this.selectPrimaryTarget();
            if (target) {
                this.createCoordinatedAttack(combatUnits, target, 'overwhelming');
                this.sendMessage('scouting', {
                    type: 'coordinate_attack',
                    target: target,
                    supportType: 'intelligence'
                });
            }
        }
    }
    
    targetVulnerableUnits(targets) {
        const hunters = this.selectHunterUnits(targets.length);
        
        hunters.forEach((hunter, index) => {
            if (targets[index]) {
                this.createHuntingMission(hunter, targets[index]);
            }
        });
    }
    
    raidUndefendedBuildings(targets) {
        const raiders = this.selectRaidUnits(targets.length);
        
        targets.forEach((target, index) => {
            if (raiders[index]) {
                this.createRaidMission(raiders[index], target);
            }
        });
    }
    
    pressAdvantage() {
        const availableUnits = this.getAvailableUnits();
        if (availableUnits.length > 3) {
            const weakestTarget = this.findWeakestTarget();
            if (weakestTarget) {
                this.createPressureAttack(availableUnits, weakestTarget);
            }
        }
    }
    
    exploitPlayerWeaknesses() {
        const weaknesses = this.engine.ai.memory.identifyPlayerWeaknesses();
        
        weaknesses.forEach(weakness => {
            switch (weakness) {
                case 'economy':
                    this.planEconomicRaid();
                    break;
                case 'military':
                    this.planMilitaryStrike();
                    break;
                case 'defense':
                    this.planDefenseExploit();
                    break;
            }
        });
    }
    
    // Revenge execution methods
    executeAssassination(revengeAction) {
        const assassins = this.selectAssassinUnits(revengeAction.intensity);
        const targets = this.selectHighValueTargets(revengeAction.intensity);
        
        assassins.forEach((assassin, index) => {
            if (targets[index]) {
                this.createAssassinationMission(assassin, targets[index], revengeAction);
            }
        });
    }
    
    executeRaid(revengeAction) {
        const raiders = this.selectRaidUnits(revengeAction.intensity);
        const targets = this.selectRaidTargets(revengeAction.intensity);
        
        this.createCoordinatedRaid(raiders, targets, revengeAction);
    }
    
    executeFullAssault(revengeAction) {
        const allUnits = this.engine.ai.team.units.filter(u => u.damage > 0);
        const primaryTarget = this.selectPrimaryTarget();
        
        if (allUnits.length > 0 && primaryTarget) {
            this.createMassiveAssault(allUnits, primaryTarget, revengeAction.intensity);
        }
    }
    
    executeEconomicWarfare(revengeAction) {
        const economicTargets = this.selectEconomicTargets();
        const raiders = this.selectFastUnits(revengeAction.intensity);
        
        this.createEconomicRaid(raiders, economicTargets);
    }
    
    executeCoordinatedStrike(revengeAction) {
        const attackGroups = this.createMultipleAttackGroups(revengeAction.intensity);
        
        attackGroups.forEach((group, index) => {
            const target = this.selectStrategicTarget(index);
            if (target) {
                this.scheduleTimedAttack(group, target, index * 30);
            }
        });
    }
    
    // Attack creation methods
    createCoordinatedAttack(units, target, strategy = 'blitz') {
        const attackId = `attack_${Date.now()}`;
        
        const attack = {
            id: attackId,
            units: units,
            target: target,
            strategy: strategy,
            phase: 'approach',
            startTime: this.engine.ai.game.gameTime,
            coordination: this.strategies[strategy]
        };
        
        this.activeAttacks.push(attack);
        
        // Set unit states
        units.forEach(unit => {
            unit.attackTarget = target;
            unit.state = 'attacking';
            unit.coordinated = true;
            unit.attackId = attackId;
            unit.strategy = strategy;
        });
        
        console.log(`Created coordinated ${strategy} attack with ${units.length} units`);
    }
    
    createHuntingMission(hunter, target) {
        hunter.attackTarget = target;
        hunter.state = 'hunting';
        hunter.priority = 'high_value_target';
        hunter.huntingStartTime = this.engine.ai.game.gameTime;
    }
    
    createRaidMission(raider, target) {
        raider.attackTarget = target;
        raider.state = 'raiding';
        raider.priority = 'hit_and_run';
        raider.raidStartTime = this.engine.ai.game.gameTime;
        raider.retreatAfter = this.engine.ai.game.gameTime + 45; // Retreat after 45 seconds
    }
    
    createAssassinationMission(assassin, target, revengeAction) {
        assassin.attackTarget = target;
        assassin.state = 'assassinating';
        assassin.priority = 'revenge_kill';
        assassin.revengeId = revengeAction.id;
        assassin.persistence = revengeAction.intensity * 30; // How long to pursue
    }
    
    createCoordinatedRaid(raiders, targets, revengeAction) {
        const raidId = `raid_${Date.now()}`;
        
        raiders.forEach((raider, index) => {
            const target = targets[index % targets.length];
            if (target) {
                raider.attackTarget = target;
                raider.state = 'coordinated_raid';
                raider.raidId = raidId;
                raider.revengeIntensity = revengeAction.intensity;
            }
        });
        
        // Schedule raid phases
        this.scheduleRaidPhases(raidId, raiders, targets);
    }
    
    createMassiveAssault(units, target, intensity) {
        // Organize units into assault waves
        const waveSize = Math.ceil(units.length / 3);
        
        for (let i = 0; i < 3; i++) {
            const wave = units.slice(i * waveSize, (i + 1) * waveSize);
            
            setTimeout(() => {
                wave.forEach(unit => {
                    unit.attackTarget = target;
                    unit.state = 'assault_wave';
                    unit.waveNumber = i + 1;
                    unit.assaultIntensity = intensity;
                });
            }, i * 15000); // 15 second delays between waves
        }
        
        console.log(`Massive assault launched with ${units.length} units in 3 waves`);
    }
    
    createEconomicRaid(raiders, targets) {
        raiders.forEach((raider, index) => {
            const target = targets[index % targets.length];
            if (target) {
                raider.attackTarget = target;
                raider.state = 'economic_raid';
                raider.switchTargetAfter = this.engine.ai.game.gameTime + 20;
                raider.economicPriority = this.calculateEconomicValue(target);
            }
        });
    }
    
    // Target selection methods
    selectPrimaryTarget() {
        // Priority: Player base > Important buildings > Military units
        const playerBase = this.engine.ai.game.teams.player.buildings.find(b => b.type === 'base');
        if (playerBase) return playerBase;
        
        const importantBuildings = this.engine.ai.game.teams.player.buildings
            .filter(b => ['factory', 'refinery', 'barracks'].includes(b.type))
            .sort((a, b) => this.calculateBuildingThreatValue(b) - this.calculateBuildingThreatValue(a));
        
        if (importantBuildings.length > 0) return importantBuildings[0];
        
        const militaryUnits = this.engine.ai.game.teams.player.units
            .filter(u => u.damage > 0)
            .sort((a, b) => this.calculateUnitValue(b) - this.calculateUnitValue(a));
        
        return militaryUnits[0] || this.engine.ai.game.teams.player.units[0];
    }
    
    selectAssassinUnits(intensity) {
        return this.engine.ai.team.units
            .filter(u => u.damage > 0 && u.speed > 30)
            .slice(0, Math.floor(intensity * 2));
    }
    
    selectHighValueTargets(intensity) {
        return this.engine.ai.game.teams.player.units
            .filter(u => u.health > 0)
            .sort((a, b) => this.calculateUnitValue(b) - this.calculateUnitValue(a))
            .slice(0, Math.floor(intensity));
    }
    
    selectRaidUnits(count) {
        return this.engine.ai.team.units
            .filter(u => u.damage > 0 && u.speed > 25)
            .slice(0, count);
    }
    
    selectRaidTargets(count) {
        return this.engine.ai.game.teams.player.buildings
            .filter(b => b.health > 0 && b.constructionProgress >= 100)
            .sort((a, b) => this.calculateBuildingThreatValue(b) - this.calculateBuildingThreatValue(a))
            .slice(0, count);
    }
    
    selectEconomicTargets() {
        const targets = [];
        
        // Harvesters
        targets.push(...this.engine.ai.game.teams.player.units.filter(u => u.type === 'harvester'));
        
        // Economic buildings
        targets.push(...this.engine.ai.game.teams.player.buildings.filter(b =>
            ['refinery', 'factory', 'powerplant'].includes(b.type)
        ));
        
        return targets;
    }
    
    selectFastUnits(intensity) {
        return this.engine.ai.team.units
            .filter(u => u.damage > 0 && u.speed > 25)
            .slice(0, Math.floor(intensity * 2));
    }
    
    selectHunterUnits(count) {
        return this.engine.ai.team.units
            .filter(u => u.damage > 0 && u.state === 'idle')
            .slice(0, count);
    }
    
    // Update methods
    updateActiveAttacks(deltaTime) {
        this.activeAttacks = this.activeAttacks.filter(attack => {
            // Check if attack is still valid
            const aliveUnits = attack.units.filter(u => u.health > 0);
            
            if (aliveUnits.length === 0 || !attack.target || attack.target.health <= 0) {
                this.completeAttack(attack);
                return false;
            }
            
            // Update attack phase
            this.updateAttackPhase(attack);
            
            return true;
        });
    }
    
    updateAttackPhase(attack) {
        const averageDistance = this.calculateAverageDistance(attack.units, attack.target);
        
        if (averageDistance < 100 && attack.phase === 'approach') {
            attack.phase = 'engage';
            console.log(`Attack ${attack.id} entering engagement phase`);
        } else if (averageDistance < 50 && attack.phase === 'engage') {
            attack.phase = 'melee';
        }
    }
    
    coordinateAttackGroups(deltaTime) {
        this.attackGroups.forEach((group, groupId) => {
            this.maintainGroupFormation(group);
            this.coordinateGroupTargeting(group);
        });
    }
    
    maintainGroupFormation(group) {
        if (group.units.length > 1) {
            const center = this.calculateGroupCenter(group.units);
            
            group.units.forEach((unit, index) => {
                if (this.distance(unit, center) > 150) {
                    unit.targetX = center.x + (Math.random() - 0.5) * 100;
                    unit.targetY = center.y + (Math.random() - 0.5) * 100;
                }
            });
        }
    }
    
    coordinateGroupTargeting(group) {
        const priorityTarget = this.selectGroupTarget(group);
        
        if (priorityTarget) {
            group.units.forEach(unit => {
                if (!unit.attackTarget || this.shouldSwitchTarget(unit, priorityTarget)) {
                    unit.attackTarget = priorityTarget;
                }
            });
        }
    }
    
    // Utility methods
    calculateAverageDistance(units, target) {
        if (units.length === 0) return Infinity;
        
        const totalDistance = units.reduce((sum, unit) => {
            return sum + this.distance(unit, target);
        }, 0);
        
        return totalDistance / units.length;
    }
    
    calculateGroupCenter(units) {
        const totalX = units.reduce((sum, unit) => sum + unit.x, 0);
        const totalY = units.reduce((sum, unit) => sum + unit.y, 0);
        
        return {
            x: totalX / units.length,
            y: totalY / units.length
        };
    }
    
    calculateUnitValue(unit) {
        let value = unit.maxHealth + (unit.damage || 0) * 5;
        
        if (['tank', 'rocketeer'].includes(unit.type)) {
            value *= 2;
        }
        
        if (unit.combatXP > 100) {
            value *= 1.5;
        }
        
        return value;
    }
    
    calculateBuildingThreatValue(building) {
        const threatValues = {
            'base': 100,
            'factory': 80,
            'barracks': 60,
            'refinery': 70,
            'powerplant': 50,
            'turret': 40,
            'radar': 30
        };
        
        return threatValues[building.type] || 20;
    }
    
    calculateEconomicValue(target) {
        if (target.type === 'harvester') return 50;
        if (target.type === 'refinery') return 80;
        if (target.type === 'factory') return 70;
        return 30;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    getAvailableUnits() {
        return this.engine.ai.team.units.filter(u => 
            u.damage > 0 && (u.state === 'idle' || u.priority === 'low')
        );
    }
    
    sendMessage(recipient, message) {
        if (this.messageCallback) {
            message.recipient = recipient;
            message.sender = 'attack';
            this.messageCallback(message);
        }
    }
    
    completeAttack(attack) {
        // Clean up attack state
        attack.units.forEach(unit => {
            if (unit.health > 0) {
                unit.coordinated = false;
                unit.attackId = null;
                unit.strategy = null;
            }
        });
        
        console.log(`Attack ${attack.id} completed`);
    }
    
    // Additional helper methods for complex operations
    createMultipleAttackGroups(intensity) {
        const allUnits = this.engine.ai.team.units.filter(u => u.damage > 0);
        const groupCount = Math.min(3, Math.floor(intensity));
        const groups = [];
        
        for (let i = 0; i < groupCount; i++) {
            const groupSize = Math.floor(allUnits.length / groupCount);
            const group = allUnits.slice(i * groupSize, (i + 1) * groupSize);
            groups.push(group);
        }
        
        return groups;
    }
    
    selectStrategicTarget(index) {
        const targets = [
            this.engine.ai.game.teams.player.buildings.find(b => b.type === 'base'),
            this.engine.ai.game.teams.player.buildings.find(b => b.type === 'factory'),
            this.engine.ai.game.teams.player.buildings.find(b => b.type === 'refinery')
        ];
        
        return targets[index] || this.engine.ai.game.teams.player.buildings[0];
    }
    
    scheduleTimedAttack(group, target, delay) {
        setTimeout(() => {
            group.forEach(unit => {
                unit.attackTarget = target;
                unit.state = 'timed_attack';
            });
        }, delay * 1000);
    }
    
    scheduleRaidPhases(raidId, raiders, targets) {
        // Phase 1: Initial strike
        setTimeout(() => {
            raiders.slice(0, Math.ceil(raiders.length / 2)).forEach(raider => {
                raider.raidPhase = 'initial_strike';
            });
        }, 0);
        
        // Phase 2: Follow-up
        setTimeout(() => {
            raiders.slice(Math.ceil(raiders.length / 2)).forEach(raider => {
                raider.raidPhase = 'follow_up';
            });
        }, 30000); // 30 seconds later
    }
}