import { distance } from '../../utils/index.js';

/**
 * Goal-Oriented Behavior
 * Manages AI goals, priorities, and goal-driven decision making
 */
export class GoalOrientedBehavior {
    constructor(engine) {
        this.engine = engine;
        
        // Goal management
        this.activeGoals = [];
        this.goalHistory = [];
        this.goalPriorities = new Map();
        
        // Goal types and templates
        this.goalTemplates = {
            'expand_economy': {
                priority: 70,
                duration: 300,
                conditions: ['low_spice', 'few_harvesters'],
                actions: ['build_harvester', 'expand_to_spice', 'build_refinery']
            },
            'build_military': {
                priority: 60,
                duration: 240,
                conditions: ['few_military_units', 'player_threat'],
                actions: ['train_soldiers', 'build_barracks', 'train_tanks']
            },
            'defend_base': {
                priority: 90,
                duration: 180,
                conditions: ['under_attack', 'enemy_nearby'],
                actions: ['build_turrets', 'position_defenders', 'reinforce_defenses']
            },
            'attack_player': {
                priority: 50,
                duration: 360,
                conditions: ['military_advantage', 'revenge_pending'],
                actions: ['launch_assault', 'harass_economy', 'scout_targets']
            },
            'secure_resources': {
                priority: 80,
                duration: 240,
                conditions: ['spice_fields_available', 'expansion_safe'],
                actions: ['expand_territory', 'build_refinery', 'escort_harvesters']
            },
            'technology_advance': {
                priority: 40,
                duration: 480,
                conditions: ['mid_game', 'stable_economy'],
                actions: ['build_research_lab', 'research_upgrades', 'advanced_units']
            }
        };
        
        this.lastGoalEvaluation = 0;
        this.goalEvaluationInterval = 15; // Evaluate goals every 15 seconds
    }
    
    initialize() {
        this.setupInitialGoals();
        console.log('Goal-Oriented Behavior initialized');
    }
    
    update(deltaTime) {
        // Update active goals
        this.updateActiveGoals(deltaTime);
        
        // Evaluate and potentially add new goals
        if (this.shouldEvaluateGoals()) {
            this.evaluateGoals();
        }
        
        // Clean up completed or expired goals
        this.cleanupGoals();
        
        // Update goal priorities based on current situation
        this.updateGoalPriorities();
    }
    
    setupInitialGoals() {
        // Add initial goals based on game start conditions
        this.addGoal('expand_economy', 80);
        this.addGoal('build_military', 60);
    }
    
    shouldEvaluateGoals() {
        const timeSinceLastEvaluation = this.engine.ai.game.gameTime - this.lastGoalEvaluation;
        return timeSinceLastEvaluation >= this.goalEvaluationInterval;
    }
    
    evaluateGoals() {
        this.lastGoalEvaluation = this.engine.ai.game.gameTime;
        
        // Check conditions for each goal template
        Object.entries(this.goalTemplates).forEach(([goalType, template]) => {
            if (this.shouldActivateGoal(goalType, template)) {
                this.addGoal(goalType, template.priority);
            }
        });
    }
    
    shouldActivateGoal(goalType, template) {
        // Don't activate if already active
        if (this.hasActiveGoal(goalType)) {
            return false;
        }
        
        // Check if conditions are met
        const conditionsMet = template.conditions.every(condition => 
            this.evaluateCondition(condition)
        );
        
        return conditionsMet;
    }
    
    evaluateCondition(condition) {
        const gameState = this.getGameState();
        
        switch (condition) {
            case 'low_spice':
                return gameState.aiSpice < 500;
            case 'few_harvesters':
                return gameState.harvesterCount < 3;
            case 'few_military_units':
                return gameState.militaryUnitCount < 5;
            case 'player_threat':
                return this.assessPlayerThreat() > 0.6;
            case 'under_attack':
                return this.isUnderAttack();
            case 'enemy_nearby':
                return this.hasEnemyNearby();
            case 'military_advantage':
                return gameState.aiPower > gameState.playerPower * 1.2;
            case 'revenge_pending':
                return this.engine.ai.revengeSystem.revengeQueue.length > 0;
            case 'spice_fields_available':
                return this.getAvailableSpiceFields().length > gameState.harvesterCount;
            case 'expansion_safe':
                return this.isExpansionSafe();
            case 'mid_game':
                return gameState.gameTime > 300 && gameState.gameTime < 1200;
            case 'stable_economy':
                return gameState.aiSpice > 1000 && gameState.spiceIncome > 5;
            default:
                return false;
        }
    }
    
    addGoal(goalType, priority = null) {
        const template = this.goalTemplates[goalType];
        if (!template) {
            console.warn(`Unknown goal type: ${goalType}`);
            return null;
        }
        
        const goal = {
            id: `goal_${Date.now()}_${goalType}`,
            type: goalType,
            priority: priority || template.priority,
            startTime: this.engine.ai.game.gameTime,
            duration: template.duration,
            template: template,
            status: 'active',
            progress: 0,
            actions: [...template.actions],
            completedActions: []
        };
        
        this.activeGoals.push(goal);
        this.goalPriorities.set(goal.id, goal.priority);
        
        console.log(`Added goal: ${goalType} (priority: ${goal.priority})`);
        return goal;
    }
    
    updateActiveGoals(deltaTime) {
        this.activeGoals.forEach(goal => {
            this.updateGoal(goal, deltaTime);
        });
    }
    
    updateGoal(goal, deltaTime) {
        const age = this.engine.ai.game.gameTime - goal.startTime;
        
        // Update progress based on completed actions
        goal.progress = goal.completedActions.length / goal.actions.length;
        
        // Check if goal should be completed
        if (this.isGoalCompleted(goal)) {
            goal.status = 'completed';
            this.completeGoal(goal);
        } else if (age > goal.duration) {
            goal.status = 'expired';
            this.expireGoal(goal);
        } else if (this.isGoalObsolete(goal)) {
            goal.status = 'obsolete';
            this.obsoleteGoal(goal);
        }
    }
    
    isGoalCompleted(goal) {
        // Goal is completed if all actions are done or conditions are no longer relevant
        return goal.progress >= 1.0 || this.areGoalConditionsSatisfied(goal);
    }
    
    areGoalConditionsSatisfied(goal) {
        // Check if the goal's purpose has been achieved
        switch (goal.type) {
            case 'expand_economy':
                return this.engine.ai.team.spice > 1000 && this.getHarvesterCount() >= 4;
            case 'build_military':
                return this.getMilitaryUnitCount() >= 8;
            case 'defend_base':
                return !this.isUnderAttack() && !this.hasEnemyNearby();
            case 'attack_player':
                return this.engine.ai.revengeSystem.revengeQueue.length === 0;
            case 'secure_resources':
                return this.getHarvesterCount() >= this.getAvailableSpiceFields().length;
            case 'technology_advance':
                return this.hasTechnologyAdvantage();
            default:
                return false;
        }
    }
    
    isGoalObsolete(goal) {
        // Check if goal conditions are no longer met
        return !goal.template.conditions.some(condition => 
            this.evaluateCondition(condition)
        );
    }
    
    completeGoal(goal) {
        console.log(`Goal completed: ${goal.type} (progress: ${(goal.progress * 100).toFixed(1)}%)`);
        this.recordGoalOutcome(goal, 'completed');
    }
    
    expireGoal(goal) {
        console.log(`Goal expired: ${goal.type} (progress: ${(goal.progress * 100).toFixed(1)}%)`);
        this.recordGoalOutcome(goal, 'expired');
    }
    
    obsoleteGoal(goal) {
        console.log(`Goal obsolete: ${goal.type} (progress: ${(goal.progress * 100).toFixed(1)}%)`);
        this.recordGoalOutcome(goal, 'obsolete');
    }
    
    recordGoalOutcome(goal, outcome) {
        const record = {
            ...goal,
            outcome: outcome,
            endTime: this.engine.ai.game.gameTime,
            totalDuration: this.engine.ai.game.gameTime - goal.startTime,
            finalProgress: goal.progress
        };
        
        this.goalHistory.push(record);
        
        // Store in AI memory for learning
        this.engine.ai.memory.storeMemory('strategic', `goal_${goal.id}`, record, 'important');
    }
    
    cleanupGoals() {
        // Remove completed, expired, or obsolete goals
        this.activeGoals = this.activeGoals.filter(goal => goal.status === 'active');
        
        // Clean up old goal history
        const maxHistoryAge = 1800; // 30 minutes
        const cutoffTime = this.engine.ai.game.gameTime - maxHistoryAge;
        
        this.goalHistory = this.goalHistory.filter(record => 
            record.endTime >= cutoffTime
        );
    }
    
    updateGoalPriorities() {
        // Adjust goal priorities based on current situation
        this.activeGoals.forEach(goal => {
            const newPriority = this.calculateDynamicPriority(goal);
            goal.priority = newPriority;
            this.goalPriorities.set(goal.id, newPriority);
        });
        
        // Sort goals by priority
        this.activeGoals.sort((a, b) => b.priority - a.priority);
    }
    
    calculateDynamicPriority(goal) {
        let priority = goal.template.priority;
        
        // Increase priority based on urgency
        const urgency = this.calculateGoalUrgency(goal);
        priority += urgency * 20;
        
        // Adjust based on current game state
        const gameState = this.getGameState();
        
        switch (goal.type) {
            case 'defend_base':
                if (this.isUnderAttack()) priority += 30;
                break;
            case 'expand_economy':
                if (gameState.aiSpice < 300) priority += 25;
                break;
            case 'attack_player':
                if (this.engine.ai.revengeSystem.revengeQueue.length > 2) priority += 20;
                break;
        }
        
        // Adjust based on AI personality
        priority = this.applyPersonalityToPriority(goal, priority);
        
        return Math.max(0, Math.min(100, priority));
    }
    
    calculateGoalUrgency(goal) {
        const age = this.engine.ai.game.gameTime - goal.startTime;
        const remainingTime = goal.duration - age;
        
        if (remainingTime < 60) return 1.0; // Very urgent
        if (remainingTime < 120) return 0.7; // Urgent
        if (remainingTime < 180) return 0.4; // Moderate
        return 0.1; // Low urgency
    }
    
    applyPersonalityToPriority(goal, priority) {
        const personality = this.engine.ai.personalityTraits;
        
        switch (goal.type) {
            case 'attack_player':
                priority *= (0.5 + personality.aggression);
                break;
            case 'expand_economy':
                priority *= (0.5 + personality.opportunism);
                break;
            case 'defend_base':
                priority *= (0.5 + (1 - personality.aggression));
                break;
        }
        
        return priority;
    }
    
    // Public interface
    getCurrentGoals() {
        return this.activeGoals.filter(goal => goal.status === 'active');
    }
    
    getHighestPriorityGoal() {
        const activeGoals = this.getCurrentGoals();
        return activeGoals.length > 0 ? activeGoals[0] : null;
    }
    
    hasActiveGoal(goalType) {
        return this.activeGoals.some(goal => 
            goal.type === goalType && goal.status === 'active'
        );
    }
    
    forceGoal(goalType, priority = 100) {
        // Force a specific goal regardless of conditions
        return this.addGoal(goalType, priority);
    }
    
    cancelGoal(goalType) {
        const goal = this.activeGoals.find(g => g.type === goalType && g.status === 'active');
        if (goal) {
            goal.status = 'cancelled';
            this.recordGoalOutcome(goal, 'cancelled');
            return true;
        }
        return false;
    }
    
    markActionCompleted(goalType, actionType) {
        const goal = this.activeGoals.find(g => g.type === goalType && g.status === 'active');
        if (goal && goal.actions.includes(actionType)) {
            if (!goal.completedActions.includes(actionType)) {
                goal.completedActions.push(actionType);
                console.log(`Action completed for goal ${goalType}: ${actionType}`);
            }
        }
    }
    
    // Helper methods
    getGameState() {
        return {
            aiPower: this.engine.ai.calculateAIPower(),
            playerPower: this.engine.ai.calculatePlayerPower(),
            aiSpice: this.engine.ai.team.spice,
            gameTime: this.engine.ai.game.gameTime,
            harvesterCount: this.getHarvesterCount(),
            militaryUnitCount: this.getMilitaryUnitCount(),
            spiceIncome: this.calculateSpiceIncome()
        };
    }
    
    getHarvesterCount() {
        return this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
    }
    
    getMilitaryUnitCount() {
        return this.engine.ai.team.units.filter(u => u.damage > 0).length;
    }
    
    calculateSpiceIncome() {
        return this.getHarvesterCount() * 2; // Approximate
    }
    
    assessPlayerThreat() {
        const playerPower = this.engine.ai.calculatePlayerPower();
        const aiPower = this.engine.ai.calculateAIPower();
        
        const powerRatio = playerPower / Math.max(1, aiPower);
        const proximityThreat = this.calculateProximityThreat();
        
        return Math.min(1, (powerRatio * 0.6) + (proximityThreat * 0.4));
    }
    
    calculateProximityThreat() {
        let threat = 0;
        const aiBuildings = this.engine.ai.team.buildings;
        const playerUnits = this.engine.ai.game.teams.player.units;
        
        aiBuildings.forEach(building => {
            const nearbyEnemies = playerUnits.filter(unit =>
                this.distance(unit, building) < 300
            ).length;
            threat += nearbyEnemies * 0.1;
        });
        
        return Math.min(1, threat);
    }
    
    isUnderAttack() {
        return this.engine.ai.team.buildings.some(building =>
            this.engine.ai.game.teams.player.units.some(unit =>
                unit.attackTarget === building
            )
        );
    }
    
    hasEnemyNearby() {
        return this.engine.ai.team.buildings.some(building =>
            this.engine.ai.game.teams.player.units.some(unit =>
                this.distance(unit, building) < 400
            )
        );
    }
    
    getAvailableSpiceFields() {
        return this.engine.ai.game.spiceFields.filter(field => field.amount > 0);
    }
    
    isExpansionSafe() {
        const nearbyEnemies = this.engine.ai.game.teams.player.units.filter(unit =>
            this.engine.ai.team.buildings.some(building =>
                this.distance(unit, building) < 500
            )
        ).length;
        
        return nearbyEnemies < 3;
    }
    
    hasTechnologyAdvantage() {
        // Simplified technology advantage check
        const aiAdvancedUnits = this.engine.ai.team.units.filter(u => 
            ['tank', 'rocketeer'].includes(u.type)
        ).length;
        
        const playerAdvancedUnits = this.engine.ai.game.teams.player.units.filter(u => 
            ['tank', 'rocketeer'].includes(u.type)
        ).length;
        
        return aiAdvancedUnits > playerAdvancedUnits;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    // Analytics and debugging
    getGoalAnalytics() {
        const completedGoals = this.goalHistory.filter(g => g.outcome === 'completed');
        const expiredGoals = this.goalHistory.filter(g => g.outcome === 'expired');
        
        return {
            activeGoals: this.activeGoals.length,
            completedGoals: completedGoals.length,
            expiredGoals: expiredGoals.length,
            averageCompletionRate: this.calculateAverageCompletionRate(),
            goalEffectiveness: this.calculateGoalEffectiveness(),
            currentPriorities: this.getCurrentGoalPriorities()
        };
    }
    
    calculateAverageCompletionRate() {
        if (this.goalHistory.length === 0) return 0;
        
        const totalProgress = this.goalHistory.reduce((sum, goal) => 
            sum + goal.finalProgress, 0
        );
        
        return totalProgress / this.goalHistory.length;
    }
    
    calculateGoalEffectiveness() {
        const recentGoals = this.goalHistory.filter(goal =>
            this.engine.ai.game.gameTime - goal.endTime < 600 // Last 10 minutes
        );
        
        if (recentGoals.length === 0) return 0.5;
        
        const successfulGoals = recentGoals.filter(goal => 
            goal.outcome === 'completed' && goal.finalProgress > 0.7
        ).length;
        
        return successfulGoals / recentGoals.length;
    }
    
    getCurrentGoalPriorities() {
        return this.activeGoals.map(goal => ({
            type: goal.type,
            priority: goal.priority,
            progress: goal.progress,
            age: this.engine.ai.game.gameTime - goal.startTime
        }));
    }
    
    getGoalSummary() {
        return {
            activeGoals: this.getCurrentGoals(),
            analytics: this.getGoalAnalytics(),
            nextEvaluation: this.goalEvaluationInterval - (this.engine.ai.game.gameTime - this.lastGoalEvaluation)
        };
    }
}