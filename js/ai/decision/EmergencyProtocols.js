import { distance } from '../../utils/index.js';

/**
 * Emergency Protocols
 * Handles critical situations and emergency response procedures
 */
export class EmergencyProtocols {
    constructor(engine) {
        this.engine = engine;
        
        // Emergency detection and response
        this.activeEmergencies = [];
        this.emergencyThresholds = {
            base_under_attack: { severity: 'critical', threshold: 0.8 },
            massive_unit_loss: { severity: 'high', threshold: 0.6 },
            economic_collapse: { severity: 'high', threshold: 0.7 },
            overwhelming_enemy: { severity: 'critical', threshold: 0.9 },
            resource_starvation: { severity: 'medium', threshold: 0.5 },
            isolation: { severity: 'medium', threshold: 0.4 },
            technology_gap: { severity: 'low', threshold: 0.3 }
        };
        
        // Emergency response protocols
        this.protocols = {
            base_under_attack: {
                priority: 100,
                actions: ['emergency_defense', 'recall_all_units', 'distress_signal'],
                duration: 180,
                cooldown: 300
            },
            massive_unit_loss: {
                priority: 90,
                actions: ['defensive_posture', 'emergency_production', 'seek_shelter'],
                duration: 240,
                cooldown: 600
            },
            economic_collapse: {
                priority: 85,
                actions: ['emergency_harvesting', 'resource_conservation', 'minimal_production'],
                duration: 300,
                cooldown: 480
            },
            overwhelming_enemy: {
                priority: 95,
                actions: ['last_stand', 'guerrilla_tactics', 'hit_and_run'],
                duration: 360,
                cooldown: 900
            },
            resource_starvation: {
                priority: 70,
                actions: ['expand_harvesting', 'raid_resources', 'conservation_mode'],
                duration: 180,
                cooldown: 240
            }
        };
        
        this.protocolCooldowns = new Map();
        this.lastEmergencyCheck = 0;
        this.emergencyCheckInterval = 5; // Check every 5 seconds
    }
    
    initialize() {
        console.log('Emergency Protocols initialized');
    }
    
    update(deltaTime) {
        // Check for emergencies periodically
        if (this.shouldCheckEmergencies()) {
            this.checkEmergencies();
        }
        
        // Update active emergencies
        this.updateActiveEmergencies(deltaTime);
        
        // Update cooldowns
        this.updateCooldowns(deltaTime);
    }
    
    shouldCheckEmergencies() {
        const timeSinceLastCheck = this.engine.ai.game.gameTime - this.lastEmergencyCheck;
        return timeSinceLastCheck >= this.emergencyCheckInterval;
    }
    
    checkEmergencies() {
        this.lastEmergencyCheck = this.engine.ai.game.gameTime;
        
        const detectedEmergencies = [];
        
        // Check each emergency type
        Object.entries(this.emergencyThresholds).forEach(([emergencyType, config]) => {
            const severity = this.detectEmergency(emergencyType);
            
            if (severity >= config.threshold) {
                detectedEmergencies.push({
                    type: emergencyType,
                    severity: severity,
                    detectedAt: this.engine.ai.game.gameTime,
                    config: config
                });
            }
        });
        
        // Process detected emergencies
        detectedEmergencies.forEach(emergency => {
            this.handleEmergency(emergency);
        });
        
        return detectedEmergencies;
    }
    
    detectEmergency(emergencyType) {
        const gameState = this.getGameState();
        
        switch (emergencyType) {
            case 'base_under_attack':
                return this.detectBaseUnderAttack(gameState);
            case 'massive_unit_loss':
                return this.detectMassiveUnitLoss(gameState);
            case 'economic_collapse':
                return this.detectEconomicCollapse(gameState);
            case 'overwhelming_enemy':
                return this.detectOverwhelmingEnemy(gameState);
            case 'resource_starvation':
                return this.detectResourceStarvation(gameState);
            case 'isolation':
                return this.detectIsolation(gameState);
            case 'technology_gap':
                return this.detectTechnologyGap(gameState);
            default:
                return 0;
        }
    }
    
    detectBaseUnderAttack(gameState) {
        const mainBase = this.engine.ai.team.buildings.find(b => b.type === 'base');
        if (!mainBase) return 1.0; // No base = critical emergency
        
        const attackersNearBase = this.engine.ai.game.teams.player.units.filter(unit =>
            unit.damage > 0 && this.distance(unit, mainBase) < 200
        ).length;
        
        const baseHealthRatio = mainBase.health / mainBase.maxHealth;
        const attackerThreat = Math.min(1, attackersNearBase / 5);
        
        return (1 - baseHealthRatio) * 0.6 + attackerThreat * 0.4;
    }
    
    detectMassiveUnitLoss(gameState) {
        const currentUnits = gameState.aiUnits;
        const expectedUnits = this.calculateExpectedUnitCount(gameState.gameTime);
        
        if (expectedUnits === 0) return 0;
        
        const unitLossRatio = 1 - (currentUnits / expectedUnits);
        return Math.max(0, unitLossRatio);
    }
    
    detectEconomicCollapse(gameState) {
        const spiceRatio = Math.min(1, gameState.aiSpice / 500);
        const incomeRatio = Math.min(1, gameState.spiceIncome / 3);
        const harvesterRatio = Math.min(1, gameState.harvesterCount / 2);
        
        const economicHealth = (spiceRatio + incomeRatio + harvesterRatio) / 3;
        return 1 - economicHealth;
    }
    
    detectOverwhelmingEnemy(gameState) {
        const powerRatio = gameState.playerPower / Math.max(1, gameState.aiPower);
        const unitRatio = gameState.playerUnits / Math.max(1, gameState.aiUnits);
        
        const overwhelmingFactor = (powerRatio * 0.7) + (unitRatio * 0.3);
        return Math.max(0, (overwhelmingFactor - 1) / 2); // Normalize to 0-1
    }
    
    detectResourceStarvation(gameState) {
        const spiceEmergency = gameState.aiSpice < 100 ? 0.8 : 0;
        const incomeEmergency = gameState.spiceIncome < 1 ? 0.6 : 0;
        const harvesterEmergency = gameState.harvesterCount === 0 ? 1.0 : 0;
        
        return Math.max(spiceEmergency, incomeEmergency, harvesterEmergency);
    }
    
    detectIsolation(gameState) {
        const playerUnits = this.engine.ai.game.teams.player.units;
        const aiBuildings = this.engine.ai.team.buildings;
        
        let isolationScore = 0;
        
        aiBuildings.forEach(building => {
            const nearbyAllies = this.engine.ai.team.units.filter(unit =>
                this.distance(unit, building) < 300
            ).length;
            
            const nearbyEnemies = playerUnits.filter(unit =>
                this.distance(unit, building) < 400
            ).length;
            
            if (nearbyAllies === 0 && nearbyEnemies > 0) {
                isolationScore += 0.2;
            }
        });
        
        return Math.min(1, isolationScore);
    }
    
    detectTechnologyGap(gameState) {
        const aiAdvancedUnits = this.engine.ai.team.units.filter(u => 
            ['tank', 'rocketeer'].includes(u.type)
        ).length;
        
        const playerAdvancedUnits = this.engine.ai.game.teams.player.units.filter(u => 
            ['tank', 'rocketeer'].includes(u.type)
        ).length;
        
        if (playerAdvancedUnits === 0) return 0;
        
        const techRatio = aiAdvancedUnits / playerAdvancedUnits;
        return Math.max(0, (0.5 - techRatio) * 2); // Gap becomes emergency when ratio < 0.5
    }
    
    handleEmergency(emergency) {
        // Check if this emergency type is already active
        const existingEmergency = this.activeEmergencies.find(e => e.type === emergency.type);
        if (existingEmergency) {
            // Update severity if higher
            if (emergency.severity > existingEmergency.severity) {
                existingEmergency.severity = emergency.severity;
            }
            return existingEmergency;
        }
        
        // Check cooldown
        if (this.isOnCooldown(emergency.type)) {
            return null;
        }
        
        // Activate emergency protocol
        const protocol = this.protocols[emergency.type];
        if (protocol) {
            emergency.protocol = protocol;
            emergency.status = 'active';
            emergency.startTime = this.engine.ai.game.gameTime;
            emergency.actionIndex = 0;
            
            this.activeEmergencies.push(emergency);
            this.executeEmergencyAction(emergency);
            
            console.log(`Emergency activated: ${emergency.type} (severity: ${emergency.severity.toFixed(2)})`);
            
            return emergency;
        }
        
        return null;
    }
    
    executeEmergencyAction(emergency) {
        const action = emergency.protocol.actions[emergency.actionIndex];
        
        if (action) {
            console.log(`Executing emergency action: ${action} for ${emergency.type}`);
            
            switch (action) {
                case 'emergency_defense':
                    this.executeEmergencyDefense();
                    break;
                case 'recall_all_units':
                    this.executeRecallAllUnits();
                    break;
                case 'distress_signal':
                    this.executeDistressSignal();
                    break;
                case 'defensive_posture':
                    this.executeDefensivePosture();
                    break;
                case 'emergency_production':
                    this.executeEmergencyProduction();
                    break;
                case 'seek_shelter':
                    this.executeSeekShelter();
                    break;
                case 'emergency_harvesting':
                    this.executeEmergencyHarvesting();
                    break;
                case 'resource_conservation':
                    this.executeResourceConservation();
                    break;
                case 'minimal_production':
                    this.executeMinimalProduction();
                    break;
                case 'last_stand':
                    this.executeLastStand();
                    break;
                case 'guerrilla_tactics':
                    this.executeGuerrillaTactics();
                    break;
                case 'hit_and_run':
                    this.executeHitAndRun();
                    break;
                case 'expand_harvesting':
                    this.executeExpandHarvesting();
                    break;
                case 'raid_resources':
                    this.executeRaidResources();
                    break;
                case 'conservation_mode':
                    this.executeConservationMode();
                    break;
            }
            
            emergency.actionIndex++;
        }
    }
    
    // Emergency action implementations
    executeEmergencyDefense() {
        const mainBase = this.engine.ai.team.buildings.find(b => b.type === 'base');
        if (mainBase) {
            this.engine.behaviorEngine.coordination.defenseCoordinator.emergencyDefense(mainBase);
        }
    }
    
    executeRecallAllUnits() {
        const mainBase = this.engine.ai.team.buildings.find(b => b.type === 'base');
        if (mainBase) {
            this.engine.ai.team.units.forEach(unit => {
                if (unit.damage > 0) {
                    unit.targetX = mainBase.x + (Math.random() - 0.5) * 200;
                    unit.targetY = mainBase.y + (Math.random() - 0.5) * 200;
                    unit.state = 'emergency_recall';
                    unit.priority = 'emergency';
                }
            });
        }
    }
    
    executeDistressSignal() {
        // Could integrate with diplomacy system for alliance requests
        console.log('AI sending distress signal');
    }
    
    executeDefensivePosture() {
        this.engine.behaviorEngine.coordination.defenseCoordinator.activateDefenses();
        this.engine.behaviorEngine.coordination.attackCoordinator.currentStrategy = 'defensive';
    }
    
    executeEmergencyProduction() {
        this.engine.behaviorEngine.coordination.economicCoordinator.rushMilitaryProduction();
    }
    
    executeSeekShelter() {
        // Move vulnerable units to safety
        this.engine.ai.team.units.forEach(unit => {
            if (unit.type === 'harvester' || unit.health < unit.maxHealth * 0.3) {
                const shelter = this.findNearestShelter(unit);
                if (shelter) {
                    unit.targetX = shelter.x;
                    unit.targetY = shelter.y;
                    unit.state = 'seeking_shelter';
                }
            }
        });
    }
    
    executeEmergencyHarvesting() {
        this.engine.behaviorEngine.coordination.economicCoordinator.emergencyResourceGathering();
    }
    
    executeResourceConservation() {
        // Pause non-essential construction and production
        this.engine.behaviorEngine.coordination.economicCoordinator.buildQueue = 
            this.engine.behaviorEngine.coordination.economicCoordinator.buildQueue.filter(item => 
                item.priority > 80 || item.urgent
            );
    }
    
    executeMinimalProduction() {
        // Only produce essential units
        this.engine.behaviorEngine.coordination.economicCoordinator.prioritizeHarvesterProduction();
    }
    
    executeLastStand() {
        this.engine.behaviorEngine.coordination.defenseCoordinator.coordinateLastStand();
    }
    
    executeGuerrillaTactics() {
        // Switch to hit-and-run tactics
        this.engine.ai.team.units.forEach(unit => {
            if (unit.damage > 0) {
                unit.tactics = 'guerrilla';
                unit.engagementRange = unit.engagementRange * 1.5;
            }
        });
    }
    
    executeHitAndRun() {
        // Execute quick strikes and retreats
        this.engine.behaviorEngine.coordination.attackCoordinator.currentStrategy = 'harassment';
    }
    
    executeExpandHarvesting() {
        this.engine.behaviorEngine.coordination.economicCoordinator.expandEconomicBase();
    }
    
    executeRaidResources() {
        // Target player's economic assets
        this.engine.behaviorEngine.coordination.attackCoordinator.executeEconomicWarfare({ intensity: 3 });
    }
    
    executeConservationMode() {
        // Minimize resource expenditure
        this.engine.behaviorEngine.coordination.economicCoordinator.switchStrategy('turtle');
    }
    
    updateActiveEmergencies(deltaTime) {
        this.activeEmergencies = this.activeEmergencies.filter(emergency => {
            const age = this.engine.ai.game.gameTime - emergency.startTime;
            
            // Check if emergency should end
            if (this.shouldEndEmergency(emergency) || age > emergency.protocol.duration) {
                this.endEmergency(emergency);
                return false;
            }
            
            // Execute next action if available
            if (emergency.actionIndex < emergency.protocol.actions.length) {
                const timeBetweenActions = emergency.protocol.duration / emergency.protocol.actions.length;
                if (age > emergency.actionIndex * timeBetweenActions) {
                    this.executeEmergencyAction(emergency);
                }
            }
            
            return true;
        });
    }
    
    shouldEndEmergency(emergency) {
        // Re-evaluate emergency condition
        const currentSeverity = this.detectEmergency(emergency.type);
        return currentSeverity < emergency.config.threshold * 0.5; // End when severity drops significantly
    }
    
    endEmergency(emergency) {
        console.log(`Emergency ended: ${emergency.type}`);
        
        // Set cooldown
        this.setCooldown(emergency.type, emergency.protocol.cooldown);
        
        // Record emergency outcome
        this.recordEmergencyOutcome(emergency);
    }
    
    recordEmergencyOutcome(emergency) {
        const outcome = {
            type: emergency.type,
            severity: emergency.severity,
            startTime: emergency.startTime,
            endTime: this.engine.ai.game.gameTime,
            duration: this.engine.ai.game.gameTime - emergency.startTime,
            actionsExecuted: emergency.actionIndex,
            totalActions: emergency.protocol.actions.length,
            resolved: this.shouldEndEmergency(emergency)
        };
        
        // Store in AI memory
        this.engine.ai.memory.storeMemory('emergency', `emergency_${Date.now()}`, outcome, 'critical');
    }
    
    // Utility methods
    getGameState() {
        return {
            aiPower: this.engine.ai.calculateAIPower(),
            playerPower: this.engine.ai.calculatePlayerPower(),
            aiSpice: this.engine.ai.team.spice,
            aiUnits: this.engine.ai.team.units.length,
            playerUnits: this.engine.ai.game.teams.player.units.length,
            gameTime: this.engine.ai.game.gameTime,
            harvesterCount: this.engine.ai.team.units.filter(u => u.type === 'harvester').length,
            spiceIncome: this.calculateSpiceIncome()
        };
    }
    
    calculateExpectedUnitCount(gameTime) {
        // Simple expected unit count based on game time
        if (gameTime < 300) return Math.floor(gameTime / 60) + 2; // Early game
        if (gameTime < 900) return Math.floor(gameTime / 45) + 3; // Mid game
        return Math.floor(gameTime / 30) + 5; // Late game
    }
    
    calculateSpiceIncome() {
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
        return harvesters * 2; // Approximate income
    }
    
    findNearestShelter(unit) {
        let nearestShelter = null;
        let minDistance = Infinity;
        
        this.engine.ai.team.buildings.forEach(building => {
            const distance = this.distance(unit, building);
            if (distance < minDistance) {
                minDistance = distance;
                nearestShelter = building;
            }
        });
        
        return nearestShelter;
    }
    
    distance(a, b) {
        return distance(a, b);
    }
    
    setCooldown(emergencyType, duration) {
        this.protocolCooldowns.set(emergencyType, this.engine.ai.game.gameTime + duration);
    }
    
    isOnCooldown(emergencyType) {
        const cooldownEnd = this.protocolCooldowns.get(emergencyType);
        return cooldownEnd && this.engine.ai.game.gameTime < cooldownEnd;
    }
    
    updateCooldowns(deltaTime) {
        const currentTime = this.engine.ai.game.gameTime;
        
        this.protocolCooldowns.forEach((endTime, emergencyType) => {
            if (currentTime >= endTime) {
                this.protocolCooldowns.delete(emergencyType);
            }
        });
    }
    
    // Public interface
    getActiveEmergencies() {
        return this.activeEmergencies;
    }
    
    forceEmergency(emergencyType, severity = 1.0) {
        const emergency = {
            type: emergencyType,
            severity: severity,
            detectedAt: this.engine.ai.game.gameTime,
            config: this.emergencyThresholds[emergencyType]
        };
        
        return this.handleEmergency(emergency);
    }
    
    getEmergencyStatus() {
        return {
            activeEmergencies: this.activeEmergencies.length,
            emergencyTypes: this.activeEmergencies.map(e => e.type),
            cooldowns: Array.from(this.protocolCooldowns.keys()),
            lastCheck: this.lastEmergencyCheck,
            nextCheck: this.lastEmergencyCheck + this.emergencyCheckInterval
        };
    }
    
    getEmergencyAnalytics() {
        const emergencyHistory = this.engine.ai.memory.getMemoriesByCategory('emergency');
        
        return {
            totalEmergencies: emergencyHistory.length,
            emergencyTypes: this.countEmergencyTypes(emergencyHistory),
            averageResolutionTime: this.calculateAverageResolutionTime(emergencyHistory),
            resolutionRate: this.calculateResolutionRate(emergencyHistory)
        };
    }
    
    countEmergencyTypes(emergencyHistory) {
        const counts = {};
        emergencyHistory.forEach(emergency => {
            const type = emergency.data.type;
            counts[type] = (counts[type] || 0) + 1;
        });
        return counts;
    }
    
    calculateAverageResolutionTime(emergencyHistory) {
        const resolvedEmergencies = emergencyHistory.filter(e => e.data.resolved);
        if (resolvedEmergencies.length === 0) return 0;
        
        const totalTime = resolvedEmergencies.reduce((sum, e) => sum + e.data.duration, 0);
        return totalTime / resolvedEmergencies.length;
    }
    
    calculateResolutionRate(emergencyHistory) {
        if (emergencyHistory.length === 0) return 0;
        
        const resolvedCount = emergencyHistory.filter(e => e.data.resolved).length;
        return resolvedCount / emergencyHistory.length;
    }
}