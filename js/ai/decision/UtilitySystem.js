import { distance } from '../../utils/index.js';

/**
 * Utility System
 * Calculates utility scores for AI decisions using weighted factors
 */
export class UtilitySystem {
    constructor(engine) {
        this.engine = engine;
        
        // Utility curves and weights
        this.utilityFactors = {
            survival: { weight: 1.0, curve: 'exponential' },
            economic: { weight: 0.7, curve: 'linear' },
            military: { weight: 0.8, curve: 'sigmoid' },
            strategic: { weight: 0.6, curve: 'logarithmic' },
            opportunistic: { weight: 0.5, curve: 'linear' }
        };
        
        // Context modifiers
        this.contextModifiers = {
            underAttack: 1.5,
            lowResources: 1.3,
            hasAdvantage: 0.8,
            earlyGame: 1.2,
            lateGame: 0.9
        };
    }
    
    initialize() {
        console.log('Utility System initialized');
    }
    
    update(deltaTime) {
        // Update utility calculations based on game state
        this.updateContextModifiers();
    }
    
    calculateUtility(option) {
        let totalUtility = 0;
        
        // Calculate base utility for each factor
        const factorUtilities = this.calculateFactorUtilities(option);
        
        // Weight and combine utilities
        Object.entries(factorUtilities).forEach(([factor, utility]) => {
            const weight = this.utilityFactors[factor]?.weight || 0.5;
            totalUtility += utility * weight;
        });
        
        // Apply context modifiers
        totalUtility = this.applyContextModifiers(totalUtility, option);
        
        // Apply personality modifiers
        totalUtility = this.applyPersonalityModifiers(totalUtility, option);
        
        // Normalize to 0-1 range
        return Math.max(0, Math.min(1, totalUtility));
    }
    
    calculateFactorUtilities(option) {
        const utilities = {};
        
        // Survival utility
        utilities.survival = this.calculateSurvivalUtility(option);
        
        // Economic utility
        utilities.economic = this.calculateEconomicUtility(option);
        
        // Military utility
        utilities.military = this.calculateMilitaryUtility(option);
        
        // Strategic utility
        utilities.strategic = this.calculateStrategicUtility(option);
        
        // Opportunistic utility
        utilities.opportunistic = this.calculateOpportunisticUtility(option);
        
        return utilities;
    }
    
    calculateSurvivalUtility(option) {
        const gameState = this.getGameState();
        let utility = 0.5; // Base survival utility
        
        // Increase utility for defensive actions when under threat
        if (gameState.underAttack) {
            if (option.category === 'survival' || option.type.includes('defense')) {
                utility += 0.4;
            }
        }
        
        // Increase utility based on threat level
        const threatLevel = this.calculateThreatLevel();
        if (threatLevel > 0.7) {
            utility += (threatLevel - 0.7) * 2;
        }
        
        // Decrease utility for risky actions when vulnerable
        if (gameState.aiPower < gameState.playerPower * 0.5) {
            if (option.category === 'military' && option.type.includes('attack')) {
                utility -= 0.3;
            }
        }
        
        return this.applyCurve(utility, 'exponential');
    }
    
    calculateEconomicUtility(option) {
        const gameState = this.getGameState();
        let utility = 0.5;
        
        // High utility for economic actions when resources are low
        if (gameState.aiSpice < 500) {
            if (option.category === 'economic') {
                utility += 0.4;
            }
        }
        
        // Utility based on economic efficiency
        const economicEfficiency = this.calculateEconomicEfficiency();
        utility += (1 - economicEfficiency) * 0.3;
        
        // Lower utility for expensive actions when resources are tight
        if (gameState.aiSpice < 1000 && option.type.includes('expensive')) {
            utility -= 0.2;
        }
        
        return this.applyCurve(utility, 'linear');
    }
    
    calculateMilitaryUtility(option) {
        const gameState = this.getGameState();
        let utility = 0.5;
        
        // High utility for military actions when we have advantage
        const powerRatio = gameState.aiPower / Math.max(1, gameState.playerPower);
        if (powerRatio > 1.2) {
            if (option.category === 'military') {
                utility += 0.3;
            }
        }
        
        // Utility based on military readiness
        const militaryReadiness = this.calculateMilitaryReadiness();
        utility += militaryReadiness * 0.2;
        
        // Higher utility for revenge actions
        if (option.type.includes('revenge') || option.type.includes('retaliate')) {
            const revengeIntensity = this.engine.ai.revengeSystem.calculateRevengeIntensity();
            utility += revengeIntensity * 0.3;
        }
        
        return this.applyCurve(utility, 'sigmoid');
    }
    
    calculateStrategicUtility(option) {
        const gameState = this.getGameState();
        let utility = 0.5;
        
        // Higher utility for strategic actions in mid-game
        if (gameState.gameTime > 300 && gameState.gameTime < 1200) {
            if (option.category === 'strategic') {
                utility += 0.2;
            }
        }
        
        // Utility based on strategic position
        const strategicPosition = this.calculateStrategicPosition();
        utility += strategicPosition * 0.25;
        
        // Higher utility for expansion when safe
        if (this.isSafeToExpand() && option.type.includes('expand')) {
            utility += 0.3;
        }
        
        return this.applyCurve(utility, 'logarithmic');
    }
    
    calculateOpportunisticUtility(option) {
        let utility = 0.5;
        
        // High utility for opportunistic actions when targets are available
        if (option.targets && option.targets.length > 0) {
            utility += Math.min(0.4, option.targets.length * 0.1);
        }
        
        // Utility based on opportunity assessment
        const opportunityScore = this.assessOpportunities();
        utility += opportunityScore * 0.3;
        
        // Higher utility when player is distracted or vulnerable
        if (this.isPlayerDistracted()) {
            utility += 0.2;
        }
        
        return this.applyCurve(utility, 'linear');
    }
    
    applyCurve(value, curveType) {
        switch (curveType) {
            case 'exponential':
                return Math.pow(value, 2);
            case 'logarithmic':
                return Math.log(value * Math.E + 1) / (Math.log(Math.E + 1));
            case 'sigmoid':
                return 1 / (1 + Math.exp(-6 * (value - 0.5)));
            case 'linear':
            default:
                return value;
        }
    }
    
    applyContextModifiers(utility, option) {
        const context = this.getCurrentContext();
        
        Object.entries(context).forEach(([contextKey, isActive]) => {
            if (isActive && this.contextModifiers[contextKey]) {
                const modifier = this.contextModifiers[contextKey];
                
                // Apply modifier based on option relevance
                if (this.isOptionRelevantToContext(option, contextKey)) {
                    utility *= modifier;
                }
            }
        });
        
        return utility;
    }
    
    applyPersonalityModifiers(utility, option) {
        const personality = this.engine.ai.personalityTraits;
        
        // Aggression modifier
        if (option.category === 'military') {
            utility *= (0.5 + personality.aggression);
        }
        
        // Cunning modifier
        if (option.category === 'opportunistic') {
            utility *= (0.5 + personality.cunning);
        }
        
        // Opportunism modifier
        if (option.type.includes('opportunity')) {
            utility *= (0.5 + personality.opportunism);
        }
        
        return utility;
    }
    
    // Helper methods
    getGameState() {
        return {
            aiPower: this.engine.ai.calculateAIPower(),
            playerPower: this.engine.ai.calculatePlayerPower(),
            aiSpice: this.engine.ai.team.spice,
            gameTime: this.engine.ai.game.gameTime,
            underAttack: this.isUnderAttack()
        };
    }
    
    calculateThreatLevel() {
        const playerUnits = this.engine.ai.game.teams.player.units.length;
        const aiUnits = this.engine.ai.team.units.length;
        
        const unitRatio = playerUnits / Math.max(1, aiUnits);
        const proximityThreat = this.calculateProximityThreat();
        
        return Math.min(1, (unitRatio * 0.5) + (proximityThreat * 0.5));
    }
    
    calculateProximityThreat() {
        let threat = 0;
        const aiBuildings = this.engine.ai.team.buildings;
        const playerUnits = this.engine.ai.game.teams.player.units;
        
        aiBuildings.forEach(building => {
            const nearbyEnemies = playerUnits.filter(unit =>
                this.distance(unit, building) < 300
            ).length;
            threat += nearbyEnemies * 0.1;
        });
        
        return Math.min(1, threat);
    }
    
    calculateEconomicEfficiency() {
        const harvesters = this.engine.ai.team.units.filter(u => u.type === 'harvester').length;
        const spiceFields = this.engine.ai.game.spiceFields.filter(f => f.amount > 0).length;
        
        return Math.min(1, harvesters / Math.max(1, spiceFields));
    }
    
    calculateMilitaryReadiness() {
        const combatUnits = this.engine.ai.team.units.filter(u => u.damage > 0).length;
        const totalUnits = this.engine.ai.team.units.length;
        
        return combatUnits / Math.max(1, totalUnits);
    }
    
    calculateStrategicPosition() {
        const aiBuildings = this.engine.ai.team.buildings.length;
        const playerBuildings = this.engine.ai.game.teams.player.buildings.length;
        
        const buildingRatio = aiBuildings / Math.max(1, playerBuildings);
        const territoryControl = this.calculateTerritoryControl();
        
        return Math.min(1, (buildingRatio * 0.6) + (territoryControl * 0.4));
    }
    
    calculateTerritoryControl() {
        // Simplified territory control calculation
        const mapCenter = {
            x: this.engine.ai.game.width / 2,
            y: this.engine.ai.game.height / 2
        };
        
        const aiDistance = this.findNearestBuilding(this.engine.ai.team.buildings, mapCenter);
        const playerDistance = this.findNearestBuilding(this.engine.ai.game.teams.player.buildings, mapCenter);
        
        if (!aiDistance || !playerDistance) return 0.5;
        
        return playerDistance / (aiDistance + playerDistance);
    }
    
    assessOpportunities() {
        let score = 0;
        
        // Check for vulnerable player units
        const vulnerableUnits = this.engine.ai.game.teams.player.units.filter(unit =>
            unit.health < unit.maxHealth * 0.3
        ).length;
        score += vulnerableUnits * 0.1;
        
        // Check for undefended buildings
        const undefendedBuildings = this.engine.ai.game.teams.player.buildings.filter(building =>
            this.countNearbyDefenders(building) < 2
        ).length;
        score += undefendedBuildings * 0.15;
        
        return Math.min(1, score);
    }
    
    isPlayerDistracted() {
        // Check if player units are spread out or engaged elsewhere
        const playerUnits = this.engine.ai.game.teams.player.units;
        if (playerUnits.length === 0) return false;
        
        const avgPosition = this.calculateAveragePosition(playerUnits);
        const spreadDistance = this.calculateSpread(playerUnits, avgPosition);
        
        return spreadDistance > 400; // Player units are spread out
    }
    
    isSafeToExpand() {
        const nearbyEnemies = this.engine.ai.game.teams.player.units.filter(unit =>
            this.engine.ai.team.buildings.some(building =>
                this.distance(unit, building) < 500
            )
        ).length;
        
        return nearbyEnemies < 3;
    }
    
    isUnderAttack() {
        return this.engine.ai.team.buildings.some(building =>
            this.engine.ai.game.teams.player.units.some(unit =>
                unit.attackTarget === building
            )
        );
    }
    
    getCurrentContext() {
        const gameState = this.getGameState();
        
        return {
            underAttack: gameState.underAttack,
            lowResources: gameState.aiSpice < 500,
            hasAdvantage: gameState.aiPower > gameState.playerPower * 1.2,
            earlyGame: gameState.gameTime < 300,
            lateGame: gameState.gameTime > 1200
        };
    }
    
    isOptionRelevantToContext(option, contextKey) {
        const relevanceMap = {
            underAttack: ['survival', 'military'],
            lowResources: ['economic'],
            hasAdvantage: ['military', 'opportunistic'],
            earlyGame: ['economic', 'strategic'],
            lateGame: ['military', 'strategic']
        };
        
        const relevantCategories = relevanceMap[contextKey] || [];
        return relevantCategories.includes(option.category);
    }
    
    updateContextModifiers() {
        // Dynamically adjust context modifiers based on AI performance
        const effectiveness = this.engine.ai.behaviorEngine?.decisionFramework?.analyzeDecisionEffectiveness() || 0.5;
        
        if (effectiveness < 0.4) {
            this.contextModifiers.underAttack = Math.min(2.0, this.contextModifiers.underAttack + 0.1);
        } else if (effectiveness > 0.7) {
            this.contextModifiers.hasAdvantage = Math.max(0.5, this.contextModifiers.hasAdvantage - 0.05);
        }
    }
    
    // Utility methods
    distance(a, b) {
        return distance(a, b);
    }
    
    findNearestBuilding(buildings, target) {
        let minDistance = Infinity;
        
        buildings.forEach(building => {
            const dist = this.distance(building, target);
            if (dist < minDistance) {
                minDistance = dist;
            }
        });
        
        return minDistance === Infinity ? null : minDistance;
    }
    
    countNearbyDefenders(building) {
        return this.engine.ai.game.teams.player.units.filter(unit =>
            unit.damage > 0 && this.distance(unit, building) < 200
        ).length;
    }
    
    calculateAveragePosition(units) {
        const totalX = units.reduce((sum, unit) => sum + unit.x, 0);
        const totalY = units.reduce((sum, unit) => sum + unit.y, 0);
        
        return {
            x: totalX / units.length,
            y: totalY / units.length
        };
    }
    
    calculateSpread(units, center) {
        const distances = units.map(unit => this.distance(unit, center));
        const totalDistance = distances.reduce((sum, dist) => sum + dist, 0);
        
        return totalDistance / units.length;
    }
    
    // Public interface for debugging
    getUtilityBreakdown(option) {
        const factorUtilities = this.calculateFactorUtilities(option);
        const context = this.getCurrentContext();
        const personality = this.engine.ai.personalityTraits;
        
        return {
            factorUtilities: factorUtilities,
            finalUtility: this.calculateUtility(option),
            context: context,
            personality: personality,
            weights: this.utilityFactors
        };
    }
}