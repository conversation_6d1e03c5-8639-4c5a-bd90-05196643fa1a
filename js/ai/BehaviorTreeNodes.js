/**
 * Behavior Tree Node Classes
 * Implements the core behavior tree node types for AI decision making
 */

/**
 * Base Behavior Node
 */
class BehaviorNode {
    constructor() {
        this.status = 'ready'; // ready, running, success, failure
    }
    
    execute() {
        throw new Error('Execute method must be implemented');
    }
}

/**
 * Selector Node - Executes children until one succeeds
 * Returns success if any child succeeds, failure if all fail
 */
class SelectorNode extends BehaviorNode {
    constructor(children) {
        super();
        this.children = children || [];
    }
    
    execute() {
        for (const child of this.children) {
            const result = child.execute();
            if (result === 'success' || result === 'running') {
                return result;
            }
        }
        return 'failure';
    }
    
    addChild(child) {
        this.children.push(child);
    }
}

/**
 * Sequence Node - Executes children in order until one fails
 * Returns failure if any child fails, success if all succeed
 */
class SequenceNode extends BehaviorNode {
    constructor(children) {
        super();
        this.children = children || [];
    }
    
    execute() {
        for (const child of this.children) {
            const result = child.execute();
            if (result === 'failure' || result === 'running') {
                return result;
            }
        }
        return 'success';
    }
    
    addChild(child) {
        this.children.push(child);
    }
}

/**
 * Parallel Node - Executes all children simultaneously
 * Can be configured for different success/failure policies
 */
class ParallelNode extends BehaviorNode {
    constructor(children, successPolicy = 'all', failurePolicy = 'any') {
        super();
        this.children = children || [];
        this.successPolicy = successPolicy; // 'all', 'any', or number
        this.failurePolicy = failurePolicy; // 'all', 'any', or number
    }
    
    execute() {
        const results = this.children.map(child => child.execute());
        
        const successCount = results.filter(r => r === 'success').length;
        const failureCount = results.filter(r => r === 'failure').length;
        const runningCount = results.filter(r => r === 'running').length;
        
        // Check failure conditions first
        if (this.checkFailureCondition(failureCount, results.length)) {
            return 'failure';
        }
        
        // Check success conditions
        if (this.checkSuccessCondition(successCount, results.length)) {
            return 'success';
        }
        
        // If any are still running, return running
        if (runningCount > 0) {
            return 'running';
        }
        
        return 'failure';
    }
    
    checkSuccessCondition(successCount, totalCount) {
        if (this.successPolicy === 'all') {
            return successCount === totalCount;
        } else if (this.successPolicy === 'any') {
            return successCount > 0;
        } else if (typeof this.successPolicy === 'number') {
            return successCount >= this.successPolicy;
        }
        return false;
    }
    
    checkFailureCondition(failureCount, totalCount) {
        if (this.failurePolicy === 'all') {
            return failureCount === totalCount;
        } else if (this.failurePolicy === 'any') {
            return failureCount > 0;
        } else if (typeof this.failurePolicy === 'number') {
            return failureCount >= this.failurePolicy;
        }
        return false;
    }
}

/**
 * Condition Node - Tests a condition and returns success/failure
 */
class ConditionNode extends BehaviorNode {
    constructor(condition) {
        super();
        this.condition = condition;
    }
    
    execute() {
        try {
            return this.condition() ? 'success' : 'failure';
        } catch (error) {
            console.error('Condition node error:', error);
            return 'failure';
        }
    }
}

/**
 * Action Node - Executes an action
 */
class ActionNode extends BehaviorNode {
    constructor(action) {
        super();
        this.action = action;
    }
    
    execute() {
        try {
            const result = this.action();
            return result || 'success';
        } catch (error) {
            console.error('Action node error:', error);
            return 'failure';
        }
    }
}

/**
 * Decorator Node - Base class for nodes that modify child behavior
 */
class DecoratorNode extends BehaviorNode {
    constructor(child) {
        super();
        this.child = child;
    }
    
    execute() {
        if (!this.child) return 'failure';
        return this.decorateExecution(this.child.execute());
    }
    
    decorateExecution(childResult) {
        return childResult;
    }
}

/**
 * Inverter Node - Inverts the result of its child
 */
class InverterNode extends DecoratorNode {
    decorateExecution(childResult) {
        if (childResult === 'success') return 'failure';
        if (childResult === 'failure') return 'success';
        return childResult; // running stays running
    }
}

/**
 * Repeater Node - Repeats its child a specified number of times
 */
class RepeaterNode extends DecoratorNode {
    constructor(child, maxRepeats = -1) {
        super(child);
        this.maxRepeats = maxRepeats; // -1 for infinite
        this.currentRepeats = 0;
    }
    
    execute() {
        if (this.maxRepeats > 0 && this.currentRepeats >= this.maxRepeats) {
            return 'success';
        }
        
        const result = super.execute();
        
        if (result === 'success' || result === 'failure') {
            this.currentRepeats++;
            
            if (this.maxRepeats > 0 && this.currentRepeats >= this.maxRepeats) {
                return 'success';
            }
            
            return 'running'; // Continue repeating
        }
        
        return result;
    }
    
    reset() {
        this.currentRepeats = 0;
    }
}

/**
 * Retry Node - Retries its child until success or max attempts
 */
class RetryNode extends DecoratorNode {
    constructor(child, maxAttempts = 3) {
        super(child);
        this.maxAttempts = maxAttempts;
        this.currentAttempts = 0;
    }
    
    execute() {
        const result = super.execute();
        
        if (result === 'success') {
            this.currentAttempts = 0;
            return 'success';
        }
        
        if (result === 'failure') {
            this.currentAttempts++;
            
            if (this.currentAttempts >= this.maxAttempts) {
                this.currentAttempts = 0;
                return 'failure';
            }
            
            return 'running'; // Try again
        }
        
        return result; // running
    }
}

/**
 * Timeout Node - Fails if child takes too long
 */
class TimeoutNode extends DecoratorNode {
    constructor(child, timeoutMs = 5000) {
        super(child);
        this.timeoutMs = timeoutMs;
        this.startTime = null;
    }
    
    execute() {
        if (this.startTime === null) {
            this.startTime = Date.now();
        }
        
        const elapsed = Date.now() - this.startTime;
        
        if (elapsed > this.timeoutMs) {
            this.startTime = null;
            return 'failure';
        }
        
        const result = super.execute();
        
        if (result === 'success' || result === 'failure') {
            this.startTime = null;
        }
        
        return result;
    }
}

/**
 * Cooldown Node - Prevents child execution until cooldown expires
 */
class CooldownNode extends DecoratorNode {
    constructor(child, cooldownMs = 1000) {
        super(child);
        this.cooldownMs = cooldownMs;
        this.lastExecution = 0;
    }
    
    execute() {
        const now = Date.now();
        
        if (now - this.lastExecution < this.cooldownMs) {
            return 'failure';
        }
        
        const result = super.execute();
        
        if (result === 'success' || result === 'failure') {
            this.lastExecution = now;
        }
        
        return result;
    }
}

/**
 * Probability Node - Executes child based on probability
 */
class ProbabilityNode extends DecoratorNode {
    constructor(child, probability = 0.5) {
        super(child);
        this.probability = Math.max(0, Math.min(1, probability));
    }
    
    execute() {
        if (Math.random() > this.probability) {
            return 'failure';
        }
        
        return super.execute();
    }
}

/**
 * Memory Node - Remembers the last result for a duration
 */
class MemoryNode extends DecoratorNode {
    constructor(child, memoryDurationMs = 2000) {
        super(child);
        this.memoryDurationMs = memoryDurationMs;
        this.lastResult = null;
        this.lastResultTime = 0;
    }
    
    execute() {
        const now = Date.now();
        
        // Return cached result if still valid
        if (this.lastResult && (now - this.lastResultTime) < this.memoryDurationMs) {
            return this.lastResult;
        }
        
        // Execute child and cache result
        const result = super.execute();
        
        if (result === 'success' || result === 'failure') {
            this.lastResult = result;
            this.lastResultTime = now;
        }
        
        return result;
    }
    
    clearMemory() {
        this.lastResult = null;
        this.lastResultTime = 0;
    }
}

// Export all node types
export const BehaviorTreeNodes = {
    BehaviorNode,
    SelectorNode,
    SequenceNode,
    ParallelNode,
    ConditionNode,
    ActionNode,
    DecoratorNode,
    InverterNode,
    RepeaterNode,
    RetryNode,
    TimeoutNode,
    CooldownNode,
    ProbabilityNode,
    MemoryNode
};