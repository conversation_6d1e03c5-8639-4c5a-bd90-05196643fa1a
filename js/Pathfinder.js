import { PriorityQueue } from './PriorityQueue.js';

export class Pathfinder {
    constructor(game) {
        this.game = game;
        this.gridSize = 25;
        this.grid = [];
        
        // Access world dimensions from gameState
        const worldWidth = game.gameState ? game.gameState.world.width : 3000;
        const worldHeight = game.gameState ? game.gameState.world.height : 3000;
        
        this.width = Math.ceil(worldWidth / this.gridSize);
        this.height = Math.ceil(worldHeight / this.gridSize);
        this.initGrid();
    }
    initGrid() { this.grid = Array(this.height).fill(null).map(() => Array(this.width).fill(1)); }
    updateGridObstacles() {
        this.initGrid();
        this.game.buildings.forEach(building => {
            if(building.constructionProgress < 100 && building.type !== 'wall') return;
            const margin = building.type === 'wall' ? 0 : this.gridSize / 3;
            const startX = Math.floor((building.x - building.width/2 - margin) / this.gridSize);
            const startY = Math.floor((building.y - building.height/2 - margin) / this.gridSize);
            const endX = Math.ceil((building.x + building.width/2 + margin) / this.gridSize);
            const endY = Math.ceil((building.y + building.height/2 + margin) / this.gridSize);
            for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
                for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                    if (x >= 0 && x < this.width && y >= 0 && y < this.height) this.grid[y][x] = 0;
                }
            }
        });
        this.game.terrain.forEach(obstacle => { // Handle different terrain types
            const startX = Math.floor((obstacle.x - obstacle.radius) / this.gridSize);
            const startY = Math.floor((obstacle.y - obstacle.radius) / this.gridSize);
            const endX = Math.ceil((obstacle.x + obstacle.radius) / this.gridSize);
            const endY = Math.ceil((obstacle.y + obstacle.radius) / this.gridSize);
            for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
                for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
                    const cellCenterX = x * this.gridSize + this.gridSize / 2;
                    const cellCenterY = y * this.gridSize + this.gridSize / 2;
                    if (Math.hypot(cellCenterX - obstacle.x, cellCenterY - obstacle.y) < obstacle.radius) {
                        if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
                            // Set terrain movement costs
                            switch(obstacle.type) {
                                case 'rock':
                                case 'cliff':
                                    this.grid[y][x] = 0; // Impassable
                                    break;
                                case 'water':
                                    this.grid[y][x] = 0; // Impassable for ground units (naval units would override this)
                                    break;
                                default:
                                    this.grid[y][x] = 0; // Default impassable
                                    break;
                            }
                        }
                    }
                }
            }
        });
    }
    findPath(startX, startY, endX, endY) {
        const startNode = { x: Math.min(this.width - 1, Math.max(0, Math.floor(startX / this.gridSize))), y: Math.min(this.height - 1, Math.max(0, Math.floor(startY / this.gridSize))) };
        const endNode = { x: Math.min(this.width - 1, Math.max(0, Math.floor(endX / this.gridSize))), y: Math.min(this.height - 1, Math.max(0, Math.floor(endY / this.gridSize))) };
        
        if (startNode.x < 0 || startNode.x >= this.width || startNode.y < 0 || startNode.y >= this.height || 
            endNode.x < 0 || endNode.x >= this.width || endNode.y < 0 || endNode.y >= this.height) {
            // console.warn("Pathfinding out of bounds attempt.");
            return [{ x: endX, y: endY }]; // Fallback
        }


        if (this.grid[endNode.y][endNode.x] === 0) {
            let foundNewEnd = false;
            for (let r = 1; r < 5 && !foundNewEnd; r++) {
                for (let dy = -r; dy <=r && !foundNewEnd; dy++) {
                    for (let dx = -r; dx <= r && !foundNewEnd; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        const nx = endNode.x + dx; const ny = endNode.y + dy;
                        if (nx >=0 && nx < this.width && ny >= 0 && ny < this.height && this.grid[ny][nx] === 1) {
                            endNode.x = nx; endNode.y = ny; foundNewEnd = true;
                        }
                    }
                }
            }
            if (!foundNewEnd) return [{ x: endX, y: endY }];
        }
        const openSet = new PriorityQueue();
        openSet.enqueue(startNode, 0);
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();
        const getKey = pos => `${pos.x},${pos.y}`;
        gScore.set(getKey(startNode), 0); fScore.set(getKey(startNode), this.heuristic(startNode, endNode));
        const visitedInOpenSet = new Set([getKey(startNode)]);
        let iterations = 0;
        const maxIterations = (this.width * this.height) / 2; // Safety break

        while (openSet.length > 0 && iterations < maxIterations) {
            iterations++;
            const current = openSet.dequeue();
            visitedInOpenSet.delete(getKey(current));
            if (current.x === endNode.x && current.y === endNode.y) return this.reconstructPath(cameFrom, current, endX, endY);
            this.getNeighbors(current).forEach(neighbor => {
                const tentativeGScore = (gScore.get(getKey(current)) || Infinity) + this.heuristic(current, neighbor);
                if (tentativeGScore < (gScore.get(getKey(neighbor)) || Infinity)) {
                    cameFrom.set(getKey(neighbor), current); gScore.set(getKey(neighbor), tentativeGScore);
                    fScore.set(getKey(neighbor), tentativeGScore + this.heuristic(neighbor, endNode));
                    if (!visitedInOpenSet.has(getKey(neighbor))) {
                        openSet.enqueue(neighbor, fScore.get(getKey(neighbor)));
                        visitedInOpenSet.add(getKey(neighbor));
                    }
                }
            });
        }
        // if (iterations >= maxIterations) console.warn("Pathfinder exceeded max iterations.");
        return [{ x: endX, y: endY }]; // Fallback if no path or too complex
    }
    heuristic(a, b) { return Math.abs(a.x - b.x) + Math.abs(a.y - b.y); }
    getNeighbors(pos) {
        const neighbors = []; const dirs = [ { x: 0, y: -1 }, { x: 1, y: 0 }, { x: 0, y: 1 }, { x: -1, y: 0 }, { x: -1, y: -1 }, { x: 1, y: -1 }, { x: 1, y: 1 }, { x: -1, y: 1 }];
        for (const dir of dirs) {
            const x = pos.x + dir.x; const y = pos.y + dir.y;
            if (x >= 0 && x < this.width && y >= 0 && y < this.height && this.grid[y][x] === 1) neighbors.push({ x, y });
        }
        return neighbors;
    }
    reconstructPath(cameFrom, current, finalTargetX, finalTargetY) {
        const totalPath = []; const getKey = pos => `${pos.x},${pos.y}`; let tempCurrent = current;
        while (cameFrom.has(getKey(tempCurrent))) {
            totalPath.unshift({ x: tempCurrent.x * this.gridSize + this.gridSize / 2, y: tempCurrent.y * this.gridSize + this.gridSize / 2 });
            tempCurrent = cameFrom.get(getKey(tempCurrent));
        }
        if (totalPath.length > 0) totalPath[totalPath.length-1] = {x: finalTargetX, y: finalTargetY};
        else totalPath.push({x: finalTargetX, y: finalTargetY}); // If start is end
        return totalPath.length > 1 ? this.smoothPath(totalPath) : totalPath;
    }
     smoothPath(path) { // Basic smoothing
        if (path.length < 3) return path;
        const newPath = [path[0]];
        let currentIdx = 0;
        while (currentIdx < path.length - 1) {
            let nextVisibleIdx = currentIdx + 1;
            for (let i = currentIdx + 2; i < path.length; i++) {
                if (this.isLineOfSight(path[currentIdx], path[i])) {
                    nextVisibleIdx = i;
                } else {
                    break;
                }
            }
            newPath.push(path[nextVisibleIdx]);
            currentIdx = nextVisibleIdx;
        }
        return newPath;
    }
    isLineOfSight(p1, p2) { // Bresenham variant
        let x0 = Math.floor(p1.x / this.gridSize); let y0 = Math.floor(p1.y / this.gridSize);
        const x1 = Math.floor(p2.x / this.gridSize); const y1 = Math.floor(p2.y / this.gridSize);
        const dx = Math.abs(x1 - x0); const dy = -Math.abs(y1 - y0);
        const sx = x0 < x1 ? 1 : -1; const sy = y0 < y1 ? 1 : -1;
        let err = dx + dy; let e2;
        while (true) {
            if (x0 < 0 || x0 >= this.width || y0 < 0 || y0 >= this.height || this.grid[y0][x0] === 0) return false;
            if (x0 === x1 && y0 === y1) break;
            e2 = 2 * err;
            if (e2 >= dy) { err += dy; x0 += sx; }
            if (e2 <= dx) { err += dx; y0 += sy; }
        }
        return true;
    }
}
