export class DiplomacySystem {
    constructor(game) {
        this.game = game;
        
        // Faction definitions
        this.factions = {
            atreides: {
                name: 'House Atreides',
                color: '#4169E1',
                personality: 'honorable',
                initialRelation: 0,
                traits: ['diplomatic', 'defensive', 'honorable'],
                preferredTech: ['defensive', 'economic'],
                description: 'Noble house known for honor and loyalty'
            },
            harkonnen: {
                name: 'House Harkonnen',
                color: '#8B0000',
                personality: 'aggressive',
                initialRelation: -20,
                traits: ['aggressive', 'military', 'ruthless'],
                preferredTech: ['military', 'advanced'],
                description: 'Brutal house focused on military dominance'
            },
            ordos: {
                name: 'House Ordos',
                color: '#32CD32',
                personality: 'mercantile',
                initialRelation: 10,
                traits: ['mercantile', 'technological', 'opportunistic'],
                preferredTech: ['economic', 'advanced'],
                description: 'Wealthy merchants with advanced technology'
            },
            fremen: {
                name: 'Fremen',
                color: '#D2691E',
                personality: 'independent',
                initialRelation: 5,
                traits: ['independent', 'environmental', 'guerrilla'],
                preferredTech: ['environmental', 'military'],
                description: 'Desert warriors with deep knowledge of Arrakis'
            },
            guild: {
                name: 'Spacing Guild',
                color: '#9370DB',
                personality: 'neutral',
                initialRelation: 0,
                traits: ['neutral', 'technological', 'trade'],
                preferredTech: ['advanced', 'strategic'],
                description: 'Powerful guild controlling space travel'
            }
        };
        
        // Diplomatic states
        this.diplomaticStates = {
            WAR: { value: -100, name: 'At War', color: '#FF0000' },
            HOSTILE: { value: -50, name: 'Hostile', color: '#FF6600' },
            UNFRIENDLY: { value: -20, name: 'Unfriendly', color: '#FFAA00' },
            NEUTRAL: { value: 0, name: 'Neutral', color: '#FFFF00' },
            FRIENDLY: { value: 20, name: 'Friendly', color: '#90EE90' },
            ALLIED: { value: 50, name: 'Allied', color: '#00FF00' },
            VASSAL: { value: 75, name: 'Vassal', color: '#00FFFF' }
        };
        
        // Current relationships (faction -> relation value)
        this.relations = {};
        
        // Active treaties and agreements
        this.treaties = {};
        
        // Trade agreements
        this.tradeAgreements = {};
        
        // Diplomatic actions cooldowns
        this.actionCooldowns = {};
        
        // Initialize relationships
        this.initializeRelations();
        
        // Diplomatic events queue
        this.diplomaticEvents = [];
        
        // Last update time for periodic events
        this.lastUpdateTime = 0;
    }
    
    initializeRelations() {
        Object.keys(this.factions).forEach(factionId => {
            this.relations[factionId] = this.factions[factionId].initialRelation;
            this.treaties[factionId] = [];
            this.tradeAgreements[factionId] = null;
            this.actionCooldowns[factionId] = {};
        });
    }
    
    // Get current diplomatic state with a faction
    getDiplomaticState(factionId) {
        const relation = this.relations[factionId] || 0;
        
        if (relation >= this.diplomaticStates.VASSAL.value) return 'VASSAL';
        if (relation >= this.diplomaticStates.ALLIED.value) return 'ALLIED';
        if (relation >= this.diplomaticStates.FRIENDLY.value) return 'FRIENDLY';
        if (relation >= this.diplomaticStates.NEUTRAL.value) return 'NEUTRAL';
        if (relation >= this.diplomaticStates.UNFRIENDLY.value) return 'UNFRIENDLY';
        if (relation >= this.diplomaticStates.HOSTILE.value) return 'HOSTILE';
        return 'WAR';
    }
    
    // Modify relationship with a faction
    modifyRelation(factionId, amount, reason = '') {
        if (!this.factions[factionId]) return false;
        
        const oldRelation = this.relations[factionId];
        this.relations[factionId] = Math.max(-100, Math.min(100, oldRelation + amount));
        
        const oldState = this.getDiplomaticStateFromValue(oldRelation);
        const newState = this.getDiplomaticState(factionId);
        
        if (oldState !== newState) {
            this.game.ui.showNotification(
                `Diplomatic status with ${this.factions[factionId].name} changed to ${this.diplomaticStates[newState].name}`,
                newState === 'WAR' || newState === 'HOSTILE' ? 'warning' : 'info'
            );
            
            // Trigger diplomatic event
            this.triggerDiplomaticEvent('relation_change', factionId, { oldState, newState, reason });
        }
        
        return true;
    }
    
    getDiplomaticStateFromValue(value) {
        if (value >= this.diplomaticStates.VASSAL.value) return 'VASSAL';
        if (value >= this.diplomaticStates.ALLIED.value) return 'ALLIED';
        if (value >= this.diplomaticStates.FRIENDLY.value) return 'FRIENDLY';
        if (value >= this.diplomaticStates.NEUTRAL.value) return 'NEUTRAL';
        if (value >= this.diplomaticStates.UNFRIENDLY.value) return 'UNFRIENDLY';
        if (value >= this.diplomaticStates.HOSTILE.value) return 'HOSTILE';
        return 'WAR';
    }
    
    // Declare war on a faction
    declareWar(factionId) {
        if (!this.canPerformAction(factionId, 'declare_war')) return false;
        
        this.relations[factionId] = this.diplomaticStates.WAR.value;
        this.breakAllTreaties(factionId);
        this.setActionCooldown(factionId, 'declare_war', 300); // 5 minute cooldown
        
        this.game.ui.showNotification(`War declared on ${this.factions[factionId].name}!`, 'warning');
        this.triggerDiplomaticEvent('war_declared', factionId);
        
        return true;
    }
    
    // Propose alliance
    proposeAlliance(factionId) {
        if (!this.canPerformAction(factionId, 'propose_alliance')) return false;
        
        const currentState = this.getDiplomaticState(factionId);
        if (currentState === 'WAR' || currentState === 'HOSTILE') {
            this.game.ui.showNotification('Cannot propose alliance to hostile factions!', 'warning');
            return false;
        }
        
        // AI decision based on faction personality and current relations
        const success = this.evaluateAllianceProposal(factionId);
        
        if (success) {
            this.relations[factionId] = Math.max(this.relations[factionId], this.diplomaticStates.ALLIED.value);
            this.addTreaty(factionId, 'alliance', { 
                type: 'military_alliance',
                duration: -1, // Permanent until broken
                benefits: ['mutual_defense', 'shared_vision', 'resource_sharing']
            });
            this.game.ui.showNotification(`Alliance formed with ${this.factions[factionId].name}!`, 'success');
        } else {
            this.modifyRelation(factionId, -5, 'rejected alliance');
            this.game.ui.showNotification(`${this.factions[factionId].name} rejected the alliance proposal.`, 'info');
        }
        
        this.setActionCooldown(factionId, 'propose_alliance', 180); // 3 minute cooldown
        return success;
    }
    
    // Propose trade agreement
    proposeTradeAgreement(factionId, playerOffer, factionRequest) {
        if (!this.canPerformAction(factionId, 'propose_trade')) return false;
        
        const currentState = this.getDiplomaticState(factionId);
        if (currentState === 'WAR') {
            this.game.ui.showNotification('Cannot trade with factions at war!', 'warning');
            return false;
        }
        
        // Check if player can afford the offer
        if (!this.game.resourceManager.canAfford(playerOffer)) {
            this.game.ui.showNotification('Insufficient resources for trade offer!', 'warning');
            return false;
        }
        
        // AI evaluation of trade proposal
        const success = this.evaluateTradeProposal(factionId, playerOffer, factionRequest);
        
        if (success) {
            // Execute trade
            this.game.resourceManager.spendResources(playerOffer);
            this.game.resourceManager.addResource(factionRequest.resource, factionRequest.amount);
            
            this.modifyRelation(factionId, 5, 'successful trade');
            this.game.ui.showNotification(`Trade completed with ${this.factions[factionId].name}!`, 'success');
            
            // Establish ongoing trade agreement if beneficial
            if (this.relations[factionId] >= this.diplomaticStates.FRIENDLY.value) {
                this.establishTradeAgreement(factionId, playerOffer, factionRequest);
            }
        } else {
            this.game.ui.showNotification(`${this.factions[factionId].name} rejected the trade proposal.`, 'info');
        }
        
        this.setActionCooldown(factionId, 'propose_trade', 60); // 1 minute cooldown
        return success;
    }
    
    // Evaluate AI response to alliance proposal
    evaluateAllianceProposal(factionId) {
        const faction = this.factions[factionId];
        const currentRelation = this.relations[factionId];
        const playerPower = this.calculatePlayerPower();
        
        let acceptanceChance = 0.3; // Base 30% chance
        
        // Relation bonus
        acceptanceChance += Math.max(0, currentRelation) / 100;
        
        // Faction personality modifiers
        if (faction.personality === 'diplomatic') acceptanceChance += 0.2;
        if (faction.personality === 'aggressive') acceptanceChance -= 0.3;
        if (faction.personality === 'mercantile') acceptanceChance += 0.1;
        
        // Power balance consideration
        if (playerPower > 0.7) acceptanceChance += 0.2; // Strong player
        if (playerPower < 0.3) acceptanceChance -= 0.2; // Weak player
        
        // Technology bonus if player has trade networks
        if (this.game.technologySystem.isResearched('trade_networks')) {
            acceptanceChance += 0.15;
        }
        
        return Math.random() < acceptanceChance;
    }
    
    // Evaluate AI response to trade proposal
    evaluateTradeProposal(factionId, playerOffer, factionRequest) {
        const faction = this.factions[factionId];
        const currentRelation = this.relations[factionId];
        
        // Calculate trade value ratio
        const offerValue = this.calculateResourceValue(playerOffer);
        const requestValue = this.calculateResourceValue({ [factionRequest.resource]: factionRequest.amount });
        
        let acceptanceChance = 0.5; // Base 50% chance
        
        // Trade value consideration
        const valueRatio = offerValue / requestValue;
        if (valueRatio >= 1.2) acceptanceChance += 0.3; // Good deal for AI
        if (valueRatio >= 1.0) acceptanceChance += 0.1; // Fair deal
        if (valueRatio < 0.8) acceptanceChance -= 0.4; // Bad deal for AI
        
        // Relation bonus
        acceptanceChance += Math.max(0, currentRelation) / 200;
        
        // Faction personality
        if (faction.personality === 'mercantile') acceptanceChance += 0.2;
        if (faction.personality === 'aggressive' && factionRequest.resource === 'spice') acceptanceChance += 0.1;
        
        return Math.random() < acceptanceChance;
    }
    
    // Calculate relative resource value
    calculateResourceValue(resources) {
        const values = { spice: 1, water: 1.2, techPoints: 2 };
        let totalValue = 0;
        
        Object.entries(resources).forEach(([resource, amount]) => {
            totalValue += (values[resource] || 1) * amount;
        });
        
        return totalValue;
    }
    
    // Calculate player power for AI decisions
    calculatePlayerPower() {
        const playerUnits = this.game.teams.player.units.length;
        const playerBuildings = this.game.teams.player.buildings.length;
        const playerResources = this.game.resourceManager.getResource('spice');
        const researchedTech = this.game.technologySystem.researchedTechnologies.size;
        
        // Normalize to 0-1 scale
        const unitPower = Math.min(1, playerUnits / 50);
        const buildingPower = Math.min(1, playerBuildings / 20);
        const resourcePower = Math.min(1, playerResources / 10000);
        const techPower = Math.min(1, researchedTech / 20);
        
        return (unitPower + buildingPower + resourcePower + techPower) / 4;
    }
    
    // Check if action can be performed (cooldown check)
    canPerformAction(factionId, action) {
        const cooldown = this.actionCooldowns[factionId][action];
        if (!cooldown) return true;
        
        return this.game.gameTime >= cooldown;
    }
    
    // Set action cooldown
    setActionCooldown(factionId, action, seconds) {
        this.actionCooldowns[factionId][action] = this.game.gameTime + (seconds * 60); // Convert to game time
    }
    
    // Add treaty
    addTreaty(factionId, treatyType, details) {
        this.treaties[factionId].push({
            type: treatyType,
            startTime: this.game.gameTime,
            ...details
        });
    }
    
    // Break all treaties with a faction
    breakAllTreaties(factionId) {
        this.treaties[factionId] = [];
        this.tradeAgreements[factionId] = null;
    }
    
    // Establish ongoing trade agreement
    establishTradeAgreement(factionId, playerOffer, factionRequest) {
        this.tradeAgreements[factionId] = {
            playerOffer,
            factionRequest,
            interval: 120, // Every 2 minutes
            lastTrade: this.game.gameTime
        };
    }
    
    // Trigger diplomatic event
    triggerDiplomaticEvent(eventType, factionId, data = {}) {
        this.diplomaticEvents.push({
            type: eventType,
            factionId,
            timestamp: this.game.gameTime,
            data
        });
        
        // Handle immediate effects
        this.handleDiplomaticEvent(eventType, factionId, data);
    }
    
    // Handle diplomatic event effects
    handleDiplomaticEvent(eventType, factionId, data) {
        switch (eventType) {
            case 'war_declared':
                // AI becomes more aggressive
                this.makeAIMoreAggressive(factionId);
                break;
            case 'alliance_formed':
                // Share vision and provide mutual support
                this.enableMutualSupport(factionId);
                break;
            case 'relation_change':
                // Update AI behavior based on new relationship
                this.updateAIBehavior(factionId);
                break;
        }
    }
    
    // Make AI more aggressive when at war
    makeAIMoreAggressive(factionId) {
        // This would integrate with the AI system to make it more aggressive
        if (this.game.aiController && this.game.aiController.teamId === factionId) {
            this.game.aiController.aggressionLevel = 1.5;
        }
    }
    
    // Enable mutual support for allies
    enableMutualSupport(factionId) {
        // Allies share vision and provide resource bonuses
        if (this.getDiplomaticState(factionId) === 'ALLIED') {
            this.game.resourceManager.addResource('spice', 500);
            this.game.ui.showNotification('Allied faction provides resource support!', 'success');
        }
    }
    
    // Update AI behavior based on diplomatic relations
    updateAIBehavior(factionId) {
        const state = this.getDiplomaticState(factionId);
        
        // Modify AI aggression and behavior based on diplomatic state
        if (this.game.aiController && this.game.aiController.teamId === factionId) {
            switch (state) {
                case 'WAR':
                case 'HOSTILE':
                    this.game.aiController.aggressionLevel = 1.5;
                    break;
                case 'ALLIED':
                case 'FRIENDLY':
                    this.game.aiController.aggressionLevel = 0.5;
                    break;
                default:
                    this.game.aiController.aggressionLevel = 1.0;
            }
        }
    }
    
    // Update diplomatic system
    update(deltaTime) {
        const now = this.game.gameTime;
        
        // Update every 30 seconds
        if (now - this.lastUpdateTime >= 30) {
            this.lastUpdateTime = now;
            
            // Process ongoing trade agreements
            this.processTradeAgreements();
            
            // Random diplomatic events
            this.processRandomEvents();
            
            // Decay relations over time if no interaction
            this.processRelationDecay();
        }
    }
    
    // Process ongoing trade agreements
    processTradeAgreements() {
        Object.entries(this.tradeAgreements).forEach(([factionId, agreement]) => {
            if (!agreement) return;
            
            if (this.game.gameTime - agreement.lastTrade >= agreement.interval) {
                if (this.game.resourceManager.canAfford(agreement.playerOffer)) {
                    this.game.resourceManager.spendResources(agreement.playerOffer);
                    this.game.resourceManager.addResource(agreement.factionRequest.resource, agreement.factionRequest.amount);
                    
                    agreement.lastTrade = this.game.gameTime;
                    this.game.ui.showNotification(`Automatic trade with ${this.factions[factionId].name} completed`, 'info');
                } else {
                    // Cancel trade agreement if player can't afford it
                    this.tradeAgreements[factionId] = null;
                    this.game.ui.showNotification(`Trade agreement with ${this.factions[factionId].name} cancelled - insufficient resources`, 'warning');
                }
            }
        });
    }
    
    // Process random diplomatic events
    processRandomEvents() {
        if (Math.random() < 0.1) { // 10% chance per update cycle
            const factionIds = Object.keys(this.factions);
            const randomFaction = factionIds[Math.floor(Math.random() * factionIds.length)];
            
            const events = [
                { type: 'resource_gift', chance: 0.3, effect: () => this.receiveResourceGift(randomFaction) },
                { type: 'territorial_dispute', chance: 0.2, effect: () => this.territorialDispute(randomFaction) },
                { type: 'trade_opportunity', chance: 0.3, effect: () => this.tradeOpportunity(randomFaction) },
                { type: 'technology_exchange', chance: 0.2, effect: () => this.technologyExchange(randomFaction) }
            ];
            
            const event = events.find(e => Math.random() < e.chance);
            if (event) {
                event.effect();
            }
        }
    }
    
    // Process gradual relation decay
    processRelationDecay() {
        Object.keys(this.factions).forEach(factionId => {
            const currentRelation = this.relations[factionId];
            
            // Relations slowly decay toward neutral if no recent interaction
            if (currentRelation > 0) {
                this.relations[factionId] = Math.max(0, currentRelation - 1);
            } else if (currentRelation < 0) {
                this.relations[factionId] = Math.min(0, currentRelation + 1);
            }
        });
    }
    
    // Random event: Receive resource gift
    receiveResourceGift(factionId) {
        const state = this.getDiplomaticState(factionId);
        if (state === 'FRIENDLY' || state === 'ALLIED') {
            const giftAmount = 200 + Math.floor(Math.random() * 300);
            this.game.resourceManager.addResource('spice', giftAmount);
            this.game.ui.showNotification(`${this.factions[factionId].name} sends a gift of ${giftAmount} spice!`, 'success');
            this.modifyRelation(factionId, 2, 'resource gift');
        }
    }
    
    // Random event: Territorial dispute
    territorialDispute(factionId) {
        const state = this.getDiplomaticState(factionId);
        if (state !== 'ALLIED') {
            this.modifyRelation(factionId, -5, 'territorial dispute');
            this.game.ui.showNotification(`Territorial dispute with ${this.factions[factionId].name}!`, 'warning');
        }
    }
    
    // Random event: Trade opportunity
    tradeOpportunity(factionId) {
        const state = this.getDiplomaticState(factionId);
        if (state === 'NEUTRAL' || state === 'FRIENDLY') {
            this.game.ui.showNotification(`${this.factions[factionId].name} offers a special trade opportunity!`, 'info');
            // This could open a special trade dialog
        }
    }
    
    // Random event: Technology exchange
    technologyExchange(factionId) {
        const state = this.getDiplomaticState(factionId);
        if (state === 'ALLIED' && this.game.technologySystem.isResearched('trade_networks')) {
            this.game.resourceManager.addResource('techPoints', 50);
            this.game.ui.showNotification(`Technology exchange with ${this.factions[factionId].name} grants 50 tech points!`, 'success');
            this.modifyRelation(factionId, 3, 'technology exchange');
        }
    }
    
    // Get diplomatic summary for UI
    getDiplomaticSummary() {
        const summary = {};
        
        Object.keys(this.factions).forEach(factionId => {
            const faction = this.factions[factionId];
            const relation = this.relations[factionId];
            const state = this.getDiplomaticState(factionId);
            
            summary[factionId] = {
                name: faction.name,
                relation,
                state,
                stateInfo: this.diplomaticStates[state],
                treaties: this.treaties[factionId],
                tradeAgreement: this.tradeAgreements[factionId],
                canDeclareWar: this.canPerformAction(factionId, 'declare_war'),
                canProposeAlliance: this.canPerformAction(factionId, 'propose_alliance'),
                canProposeTrade: this.canPerformAction(factionId, 'propose_trade')
            };
        });
        
        return summary;
    }
}