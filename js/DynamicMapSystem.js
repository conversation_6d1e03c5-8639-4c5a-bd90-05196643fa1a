export class DynamicMapSystem {
    constructor(game) {
        this.game = game;
        
        // Map generation parameters
        this.mapConfig = {
            width: 3000,
            height: 3000,
            biomes: ['desert', 'rocky', 'oasis', 'dunes', 'canyon'],
            spiceFieldCount: { min: 8, max: 15 },
            neutralStructureCount: { min: 3, max: 8 },
            terrainFeatureCount: { min: 10, max: 20 }
        };
        
        // Dynamic elements
        this.dynamicSpiceFields = [];
        this.neutralStructures = [];
        this.mapEvents = [];
        this.sandwormActivity = [];
        
        // Spice bloom system
        this.spiceBloomTimer = 0;
        this.spiceBloomInterval = 180; // 3 minutes
        
        // Sandworm system
        this.sandworms = [];
        this.sandwormSpawnTimer = 0;
        this.sandwormSpawnInterval = 240; // 4 minutes
        
        // Resource depletion tracking
        this.depletedFields = new Set();
        
        // Map event types
        this.eventTypes = {
            spice_bloom: {
                name: 'Spice Bloom',
                description: 'A massive spice eruption creates new deposits',
                frequency: 0.3,
                effect: this.createSpiceBloom.bind(this)
            },
            sandworm_activity: {
                name: 'Sandworm Activity',
                description: 'Giant sandworms emerge, threatening units',
                frequency: 0.2,
                effect: this.spawnSandworm.bind(this)
            },
            resource_discovery: {
                name: 'Resource Discovery',
                description: 'New resource deposits are discovered',
                frequency: 0.25,
                effect: this.discoverResources.bind(this)
            },
            weather_storm: {
                name: 'Desert Storm',
                description: 'Sandstorm reduces visibility and movement',
                frequency: 0.15,
                effect: this.createSandstorm.bind(this)
            },
            neutral_settlement: {
                name: 'Neutral Settlement',
                description: 'Independent settlements appear on the map',
                frequency: 0.1,
                effect: this.createNeutralSettlement.bind(this)
            }
        };
        
        this.lastEventTime = 0;
        this.eventInterval = 120; // 2 minutes between potential events
    }
    
    // Generate procedural terrain with varied spice deposits
    generateProceduralTerrain() {
        // Use the correct gameState entity paths
        this.game.gameState.entities.terrain = [];
        this.game.gameState.entities.spiceFields = [];
        this.dynamicSpiceFields = [];
        
        // Generate base terrain features
        this.generateTerrainFeatures();
        
        // Generate initial spice fields
        this.generateInitialSpiceFields();
        
        // Generate neutral structures
        this.generateNeutralStructures();
        
        // Update pathfinding grid
        this.game.pathfinder.updateGridObstacles();
    }
    
    generateTerrainFeatures() {
        const featureCount = this.mapConfig.terrainFeatureCount.min + 
            Math.floor(Math.random() * (this.mapConfig.terrainFeatureCount.max - this.mapConfig.terrainFeatureCount.min));
        
        for (let i = 0; i < featureCount; i++) {
            const featureType = this.getRandomTerrainType();
            const feature = this.createTerrainFeature(featureType);
            
            if (this.isValidTerrainPlacement(feature)) {
                this.game.gameState.entities.terrain.push(feature);
            }
        }
    }
    
    getRandomTerrainType() {
        const types = [
            { type: 'rock', weight: 0.4 },
            { type: 'water', weight: 0.2 },
            { type: 'cliff', weight: 0.2 },
            { type: 'dune', weight: 0.15 },
            { type: 'canyon', weight: 0.05 }
        ];
        
        const random = Math.random();
        let cumulative = 0;
        
        for (const typeData of types) {
            cumulative += typeData.weight;
            if (random <= cumulative) {
                return typeData.type;
            }
        }
        
        return 'rock';
    }
    
    createTerrainFeature(type) {
        const x = Math.random() * this.mapConfig.width;
        const y = Math.random() * this.mapConfig.height;
        
        const baseRadius = 30 + Math.random() * 40;
        
        switch (type) {
            case 'rock':
                return {
                    x, y,
                    radius: baseRadius,
                    type: 'rock',
                    hardness: 0.8 + Math.random() * 0.2
                };
            case 'water':
                return {
                    x, y,
                    radius: baseRadius * 1.5,
                    type: 'water',
                    depth: Math.random()
                };
            case 'cliff':
                return {
                    x, y,
                    radius: baseRadius * 0.8,
                    type: 'cliff',
                    height: 50 + Math.random() * 100
                };
            case 'dune':
                return {
                    x, y,
                    radius: baseRadius * 2,
                    type: 'dune',
                    height: 20 + Math.random() * 30,
                    moveable: true
                };
            case 'canyon':
                return {
                    x, y,
                    radius: baseRadius * 1.2,
                    type: 'canyon',
                    depth: 30 + Math.random() * 50
                };
            default:
                return { x, y, radius: baseRadius, type: 'rock' };
        }
    }
    
    isValidTerrainPlacement(feature) {
        // Check distance from starting bases
        const playerBase = { x: 400, y: 400 };
        const enemyBase = { x: 2600, y: 2600 };
        
        const distToPlayer = Math.hypot(feature.x - playerBase.x, feature.y - playerBase.y);
        const distToEnemy = Math.hypot(feature.x - enemyBase.x, feature.y - enemyBase.y);
        
        if (distToPlayer < 300 || distToEnemy < 300) return false;
        
        // Check overlap with existing terrain
        return !this.game.gameState.entities.terrain.some(existing => {
            const dist = Math.hypot(existing.x - feature.x, existing.y - feature.y);
            return dist < (existing.radius + feature.radius + 20);
        });
    }
    
    generateInitialSpiceFields() {
        const fieldCount = this.mapConfig.spiceFieldCount.min + 
            Math.floor(Math.random() * (this.mapConfig.spiceFieldCount.max - this.mapConfig.spiceFieldCount.min));
        
        for (let i = 0; i < fieldCount; i++) {
            const field = this.createSpiceField(i);
            if (this.isValidSpiceFieldPlacement(field)) {
                this.game.gameState.entities.spiceFields.push(field);
                this.dynamicSpiceFields.push({
                    ...field,
                    originalAmount: field.amount,
                    depletionRate: 0.1 + Math.random() * 0.2,
                    regenerationRate: 0.05 + Math.random() * 0.1
                });
            }
        }
    }
    
    createSpiceField(id) {
        const quality = Math.random();
        let radius, amount, maxAmount;
        
        if (quality < 0.6) {
            // Common field
            radius = 60 + Math.random() * 40;
            amount = 5000 + Math.random() * 5000;
            maxAmount = 10000;
        } else if (quality < 0.9) {
            // Rich field
            radius = 80 + Math.random() * 60;
            amount = 8000 + Math.random() * 7000;
            maxAmount = 15000;
        } else {
            // Legendary field
            radius = 100 + Math.random() * 80;
            amount = 12000 + Math.random() * 13000;
            maxAmount = 25000;
        }
        
        return {
            id: `dynamic_spice_${id}`,
            x: Math.random() * (this.mapConfig.width - 600) + 300,
            y: Math.random() * (this.mapConfig.height - 600) + 300,
            radius,
            amount,
            maxAmount,
            type: 'spice_field',
            quality: quality < 0.6 ? 'common' : quality < 0.9 ? 'rich' : 'legendary'
        };
    }
    
    isValidSpiceFieldPlacement(field) {
        // Check distance from bases
        const playerBase = { x: 400, y: 400 };
        const enemyBase = { x: 2600, y: 2600 };
        
        const distToPlayer = Math.hypot(field.x - playerBase.x, field.y - playerBase.y);
        const distToEnemy = Math.hypot(field.x - enemyBase.x, field.y - enemyBase.y);
        
        if (distToPlayer < 400 || distToEnemy < 400) return false;
        
        // Check overlap with terrain and other spice fields
        const overlapsWithTerrain = this.game.gameState.entities.terrain.some(terrain => {
            const dist = Math.hypot(terrain.x - field.x, terrain.y - field.y);
            return dist < (terrain.radius + field.radius + 50);
        });
        
        const overlapsWithSpice = this.game.gameState.entities.spiceFields.some(existing => {
            const dist = Math.hypot(existing.x - field.x, existing.y - field.y);
            return dist < (existing.radius + field.radius + 100);
        });
        
        return !overlapsWithTerrain && !overlapsWithSpice;
    }
    
    generateNeutralStructures() {
        const structureCount = this.mapConfig.neutralStructureCount.min + 
            Math.floor(Math.random() * (this.mapConfig.neutralStructureCount.max - this.mapConfig.neutralStructureCount.min));
        
        const structureTypes = [
            { type: 'outpost', weight: 0.4, benefit: 'vision' },
            { type: 'research_station', weight: 0.2, benefit: 'tech_points' },
            { type: 'resource_cache', weight: 0.3, benefit: 'resources' },
            { type: 'comm_array', weight: 0.1, benefit: 'global_coordination' }
        ];
        
        for (let i = 0; i < structureCount; i++) {
            const structureType = this.selectWeightedStructureType(structureTypes);
            const structure = this.createNeutralStructure(structureType, i);
            
            if (this.isValidNeutralStructurePlacement(structure)) {
                this.neutralStructures.push(structure);
            }
        }
    }
    
    selectWeightedStructureType(types) {
        const random = Math.random();
        let cumulative = 0;
        
        for (const typeData of types) {
            cumulative += typeData.weight;
            if (random <= cumulative) {
                return typeData;
            }
        }
        
        return types[0];
    }
    
    createNeutralStructure(typeData, id) {
        return {
            id: `neutral_structure_${id}`,
            x: Math.random() * (this.mapConfig.width - 800) + 400,
            y: Math.random() * (this.mapConfig.height - 800) + 400,
            type: typeData.type,
            benefit: typeData.benefit,
            health: 500,
            maxHealth: 500,
            controlled: false,
            controlledBy: null,
            captureProgress: 0,
            captureTime: 30, // 30 seconds to capture
            width: 60,
            height: 60
        };
    }
    
    isValidNeutralStructurePlacement(structure) {
        // Check distance from bases and other structures
        const minDistance = 200;
        
        const tooClose = this.neutralStructures.some(existing => {
            const dist = Math.hypot(existing.x - structure.x, existing.y - structure.y);
            return dist < minDistance;
        });
        
        if (tooClose) return false;
        
        // Check overlap with terrain
        const overlapsWithTerrain = this.game.gameState.entities.terrain.some(terrain => {
            const dist = Math.hypot(terrain.x - structure.x, terrain.y - structure.y);
            return dist < (terrain.radius + 80);
        });
        
        return !overlapsWithTerrain;
    }
    
    // Dynamic spice bloom event
    createSpiceBloom() {
        const bloomLocation = {
            x: Math.random() * this.mapConfig.width,
            y: Math.random() * this.mapConfig.height
        };
        
        // Create new spice field at bloom location
        const newField = {
            id: `bloom_spice_${Date.now()}`,
            x: bloomLocation.x,
            y: bloomLocation.y,
            radius: 80 + Math.random() * 60,
            amount: 8000 + Math.random() * 7000,
            maxAmount: 15000,
            type: 'spice_field',
            quality: 'bloom'
        };
        
        // Check if placement is valid
        if (this.isValidSpiceFieldPlacement(newField)) {
            this.game.gameState.entities.spiceFields.push(newField);
            this.dynamicSpiceFields.push({
                ...newField,
                originalAmount: newField.amount,
                depletionRate: 0.15,
                regenerationRate: 0.08,
                isBloom: true
            });
            
            // Create visual effect
            this.game.createExplosion(bloomLocation.x, bloomLocation.y, 100, 'spice_bloom');
            
            this.game.ui.showNotification('Spice bloom detected! New spice field discovered!', 'success');
            
            return true;
        }
        
        return false;
    }
    
    // Spawn sandworm
    spawnSandworm() {
        // Find an area with spice activity
        const activeSpiceFields = this.game.gameState.entities.spiceFields.filter(field => field.amount > 1000);
        if (activeSpiceFields.length === 0) return false;
        
        const targetField = activeSpiceFields[Math.floor(Math.random() * activeSpiceFields.length)];
        
        const sandworm = {
            id: `sandworm_${Date.now()}`,
            x: targetField.x + (Math.random() - 0.5) * 200,
            y: targetField.y + (Math.random() - 0.5) * 200,
            health: 2000,
            maxHealth: 2000,
            damage: 300,
            speed: 40,
            size: 80,
            type: 'sandworm',
            team: 'neutral',
            aggressive: true,
            detectionRange: 200,
            lifetime: 180, // 3 minutes
            spawnTime: this.game.gameTime
        };
        
        this.sandworms.push(sandworm);
        this.game.createUnit('sandworm', sandworm.x, sandworm.y, 'neutral');
        
        this.game.ui.showNotification('Sandworm activity detected! Massive creature spotted!', 'warning');
        
        return true;
    }
    
    // Discover new resources
    discoverResources() {
        const discoveryTypes = [
            { type: 'water', amount: 300, message: 'Underground water source discovered!' },
            { type: 'techPoints', amount: 100, message: 'Ancient technology cache found!' },
            { type: 'spice', amount: 1000, message: 'Hidden spice reserve uncovered!' }
        ];
        
        const discovery = discoveryTypes[Math.floor(Math.random() * discoveryTypes.length)];
        
        this.game.resourceManager.addResource(discovery.type, discovery.amount);
        this.game.ui.showNotification(discovery.message, 'success');
        
        return true;
    }
    
    // Create sandstorm weather event
    createSandstorm() {
        if (!this.game.environmentalSystem) return false;
        
        // Create a large sandstorm that affects visibility and movement
        this.game.environmentalSystem.createWeatherEvent('sandstorm', {
            x: Math.random() * this.mapConfig.width,
            y: Math.random() * this.mapConfig.height,
            radius: 300 + Math.random() * 200,
            duration: 120, // 2 minutes
            effects: {
                visibilityReduction: 0.5,
                movementReduction: 0.3,
                damageOverTime: 2
            }
        });
        
        this.game.ui.showNotification('Sandstorm approaching! Visibility and movement reduced!', 'warning');
        
        return true;
    }
    
    // Create neutral settlement
    createNeutralSettlement() {
        const settlement = this.createNeutralStructure({ type: 'settlement', benefit: 'trade' }, Date.now());
        
        if (this.isValidNeutralStructurePlacement(settlement)) {
            settlement.health = 800;
            settlement.maxHealth = 800;
            settlement.tradeGoods = {
                spice: 500 + Math.random() * 1000,
                water: 200 + Math.random() * 300,
                techPoints: 50 + Math.random() * 100
            };
            
            this.neutralStructures.push(settlement);
            this.game.ui.showNotification('Neutral settlement established! Trade opportunities available!', 'info');
            
            return true;
        }
        
        return false;
    }
    
    // Update dynamic map elements
    update(deltaTime) {
        const now = this.game.gameTime;
        
        // Update spice bloom timer
        this.spiceBloomTimer += deltaTime;
        if (this.spiceBloomTimer >= this.spiceBloomInterval) {
            this.spiceBloomTimer = 0;
            if (Math.random() < 0.3) { // 30% chance
                this.createSpiceBloom();
            }
        }
        
        // Update sandworm spawn timer
        this.sandwormSpawnTimer += deltaTime;
        if (this.sandwormSpawnTimer >= this.sandwormSpawnInterval) {
            this.sandwormSpawnTimer = 0;
            if (Math.random() < 0.2) { // 20% chance
                this.spawnSandworm();
            }
        }
        
        // Process random map events
        if (now - this.lastEventTime >= this.eventInterval) {
            this.lastEventTime = now;
            this.processRandomMapEvent();
        }
        
        // Update dynamic spice fields
        this.updateDynamicSpiceFields(deltaTime);
        
        // Update sandworm behavior
        this.updateSandworms(deltaTime);
        
        // Update neutral structures
        this.updateNeutralStructures(deltaTime);
    }
    
    processRandomMapEvent() {
        if (Math.random() < 0.15) { // 15% chance per interval
            const eventTypes = Object.keys(this.eventTypes);
            const randomEvent = eventTypes[Math.floor(Math.random() * eventTypes.length)];
            const eventData = this.eventTypes[randomEvent];
            
            if (Math.random() < eventData.frequency) {
                eventData.effect();
                
                this.mapEvents.push({
                    type: randomEvent,
                    timestamp: this.game.gameTime,
                    description: eventData.description
                });
            }
        }
    }
    
    updateDynamicSpiceFields(deltaTime) {
        this.dynamicSpiceFields.forEach((field, index) => {
            const gameField = this.game.gameState.entities.spiceFields.find(f => f.id === field.id);
            if (!gameField) return;
            
            // Handle depletion
            if (gameField.amount <= 0 && !this.depletedFields.has(field.id)) {
                this.depletedFields.add(field.id);
                this.game.ui.showNotification(`Spice field depleted: ${field.quality} quality deposit`, 'warning');
            }
            
            // Handle regeneration for bloom fields
            if (field.isBloom && gameField.amount < field.maxAmount) {
                const regenAmount = field.regenerationRate * deltaTime * 60;
                gameField.amount = Math.min(field.maxAmount, gameField.amount + regenAmount);
            }
        });
    }
    
    updateSandworms(deltaTime) {
        this.sandworms = this.sandworms.filter(sandworm => {
            const age = this.game.gameTime - sandworm.spawnTime;
            
            if (age >= sandworm.lifetime) {
                // Remove sandworm from game
                const gameUnit = this.game.units.find(u => u.x === sandworm.x && u.y === sandworm.y && u.type === 'sandworm');
                if (gameUnit) {
                    gameUnit.health = 0; // This will trigger removal in next update
                }
                return false;
            }
            
            return true;
        });
    }
    
    updateNeutralStructures(deltaTime) {
        this.neutralStructures.forEach(structure => {
            if (structure.controlled) {
                // Provide benefits to controlling player
                this.provideBenefit(structure, deltaTime);
            } else {
                // Check for capture attempts
                this.checkCaptureAttempts(structure, deltaTime);
            }
        });
    }
    
    provideBenefit(structure, deltaTime) {
        const benefitInterval = 30; // Every 30 seconds
        
        if (!structure.lastBenefitTime) structure.lastBenefitTime = 0;
        
        if (this.game.gameTime - structure.lastBenefitTime >= benefitInterval) {
            structure.lastBenefitTime = this.game.gameTime;
            
            switch (structure.benefit) {
                case 'vision':
                    // Extended vision range around structure
                    break;
                case 'tech_points':
                    this.game.resourceManager.addResource('techPoints', 25);
                    this.game.ui.showNotification('Research station provides tech points!', 'info');
                    break;
                case 'resources':
                    this.game.resourceManager.addResource('spice', 200);
                    this.game.resourceManager.addResource('water', 50);
                    this.game.ui.showNotification('Resource cache provides supplies!', 'info');
                    break;
                case 'global_coordination':
                    // Improved unit coordination
                    break;
                case 'trade':
                    // Trade opportunities
                    break;
            }
        }
    }
    
    checkCaptureAttempts(structure, deltaTime) {
        // Check if player units are near the structure
        const nearbyUnits = this.game.teams.player.units.filter(unit => {
            const dist = Math.hypot(unit.x - structure.x, unit.y - structure.y);
            return dist < 100; // Within 100 units
        });
        
        if (nearbyUnits.length > 0) {
            structure.captureProgress += deltaTime;
            
            if (structure.captureProgress >= structure.captureTime) {
                structure.controlled = true;
                structure.controlledBy = 'player';
                structure.captureProgress = 0;
                
                this.game.ui.showNotification(`Neutral structure captured: ${structure.type}!`, 'success');
            }
        } else {
            // Reset capture progress if no units nearby
            structure.captureProgress = Math.max(0, structure.captureProgress - deltaTime * 2);
        }
    }
    
    // Get map status for UI
    getMapStatus() {
        return {
            totalSpiceFields: this.game.gameState.entities.spiceFields.length,
            depletedFields: this.depletedFields.size,
            neutralStructures: this.neutralStructures.length,
            controlledStructures: this.neutralStructures.filter(s => s.controlled).length,
            activeSandworms: this.sandworms.length,
            recentEvents: this.mapEvents.slice(-5) // Last 5 events
        };
    }
    
    // Draw dynamic map elements
    draw(ctx) {
        // Draw neutral structures
        this.neutralStructures.forEach(structure => {
            this.drawNeutralStructure(ctx, structure);
        });
        
        // Draw sandworm indicators
        this.sandworms.forEach(sandworm => {
            this.drawSandwormIndicator(ctx, sandworm);
        });
    }
    
    drawNeutralStructure(ctx, structure) {
        ctx.save();
        
        // Structure base
        ctx.fillStyle = structure.controlled ? '#00FF00' : '#888888';
        ctx.strokeStyle = structure.controlled ? '#00AA00' : '#666666';
        ctx.lineWidth = 2;
        
        ctx.fillRect(
            structure.x - structure.width / 2,
            structure.y - structure.height / 2,
            structure.width,
            structure.height
        );
        ctx.strokeRect(
            structure.x - structure.width / 2,
            structure.y - structure.height / 2,
            structure.width,
            structure.height
        );
        
        // Capture progress indicator
        if (!structure.controlled && structure.captureProgress > 0) {
            const progressWidth = structure.width * (structure.captureProgress / structure.captureTime);
            ctx.fillStyle = '#FFFF00';
            ctx.fillRect(
                structure.x - structure.width / 2,
                structure.y - structure.height / 2 - 10,
                progressWidth,
                5
            );
        }
        
        // Structure type indicator
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(structure.type.charAt(0).toUpperCase(), structure.x, structure.y + 4);
        
        ctx.restore();
    }
    
    drawSandwormIndicator(ctx, sandworm) {
        ctx.save();
        
        // Sandworm warning indicator
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 3;
        ctx.setLineDash([5, 5]);
        
        ctx.beginPath();
        ctx.arc(sandworm.x, sandworm.y, sandworm.detectionRange, 0, Math.PI * 2);
        ctx.stroke();
        
        ctx.setLineDash([]);
        ctx.restore();
    }
}