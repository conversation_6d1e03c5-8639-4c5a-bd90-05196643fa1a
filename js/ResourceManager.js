export class ResourceManager {
    constructor(game) {
        this.game = game;
        
        // Extended resource system
        this.resources = {
            spice: 3000,
            water: 500,
            techPoints: 0,
            power: 0,
            maxPower: 0
        };
        
        // Resource generation rates
        this.generation = {
            water: 0,
            techPoints: 0
        };
        
        // Resource consumption tracking
        this.consumption = {
            water: 0,
            techPoints: 0
        };
        
        // Resource interdependencies
        this.dependencies = {
            // Units that require water
            waterUnits: ['frigate', 'submarine', 'medic'],
            // Buildings that require water
            waterBuildings: ['naval_yard', 'water_extraction'],
            // Units that generate tech points when built/killed
            techPointSources: ['engineer', 'medic', 'stealth_trooper', 'siege_tank']
        };
        
        this.lastUpdateTime = 0;
    }
    
    // Get current resource amount
    getResource(type) {
        return this.resources[type] || 0;
    }
    
    // Set resource amount
    setResource(type, amount) {
        if (this.resources.hasOwnProperty(type)) {
            this.resources[type] = Math.max(0, amount);
            this.game.ui.updateResourceDisplay();
        }
    }
    
    // Add resources
    addResource(type, amount) {
        if (this.resources.hasOwnProperty(type)) {
            this.resources[type] += amount;
            this.game.ui.updateResourceDisplay();
        }
    }
    
    // Spend resources (returns true if successful)
    spendResource(type, amount) {
        if (this.resources[type] >= amount) {
            this.resources[type] -= amount;
            this.game.ui.updateResourceDisplay();
            return true;
        }
        return false;
    }
    
    // Check if player can afford something
    canAfford(costs) {
        for (const [resource, cost] of Object.entries(costs)) {
            if (this.resources[resource] < cost) {
                return false;
            }
        }
        return true;
    }
    
    // Spend multiple resources
    spendResources(costs) {
        if (!this.canAfford(costs)) {
            return false;
        }
        
        for (const [resource, cost] of Object.entries(costs)) {
            this.resources[resource] -= cost;
        }
        
        this.game.ui.updateResourceDisplay();
        return true;
    }
    
    // Check if unit/building requires water
    requiresWater(type, category) {
        if (category === 'unit') {
            return this.dependencies.waterUnits.includes(type);
        } else if (category === 'building') {
            return this.dependencies.waterBuildings.includes(type);
        }
        return false;
    }
    
    // Award tech points for actions
    awardTechPoints(amount, reason) {
        this.addResource('techPoints', amount);
        if (reason && this.game.ui) {
            this.game.ui.showNotification(`+${amount} Tech Points (${reason})`, 'info');
        }
    }
    
    // Update resource generation and consumption
    update(deltaTime) {
        const now = this.game.gameTime;
        
        // Update every second
        if (now - this.lastUpdateTime >= 1) {
            this.lastUpdateTime = now;
            
            // Calculate water generation
            this.generation.water = 0;
            this.game.teams.player.buildings.forEach(building => {
                if (building.type === 'water_extraction' && building.constructionProgress >= 100) {
                    this.generation.water += building.waterProduction || 10;
                }
            });
            
            // Calculate tech point generation from research labs
            this.generation.techPoints = 0;
            this.game.teams.player.buildings.forEach(building => {
                if (building.type === 'research_lab' && building.constructionProgress >= 100) {
                    this.generation.techPoints += 2; // Base tech point generation
                }
            });
            
            // Apply generation
            this.addResource('water', this.generation.water);
            this.addResource('techPoints', this.generation.techPoints);
            
            // Calculate consumption
            this.calculateConsumption();
        }
    }
    
    // Calculate current resource consumption
    calculateConsumption() {
        this.consumption.water = 0;
        this.consumption.techPoints = 0;
        
        // Water consumption from units
        this.game.teams.player.units.forEach(unit => {
            if (this.requiresWater(unit.type, 'unit')) {
                this.consumption.water += 1; // 1 water per water-dependent unit per update
            }
        });
        
        // Water consumption from buildings
        this.game.teams.player.buildings.forEach(building => {
            if (this.requiresWater(building.type, 'building') && building.constructionProgress >= 100) {
                this.consumption.water += 2; // 2 water per water-dependent building per update
            }
        });
        
        // Apply consumption
        this.resources.water = Math.max(0, this.resources.water - this.consumption.water);
        
        // If water runs out, show warning
        if (this.resources.water === 0 && this.consumption.water > 0) {
console.log('DEBUG: Attempting to show water shortage notification. Water:', this.resources.water, 'Consumption:', this.consumption.water);
            this.game.ui.showNotification('Water shortage! Build more Water Extraction Plants!', 'warning');
        }
    }
    
    // Get resource generation info for UI
    getResourceInfo() {
        return {
            resources: { ...this.resources },
            generation: { ...this.generation },
            consumption: { ...this.consumption }
        };
    }
    
    // Phase 4 Integration Methods
    updateEconomicSettings(settings) {
        // Update resource generation multipliers
        if (settings.resourceMultiplier) {
            this.resourceMultiplier = settings.resourceMultiplier;
        }
        
        // Update consumption rates
        if (settings.consumptionMultiplier) {
            this.consumptionMultiplier = settings.consumptionMultiplier;
        }
        
        // Update starting resources for difficulty
        if (settings.startingResources) {
            Object.assign(this.resources, settings.startingResources);
            this.game.ui.updateResourceDisplay();
        }
    }
    
    // Export state for save/load
    exportState() {
        return {
            resources: { ...this.resources },
            generation: { ...this.generation },
            consumption: { ...this.consumption },
            lastUpdateTime: this.lastUpdateTime,
            resourceMultiplier: this.resourceMultiplier || 1,
            consumptionMultiplier: this.consumptionMultiplier || 1
        };
    }
    
    // Import state for save/load
    importState(state) {
        this.resources = { ...state.resources };
        this.generation = { ...state.generation };
        this.consumption = { ...state.consumption };
        this.lastUpdateTime = state.lastUpdateTime || 0;
        this.resourceMultiplier = state.resourceMultiplier || 1;
        this.consumptionMultiplier = state.consumptionMultiplier || 1;
        
        this.game.ui.updateResourceDisplay();
    }
    
    // Get efficiency rating for balance manager
    getEfficiencyRating() {
        const totalGeneration = Object.values(this.generation).reduce((sum, val) => sum + val, 0);
        const totalConsumption = Object.values(this.consumption).reduce((sum, val) => sum + val, 0);
        
        if (totalConsumption === 0) return 1;
        return Math.min(1, totalGeneration / totalConsumption);
    }
    
    // Apply difficulty modifiers
    applyDifficultyModifiers(difficulty) {
        const modifiers = {
            easy: { resourceMultiplier: 1.5, consumptionMultiplier: 0.8 },
            normal: { resourceMultiplier: 1.0, consumptionMultiplier: 1.0 },
            hard: { resourceMultiplier: 0.7, consumptionMultiplier: 1.3 },
            nightmare: { resourceMultiplier: 0.5, consumptionMultiplier: 1.5 }
        };
        
        const modifier = modifiers[difficulty] || modifiers.normal;
        this.updateEconomicSettings(modifier);
    }
    
    // Set difficulty multiplier for balance manager
    setDifficultyMultiplier(multiplier) {
        this.resourceMultiplier = multiplier;
    }
}