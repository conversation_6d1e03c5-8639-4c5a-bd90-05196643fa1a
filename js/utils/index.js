/**
 * Main utility exports for the Dune2 game
 * Provides centralized access to all utility functions and constants
 */

// Import all utility modules
export * from './MathUtils.js';
export * from './GameUtils.js';
export * from './Constants.js';

// Re-export main utility objects for convenience
export { MathUtils } from './MathUtils.js';
export { GameUtils } from './GameUtils.js';
export { Constants } from './Constants.js';

// Legacy exports for backward compatibility
export { 
    distance,
    angleBetween,
    normalizeVector,
    DistanceUtils,
    AngleUtils,
    VectorUtils,
    PolarUtils,
    CollisionUtils,
    InterpolationUtils
} from './MathUtils.js';

export {
    ResourceUtils,
    UnitUtils,
    BuildingUtils,
    ValidationUtils,
    TeamUtils,
    TimeUtils
} from './GameUtils.js';

// Commonly used constants for quick access
export {
    TEAMS,
    UNIT_TYPES,
    BUILDING_TYPES,
    FORMATIONS,
    COLORS,
    MOVEMENT,
    COMBAT
} from './Constants.js';