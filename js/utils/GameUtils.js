/**
 * Game-specific utility functions for the Dune2 game
 * Contains validation, formatting, and common game logic utilities
 */

/**
 * Resource formatting utilities
 */
export const ResourceUtils = {
    /**
     * Format resource amount for display
     * @param {number} amount - Resource amount
     * @param {boolean} showDecimals - Whether to show decimal places
     * @returns {string} Formatted resource string
     */
    formatAmount(amount, showDecimals = false) {
        if (amount >= 1000000) {
            return (amount / 1000000).toFixed(showDecimals ? 1 : 0) + 'M';
        } else if (amount >= 1000) {
            return (amount / 1000).toFixed(showDecimals ? 1 : 0) + 'K';
        }
        return Math.floor(amount).toString();
    },

    /**
     * Calculate resource efficiency
     * @param {number} produced - Amount produced
     * @param {number} consumed - Amount consumed
     * @returns {number} Efficiency ratio
     */
    calculateEfficiency(produced, consumed) {
        if (consumed === 0) return produced > 0 ? Infinity : 0;
        return produced / consumed;
    },

    /**
     * Check if player has enough resources
     * @param {Object} playerResources - Player's current resources
     * @param {Object} requiredResources - Required resources
     * @returns {boolean} True if player has enough resources
     */
    hasEnoughResources(playerResources, requiredResources) {
        for (const [resource, amount] of Object.entries(requiredResources)) {
            if ((playerResources[resource] || 0) < amount) {
                return false;
            }
        }
        return true;
    }
};

/**
 * Unit type checking utilities
 */
export const UnitUtils = {
    /**
     * Check if unit is a combat unit
     * @param {Object} unit - Unit object
     * @returns {boolean} True if unit is combat type
     */
    isCombatUnit(unit) {
        const combatTypes = ['soldier', 'tank', 'trooper', 'devastator', 'siege_tank'];
        return combatTypes.includes(unit.type);
    },

    /**
     * Check if unit is a worker/harvester unit
     * @param {Object} unit - Unit object
     * @returns {boolean} True if unit is worker type
     */
    isWorkerUnit(unit) {
        const workerTypes = ['harvester', 'worker', 'engineer'];
        return workerTypes.includes(unit.type);
    },

    /**
     * Check if unit is a scout unit
     * @param {Object} unit - Unit object
     * @returns {boolean} True if unit is scout type
     */
    isScoutUnit(unit) {
        const scoutTypes = ['scout', 'trike', 'ornithopter'];
        return scoutTypes.includes(unit.type);
    },

    /**
     * Get unit's movement speed modifier based on terrain
     * @param {Object} unit - Unit object
     * @param {string} terrainType - Type of terrain
     * @returns {number} Speed modifier (1.0 = normal speed)
     */
    getTerrainSpeedModifier(unit, terrainType) {
        const modifiers = {
            sand: { default: 1.0, tracked: 1.2, wheeled: 0.8, hover: 1.1 },
            rock: { default: 0.8, tracked: 0.9, wheeled: 0.7, hover: 1.0 },
            spice: { default: 0.9, tracked: 1.0, wheeled: 0.8, hover: 1.0 }
        };

        const unitMovementType = unit.movementType || 'default';
        return modifiers[terrainType]?.[unitMovementType] || 1.0;
    },

    /**
     * Calculate unit's effective combat strength
     * @param {Object} unit - Unit object
     * @param {Object} upgrades - Applied upgrades
     * @returns {number} Effective combat strength
     */
    getEffectiveCombatStrength(unit, upgrades = {}) {
        let strength = unit.attack || 0;
        
        // Apply upgrade modifiers
        if (upgrades.weaponUpgrade) {
            strength *= (1 + upgrades.weaponUpgrade * 0.2);
        }
        
        // Apply health modifier
        if (unit.health && unit.maxHealth) {
            strength *= (unit.health / unit.maxHealth);
        }
        
        return Math.floor(strength);
    }
};

/**
 * Building utilities
 */
export const BuildingUtils = {
    /**
     * Check if building is a production building
     * @param {Object} building - Building object
     * @returns {boolean} True if building produces units
     */
    isProductionBuilding(building) {
        const productionTypes = ['barracks', 'factory', 'starport', 'palace'];
        return productionTypes.includes(building.type);
    },

    /**
     * Check if building is a defensive structure
     * @param {Object} building - Building object
     * @returns {boolean} True if building is defensive
     */
    isDefensiveBuilding(building) {
        const defensiveTypes = ['gun_turret', 'rocket_turret', 'wall'];
        return defensiveTypes.includes(building.type);
    },

    /**
     * Get building's power consumption/generation
     * @param {Object} building - Building object
     * @returns {number} Power value (negative = consumption, positive = generation)
     */
    getPowerValue(building) {
        const powerValues = {
            windtrap: 100,
            refinery: -30,
            factory: -40,
            barracks: -20,
            starport: -50,
            palace: -60,
            gun_turret: -10,
            rocket_turret: -20
        };
        
        return powerValues[building.type] || 0;
    }
};

/**
 * Validation utilities
 */
export const ValidationUtils = {
    /**
     * Check if coordinates are within map bounds
     * @param {number} x - X coordinate
     * @param {number} y - Y coordinate
     * @param {number} mapWidth - Map width
     * @param {number} mapHeight - Map height
     * @returns {boolean} True if coordinates are valid
     */
    isWithinBounds(x, y, mapWidth, mapHeight) {
        return x >= 0 && x < mapWidth && y >= 0 && y < mapHeight;
    },

    /**
     * Check if position is valid for building placement
     * @param {number} x - X coordinate
     * @param {number} y - Y coordinate
     * @param {Object} buildingType - Building type data
     * @param {Array} existingBuildings - Array of existing buildings
     * @param {number} mapWidth - Map width
     * @param {number} mapHeight - Map height
     * @returns {boolean} True if position is valid
     */
    isValidBuildingPosition(x, y, buildingType, existingBuildings, mapWidth, mapHeight) {
        const halfWidth = buildingType.width / 2;
        const halfHeight = buildingType.height / 2;
        
        // Check map bounds
        if (x - halfWidth < 0 || x + halfWidth > mapWidth || 
            y - halfHeight < 0 || y + halfHeight > mapHeight) {
            return false;
        }
        
        // Check collision with existing buildings
        const buildingBuffer = 5;
        for (const building of existingBuildings) {
            const distance = Math.abs(building.x - x) + Math.abs(building.y - y);
            const minDistance = (building.width + buildingType.width) / 2 + 
                              (building.height + buildingType.height) / 2 + buildingBuffer;
            
            if (distance < minDistance) {
                return false;
            }
        }
        
        return true;
    },

    /**
     * Validate unit formation parameters
     * @param {Array} units - Array of units
     * @param {string} formationType - Type of formation
     * @returns {boolean} True if formation is valid
     */
    isValidFormation(units, formationType) {
        if (!units || units.length === 0) return false;
        
        const validFormations = ['line', 'column', 'wedge', 'box', 'circle'];
        if (!validFormations.includes(formationType)) return false;
        
        // Check if all units can form this formation
        if (formationType === 'circle' && units.length < 3) return false;
        if (formationType === 'wedge' && units.length < 3) return false;
        
        return true;
    }
};

/**
 * Team and faction utilities
 */
export const TeamUtils = {
    /**
     * Check if two entities are on the same team
     * @param {Object} entity1 - First entity
     * @param {Object} entity2 - Second entity
     * @returns {boolean} True if entities are allies
     */
    areAllies(entity1, entity2) {
        return entity1.team === entity2.team;
    },

    /**
     * Check if two entities are enemies
     * @param {Object} entity1 - First entity
     * @param {Object} entity2 - Second entity
     * @returns {boolean} True if entities are enemies
     */
    areEnemies(entity1, entity2) {
        return entity1.team !== entity2.team && 
               entity1.team !== 'neutral' && 
               entity2.team !== 'neutral';
    },

    /**
     * Get team color
     * @param {string} team - Team identifier
     * @returns {string} Hex color code
     */
    getTeamColor(team) {
        const teamColors = {
            player: '#4A90E2',
            ai: '#E74C3C',
            neutral: '#95A5A6',
            atreides: '#4A90E2',
            harkonnen: '#E74C3C',
            ordos: '#27AE60'
        };
        
        return teamColors[team] || '#95A5A6';
    }
};

/**
 * Time and scheduling utilities
 */
export const TimeUtils = {
    /**
     * Format game time for display
     * @param {number} gameTime - Game time in milliseconds
     * @returns {string} Formatted time string
     */
    formatGameTime(gameTime) {
        const totalSeconds = Math.floor(gameTime / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },

    /**
     * Check if enough time has passed since last action
     * @param {number} lastActionTime - Time of last action
     * @param {number} currentTime - Current time
     * @param {number} cooldown - Required cooldown period
     * @returns {boolean} True if cooldown has elapsed
     */
    isCooldownElapsed(lastActionTime, currentTime, cooldown) {
        return (currentTime - lastActionTime) >= cooldown;
    },

    /**
     * Calculate remaining cooldown time
     * @param {number} lastActionTime - Time of last action
     * @param {number} currentTime - Current time
     * @param {number} cooldown - Total cooldown period
     * @returns {number} Remaining cooldown time
     */
    getRemainingCooldown(lastActionTime, currentTime, cooldown) {
        const elapsed = currentTime - lastActionTime;
        return Math.max(0, cooldown - elapsed);
    }
};

// Export all utilities as a single object
export const GameUtils = {
    Resource: ResourceUtils,
    Unit: UnitUtils,
    Building: BuildingUtils,
    Validation: ValidationUtils,
    Team: TeamUtils,
    Time: TimeUtils
};