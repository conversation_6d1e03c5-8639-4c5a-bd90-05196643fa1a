/**
 * Centralized mathematical utility functions for the Dune2 game
 * Eliminates duplicate implementations across the codebase
 */

/**
 * Distance calculation utilities
 */
export const DistanceUtils = {
    /**
     * Calculate Euclidean distance between two points
     * @param {Object} a - First point with x, y properties
     * @param {Object} b - Second point with x, y properties
     * @returns {number} Distance between points
     */
    euclidean(a, b) {
        return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
    },

    /**
     * Calculate squared distance (faster when you don't need exact distance)
     * @param {Object} a - First point with x, y properties
     * @param {Object} b - Second point with x, y properties
     * @returns {number} Squared distance between points
     */
    squaredDistance(a, b) {
        return (a.x - b.x) ** 2 + (a.y - b.y) ** 2;
    },

    /**
     * Calculate Manhattan distance between two points
     * @param {Object} a - First point with x, y properties
     * @param {Object} b - Second point with x, y properties
     * @returns {number} Manhattan distance between points
     */
    manhattan(a, b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
    },

    /**
     * Calculate distance using Math.hypot (most accurate)
     * @param {Object} a - First point with x, y properties
     * @param {Object} b - Second point with x, y properties
     * @returns {number} Distance between points
     */
    hypot(a, b) {
        return Math.hypot(a.x - b.x, a.y - b.y);
    }
};

/**
 * Angle calculation utilities
 */
export const AngleUtils = {
    /**
     * Calculate angle between two points
     * @param {Object} from - Starting point with x, y properties
     * @param {Object} to - Target point with x, y properties
     * @returns {number} Angle in radians
     */
    angleBetween(from, to) {
        return Math.atan2(to.y - from.y, to.x - from.x);
    },

    /**
     * Normalize angle to 0-2π range
     * @param {number} angle - Angle in radians
     * @returns {number} Normalized angle
     */
    normalizeAngle(angle) {
        while (angle < 0) angle += Math.PI * 2;
        while (angle >= Math.PI * 2) angle -= Math.PI * 2;
        return angle;
    },

    /**
     * Calculate angle difference (shortest path)
     * @param {number} angle1 - First angle in radians
     * @param {number} angle2 - Second angle in radians
     * @returns {number} Angle difference in radians
     */
    angleDifference(angle1, angle2) {
        let diff = angle2 - angle1;
        while (diff > Math.PI) diff -= Math.PI * 2;
        while (diff < -Math.PI) diff += Math.PI * 2;
        return diff;
    }
};

/**
 * Vector operation utilities
 */
export const VectorUtils = {
    /**
     * Create a vector from two points
     * @param {Object} from - Starting point with x, y properties
     * @param {Object} to - Target point with x, y properties
     * @returns {Object} Vector with x, y properties
     */
    fromPoints(from, to) {
        return {
            x: to.x - from.x,
            y: to.y - from.y
        };
    },

    /**
     * Normalize a vector to unit length
     * @param {Object} vector - Vector with x, y properties
     * @returns {Object} Normalized vector
     */
    normalize(vector) {
        const length = Math.hypot(vector.x, vector.y);
        if (length === 0) return { x: 0, y: 0 };
        return {
            x: vector.x / length,
            y: vector.y / length
        };
    },

    /**
     * Scale a vector by a scalar
     * @param {Object} vector - Vector with x, y properties
     * @param {number} scalar - Scaling factor
     * @returns {Object} Scaled vector
     */
    scale(vector, scalar) {
        return {
            x: vector.x * scalar,
            y: vector.y * scalar
        };
    },

    /**
     * Add two vectors
     * @param {Object} a - First vector with x, y properties
     * @param {Object} b - Second vector with x, y properties
     * @returns {Object} Sum vector
     */
    add(a, b) {
        return {
            x: a.x + b.x,
            y: a.y + b.y
        };
    },

    /**
     * Subtract two vectors
     * @param {Object} a - First vector with x, y properties
     * @param {Object} b - Second vector with x, y properties
     * @returns {Object} Difference vector
     */
    subtract(a, b) {
        return {
            x: a.x - b.x,
            y: a.y - b.y
        };
    },

    /**
     * Calculate dot product of two vectors
     * @param {Object} a - First vector with x, y properties
     * @param {Object} b - Second vector with x, y properties
     * @returns {number} Dot product
     */
    dot(a, b) {
        return a.x * b.x + a.y * b.y;
    },

    /**
     * Get vector length
     * @param {Object} vector - Vector with x, y properties
     * @returns {number} Vector length
     */
    length(vector) {
        return Math.hypot(vector.x, vector.y);
    }
};

/**
 * Circular/polar coordinate utilities
 */
export const PolarUtils = {
    /**
     * Convert polar coordinates to cartesian
     * @param {number} angle - Angle in radians
     * @param {number} distance - Distance from origin
     * @returns {Object} Cartesian coordinates {x, y}
     */
    toCartesian(angle, distance) {
        return {
            x: Math.cos(angle) * distance,
            y: Math.sin(angle) * distance
        };
    },

    /**
     * Convert cartesian coordinates to polar
     * @param {number} x - X coordinate
     * @param {number} y - Y coordinate
     * @returns {Object} Polar coordinates {angle, distance}
     */
    toPolar(x, y) {
        return {
            angle: Math.atan2(y, x),
            distance: Math.hypot(x, y)
        };
    },

    /**
     * Generate points in a circle
     * @param {Object} center - Center point with x, y properties
     * @param {number} radius - Circle radius
     * @param {number} count - Number of points to generate
     * @param {number} startAngle - Starting angle offset
     * @returns {Array} Array of points
     */
    generateCirclePoints(center, radius, count, startAngle = 0) {
        const points = [];
        const angleStep = (Math.PI * 2) / count;
        
        for (let i = 0; i < count; i++) {
            const angle = i * angleStep + startAngle;
            points.push({
                x: center.x + Math.cos(angle) * radius,
                y: center.y + Math.sin(angle) * radius
            });
        }
        
        return points;
    },

    /**
     * Generate random point in circle
     * @param {Object} center - Center point with x, y properties
     * @param {number} radius - Circle radius
     * @returns {Object} Random point in circle
     */
    randomPointInCircle(center, radius) {
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.sqrt(Math.random()) * radius; // Uniform distribution
        return {
            x: center.x + Math.cos(angle) * distance,
            y: center.y + Math.sin(angle) * distance
        };
    }
};

/**
 * Collision detection utilities
 */
export const CollisionUtils = {
    /**
     * Check if two circles intersect
     * @param {Object} a - First circle with x, y, radius properties
     * @param {Object} b - Second circle with x, y, radius properties
     * @returns {boolean} True if circles intersect
     */
    circleIntersection(a, b) {
        const distance = DistanceUtils.euclidean(a, b);
        return distance < (a.radius + b.radius);
    },

    /**
     * Check if point is inside circle
     * @param {Object} point - Point with x, y properties
     * @param {Object} circle - Circle with x, y, radius properties
     * @returns {boolean} True if point is inside circle
     */
    pointInCircle(point, circle) {
        const distance = DistanceUtils.euclidean(point, circle);
        return distance <= circle.radius;
    },

    /**
     * Check if point is inside rectangle
     * @param {Object} point - Point with x, y properties
     * @param {Object} rect - Rectangle with x, y, width, height properties
     * @returns {boolean} True if point is inside rectangle
     */
    pointInRectangle(point, rect) {
        return point.x >= rect.x && 
               point.x <= rect.x + rect.width &&
               point.y >= rect.y && 
               point.y <= rect.y + rect.height;
    },

    /**
     * Check if two rectangles intersect
     * @param {Object} a - First rectangle with x, y, width, height properties
     * @param {Object} b - Second rectangle with x, y, width, height properties
     * @returns {boolean} True if rectangles intersect
     */
    rectangleIntersection(a, b) {
        return a.x < b.x + b.width &&
               a.x + a.width > b.x &&
               a.y < b.y + b.height &&
               a.y + a.height > b.y;
    }
};

/**
 * Interpolation and easing utilities
 */
export const InterpolationUtils = {
    /**
     * Linear interpolation between two values
     * @param {number} a - Start value
     * @param {number} b - End value
     * @param {number} t - Interpolation factor (0-1)
     * @returns {number} Interpolated value
     */
    lerp(a, b, t) {
        return a + (b - a) * t;
    },

    /**
     * Clamp value between min and max
     * @param {number} value - Value to clamp
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {number} Clamped value
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * Map value from one range to another
     * @param {number} value - Input value
     * @param {number} inMin - Input range minimum
     * @param {number} inMax - Input range maximum
     * @param {number} outMin - Output range minimum
     * @param {number} outMax - Output range maximum
     * @returns {number} Mapped value
     */
    map(value, inMin, inMax, outMin, outMax) {
        return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
    }
};

// Export all utilities as a single object for convenience
export const MathUtils = {
    Distance: DistanceUtils,
    Angle: AngleUtils,
    Vector: VectorUtils,
    Polar: PolarUtils,
    Collision: CollisionUtils,
    Interpolation: InterpolationUtils
};

// Legacy function exports for backward compatibility
export const distance = DistanceUtils.euclidean;
export const angleBetween = AngleUtils.angleBetween;
export const normalizeVector = VectorUtils.normalize;