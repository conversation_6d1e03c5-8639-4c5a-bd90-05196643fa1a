/**
 * Centralized constants for the Dune2 game
 * Eliminates magic numbers and repeated values across the codebase
 */

/**
 * Game dimensions and grid settings
 */
export const GAME_DIMENSIONS = {
    DEFAULT_WIDTH: 2000,
    DEFAULT_HEIGHT: 1500,
    GRID_SIZE: 25,
    PATHFINDER_GRID_SIZE: 25
};

/**
 * Unit movement and collision constants
 */
export const MOVEMENT = {
    BASE_COLLISION_RADIUS: 15,
    SEPARATION_FORCE: 50,
    AVOIDANCE_DISTANCE: 40,
    FORMATION_SPACING: 40,
    DETOUR_DISTANCE: 50,
    MAX_SPEED_DEFAULT: 60,
    ROTATION_SPEED: 0.1,
    ARRIVAL_THRESHOLD: 5
};

/**
 * Combat system constants
 */
export const COMBAT = {
    DEFAULT_ATTACK_RANGE: 100,
    MELEE_RANGE: 30,
    PROJECTILE_SPEED: 200,
    EXPLOSION_RADIUS: 50,
    DAMAGE_FALLOFF_RATE: 0.5,
    CRITIC<PERSON>_HIT_CHANCE: 0.1,
    ARMOR_DAMAGE_REDUCTION: 0.2
};

/**
 * Building placement and construction
 */
export const BUILDING = {
    PLACEMENT_BUFFER: 5,
    SNAP_GRID_SIZE: 12.5, // Half of pathfinder grid
    CONSTRUCTION_TIME_BASE: 3000,
    POWER_CONSUMPTION_MULTIPLIER: 1.0,
    TURRET_ROTATION_SPEED: 0.05,
    RALLY_POINT_OFFSET: 60
};

/**
 * Resource and economy constants
 */
export const ECONOMY = {
    STARTING_SPICE: 2000,
    SPICE_FIELD_CAPACITY: 5000,
    HARVEST_RATE: 10,
    REFINERY_CAPACITY: 1000,
    SPICE_DECAY_RATE: 0.001,
    TRADE_TAX_RATE: 0.1
};

/**
 * AI behavior constants
 */
export const AI = {
    DECISION_INTERVAL: 1000,
    THREAT_ASSESSMENT_RANGE: 300,
    EXPANSION_DISTANCE: 500,
    SCOUT_PATROL_RADIUS: 200,
    ATTACK_GROUP_MIN_SIZE: 3,
    DEFENSE_PERIMETER_RADIUS: 150,
    RESOURCE_PRIORITY_THRESHOLD: 0.3
};

/**
 * Visual effects and animation constants
 */
export const VISUAL_EFFECTS = {
    PARTICLE_LIFETIME: 1000,
    EXPLOSION_PARTICLE_COUNT: 20,
    BLOOD_SPLATTER_COUNT: 8,
    DEBRIS_SCATTER_DISTANCE: 50,
    SPARK_COUNT: 15,
    ANIMATION_DURATION: 500,
    FADE_DURATION: 300
};

/**
 * UI and interface constants
 */
export const UI = {
    MINIMAP_SIZE: 200,
    SELECTION_BOX_MIN_SIZE: 10,
    TOOLTIP_DELAY: 500,
    NOTIFICATION_DURATION: 3000,
    ZOOM_MIN: 0.5,
    ZOOM_MAX: 2.0,
    ZOOM_STEP: 0.1,
    PANEL_ANIMATION_SPEED: 300
};

/**
 * Audio constants
 */
export const AUDIO = {
    MASTER_VOLUME: 0.7,
    SFX_VOLUME: 0.8,
    MUSIC_VOLUME: 0.6,
    VOICE_VOLUME: 0.9,
    FADE_DURATION: 1000,
    MAX_CONCURRENT_SOUNDS: 10
};

/**
 * Performance and optimization constants
 */
export const PERFORMANCE = {
    MAX_PARTICLES: 500,
    MAX_PROJECTILES: 100,
    CULLING_MARGIN: 100,
    LOD_DISTANCE_THRESHOLD: 500,
    UPDATE_FREQUENCY: 60, // FPS
    PATHFINDING_BUDGET: 5, // Max pathfinding operations per frame
    MAX_UNITS_PER_FRAME: 20
};

/**
 * Team and faction identifiers
 */
export const TEAMS = {
    PLAYER: 'player',
    AI: 'ai',
    NEUTRAL: 'neutral',
    ATREIDES: 'atreides',
    HARKONNEN: 'harkonnen',
    ORDOS: 'ordos'
};

/**
 * Unit type categories
 */
export const UNIT_TYPES = {
    COMBAT: ['soldier', 'tank', 'trooper', 'devastator', 'siege_tank'],
    WORKER: ['harvester', 'worker', 'engineer'],
    SCOUT: ['scout', 'trike', 'ornithopter'],
    SUPPORT: ['carryall', 'miner', 'repair_vehicle']
};

/**
 * Building type categories
 */
export const BUILDING_TYPES = {
    PRODUCTION: ['barracks', 'factory', 'starport', 'palace'],
    DEFENSIVE: ['gun_turret', 'rocket_turret', 'wall'],
    ECONOMIC: ['refinery', 'silo', 'windtrap'],
    SUPPORT: ['radar', 'repair_facility', 'research_lab']
};

/**
 * Terrain type constants
 */
export const TERRAIN = {
    SAND: 'sand',
    ROCK: 'rock',
    SPICE: 'spice',
    DUNES: 'dunes',
    CLIFF: 'cliff'
};

/**
 * Movement type modifiers
 */
export const MOVEMENT_MODIFIERS = {
    SAND: { default: 1.0, tracked: 1.2, wheeled: 0.8, hover: 1.1 },
    ROCK: { default: 0.8, tracked: 0.9, wheeled: 0.7, hover: 1.0 },
    SPICE: { default: 0.9, tracked: 1.0, wheeled: 0.8, hover: 1.0 },
    DUNES: { default: 0.7, tracked: 0.9, wheeled: 0.5, hover: 1.0 }
};

/**
 * Color constants for teams and UI
 */
export const COLORS = {
    TEAMS: {
        [TEAMS.PLAYER]: '#4A90E2',
        [TEAMS.AI]: '#E74C3C',
        [TEAMS.NEUTRAL]: '#95A5A6',
        [TEAMS.ATREIDES]: '#4A90E2',
        [TEAMS.HARKONNEN]: '#E74C3C',
        [TEAMS.ORDOS]: '#27AE60'
    },
    UI: {
        SELECTION: '#00FF00',
        HEALTH_FULL: '#00FF00',
        HEALTH_MEDIUM: '#FFFF00',
        HEALTH_LOW: '#FF0000',
        ENERGY_FULL: '#0080FF',
        BACKGROUND: '#2C3E50',
        TEXT: '#FFFFFF',
        BUTTON: '#34495E',
        BUTTON_HOVER: '#4A6741'
    },
    EFFECTS: {
        EXPLOSION: '#FF6B35',
        LASER: '#FF0080',
        PLASMA: '#8A2BE2',
        FIRE: '#FF4500',
        SMOKE: '#696969'
    }
};

/**
 * Formation type constants
 */
export const FORMATIONS = {
    LINE: 'line',
    COLUMN: 'column',
    WEDGE: 'wedge',
    BOX: 'box',
    CIRCLE: 'circle',
    SPREAD: 'spread'
};

/**
 * Game state constants
 */
export const GAME_STATES = {
    MENU: 'menu',
    LOADING: 'loading',
    PLAYING: 'playing',
    PAUSED: 'paused',
    GAME_OVER: 'game_over',
    VICTORY: 'victory'
};

/**
 * Input and control constants
 */
export const INPUT = {
    DOUBLE_CLICK_TIME: 300,
    DRAG_THRESHOLD: 5,
    SCROLL_SENSITIVITY: 0.1,
    KEY_REPEAT_DELAY: 500,
    KEY_REPEAT_RATE: 50
};

/**
 * Mathematical constants commonly used in game
 */
export const MATH = {
    PI2: Math.PI * 2,
    PI_HALF: Math.PI / 2,
    PI_QUARTER: Math.PI / 4,
    DEG_TO_RAD: Math.PI / 180,
    RAD_TO_DEG: 180 / Math.PI,
    SQRT2: Math.sqrt(2),
    GOLDEN_RATIO: 1.618033988749
};

/**
 * Time constants (in milliseconds)
 */
export const TIME = {
    SECOND: 1000,
    MINUTE: 60000,
    HOUR: 3600000,
    FRAME_16MS: 16.67, // 60 FPS
    FRAME_33MS: 33.33, // 30 FPS
    ANIMATION_FAST: 150,
    ANIMATION_NORMAL: 300,
    ANIMATION_SLOW: 500
};

// Export all constants as a single object for convenience
export const Constants = {
    GAME_DIMENSIONS,
    MOVEMENT,
    COMBAT,
    BUILDING,
    ECONOMY,
    AI,
    VISUAL_EFFECTS,
    UI,
    AUDIO,
    PERFORMANCE,
    TEAMS,
    UNIT_TYPES,
    BUILDING_TYPES,
    TERRAIN,
    MOVEMENT_MODIFIERS,
    COLORS,
    FORMATIONS,
    GAME_STATES,
    INPUT,
    MATH,
    TIME
};